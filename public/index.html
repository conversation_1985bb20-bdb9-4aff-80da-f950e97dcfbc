<!DOCTYPE html>
<html lang="en" style="font-size: 16px;">
	<head>
		<meta charset="utf-8" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<!-- 清除缓存 -->
		<meta http-equiv="pragram" content="no-cache">
		<meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate">
		<meta http-equiv="expires" content="0">
		<!-- DNS预解析 -->
		<link rel="dns-prefetch" href="//cdn.staticfile.net"/>
		<meta name="viewport"
			content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=0,minimal-ui,viewport-fit=cover" />
		<link rel="icon" href="<%= BASE_URL %>favicon.ico" />
		<!-- 使用CDN加速的CSS文件，配置在vue.config.js下 -->
		<% for (var i in htmlWebpackPlugin.options.cdn&&htmlWebpackPlugin.options.cdn.css) { %>
		<link href="<%= htmlWebpackPlugin.options.cdn.css[i] %>" rel="preload" as="style" />
		<link href="<%= htmlWebpackPlugin.options.cdn.css[i] %>" rel="stylesheet" />
		<% } %>
		<title>商户通</title>
	</head>

	<body>
		<noscript>
			<strong>We're sorry but vuedemo doesn't work properly without JavaScript enabled. Please enable it to
				continue.</strong>
		</noscript>
		<div id="microApp"></div>
	</body>
	<!-- 使用CDN加速的JS文件，配置在vue.config.js下 -->
	<% for (var i in htmlWebpackPlugin.options.cdn&&htmlWebpackPlugin.options.cdn.js) { %>
	<script src="<%= htmlWebpackPlugin.options.cdn.js[i] %>"></script>
	<% } %>
	<!-- built files will be auto injected -->
</html>
