#!/bin/bash

# 获取当前时间的时间戳
timestamp=$(date +%s%N | cut -b1-13)
# Constants for paths and URLs
initialVersion="1.4.9"
json_file="public/version.json"
remote_url="https://www.rrsjk.com/mch/version.json?t=${timestamp}"

# Ensure the public directory exists
mkdir -p public

# Fetch remote version or initialize to 1.0.0 if not available
fetch_or_init_version() {
  if remote_data=$(curl --silent --show-error --fail "$remote_url"); then
    version=$(echo "$remote_data" | jq -r '.[0].version')
    [[ -z "$version" ]] && version=$initialVersion
  else
    version=$initialVersion
  fi

  echo "$version"
}

# Update version based on given type (major, minor, patch)
update_version_with_jq() {
  # shellcheck disable=SC2155
  local current_version=$(fetch_or_init_version)
  IFS='.' read -r -a version <<< "$current_version"

#  echo "current_version:"${version[*]}"";
#  exit 1;
  # Increment the correct part of the version based on the first argument
  case $1 in
    major) ((version[0]++)); version[1]=0; version[2]=0;;
    minor) ((version[1]++)); version[2]=0;;
    patch) ((version[2]++));;
    *) echo "Invalid argument"; exit 1;;
  esac

  # Reconstruct the new version string
  # shellcheck disable=SC2155
  local new_version=$(printf "%s\n" "${version[@]}" | tr '\n' '.')
  local new_version="${new_version%.}"  # 移除末尾的点

  # Prepare metadata for the JSON file
  local description="$2"
  local router_key="$3"
  # shellcheck disable=SC2155
  local timestamp=$(date +"%Y-%m-%dT%H:%M:%S")

  # Use jq to update the first object in the existing JSON file
  if [[ -f "$json_file" ]]; then
    jq --arg version "$new_version" \
       --arg description "$description" \
       --arg routerKey "$router_key" \
       --arg timestamp "$timestamp" \
       '.[0] |= {version: $version, description: $description, routerKey: $routerKey, timestamp: $timestamp}' "$json_file" > "$json_file".tmp && mv "$json_file".tmp "$json_file"
  else
    # Create the initial JSON file with the updated version
    cat > "$json_file" <<EOF
[
  {
    "version": "$new_version",
    "description": "$description",
    "routerKey": "$router_key",
    "timestamp": "$timestamp"
  }
]
EOF
  fi

  # Display new version
  echo "New version: $new_version"
}

# Main program
if [[ $# -ne 3 ]]; then
  echo "Usage: $0 {major|minor|patch} <description> <routerKey>"
  exit 1
fi

# Update version and generate JSON file
update_version_with_jq "$1" "$2" "$3"
