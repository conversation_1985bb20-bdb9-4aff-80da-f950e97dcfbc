const node_env=  process.env.NODE_ENV

module.exports = {
    root: true,
    env: {
        "browser": node_env !== "production",
        "node": true,
    },
    extends: ["plugin:vue/essential"],
    rules: {
        "no-console": "off",
        "no-debugger": node_env === "production" ? "error" : "off",
        // 强制使用单引号
        "quotes": "off",
        "comma-dangle": [0, "never"], //是否允许对象中出现结尾逗号
        "space-before-function-paren": "off",  // 关闭函数名与后面括号间必须空格规则
        "one-var": "off",  // 关闭var 声明，每个声明占一行规则。
        "semi": "off", //结尾分号
        "indent": "off", //文本缩进风格
        "no-irregular-whitespace": "off",
        "no-multi-spaces": "off",
        "eol-last": "off",//文本是否已单一换行符结束
        'vue/no-parsing-error': [2, { "x-invalid-end-tag": false }]   // 关闭标签 end 校验，处理 UI组件误报
    },
    parserOptions: {
        parser: "babel-eslint"
    }
};
