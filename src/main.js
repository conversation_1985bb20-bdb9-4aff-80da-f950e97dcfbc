import store from "@/store";
import menus from "@/store/modules/index/_type_menus";
import installTrimDirective from "@/utils/trimDirective";
import ViewUI from "view-design";
import Vue from "vue";
import PerfectScrollbar from "vue2-perfect-scrollbar";
import "vue2-perfect-scrollbar/dist/vue2-perfect-scrollbar.css";
import App from "./App.vue";
import debounce from "./mixins/debounce"; // 全局防抖
import "./public-path";
import router from "./router/index";
import descriptions from "@/components/descriptions/descriptions";
import descriptionsItem from "@/components/descriptions/descriptions-item";
import MessageBox from "@/components/message-box";
import "@/components/message-box/style/button-group.css";
import "@/components/message-box/style/button.css";
import "@/components/message-box/style/input.css";
import "@/components/message-box/style/message-box.css";
import * as _ from "lodash";
import("view-design/dist/styles/iview.css");
Vue.prototype.$_ = _;

Vue.use(PerfectScrollbar);
Vue.use(ViewUI);
Vue.use(descriptions);
Vue.use(descriptionsItem);
Vue.mixin(debounce);
Vue.component(MessageBox.name, MessageBox);
Vue.prototype.$message = MessageBox;
Vue.prototype.$bus = new Vue();
Vue.use(installTrimDirective); // * 全局去空格 v-trim
// 阻止启动生产消息
Vue.config.productionTip = false;

let instance = null;
function render(props = {}) {
  instance = new Vue({
    el: "#microApp", // 这里应该替换为你实际的挂载点
    router,
    store,
    render: (h) => h(App),
  });
}

if (!window.__POWERED_BY_QIANKUN__) {
  render();
}

export async function bootstrap(props) {}

export async function mount(props) {
  //console.log(window.store)
  props.setGlobalState({ menus });

  if (!instance) {
    render();
  }
}

export async function unmount() {
  if (instance) {
    // 卸载 Vue 实例
    instance.$destroy();
    instance.$el.innerHTML = "";
    instance = null;
  }
}
