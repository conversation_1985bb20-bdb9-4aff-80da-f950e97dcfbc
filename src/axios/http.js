import axios from "axios";
import qs from "qs";
import router from "../router";
import {
    Message, Spin
} from 'view-design';
import Loading from '@/components/public/loading.vue'
import { storageClear } from "@/utils/common";

const base = process.env.VUE_APP_BASE_API;
const ciphertext = process.env.VUE_APP_BASE_AUTH;
axios.defaults.timeout = 15000;
axios.defaults.baseURL = base;

// 请求拦截器，只做请求配置
axios.interceptors.request.use(
    config => {
        // 响应前拦截处理 token
        // 注意不能在 js 加载时就调用，会造成异步响应登录后无法立即拿到 token 的情况
        switch (config.url) {
            // 注册和登录单独配置
            case "/merchant/member/reg/do_regist.do":
            case "/oauth2/oauth/token":
                config.headers.Authorization = ciphertext;
                break
            case "/mecv-protocol/niuren/wms":
                // 历史注释...
                // axios.defaults.headers['Content-Type'] = 'application/json'
                // config.baseURL = 'http://*************:8081'
                // return Promise.resolve(config)
                break
            default:
                const token = JSON.parse(window.localStorage.getItem("logins"))?.access_token || 'unAuthor';
                config.headers.Authorization = `Bearer ${token}`
                break
        }
        return Promise.resolve(config);
    },
    error => {
        return Promise.reject(error);
    }
);

// 响应拦截器，处理请求问题
axios.interceptors.response.use(
    response => {
        const errCode = response.data.error
        errCode && resHandle(errCode)
        return Promise.resolve(response);
    },
    error => {
        errHandle(error)
        return Promise.reject(error.response);
    }
);

// 请求成功返回的异常处理
const resHandle= (errCode) => {
    Message.destroy()
    switch (errCode) {
        case "invalid_token":
            storageClear()
            Message.error("登录超时，请重新登录");
            router.push("/login");
            break;
        case "NO_SHOP_INFO":
            Message.error("没有关联店铺,请联系管理员");
            break;
        case "DISABLE":
            Message.error("店铺被冻结,请联系管理员");
            break;
        case "WAIT_CHECK":
            Message.error("店铺待审核,请联系管理员");
            break;
        case "CHECK_FAIL":
            Message.error("店铺审核驳回,请联系管理员");
            break;
    }
}

// 一般请求的异常处理，其它在自己页面自定义
const errHandle = (err) => {
    Message.destroy()
    if (err.response) {
       switch (err.response.status) {
            // case 400: Message.error("请求错误（400）");  // 登录单独处理
            //     break
            // case 401: Message.error("未授权，请重新登录（401）");
            // 	break;
            case 403: Message.error("拒绝访问（403）");
            	break;
            case 404: Message.error("请求出错（404）");
            	break;
            case 408: Message.error("请求超时（408）");
            	break;
            case 500: Message.error("服务器错误（500）");
            	break;
            case 501: Message.error("服务未实现（501）");
            	break;
            case 502: Message.error("网络错误（502）");
            	break;
            case 503: Message.error("服务不可用（503）");
            	break;
            case 504: Message.error("网络超时（504）");
            	break;
            case 505: Message.error("HTTP版本不受支持（505）");
            	break;
       }
    } else {
        Message.error(process.env.NODE_ENV === 'production'? '连接服务器失败' : '后台服务或在发版，稍后重试' );
    }
}

/**
 * 封装get方法，对应get请求
 * @param {String} url [请求的url地址]
 * @param {Object} params [请求时携带的参数]
 */
const get = (url, params = {}, sign) => {
    if (sign === 3) {
        axios.defaults.responseType = "blob";
        axios.defaults.headers["Content-Type"] = "application/x-download;charset=utf-8";
    } else {
        axios.defaults.responseType = "";
        axios.defaults.headers["Content-Type"] = "application/json";
    }
    return new Promise((resolve, reject) => {
        axios
            .get(url, {
                params: params
            })
            .then(res => {
                resolve(res);
            })
            .catch(err => {
                reject(err);
            });
    });
    // 或者return axios.get();
}
/**
 * post方法，对应post请求
 * @param {String} url [请求的url地址]
 * @param {Object} params [请求时携带的参数]
 * qs.stringify()
 */
const post = (url, params, sign, hideShowSpin) => {
    return new Promise((resolve, reject) => {
        axios.defaults.responseType = "";
        if (sign === 1) {
            axios.defaults.headers["Content-Type"] = "application/json";
        } else if (sign === 3) { // 导出
            axios.defaults.responseType = "blob";
            axios.defaults.headers["Content-Type"] = "application/json";
        } else {
            axios.defaults.headers["Content-Type"] = "application/x-www-form-urlencoded;charset=UTF-8";
        }
        if (!hideShowSpin) !url.includes("isSignedAgreement") && showSpin()
        axios.post(url, (sign === 1 || sign === 3) ? params : qs.stringify(params))
            .then(res => {
                setTimeout(() => {
                    Spin.hide();
                    resolve(res);
                }, 300)
            })
            .catch(err => {
                setTimeout(() => {
                    Spin.hide();
                    reject(err);
                }, 300)
            });
    });
    //  或者return axios.post();
}

/**
 * put方法，对应put请求
 * @param {String} url [请求的url地址]
 * @param {Object} params [请求时携带的参数]
 */
const put = (url, params, sign) => {
    return new Promise((resolve, reject) => {
        axios.defaults.responseType = "";
        if (sign !== 1) {
            axios.defaults.headers["Content-Type"] = "application/json";
        }else {
            axios.defaults.headers["Content-Type"] = "application/x-www-form-urlencoded;charset=utf-8";
        }
        axios.put(url, sign !== 1 ? params : qs.stringify(params))
            .then(res => {
                resolve(res);
            })
            .catch(err => {
                reject(err);
            });
    });
}
const deletes = (url, params = {}) => {
    return new Promise((resolve, reject) => {
        axios.defaults.responseType = "";
        axios
            .delete(url, {
                params: params
            })
            .then(res => {
                resolve(res);
            })
            .catch(err => {
                reject(err);
            });
    });
}

const showSpin = () => {
    Spin.show({
        render: (h) => h(Loading, {}, [])
    });
}

export {
    get,
    post,
    put,
    deletes
};
