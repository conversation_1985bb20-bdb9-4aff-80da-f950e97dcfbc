/*
 * @Author: 单长闯 <EMAIL>
 * @Date: 2024-12-12 09:36:38
 * @LastEditors: 单长闯 <EMAIL>
 * @LastEditTime: 2024-12-12 10:39:18
 * @FilePath: /nahui-pv.merchant-micro.zch/src/store/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from "vue";
import Vuex from "vuex";
import index from "./modules/index/index";
import dict from "./modules/dict/index";
Vue.use(Vuex);
export default new Vuex.Store({
  modules: { index, dict },
  state: {
    direction: "forward",
  },
  mutations: {
    updateDirection(state, direction) {
      state.direction = direction;
    },
  },
  actions: {},
  getters: {},
});
