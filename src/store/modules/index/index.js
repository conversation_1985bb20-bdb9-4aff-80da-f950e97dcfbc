/*
 * @Author: 单长闯 <EMAIL>
 * @Date: 2024-12-12 09:36:38
 * @LastEditors: 单长闯 <EMAIL>
 * @LastEditTime: 2024-12-12 10:39:11
 * @FilePath: /nahui-pv.merchant-micro.zch/src/store/modules/index/index.js
 * @Description: vuex
 */
export default {
  namespaced: true,
  state: {
    spInfo: null,
    spMode: null,
    spModeArr: null,
  },

  mutations: {
    // 通用储存
    setState(state, res) {
      state[res.name] = res.value || null;
    },
  },
  actions: {
    // 通用储存
    comeSetState(context, res) {
      context.commit("setState", res);
    },
  },
  getters: {},
};
