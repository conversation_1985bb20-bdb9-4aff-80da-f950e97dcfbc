const files = require.context("./module", true, /\.js$/);
const modules = {};
const autoDrawStates = {};

files.keys().forEach(modulePath => {
  const moduleName = modulePath.replace(/^\.\/(.*)\.js+$/, "$1");
  const value = files(modulePath);
  if (value.default && typeof value.default.state !== 'undefined' && typeof value.default.mutations !== 'undefined') {
    modules[moduleName] = { ...value.default, namespaced: true };
  } else if (value.default && moduleName === 'autoDraw') {
    Object.keys(value.default).forEach((key) => {
        autoDrawStates[`autoDraw/${key}`] = value.default[key];
    });
  }
});

export default {
  namespaced: true,
  state: {
    ...autoDrawStates
  },
  mutations: {},
  actions: {},
  getters: {
    drawType: (state) => {
      return state["autoDraw/drawType"];
    },
    roofStyles: (state) => {
      return state["autoDraw/roofStyles"];
    },
    meterialDictFormMap: (state) => {
      return state["autoDraw/meterialDictFormMap"];
    }
  },
  modules,
};
