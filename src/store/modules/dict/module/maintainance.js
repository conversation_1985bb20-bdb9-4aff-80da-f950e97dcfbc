// import { defineStore } from 'pinia';
// 导入维护模块的 API
import API from '@/api/maintainance';

export default {
  namespaced: true,
  state: {
    // 使用对象存储不同类型的字典，键为字典类型，值为数组
    dictData: {},
    // 存储每种字典的加载状态
    loadingStatus: {},
    // 存储字典的map对象
    dictMap: {}
  },
  getters: {
    /**
     * 根据类型获取字典数据
     * @param {object} state - Vuex state
     * @returns {function(string): array} - 返回一个函数，接收字典类型，返回对应的字典数组
     */
    getDictByType: (state) => (type, fieldMap = { label: 'label', value: 'value' }) => {
      const dictData = state.dictData[type] || [];
      return dictData.map(item => ({
        [fieldMap.label]: item.itemText,
        [fieldMap.value]: item.itemValue
      }));
    },
    /**
     * 根据类型获取字典map数据
     * @param {object} state - Vuex state
     * @returns {function(string): object} - 返回一个函数，接收字典类型，返回对应的字典map对象
     */
    getDictMapByType: (state) => (type) => {
      return state.dictMap[type] || {};
    },
    /**
     * 检查特定类型的字典是否正在加载
     * @param {object} state - Vuex state
     * @returns {function(string): boolean} - 返回一个函数，接收字典类型，返回加载状态
     */
    isLoading: (state) => (type) => {
      return !!state.loadingStatus[type];
    },
  },
  mutations: {
    SET_DICT_DATA(state, { type, data }) {
      state.dictData = { ...state.dictData, [type]: data };
    },
    SET_LOADING_STATUS(state, { type, status }) {
      state.loadingStatus = { ...state.loadingStatus, [type]: status };
    },
    SET_DICT_MAP(state, { type, map }) {
      state.dictMap = { ...state.dictMap, [type]: map };
    },
    CLEAR_DICT_TYPE_DATA(state, type) {
      const newDictData = { ...state.dictData };
      delete newDictData[type];
      state.dictData = newDictData;

      const newDictMap = { ...state.dictMap };
      delete newDictMap[type];
      state.dictMap = newDictMap;

      const newLoadingStatus = { ...state.loadingStatus };
      delete newLoadingStatus[type];
      state.loadingStatus = newLoadingStatus;
    },
    CLEAR_ALL_DICT_DATA(state) {
      state.dictData = {};
      state.dictMap = {};
      state.loadingStatus = {};
    }
  },
  actions: {
    /**
     * 异步获取并设置指定类型或类型数组的字典数据
     * 如果某个类型的数据已存在或正在加载，则跳过该类型
     * @param {object} context - Vuex action context { commit, state, dispatch }
     * @param {string|string[]} types - 需要获取的字典类型或类型数组
     */
    async fetchDict({ commit, state, rootState, dispatch }, types) {
      const requestedTypes = Array.isArray(types) ? types : [types];
      const typesToFetch = [];

      for (const type of requestedTypes) {
        if (!type) {
            console.warn('请求的字典类型为空，已跳过');
            continue;
        }
        if (!state.dictData[type] && !state.loadingStatus[type]) {
          typesToFetch.push(type);
          commit('SET_LOADING_STATUS', { type, status: true });
        }
      }

      if (typesToFetch.length === 0) {
        return;
      }

      try {
        const res = await API.getMaintainanceDicts(typesToFetch);

        if (res.data.success && res.data.result) {
          const returnedData = res.data.result;

          for (const type of typesToFetch) {
            const dictCodes =  returnedData.filter(item => item.dictCode === type);
            commit('SET_DICT_DATA', { type, data: dictCodes });

            const currentTypeMap = {};
            dictCodes.forEach(item => {
              currentTypeMap[item.itemValue] = item.itemText;
            });
            commit('SET_DICT_MAP', { type, map: currentTypeMap });
            commit('SET_LOADING_STATUS', { type, status: false });
          }
        } else {
          for (const type of typesToFetch) {
            commit('SET_DICT_DATA', { type, data: [] });
            commit('SET_DICT_MAP', { type, map: {} });
            commit('SET_LOADING_STATUS', { type, status: false });
          }
        }
      } catch (error) {
        for (const type of typesToFetch) {
          commit('SET_DICT_DATA', { type, data: [] });
          commit('SET_DICT_MAP', { type, map: {} });
          commit('SET_LOADING_STATUS', { type, status: false });
        }
      }
    },

    /**
     * 清空指定类型或所有类型的字典数据
     * @param {object} context - Vuex action context { commit }
     * @param {string} [type] - 可选，要清空的字典类型。如果省略，则清空所有字典。
     */
    clearDict({ commit }, type) {
      if (type) {
        commit('CLEAR_DICT_TYPE_DATA', type);
      } else {
        commit('CLEAR_ALL_DICT_DATA');
      }
    },

    /**
     * 强制重新获取指定类型的字典数据
     * @param {object} context - Vuex action context { dispatch }
     * @param {string} type - 需要重新获取的字典类型
     */
    async forceFetchDict({ dispatch }, type) {
        // 先清除旧数据和加载状态
        await dispatch('clearDict', type);
        // 再调用 fetchDict 获取
        await dispatch('fetchDict', [type]);
    }
  },
}; // 结束 Vuex module export
