/*
 * @Author: 单长闯 <EMAIL>
 * @Date: 2024-12-12 09:36:38
 * @LastEditors: 单长闯 <EMAIL>
 * @LastEditTime: 2024-12-20 10:51:42
 * @FilePath: /nahui-pv.merchant-micro.zch/src/store/modules/index/constance.js
 * @Description: 字典
 */

export default {
  drawType: [
    {
      label: "人工设计出图",
      value: 1,
    },
    {
      label: "自动设计出图",
      value: 2,
    },
  ],
  roofStyles: [
    {
      label: "单坡",
      value: "single_slope",
    },
    {
      label: "人字坡",
      value: "gable_roof",
    },
  ],
  //物料字典与表单字段映射表
  meterialDictFormMap: {
    module: {
      comModel: "sku", //组件 - 型号
      comModelName: "skuName", //组件 - 型号名称
      comBrandName: "brandName", //组件 - 品牌
      comPower: "power", //组件 - 功率
      comPurlinSpace: "purlinSpace", //组件 - 檩条间距
      comSize: (result) => `${result.length}x${result.width}x${result.height}`, //组件 - 尺寸
      componentWorkMinTemp: "componentWorkMinTemp", // 组件工作最低温度
      componentWorkMaxTemp: "componentWorkMaxTemp", // 组件工作最高温度
      openCircuitVoltageVoc: "componentOpenCircuitVoltageVoc", // 组件开路电压Voc
      workingVoltageVmp: "componentWorkingVoltageVmp", // 组件工作电压Vmp
      openCircuitVoltageTempCoeff: "componentOpenCircuitVoltageTempCoeff", // 组件开路电压的温度系数
      workingVoltageTempCoeff: "componentWorkingVoltageTempCoeff", // 组件工作电压的温度系数
    },
    inverter: {
      inverterModel: "sku", //逆变器 - 型号
      inverterModelName: "skuName", //逆变器 - 型号名称
      inverterBrandName: "brandName", //逆变器 - 品牌
      inverterPower: "power", //逆变器 - 功率
      mpptMaxVoltage: "mpptMaxVoltage", // MPPT电压最大值
      mpptMinVoltage: "mpptMinVoltage", // MPPT电压最小值
      mpptMaxInputCurrent: "mpptMaxInputCurrent", // MPPT最大输入电流
      mpptPhaseCount: "mpptPhaseCount", // MPPT路数
      groupSeriesCount: "groupSeriesCount", // 组串数量
    },
    storageBattery: {
      storageBatteryModel: "sku", //储能 - 型号
      storageBatteryModelName: "skuName", //储能 - 型
      storageBatteryBrandName: "brandName", //储能 - 品牌
      storageBatteryCapacity: "capacity", //储能 - 容量
    },
  },
};
