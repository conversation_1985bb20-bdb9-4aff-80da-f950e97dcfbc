<template>
    <div class="micro-app"><router-view /></div>
</template>
<script>

export default {
    name: 'microApp',
};
</script>

<style lang="scss">
@import '@/style/reset.scss';
.sub-app {
    font-size: 12px;
}

#app {
    font-family: 'Avenir', Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-align: center;
    color: #2c3e50;
    height: 100vh;
}

#nav {
    a {
        font-weight: bold;
        color: #2c3e50;
        font-size: 32px;

        &.router-link-exact-active {
            color: $color-main;
        }
    }
}

h3 {
    font-size: 18px;
}

.router {
    width: 100%;
    height: 100vh;
    position: fixed;
    overflow: hidden !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch;
    background: #ffffff;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

.picker-item {
    font-size: 0.14rem;
}

.buttonSign {
    background-color: #66cc66 !important;
    border-color: #66cc66 !important;
}

.buttonSignOne {
    background-color: #fff !important;
    border-color: #66cc66 !important;
    color: #66cc66 !important;
}

.buttonWidth {
    width: 100px;
}

input:-webkit-autofill {
    -webkit-box-shadow: 0 0 999px 999px #f3f3f3 inset;
}
</style>
