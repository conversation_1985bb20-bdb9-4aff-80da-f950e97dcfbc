<!-- 服务资质 -->
<template>
    <div class="cus_list">
        <div class="list_son">
            <p class="cus_title" style="margin-bottom: 20px;">服务资质</p>
            <div class="list_but">
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                </div>
            </div>
            <div class="commListDiv">
                <table class="cus_table" border="0" cellspacing="0" cellpadding="0" width="100%">
                    <tr>
                        <th align="center">序号</th>
                        <th align="center">运维商名称</th>
                        <th align="center">运维商类型</th>
                        <th align="center">审核状态</th>
                        <th align="center">驳回原因</th>
                        <th align="center">电力设施许可证有效日期</th>
                        <th align="center">电力设施许可证</th>
                        <th align="center">安全生产许可证有效日期</th>
                        <th align="center">安全生产许可证</th>
                        <th align="center">承接运维承诺书</th>
                        <th align="center" class="fixedRight">操作</th>
                    </tr>
                    <tr v-for="(item, index) in commList" :key="index">
                        <td align="center" width="10%">{{ index + 1 }}</td>
                        <td align="center" width="120" style="min-width: 110px">{{ item.name || "-" }}</td>
                        <td align="center" width="120" style="min-width: 110px">{{ typeVal('merchantType',
                            item.identityType) || "-" }}</td>
                        <td align="center" width="100" style="min-width: 100px">{{ typeVal('qualsStatus', item.status)
                            || "-" }}</td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.auditRemark || "-" }}</td>
                        <td align="center" width="100" style="min-width: 100px">
                            {{ item.electricityEndTime?.replace('T', ' ') || '-' }}
                        </td>
                        <td align="center" width="160" style="min-width: 160px">
                            <a v-if="item.electricityUrl" :href="item.electricityUrl" target="_blank">查看</a>
                            <span v-else>-</span>
                        </td>
                        <td align="center" width="100" style="min-width: 100px">
                            {{ item.safetyEndTime?.replace('T', ' ') || '-' }}
                        </td>
                        <td align="center" width="100" style="min-width: 100px">
                            <a v-if="item.safetyUrl" :href="item.safetyUrl" target="_blank">查看</a>
                            <span v-else>-</span>
                        </td>
                        <td align="center" width="100" style="min-width: 100px">
                            <a v-if="item.opUrl" @click="debounce(showFile(item.opUrl))" target="_blank">查看</a>
                            <span v-else>-</span>
                        </td>
                        <td align="center" width="230" style="min-width: 150px" class="fixedRight">
                            <Button v-if="item.status === null" type="info" ghost size="small"
                                @click="addData(item)">上传资料</Button>
                            <!-- <span v-else>-</span> -->
                            <!-- v-else-if="item.status === 'AUDI_REJECT' || !item.electricityUrl || !item.safetyUrl || !item.opUrl" -->
                            <Button v-else type="success" ghost size="small" style="margin: 3px"
                                @click="toEdit(item)">修改资料</Button>
                            <!-- <span v-else>-</span> -->
                        </td>
                    </tr>
                    <tr v-if="!commList.length">
                        <td colspan="25" align="center">暂无数据</td>
                    </tr>
                </table>
            </div>
        </div>
        <Modal width="700" v-model="modelEdit" draggable sticky scrollable :title="title" :footer-hide="true"
            :mask-closable="false">
            <div>
                <p class="cus_title" style="margin-bottom: 10px;">基本信息</p>
                <Form :label-width="150" inline>
                    <FormItem label="运维商名称：" prop="name" style="margin-bottom: 0;">
                        {{ this.stationInfo.name || '-' }}
                    </FormItem>
                    <FormItem label="运维商类型：" prop="problem" style="margin-bottom: 0;">
                        {{ typeVal('merchantType', this.stationInfo.identityType) || '-' }}
                    </FormItem>
                    <FormItem label="联系人：" prop="consignee" style="margin-bottom: 0;">
                        {{ this.stationInfo.consignee || '-' }}
                    </FormItem>
                    <FormItem label="联系电话：" prop="consigneeMobile" style="margin-bottom: 0;">
                        {{ this.stationInfo.consigneeMobile || '-' }}
                    </FormItem>
                </Form>
            </div>
            <Form :model="formItem" :label-width="150" ref="formItem" inline>
                <p class="cus_title">资质信息</p>
                <FormItem label="承装（修、试）电力设施许可证有效日期">
                    <DatePicker type="date" :value="formItem.electricityStartTime" placeholder="起始日期" @on-change="data => {
                        return selectDate(data, 'electricityStartTime');
                    }
                        " format="yyyy-MM-dd" style="width: 180px; margin: 10px 5px 0 0;"></DatePicker>
                    <DatePicker type="date" :value="formItem.electricityEndTime" placeholder="截止日期" @on-change="data => {
                        return selectDate(data, 'electricityEndTime');
                    }
                        " format="yyyy-MM-dd" style="width: 180px"></DatePicker>
                </FormItem>
                <FormItem label="承装（修、试）电力设施许可证" prop="electricityUrl">
                    <div style="display: flex; align-items: center;">
                        <Upload class="inlinebox" accept=".pdf, .jpg, .jpeg, .png" :show-upload-list="false"
                            :action="UploadClass.actionUrl('file')" :headers="UploadClass.headers"
                            :before-upload="UploadClass.progress"
                            :on-success="(response) => { formItem.electricityUrl = response.result[0].fileUrl; return UploadClass.upSuccess() }"
                            :format="['pdf', 'jpg', 'jpeg', 'png']" :max-size="102400"
                            :on-format-error="UploadClass.formatError" :on-exceeded-size="UploadClass.maxSizeError">
                            <div style="display: flex;">
                                <Button icon="md-cloud-upload" style="margin: 10px 5px 0 0;">
                                    {{ formItem.electricityUrl ? '重新' : '' }}上传文件
                                </Button>
                            </div>
                        </Upload>
                        <p class="tips" style="margin:18px 0 0 20px;"> 支持扩展名：.pdf .jpg .jpeg .png... </p>
                    </div>
                    <a v-if="formItem.electricityUrl" :href='formItem.electricityUrl' target="_blank"
                        style="white-space: nowrap;font-size: 15px;">
                        <Icon type="md-document" /> 查看文件
                    </a>
                </FormItem>
                <FormItem label="安全生产许可证有效日期">
                    <DatePicker type="date" :value="formItem.safetyStartTime" placeholder="起始日期" @on-change="data => {
                        return selectDate(data, 'safetyStartTime');
                    }
                        " format="yyyy-MM-dd" style="width: 180px; margin: 10px 5px 0 0;"></DatePicker>
                    <DatePicker type="date" :value="formItem.safetyEndTime" placeholder="截止日期" @on-change="data => {
                        return selectDate(data, 'safetyEndTime');
                    }
                        " format="yyyy-MM-dd" style="width: 180px"></DatePicker>
                </FormItem>
                <FormItem label="安全生产许可证" prop="safetyUrl">
                    <div style="display: flex; align-items: center;">
                        <Upload class="inlinebox" accept=".pdf, .jpg, .jpeg, .png" :show-upload-list="false"
                            :action="UploadClass.actionUrl('file')" :headers="UploadClass.headers"
                            :before-upload="UploadClass.progress"
                            :on-success="(response) => { formItem.safetyUrl = response.result[0].fileUrl; return UploadClass.upSuccess() }"
                            :format="['pdf', 'jpg', 'jpeg', 'png']" :max-size="102400"
                            :on-format-error="UploadClass.formatError" :on-exceeded-size="UploadClass.maxSizeError">
                            <div style="display: flex;">
                                <Button icon="md-cloud-upload">{{ formItem.safetyUrl ? '重新' : '' }}上传文件</Button>
                            </div>
                        </Upload>
                        <p class="tips" style="margin:10px 0 0 20px; "> 支持扩展名：.pdf .jpg .jpeg .png... </p>
                    </div>

                    <a v-if="formItem.safetyUrl" :href='formItem.safetyUrl' target="_blank"
                        style="white-space: nowrap;font-size: 15px;">
                        <Icon type="md-document" /> 查看文件
                    </a>
                </FormItem>
                <FormItem label="承接运维承诺书" prop="opUrl">
                    <div style="display: flex; align-items: center;">
                        <Button type="primary" ghost size="small" style="height: 30px; margin-right: 20px;"
                            @click="debounce(secureDown)">
                            承接运维承诺书
                        </Button>
                        <div style="display: flex; align-items: center;">
                            <Upload class="inlinebox" accept=".docx" :show-upload-list="false"
                                :action="UploadClass.actionUrl('file')" :headers="UploadClass.headers"
                                :before-upload="UploadClass.progress"
                                :on-success="(response) => { formItem.opUrl = response.result[0].fileUrl; return UploadClass.upSuccess() }"
                                :format="['docx']" :max-size="102400" :on-format-error="UploadClass.formatError"
                                :on-exceeded-size="UploadClass.maxSizeError">
                                <div style="display: flex;">
                                    <Button icon="md-cloud-upload">
                                        {{ formItem.opUrl ? '重新' : '' }}上传文件
                                    </Button>
                                </div>
                            </Upload>
                            <p class="tips" style="margin:18px 0 0 20px;"> 支持扩展名：.docx </p>
                        </div>
                    </div>
                    <a v-if="formItem.opUrl" @click="debounce(showFile(formItem.opUrl))" target="_blank"
                        style="white-space: nowrap;font-size: 15px;">
                        <Icon type="md-document" /> 查看文件
                    </a>
                </FormItem>
            </Form>
            <div style="text-align: center; margin: 30px 0">
                <Button type="default" size="large" style="width: 120px; margin-right: 30px"
                    @click="modelEdit = false">取消</Button>
                <Button type="primary" size="large" style="width: 120px" @click="debounce(toNewBuilt)">
                    确认
                </Button>
            </div>
        </Modal>
    </div>
</template>
<script>
import _data from "@/views/osp/_edata"; // 数据字典
import API from "@/api/osp";
import { UploadClass } from '@/utils/common';
import { getDicts } from "@/api/public/dict";
export default {
    name: "qualifications",
    data() {
        return {
            UploadClass,
            modelEdit: false,//新增编辑
            title: "服务资质上传资料",
            commList: [],
            terminationList: [],
            formItem: {
                id: "",
                electricityStartTime: "",
                electricityEndTime: "",
                electricityUrl: "",
                safetyStartTime: "",
                safetyEndTime: "",
                safetyUrl: "",
                opUrl: "",
            },
            stationInfo: {},
            merchantType: [],
            qualsStatus: [],
            actionUrl: `${process.env.VUE_APP_BASE_API}/merchant/oss/image/upload.do`,
            headers: { Authorization: `Bearer ${JSON.parse(window.localStorage.getItem('logins')).access_token}` }
        };
    },
    async created() {
        this.merchantType = await getDicts("osp/merchant", "merchantType").then(e => e?.list)
        this.qualsStatus = await getDicts("osp/merchant", "qualsStatus").then(e => e?.list)
        this.queryList();
    },
    // props: {
    //     stationInfo: {
    //         commObj: Object,
    //         required: true
    //     }
    // },
    mounted() {
        this.getCommList();
    },
    methods: {
        // 查询按钮
        queryList() {
            this.getCommList();
        },
        // 选择日期
        selectDate(data, refObj) {
            if (!data) {
                this.formItem[refObj] = ''; // 或者根据需求设置为 null
                return;
            }
            let date = data.replace('T', ' ');
            if (refObj.includes('StartTime')) {
                this.formItem[refObj] = `${date} 00:00:00`;
            } else if (refObj.includes('EndTime')) {
                this.formItem[refObj] = `${date} 23:59:59`;
            } else {
                this.formItem[refObj] = date;
            }
        },
        // 获取列表
        getCommList() {
            API.qualsFindData().then((res) => {
                let request = res.data.result;
                if (res.data.success) {
                    this.commList = request;
                } else {
                    this.commList = [];
                }
            });
        },
        secureDown() {
            const link = document.createElement('a');
            link.href = 'https://cdn01.rrsjk.com/images/承接电站运维承诺书(签字盖章）.docx';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },
        showFile(url) {
            this.downFile(url)
        },
        downFile(url) {
            const fileName = '承接运维承诺书.docx';
            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络响应不正常');
                    }
                    return response.blob();
                })
                .then(blob => {
                    const link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = fileName;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                })
                .catch(error => {
                    console.error('下载文件时出错:', error);
                });
        },
        //新增
        addData(item) {
            this.title = '服务资质上传资料'
            this.stationInfo = item
            this.formItem = this.$options.data.call(this).formItem;
            this.formItem.id = item.id
            this.modelEdit = true
        },
        //编辑
        toEdit(item) {
            this.stationInfo = item
            this.title = '编辑'
            const {
                id,
                electricityStartTime,
                electricityEndTime,
                electricityUrl,
                safetyStartTime,
                safetyEndTime,
                safetyUrl,
                opUrl
            } = item
            this.formItem = {
                id,
                electricityStartTime,
                electricityEndTime,
                electricityUrl,
                safetyStartTime,
                safetyEndTime,
                safetyUrl,
                opUrl
            };
            this.modelEdit = true
        },
        //提交/编辑
        toNewBuilt() {
            const params = this.formItem
            API.qualsUploadData(params).then((res) => {
                if (res.data.success) {
                    this.$Message.success("提交成功");
                    this.getCommList();
                    this.modelEdit = false;
                } else {
                    this.$Message.error(res.data.error);
                }
            });
        },
        process(item) {
            this.processInfo = true
            this.terminationList = item.logList
        },
        typeVal(list, value) {
            const obj = {};
            this[list].map((e) => {
                obj[e.value] = e.label || e.value;
            });
            return obj[value];
        },
    },
};
</script>
<style lang="scss" scoped>
@import "@/style/_cus_reg.scss";
@import "@/style/_cus_list.scss";
@import '@/style/_ivu-modal.scss';

.ivu-form-item {
    width: 100%;
}

.pdf {
    width: 100%;
    height: 100%;
}

.tips {
    color: #aaaaaa;
}

// ::v-deep .ivu-form-inline {
//     width: 100%;
// }</style>
