<!-- 人员管理 -->
<template>
    <div class="cus_list">
        <div class="list_son">
            <div class="list_but">
                <div class="condition"><Button type="primary" @click="staffAdd()">新增</Button></div>
            </div>
            <div class="commListDiv">
                <table class="cus_table" border="0" cellspacing="0" cellpadding="0" width="100%">
                    <tr>
                        <th align="center">员工编号</th>
                        <th align="center">员工姓名</th>
                        <th align="center">性别</th>
                        <th align="center">岗位</th>
                        <th align="center">手机号码</th>
                        <th align="center">员工状态</th>
                        <th align="center">激活状态</th>
                        <th align="center">实名认证</th>
                        <th align="center">身份证号</th>
                        <th align="center">劳动合同</th>
                        <th align="center">人身意外险</th>
                        <th align="center">负责区域</th>
                        <th align="center">加入时间</th>
                        <th align="center" class="fixedRight">操作</th>
                    </tr>
                    <tr v-for="(item, index) in commList" :key="index">
                        <td align="center" width="80">{{ item.staffNo }}</td>
                        <td align="center" width="120">{{ item.staffName }}</td>
                        <td align="center" width="70">{{ typeVal('genderList', item.sex) }}</td>
                        <td align="center" width="100" style="min-width: 110px">{{ typeVal('dutyList', item.duty) }}
                        </td>
                        <td align="center" width="100">{{ item.mobile }}</td>
                        <td align="center" width="100">{{ typeVal('staffStatusList', item.staffStatus) }}</td>
                        <td align="center" width="100">{{ item.memberId ? '已激活' : '待激活' }}</td>
                        <td align="center" width="80" style="min-width: 80px">{{ typeVal('realNameAuth',
                    item.realNameAuth)
                            }}</td>
                        <td align="center" width="120" style="min-width: 110px">{{ item.idCardNo }}</td>
                        <td align="center" width="120" style="min-width: 110px"><a v-if="item.labourContractUrl"
                                :href="item.labourContractUrl" target="_blank">查看</a></td>
                        <td align="center" width="120" style="min-width: 110px"><a v-if="item.labourInsuranceUrl"
                                :href="item.labourInsuranceUrl" target="_blank">查看</a></td>
                        <td align="center" width="120" style="min-width: 200px;"
                            :style="item.regionList && item.regionList.length ? regionClick : ''"
                            @click="displayRegion(item)">{{ regionDisplay(item) }}</td>
                        <td align="center" width="120" style="min-width: 110px">{{ item.createdAt &&
                    item.createdAt.replace('T', ' ') }}</td>
                        <td class="fixedRight" align="center" width="80" style="min-width: 100px">
                            <Button type="info" ghost size="small" @click="getView(item)"
                                style="margin: 3px">员工状态设置</Button>
                        </td>
                    </tr>
                    <tr v-if="!commList.length">
                        <td colspan="11" align="center">暂无数据</td>
                    </tr>
                </table>
            </div>
            <Page style="margin: 20px" @on-change="changeCurrent" :current="staffObj.pageNum" :total="totalElements"
                show-total show-elevator />
        </div>
        <Modal v-model="modalRegion" title="区域" :footer-hide="true">
            <div v-for="item in currentChoose.regionList" :key="item.regionId">
                {{ `${item.provinceName} ${item.cityName} ${item.regionName}` }}
            </div>
        </Modal>
        <Modal width="700" v-model="modelEdit" draggable sticky scrollable :title="titles" :footer-hide="true"
            :mask-closable="false">
            <Form :model="formItem" :label-width="150" ref="formItem" inline>
                <FormItem label="员工姓名" prop="staffName" :rules="{ required: true, message: ' ' }">
                    <Input type="text" maxlength="15" v-model="formItem.staffName" clearable placeholder="输入姓名"
                        style="width: 200px" :disabled="modelType" />
                </FormItem>
                <FormItem label="性别" prop="sex" :rules="{ required: true, message: ' ' }">
                    <Select v-model="formItem.sex" style="width: 120px; margin-right: 5px" :disabled="modelType">
                        <Option v-for="item in genderList" :value="item.value" :key="item.value">{{ item.label }}
                        </Option>
                    </Select>
                </FormItem>
                <FormItem label="岗位" prop="duty" :rules="{ required: true, message: ' ' }"
                    :disabled="modelType && modelAudit">
                    <Select v-model="formItem.duty" style="width: 200px; margin-right: 5px" :disabled="modelType">
                        <Option v-for="item in dutyList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                </FormItem>
                <FormItem label="员工状态" prop="staffStatus" :rules="{ required: true, message: ' ' }">
                    <Select v-model="formItem.staffStatus" style="width: 120px; margin-right: 5px"
                        :disabled="disStaffStatus === 'QUIT'">
                        <Option v-for="item in staffStatusList" :value="item.value" :key="item.value">{{ item.label }}
                        </Option>
                    </Select>
                </FormItem>
                <FormItem label="手机号码" prop="mobile" :rules="{ required: true, message: ' ' }">
                    <Input type="text" maxlength="11" v-model="formItem.mobile" clearable placeholder="即员工登录账号"
                        style="width: 130px" :disabled="modelType" />
                </FormItem>
                <FormItem label="身份证号" prop="idCardNo" :rules="{ required: true, len: 18, message: ' ' }">
                    <Input type="text" v-model="formItem.idCardNo" min="18" maxlength="18" show-word-limit
                        placeholder="请输入" style="width: 210px" :disabled="modelType" />
                </FormItem>
                <FormItem label="劳动合同" required>
                    <Upload :action="actionUrl" :headers="headers" :show-upload-list="false" :on-success="(response, file, fileList) => {
                    return fileUploadSuccess(response, file, fileList, 'labourContractUrl');
                }
                    " :on-progress="progress" :format="['jpg', 'jpeg', 'png', 'gif']" :max-size="18432"
                        :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize"
                        style="margin-bottom: 10px;">
                        <div style="display: flex;">
                            <Button icon="md-cloud-upload" :disabled="modelType && modelAudit">上传文件</Button>
                            <p class="tips" style="margin-left: 20px">支持扩展名：.jpg .jpeg .png...</p>
                        </div>
                    </Upload>
                    <a v-if="formItem.labourContractUrl" :href="formItem.labourContractUrl" target="_blank"><img
                            :src="formItem.labourContractUrl" width="100px" /></a>
                </FormItem>
                <FormItem label="劳动合同起止时间" required>
                    <DatePicker type="date" :value="formItem.contractStartDate" placeholder="起始日期" @on-change="data => {
                    return selectDate(data, 'contractStartDate');
                }
                    " format="yyyy-MM-dd HH:mm:ss" style="width: 200px; margin-right: 5px"></DatePicker>
                    <DatePicker type="date" :value="formItem.contractEndDate" placeholder="截止日期" @on-change="data => {
                    return selectDate(data, 'contractEndDate');
                }
                    " format="yyyy-MM-dd HH:mm:ss" style="width: 200px"></DatePicker>
                </FormItem>
                <FormItem label="人身意外险" required>
                    <Upload :action="actionUrl" :headers="headers" :show-upload-list="false" :on-success="(response, file, fileList) => {
                    return fileUploadSuccess(response, file, fileList, 'labourInsuranceUrl');
                }
                    " :on-progress="progress" :format="['jpg', 'jpeg', 'png', 'gif']" :max-size="18432"
                        :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize"
                        style="margin-bottom: 10px;">
                        <div style="display: flex;">
                            <Button icon="md-cloud-upload" :disabled="modelType && modelAudit">上传文件</Button>
                            <p class="tips" style="margin-left: 20px">支持扩展名：.jpg .jpeg .png...</p>
                        </div>
                    </Upload>
                    <a v-if="formItem.labourInsuranceUrl" :href="formItem.labourInsuranceUrl" target="_blank"><img
                            :src="formItem.labourInsuranceUrl" width="100px" /></a>
                </FormItem>
                <FormItem label="人身意外险起止时间" required>
                    <DatePicker type="date" :value="formItem.insuranceStartDate" placeholder="起始日期" @on-change="data => {
                    return selectDate(data, 'insuranceStartDate');
                }
                    " format="yyyy-MM-dd HH:mm:ss" style="width: 200px; margin-right: 5px"></DatePicker>
                    <DatePicker type="date" :value="formItem.insuranceEndDate" placeholder="截止日期" @on-change="data => {
                    return selectDate(data, 'insuranceEndDate');
                }
                    " format="yyyy-MM-dd HH:mm:ss" style="width: 200px"></DatePicker>
                </FormItem>
                <FormItem label="负责区域">
                    <div v-for="(it, ind) in AreaChange.regionList" :key="ind" class="region-item">
                        <Select v-if="!ind" v-model="it.provinceId"
                            @on-change="(item) => { AreaChange.typeOneChange(item, ind) }"
                            style="width:120px;margin-right:5px;">
                            <Option v-for="(item, index) in AreaChange.addressOneList" :value="item.id" :key="index">
                                {{ item.name }}</Option>
                        </Select>
                        <div v-else style="width:120px;margin-right:5px;"></div>
                        <Select v-model="it.cityId" @on-change="(item) => AreaChange.typeTwoChange(item, ind)"
                            style="width:120px;margin-right:5px;">
                            <Option v-for="(item, index) in it.addressTwoList" :value="item.id" :key="index">{{
                    item.name }}</Option>
                        </Select>
                        <Select v-model="it.regionId" style="width:120px;">
                            <Option v-for="(item, index) in it.addressThreeList" :value="item.id" :key="index">{{
                    item.name }}</Option>
                        </Select>
                        <i-button v-if="ind" :disabled="!it.provinceId" @click="AreaChange.deleteRegion(ind)"
                            style="margin: 0 0 0 10px" shape="circle" icon="ios-close-circle">
                        </i-button>
                        <i-button v-if="ind === AreaChange.regionList.length - 1" :disabled="!it.provinceId"
                            @click="AreaChange.addRegion(ind)" type="primary" style="margin: 0 0 0 10px" shape="circle"
                            icon="ios-add-circle">
                        </i-button>
                    </div>
                </FormItem>
            </Form>
            <div style="text-align: center; margin: 30px 0">
                <Button type="default" size="large" style="width: 120px; margin-right: 30px"
                    @click="modelEdit = false">取消</Button>
                <Button type="primary" size="large" v-if="!formItem.id" style="width: 120px"
                    @click="debounce(saveStaff)">保存</Button>
                <Button type="primary" size="large" v-else style="width: 120px" @click="debounce(editStaff)">修改</Button>
            </div>
        </Modal>
    </div>
</template>
<script>
import _data from '@/views/osp/_edata'; // 数据字典
import API from '@/api/osp';
import AreaChange from '@/utils/areaChangeMulti';
export default {
    name: 'personnelList',
    data() {
        return {
            modalRegion: false,
            currentChoose: {},
            regionClick: 'color: blue;cursor:pointer',
            modelEdit: false,
            modelType: false,
            modelAudit: true,
            titles: '',
            staffObj: {
                staffStatus: '', // 员工状态
                staffNo: '', // 员工编号
                staffName: '', // 员工姓名
                sex: '', // 性别
                realNameAuth: '', // 实名认证
                pageNum: 1,
                pageSize: 10,
                mobile: '', // 手机号码
                idCardNo: '', // 身份证号
                electricNo: '', // 电工证
                duty: '', // 岗位
                auditStatus: '', // 审核状态
            },
            formItem: {
                staffName: '',
                sex: '',
                duty: '',
                staffStatus: '',
                mobile: '',
                idCardNo: '',
                labourContractUrl: '',
                labourInsuranceUrl: '',
                provinceName: '',
                regionName: '',
                cityName: '',
                contractEndDate: '',
                contractStartDate: '',
                insuranceStartDate: '',
                insuranceEndDate: '',
            },
            disStaffStatus: '',
            totalElements: 0,
            genderList: _data.genderList,
            dutyList: _data.dutyList,
            staffStatusList: _data.staffStatusList,
            realNameAuth: _data.realNameAuth,
            commList: [],
            AreaChange,
            actionUrl: `${process.env.VUE_APP_BASE_API}/merchant/oss/image/upload.do`,
            headers: { Authorization: `Bearer ${JSON.parse(window.localStorage.getItem('logins')).access_token}` }
        };
    },
    mounted() {
        this.getCommList();
        AreaChange.getaddressList(this.id, this, true); // 提交需要
    },
    methods: {
        displayRegion(item) {
            this.currentChoose = item
            if (item.regionList && item.regionList.length) {
                this.modalRegion = true
            }
        },

        regionDisplay(item) {
            if (item.regionList && item.regionList.length) {
                const { provinceName, cityName, regionName } = item.regionList[0]
                return `${provinceName} ${cityName} ${regionName}`
            }
            return '-'
        },

        typeVal(list, value) {
            const obj = {};
            this[list].map(e => {
                obj[e.value] = e.label || e.value;
            });
            return obj[value];
        },
        // 切换分页
        changeCurrent(curr) {
            this.staffObj.pageNum = curr;
            this.getCommList();
        },

        // 查询按钮
        queryList() {
            this.staffObj.pageNum = 1;
            this.getCommList();
        },

        // 重置按钮
        emptyList() {
            this.staffObj = {
                staffStatus: '', // 员工状态
                staffNo: '', // 员工编号
                staffName: '', // 员工姓名
                sex: '', // 性别
                realNameAuth: '', // 实名认证
                pageNum: 1,
                pageSize: 10,
                mobile: '', // 手机号码
                idCardNo: '', // 身份证号
                electricNo: '', // 电工证
                duty: '', // 岗位
                auditStatus: '' // 审核状态
            };
        },

        staffAdd() {
            this.formItem = this.$options.data().formItem;
            this.$refs['formItem'].resetFields();
            this.disStaffStatus = ''
            this.titles = '新增';
            this.modelEdit = true;
            this.modelType = false;
            AreaChange.regionList.forEach(item => {
                item.provinceId = '';
                item.cityId = '';
                item.regionId = '';
            });
            AreaChange.regionList.length = 1
        },
        isNoEmpty(e) {
            return !['', undefined, 'null', 'undefined', 0, null].includes(e)
        },
        handleRegionList() {
            const regionIdList = []
            const list = []
            for (let i = 0; i < AreaChange.regionList.length; i++) {
                const { provinceId } = AreaChange.regionList[0]
                const item = AreaChange.regionList[i]
                const { cityId, regionId, addressTwoList, addressThreeList } = item
                const provinceName = this.isNoEmpty(provinceId) ? AreaChange.addressOneList.filter(item => item.id === provinceId)[0].name : ''
                const cityName = this.isNoEmpty(cityId) ? addressTwoList.filter(item => item.id === cityId)[0].name : ''
                const regionName = this.isNoEmpty(regionId) ? addressThreeList.filter(item => item.id === regionId)[0].name : ''
                if (!provinceName || !cityName || !regionName) {
                    this.$Message.warning('区域选择不可为空!');
                    return true
                }
                regionIdList.push(regionId)
                if (regionIdList.length !== [...new Set(regionIdList)].length) {
                    this.$Message.warning('区域项不可重复!');
                    return true
                }
                list.push({ provinceId, cityId, regionId, provinceName, cityName, regionName })
            }
            this.formItem.regionList = list
            return false
        },
        // 选择日期
        selectDate(data, refObj) {
            let date = data.replace('T', ' ');
            this.formItem[refObj] = date;
        },
        // 新增员工
        saveStaff() {
            let datas = this.formItem;
            if (this.handleRegionList()) return
            if (datas.contractEndDate === '' || datas.contractStartDate === null) {
                this.$Message.error('合同开始日期不能为空');
            } else if (datas.contractStartDate === '' || datas.contractStartDate === null) {
                this.$Message.error('合同结束日期不能为空');
            } else if (datas.insuranceStartDate === '' || datas.insuranceStartDate === null) {
                this.$Message.error('人身意外险开始日期不能为空');
            } else if (datas.insuranceEndDate === '' || datas.insuranceEndDate === null) {
                this.$Message.error('人身意外险结束日期不能为空');
            } else {
                API.newEmployee(datas)
                    .then(res => {
                        if (res.data.success) {
                            this.$Message.success('添加成功');
                            this.getCommList();
                            this.modelEdit = false;
                        } else {
                            this.$Message.error(res.data.error);
                        }
                    })
                    .catch(res => {
                        this.$Message.error(res.data.message);
                    });
            }

        },
        // 修改员工状态
        editStaff() {
            let datas = this.formItem;
            if (this.handleRegionList()) return
            if (datas.contractEndDate === '' || datas.contractStartDate === null) {
                this.$Message.error('合同开始日期不能为空');
            } else if (datas.contractStartDate === '' || datas.contractStartDate === null) {
                this.$Message.error('合同结束日期不能为空');
            } else if (datas.insuranceStartDate === '' || datas.insuranceStartDate === null) {
                this.$Message.error('人身意外险开始日期不能为空');
            } else if (datas.insuranceEndDate === '' || datas.insuranceEndDate === null) {
                this.$Message.error('人身意外险结束日期不能为空');
            } else {
                API.editStatus(datas)
                    .then(res => {
                        if (res.data.success) {
                            this.$Message.success('修改成功');
                            this.getCommList();
                            this.modelEdit = false;
                        } else {
                            this.$Message.error(res.data.error);
                        }
                    })
                    .catch(res => {
                        this.$Message.error(res.data.message);
                    });
            }

        },
        // 员工列表
        getCommList() {
            this.$Message.loading({
                content: '查询中...',
                duration: 0
            });
            let datas = this.staffObj;
            API.getPersonnelList(datas).then(res => {
                this.$Message.destroy();
                let request = res.data.result;
                if (request.content.length > 0) {
                    this.totalElements = request.totalElements;
                    this.commList = request.content;
                    this.$emit('dataReceived', request.content);
                } else {
                    this.totalElements = 0;
                    this.commList = [];
                }
            });
        },

        // 员工详情
        getView(item) {
            this.disStaffStatus = item.staffStatus
            this.titles = '修改';
            this.modelType = true;
            let datas = {
                id: item.id
            };

            API.getPersonDetails(datas).then(res => {
                // 信息
                if (res.data.result) {
                    const obj = res.data.result
                    AreaChange.cityInit(obj, true)
                    // AreaChange.provinceId = String(obj.provinceId)
                    // AreaChange.cityId = String(obj.cityId)
                    // AreaChange.regionId = String(obj.regionId)
                    this.formItem = obj;
                    this.modelAudit = !(res.data.result.auditStatus === 'TWOREJECTED' || res.data.result.auditStatus === 'REJECTED')
                    this.modelEdit = true;
                    let allowEdit = !res.data.result.memberId && (!res.data.result.auditStatus || res.data.result.auditStatus === 'REJECTED')
                    this.modelType = !allowEdit
                    obj.regionList.forEach((item, i) => {
                        AreaChange.provinceId = String(item.provinceId)
                        setTimeout(() => {
                            AreaChange.cityId = item.cityId
                            AreaChange.regionId = String(item.regionId)
                        }, 1000);
                    })
                } else {
                    this.$Message.error(res.data.error);
                }
            });
        },

        // 文件上传
        fileUploadSuccess(response, file, fileList, refObj) {
            let that = this;
            this.formItem[refObj] = response.result[0].imageUrl;
            setTimeout(() => {
                that.$Message.destroy();
            }, 500);
        },

        progress() {
            this.$Message.loading({
                content: '上传中...',
                duration: 0
            });
        },
        handleFormatError(file) {
            this.$Notice.warning({
                title: '上传格式不正确',
                desc: '请上传正确的格式文件'
            });
        },

        handleMaxSize(file) {
            this.$Notice.warning({
                title: '上传图片过大',
                desc: file.name + '图片不应超过 20M，请压缩后上传.'
            });
        }
    }
};
</script>
<style lang="scss" scoped>
@import '@/style/_cus_list.scss';
@import '@/style/_ivu-modal.scss';
.region-item {
    display: flex;

    &:nth-child(n+2) {
        margin-top: 10px;
    }
}
</style>
