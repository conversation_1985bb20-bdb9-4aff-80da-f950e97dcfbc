<template>
    <div class="cus_list">
        <div class="list_son">
            <div class="step_div">
                <Steps :current="stepKey">
                    <Step title="去注册" content="电签注册"></Step>
                    <Step title="去认证" content="管理员/法人身份认证"></Step>
                    <Step title="去认证" content="企业信息采集"></Step>
                    <Step title="去认证" content="企业信息验证"></Step>
                    <Step title="去认证" content="企业授权意愿验证"></Step>
                    <Step title="认证完成" content="认证完成"></Step>
                </Steps>
            </div>
            <div class="commListDiv">
                <table class="cus_table_info" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                        <th width="200">企业名称</th>
                        <td>{{ itemObj.companyName || '-' }}</td>
                    </tr>
                    <tr>
                        <th>统一信息代码</th>
                        <td>{{ itemObj.businessLicenceNo || '-' }}</td>
                    </tr>
                    <tr>
                        <th>管理员</th>
                        <td>{{ itemObj.sealUseManager || '-' }}</td>
                    </tr>
                    <tr>
                        <th>管理员手机号</th>
                        <td>{{ itemObj.sealUseMobile || '-' }}</td>
                    </tr>
                    <tr>
                        <th>认证渠道</th>
                        <td>法大大</td>
                    </tr>
                    <tr>
                        <th>注册状态</th>
                        <td>
                            {{ itemObj.registStatus === 'YES' ? '已注册' : '待注册' || '待注册' }}
                            <Button v-if="itemObj.registStatus === 'NO'" type="success" style="margin-left: 20px;"
                                @click="debounce(toRegist)">去电签注册</Button>
                        </td>
                    </tr>
                    <tr>
                        <th>认证状态</th>
                        <td>
                            {{ itemObj.authStatus === 'YES' ? '已认证' : '待认证' || '待认证' }}
                            <Button v-if="itemObj.authStatus === 'NO'" type="primary" style="margin-left: 20px;"
                                @click="debounce(handelAuth)">去认证</Button>
                            <Button v-if="itemObj.authStatus === 'WAIT'" type="success" style="margin-left: 20px;"
                                @click="debounce(getInfo)">刷新状态</Button>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        <Modal v-model="modelView" reset-drag-position draggable sticky scrollable width="600" title="认证提示"
            :mask-closable="false" class-name="vertical-center-modal">
            <div style="margin: 0 30px;">
                <p>贵司已在法大大平台做过认证：</p>
                <ul class="model_view" style="margin: 0 30px;">
                    <li>公司名称：{{ limitData?.companyName || '-' }}</li>
                    <li>统一信用代码：{{ limitData?.taxCode || '-' }}</li>
                    <li>管理员：{{ limitData?.name || '-' }}</li>
                    <li>手机号：{{ limitData.phone ? desensitized.judgeType(limitData.phone) : '-' }}</li>
                </ul>
            </div>
            <div slot="footer"><Button type="success" @click="debounce(closeModelView)">确定</Button></div>
        </Modal>
        <Modal width="800" v-model="modelAdd" draggable sticky scrollable title="电签注册" :footer-hide="true"
            :mask-closable="false">
            <Form :model="formItem" :label-width="150">
                <FormItem label="企业名称" required>
                    <Input class="item_large" readonly maxlength="15" v-model="formItem.companyName"
                        placeholder="输入企业名称" style="width: 200px;" />
                </FormItem>
                <FormItem label="统一信息代码" required>
                    <Input class="item_large" readonly maxlength="20" v-model="formItem.taxCode" placeholder="输入统一信息代码"
                        style="width: 200px;" />
                </FormItem>
                <FormItem label="姓名" required>
                    <Tooltip content="*提交认证的时候绑定的人员，是该企业最初的印章管理员。请慎重选择绑定人员" max-width="300" placement="right">
                        <Input class="item_large" maxlength="10" v-model="formItem.userName" clearable
                            placeholder="输入姓名" style="width: 200px;" />
                    </Tooltip>
                </FormItem>
                <FormItem label="手机号" required>
                    <Input class="item_large" maxlength="11" v-model="formItem.phone" clearable placeholder="输入手机号"
                        style="width: 200px;" />
                </FormItem>
                <FormItem label="验证码" required>
                    <Input class="item_large" maxlength="6" v-model="formItem.adminPhoneValidateCode" clearable
                        placeholder="输入验证码" style="width: 200px;" />
                    <Button type="primary" :disabled="timer ? true : false" @click="getSendSms"
                        style="margin-left: 20px;">{{ smsTxt }}</Button>
                </FormItem>
                <FormItem label="认证渠道" required>
                    <Input class="item_large" readonly maxlength="10" v-model="formItem.authChannel"
                        placeholder="输入认证渠道" style="width: 200px;" />
                </FormItem>
            </Form>
            <div style="text-align: center;margin: 30px 0;">
                <Button type="default" size="large" style="width: 120px;margin-right: 30px;"
                    @click="modelAdd = false">取消</Button>
                <Button type="success" size="large" style="width: 120px" @click="debounce(handelRegist)">提交</Button>
            </div>
        </Modal>
    </div>
</template>

<script>
import API, { fddCheckIsRegist } from '@/api/apv';
import _data from '@/views/apv/_edata.js'; // 数据字典
import { debounce } from 'lodash';
import { Desensitized } from "@/utils/Desensitized";

export default {
    data() {
        return {
            modelAdd: false,
            modelView: false,
            stepKey: 0,
            taxpayerTypeList: _data.taxpayerTypeList,
            itemObj: '',
            smsTxt: '获取验证码',
            timer: null,
            authUrl: '',
            formItem: {
                adminPhoneValidateCode: '',
                authChannel: '法大大',
                phone: '',
                userName: '',
                companyName: '',
                taxCode: '',
            },
            limitData: {
                adminPhoneValidateCode: '',
                authChannel: '法大大',
                phone: '',
                userName: '',
                companyName: '',
                taxCode: '',
            },
        };

        props: {
            companyType: String   // SERVICE_PROVIDER（建站服务商），OPERATION_PROVIDER（售后运维服务商）
        }
    },

    mounted() {
        document.addEventListener('visibilitychange', this.handleVisiable)
        this.getInfo();
    },

    destroyed() {
        document.removeEventListener('visibilitychange', this.handleVisiable)
    },

    computed: {
        desensitized() {
            return Desensitized;
        }
    },

    methods: {
        handleVisiable(e) {
            if (e.target.visibilityState === 'visible') {
                // console.log("标签激活");
                this.getInfo();
            }
        },

        // 企业注册信息
        getInfo() {
            const type = {
                companyType: this.$attrs.companyType,
            }
            API.detail(type).then(res => {
                // console.log(res, 'res');
                if (res.data.success && res.data.result) {
                    this.itemObj = res.data.result;
                    this.formItem.companyName = res.data.result.companyName;
                    this.formItem.taxCode = res.data.result.businessLicenceNo;
                    if (res.data.result.registStatus === 'YES') {
                        this.stepKey = 1
                        if (res.data.result.authStatus === 'YES') {
                            this.stepKey = 5
                        }
                    }
                }
            });
        },
        // 验证码
        getSendSms() {
            let datas = {
                mobile: this.formItem.phone
            };
            if (datas.mobile.length !== 11) {
                this.$Message.warning('输入11位手机号');
                return;
            }
            API.partnerRegistSendSms(datas).then(res => {
                if (res.data.success) {
                    let count = 60;
                    this.timer = setInterval(() => {
                        if (count === 0) {
                            clearInterval(this.timer);
                            this.timer = null;
                            this.smsTxt = '重新获取';
                        } else {
                            this.smsTxt = count-- + 's后重新获取 ';
                        }
                    }, 1000);
                } else {
                    this.$Message.error(res.data.error);
                }
            });
        },

        // 提交
        handelRegist() {
            let datas = this.formItem;
            datas.companyType = this.$attrs.companyType
            API.fddRegist(datas)
                .then(res => {
                    if (res.data.success) {
                        this.$Message.success('提交成功');
                        this.modelAdd = false;
                        this.getInfo();
                    } else {
                        this.$Message.error(res.data.error);
                    }
                })
                .catch(res => {
                    this.$Message.error(res.data.message);
                });
        },
        // 认证
        async handelAuth() {
            if (!this.itemObj.registStatus || this.itemObj.registStatus === 'NO') {
                this.$Message.error("请先完成电签注册");
                return
            }
            if (this.authUrl) {
                // console.log(this.authUrl,'this.authUrl')
                this.confirmTo(this.authUrl)
                return
            }
            const type = {
                companyType: this.$attrs.companyType,
            }
            const res = await API.fddAuth(type).catch(res => {
                this.$Message.error(res.data.message);
            });

            if (res.data.success) {
                this.authUrl = res.data.result
                this.confirmTo(res.data.result)
            } else {
                this.$Message.error(res.data.error);
            }
        },

        confirmTo(authUrl) {
            let that = this
            this.$Modal.confirm({
                title: '提示',
                content: '即将跳转页面进行认证',
                onOk: () => {
                    that.itemObj.authStatus = "WAIT"
                    window.open(authUrl)
                },
                onCancel: () => { }
            })
        },

        toRegist() {
            let datas = this.formItem;
            datas.companyType = this.$attrs.companyType
            fddCheckIsRegist(datas).then(res => {
                if (res.data.success) {
                    if (res.data.result) {
                        this.modelView = true
                        this.limitData = res.data.result
                    } else {
                        this.modelAdd = true
                    }
                } else {
                    this.$Message.error(res.data.error);
                }
            })
        },

        closeModelView() {
            this.modelView = false
            this.getInfo();
        },
    }
};
</script>

<style lang="scss" scoped>
@import 'style/_cus_list.scss';

.cus_list {
    min-height: auto;
}

.tips {
    padding: 10px 10px 0;
    font-size: 12px;
    color: #999;
}
</style>
