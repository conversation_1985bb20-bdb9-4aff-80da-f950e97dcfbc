<template>
    <Modal v-model="shipModel" :footer-hide="true" title="订单发货" draggable sticky>
        <p class="express_p">
            <span class="skuconten_span">发货类型</span>
            <Dropdown @on-click="dropdownOne" @on-visible-change="showVisibleOne('logistics')">
                <Button :class="isDropdownSign == 'logistics' ? 'buttonSignOne' : ''">物流</Button>
                <DropdownMenu slot="list" style="max-height: 300px; overflow: auto">
                    <DropdownItem v-for="(item, index) in expressList" :key="index" :name="item.code">{{ item.name }}</DropdownItem>
                </DropdownMenu>
            </Dropdown>
            <Dropdown @on-click="dropdownOne" @on-visible-change="showVisibleOne('express')" style="margin-left: 20px">
                <Button :class="isDropdownSign == 'express' ? 'buttonSignOne' : ''">快递</Button>
                <DropdownMenu slot="list" style="max-height: 300px; overflow: auto">
                    <DropdownItem v-for="(item, index) in expressList" :key="index" :name="item.code">{{ item.name }}</DropdownItem>
                </DropdownMenu>
            </Dropdown>
            <Dropdown @on-click="dropdownOne" @on-visible-change="showVisibleOne('pick')" style="margin-left: 20px">
                <Button :class="isDropdownSign == 'pick' ? 'buttonSignOne' : ''">自提</Button>
                <DropdownMenu slot="list" style="max-height: 300px;overflow: auto">
                    <DropdownItem v-for="(item, index) in expressList" :key="index" :name="item.code">{{ item.name }}</DropdownItem>
                </DropdownMenu>
            </Dropdown>
        </p>
        <p class="express_p">
            <span class="skuconten_span">公司名称</span>
            <Input v-model="shipObj.expressName" :readonly="isEditExpressName" placeholder="选择或输入..." style="width: 300px" />
        </p>
        <p v-if="!isPick" class="express_p">
            <span class="skuconten_span">运单号</span>
            <Input v-model="shipObj.expressNumber" placeholder="请输入..." style="width: 300px" />
        </p>
        <div class="rzcondition" style="text-align: center">
            <Button @click="isOkExpress" style="margin: 20px 10px" class="buttonSign" type="primary">提交</Button>
            <Button @click="shipModel = false" style="margin: 20px 10px" class="buttonSignOne">取消</Button>
        </div>
    </Modal>
</template>

<script>
    import { listAllExpress } from '@/api/index';
    export default {
        data() {
            return {
                isPick: false,
                isDropdownSign: '',
                isEditExpressName: false,
                shipModel: false,
                expressList: [],
                visibleObj: {
                    logistics: [],
                    express: [],
                    pick: []
                },
                shipObj: {
                    id: '',
                    deliveryType: '',
                    expressName: '',
                    expressCode: '',
                    expressNumber: ''
                }
            };
        },

        methods: {
            //   显示快递列表
            showVisibleOne(visible) {
                if (visible === 'logistics') {
                    this.getExpressList('logistics');
                } else if (visible === 'express') {
                    this.getExpressList('express');
                } else if (visible === 'pick') {
                    this.getExpressList('pick');
                }
                this.shipObj.deliveryType = visible;
            },

            //   选择 快递类型
            dropdownOne(item) {
                this.isDropdownSign = this.shipObj.deliveryType;
                this.shipObj.expressCode = item;
                if (this.shipObj.expressCode === 'other_express' || this.shipObj.expressCode === 'other_logistics') {
                    this.shipObj.expressName = '';
                    this.isEditExpressName = false;
                    return;
                }
                if (this.isDropdownSign === 'pick') {
                    this.isPick = true;
                } else {
                    this.isPick = false;
                }
                this.isEditExpressName = true;
                this.expressList.forEach(item => {
                    if (item.code === this.shipObj.expressCode) {
                        this.shipObj.expressName = item.name;
                    }
                });
            },

            getExpressList(type) {
                if (type === this.shipObj.deliveryType) {
                    return;
                }
                if (this.visibleObj[`${type}`].length > 0) {
                    this.expressList = this.visibleObj[`${type}`];
                    return;
                }
                let data = {
                    expressType: type || 'logistics'
                };
                listAllExpress(data).then(res => {
                    if (res.data.success) {
                        if (res.data.result.length > 0) {
                            if (type === 'logistics') {
                                this.visibleObj.logistics = res.data.result;
                            } else if (type === 'express') {
                                this.visibleObj.express = res.data.result;
                            } else if (type === 'pick') {
                                this.visibleObj.pick = res.data.result;
                            }
                            this.expressList = res.data.result;
                        }
                    }
                });
            },

            isOkExpress() {
                if (!this.shipObj.expressCode) {
                    this.$Message.warning('填写或选择内容不能为空');
                    return;
                }
                if (!this.shipObj.expressNumber && !this.isPick) {
                    this.$Message.warning('填写或选择内容不能为空');
                    return;
                }
                let expressName = '';
                this.expressList.filter(item => {
                    if (item.code === this.shipObj.expressCode) {
                        expressName = item.name;
                    }
                });
                this.$emit('bindcall')
            }
        }
    };
</script>

<style lang="scss" scoped>
@import 'style/orderList.scss';
</style>
