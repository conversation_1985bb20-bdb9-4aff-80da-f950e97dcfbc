<script setup>

</script>

<template>
    <div class="flex-column-center">
        <div class="spinner">
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
        </div>
        <div class="spinner-text">正在处理，稍等片刻</div>
    </div>
</template>

<style scoped lang="scss">
.spinner {
    width: 30px;
    height: 30px;
    animation: spinner-y0fdc1 3s infinite ease;
    transform-style: preserve-3d;
}

.spinner-text {
    margin-top: 35px;
    color: #19be6b;
    font-size: 15px;
}

.spinner > div {
    background-color: rgba(0,77,255,0.2);
    height: 100%;
    position: absolute;
    width: 100%;
    border: 1px solid #2d8cf0;
}

.spinner div:nth-of-type(1) {
    transform: translateZ(-15px) rotateY(180deg);
}

.spinner div:nth-of-type(2) {
    transform: rotateY(-270deg) translateX(50%);
    transform-origin: top right;
}

.spinner div:nth-of-type(3) {
    transform: rotateY(270deg) translateX(-50%);
    transform-origin: center left;
}

.spinner div:nth-of-type(4) {
    transform: rotateX(90deg) translateY(-50%);
    transform-origin: top center;
}

.spinner div:nth-of-type(5) {
    transform: rotateX(-90deg) translateY(50%);
    transform-origin: bottom center;
}

.spinner div:nth-of-type(6) {
    transform: translateZ(15px);
}

@keyframes spinner-y0fdc1 {
    0% {
        transform: rotate(45deg) rotateX(-25deg) rotateY(25deg);
    }

    50% {
        transform: rotate(45deg) rotateX(-385deg) rotateY(25deg);
    }

    100% {
        transform: rotate(45deg) rotateX(-385deg) rotateY(385deg);
    }
}
</style>
