<script setup>
import { computed, defineProps, ref } from "vue";
import { Modal, Tooltip } from "view-design";

const props = defineProps({
    // * 图片链接
    imgUrl: {
        type: String,
        default: ""
    },
    // * 图片名称
    imgName: {
        type: String,
        default: ""
    },
    // * 额外窗口外信息
    imgTips: {
        type: String,
        default: ""
    },
    // * 是否必填
    required: {
        type: Boolean,
        default: false
    },
    // * 是否禁用
    disabled: {
        type: Boolean,
        default: false
    },
    // * 是否展示
    show: {
        type: Boolean,
        default: true
    },
    // * 额外的自定义 class
    className: {
        type: String,
        default: ""
    },
    width: {
        type: Number,
        default: 120
    }
});

let previewView = ref(false);

const openImg = (url) => {
    if (['.zip', '.pdf', '.dwg', '.dws', '.dwt', '.dxf'].some(keywords => url.includes(keywords))) {
        window.open(url);
    } else {
        previewView.value = true
    }
}

const isVideo = computed(() => {
    return ['.mp4'].some(keywords => props.imgUrl?.includes(keywords))
});

const toTarget = (url) => {
    window.open(url);
};

</script>

<template>
    <div class="imgview" v-show="show" v-if="imgUrl" :style="{ width: `${width}px` }">
        <img v-if="['.zip', '.pdf', '.dwg', '.dws', '.dwt', '.dxf'].some(keyword => imgUrl.includes(keyword))"
            @click="openImg(imgUrl)" width="22px" alt="upload" :src="require('../../assets/icon/uploaded.svg')"
            :style="{ minHeight: `${width}px` }">
        <img v-else class="imagefit"
            :src="imgUrl + (isVideo ? '?x-oss-process=video/snapshot,t_1,f_jpg,w_0,h_0,m_fast' : '?x-oss-process=image/resize,m_lfit,h_200,w_200')"
            @click="openImg(imgUrl)" width="100%" loading="lazy" :title="imgName" :style="{ minHeight: `${width}px` }"
            :alt="imgName" />
        <span class="imgname" v-if="imgName" v-html="imgName"></span>
        <Modal width="99" v-model="previewView" reset-drag-position draggable sticky :title="imgName || '图片详情'"
            footer-hide class-name="vertical-center-modal">
            <div slot="header" class="ivu-modal-header-inner" v-html="imgName || '预览'" style="height: auto"></div>
            <Tooltip content="点击查看源文件" theme="light">
                <video v-if="isVideo" class="preview" controls autoplay :src="imgUrl" @click="toTarget(imgUrl)"></video>
                <img v-else class="preview" :src="imgUrl" @click="toTarget(imgUrl)" alt="visibleImg">
            </Tooltip>
        </Modal>
    </div>
</template>

<style scoped lang="scss">
$border-color-upload: 1px solid $theme-sea-main;

.imgview {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    margin-right: 15px;
    margin-bottom: 15px;
    background: rgb(223 244 252 / 20%);
    backdrop-filter: blur(1px);
    border: $border-color-upload !important;
    height: fit-content;
    border-radius: 8px;
    overflow: hidden;

    .imgname {
        padding: 10px;
        width: 100%;
        font-size: 12px;
        text-align: center;
        color: transparentize($theme-sea-heavy, 0.35);
        background: $theme-sea-main;
        zoom: 0.9;
        z-index: 1;
    }

    &:last-of-type {
        margin-right: 0;
    }
}

img {
    cursor: pointer;
    vertical-align: text-top;
    aspect-ratio: 1;
    object-fit: contain;
}

:deep(.vertical-center-modal) {
    .ivu-modal {
        display: flex;
        justify-content: center;
    }

    .ivu-modal-content {
        width: fit-content !important;
    }

    .ivu-tooltip {
        font-size: 0;
    }

    .ivu-modal-body {
        @extend .flex-row-center-center;
        padding: 0;
        max-width: 100%;
        min-width: 360px;
        min-height: 40vh;
        max-height: 75vh;
        overflow-y: auto;
        overflow-x: hidden;

        .preview {
            max-width: 100%;
            max-height: 75vh;
            cursor: pointer;
        }
    }
}
</style>
