<script setup>
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Message, Spin, Upload } from "view-design";
import { ref, defineProps, defineEmits, computed } from "vue";

const props = defineProps({
  imgUrl: {
    type: Array,
    default: () => []
  },
  imgName: {
    type: String,
    default: ""
  },
  required: {
    type: Boolean | Number,
    default: false
  },
  className: {
    type: String,
    default: ""
  },
  type: {
    type: String,
    default: "image"
  },
  disabled: {
    type: Boolean,
    default: false
  },
  showList: {
    type: Boolean,
    default: false
  },
  maxSize: {
    type: Number,
  },
  canDel: {
    type: Boolean,
    default: true
  },
  maxCount: {
    type: Number,
    default: 3
  }
});

let showSpin = ref(false);
let previewUpload = ref(false);
let previewUrl = ref("");

const watchFile = (url) => {
  const currentFileType = props.type;

  if (currentFileType !== 'image' && currentFileType !== 'video') {
    if (url.endsWith('.pdf')) {
      window.open(url);
    } else {
      Modal.confirm({
        title: '提醒',
        content: '<p>该文件可能需下载后查看</p>',
        okText: '知晓并确认',
        onOk: () => {
          window.open(url);
        }
      });
    }
  } else {
    previewUrl.value = url;
    previewUpload.value = true;
  }
}

const toTarget = (url) => {
  window.open(url);
};

const emit = defineEmits(["update:imgUrl", "change"]);

const headers = { Authorization: `Bearer ${JSON.parse(window.localStorage.getItem("logins")).access_token}` }

const actionUrl = `${process.env.VUE_APP_BASE_API}/merchant/oss/image/uploadFile.do`

const upProgress = () => {
  showSpin.value = true;
};

const upSuccess = (response, file, fileList) => {
  if (response.success) {
    const newFileUrl = response.result[0]?.imageUrl || response.result[0]?.pdfUrl || response.result[0]?.fileUrl;
    if (newFileUrl) {
      const currentFiles = [...props.imgUrl];
      if (currentFiles.length < props.maxCount) {
        currentFiles.push(newFileUrl);
        emit("update:imgUrl", currentFiles);
        emit("change", currentFiles, file, fileList);
      } else {
        Message.warning(`最多只能上传 ${props.maxCount} 个文件。`);
      }
    } else {
      Message.error("上传成功，但未返回文件链接。");
    }
  } else {
    Message.error(response?.error || "上传失败");
  }
  showSpin.value = false;
};

const upFail = () => {
  setTimeout(() => {
    showSpin.value = false;
    Message.error("上传失败");
  }, 300);
};

const formatError = (_file) => {
  Message.warning({
    content: "上传格式不正确，请上传正确的格式文件",
    duration: 5
  });
};

const maxSizeError = (_file) => {
  Message.warning({
    content: `${_file.name}文件太大，请压缩后低于${(dynaicMaxSize.value) / 1024} MB上传.`,
    duration: 5
  });
};

const delImg = (index) => {
  const currentFiles = [...props.imgUrl];
  currentFiles.splice(index, 1);
  emit("update:imgUrl", currentFiles);
  emit("change", currentFiles);
};

const isImage = computed(() => {
  let type = props.type;
  return ["image"].includes(type);
});

const isVideo = computed(() => {
  let type = props.type;
  return ["video"].includes(type);
});

const isPdf = computed(() => {
  let type = props.type;
  return ["pdf"].includes(type);
});

const isFile = computed(() => {
  let type = props.type;
  return !["image", "video"].includes(type);
});

const isCad = computed(() => {
  let type = props.type;
  return ["file", "image"].includes(type);
});

const hasCover = computed(() => {
  if (!props.imgUrl || props.imgUrl.length === 0) return false;
  return (isImage.value || isVideo.value);
});

const acceptType = computed(() => {
  const resObj = {
    "image": ".jpg, .jpeg, .png",
    "video": ".mp4",
    "pdf": ".pdf",
    "file": ".zip, .rar, .dwg, .dws, .dwt, .dxf"
  };
  return resObj[props.type] || ".png,.jpg,.jpeg,.gif,.bmp,.pdf,.rar,.zip,.mp4,.dwg,.dws,.dwt,.dxf";
});

const formatType = computed(() => {
  const resObj = {
    "image": ["jpg", "jpeg", "png"],
    "video": ["mp4"],
    "pdf": ["pdf"],
    "file": ["zip", "rar", "dwg", "dws", "dwt", "dxf"]
  };
  return resObj[props.type] || ["png", "jpg", "jpeg", "gif", "bmp", "pdf", "rar", "zip", "mp4", "dwg", "dws", "dwt", "dxf"];
});

const dynaicMaxSize = computed(() => {
  const resObj = {
    "image": 10240,
    "video": 102400,
    "pdf": 102400,
    "file": 102400
  };
  return props.maxSize || resObj[props.type];
});

const handleBeforeUpload = () => {
  if (props.imgUrl.length >= props.maxCount) {
    Message.warning(`最多只能上传 ${props.maxCount} 个文件。`);
    return false;
  }
  return true;
};

const uploadHintText = computed(() => {
  const itemType = props.type === 'image' ? '图片' : (props.type === 'video' ? '视频' : '文件');
  const maxSizeMB = Math.max(1, Math.round(dynaicMaxSize.value / 1024));
  const formats = formatType.value.join(', ');
  return `最多上传 ${props.maxCount} 个${itemType}，单个不超过 ${maxSizeMB}MB。支持格式：${formats}`;
});

</script>

<template>
  <div>
    <div class="cus-multi-upload-wrapper">
      <template v-if="props.imgUrl && props.imgUrl.length">
        <div class="uploaded-item-list" v-for="(itemUrl, index) in props.imgUrl" :key="index">
          <img v-if="isImage" :src="itemUrl" class="uploaded-item-preview-img" alt="Uploaded Image" />
          <video v-else-if="isVideo" :src="itemUrl" class="uploaded-item-preview-video" controls></video>
          <div v-else class="uploaded-item-file-placeholder">
            <Icon type="md-document" size="30" />
            <span class="file-name-ellipsis">{{ itemUrl.substring(itemUrl.lastIndexOf('/') + 1) }}</span>
          </div>
          <div class="uploaded-item-actions">
            <Icon type="ios-eye-outline" @click="watchFile(itemUrl)"></Icon>
            <Icon v-if="canDel" type="ios-trash-outline" @click="delImg(index)"></Icon>
          </div>
        </div>
      </template>
      <Upload :accept="acceptType" :format="formatType" :disabled="disabled" :show-upload-list="false"
        :on-progress="upProgress" :on-error="upFail" :max-size="dynaicMaxSize" :on-success="upSuccess"
        :headers="headers" :action="actionUrl" :on-format-error="formatError" :on-exceeded-size="maxSizeError"
        :multiple="true" v-if="props.imgUrl.length < props.maxCount" type="drag" class="multi-upload-drag-area">
        <div class="upload-drag-content" v-if="!disabled">
          <Spin v-if="showSpin" fix style="background: rgba(255,255,255,0.8); z-index:10;white-space: nowrap">
            <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
            <span class="load-txt">上传中</span>
          </Spin>
          <Icon v-else type="md-add" size="30" />
        </div>
        <div v-if="imgName" class="name-upload-trigger" @click.stop>{{ required ? "*" : "" }} {{ imgName }}
        </div>
      </Upload>
    </div>
    <div class="upload-rules-hint" v-if="props.imgUrl.length < props.maxCount && !disabled">
      {{ uploadHintText }}
    </div>
    <Modal width="99" v-model="previewUpload" reset-drag-position draggable sticky :title="previewUrl || '文件详情'"
      footer-hide class-name="vertical-center-modal">
      <Tooltip content="点击查看源文件" theme="light">
        <video v-if="isVideo" class="preview" controls autoplay :src="imgUrl" @click="toTarget(imgUrl)"></video>
        <img v-else-if="isImage" class="preview" :src="imgUrl" @click="toTarget(imgUrl)" alt="visibleImg">
      </Tooltip>
    </Modal>
  </div>
</template>

<style scoped lang="scss">
.cus-multi-upload-wrapper {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: 12px;
  width: 100%;
}

.uploaded-item-list {
  width: 100px;
  height: 100px;
  border: 1px solid #dcdee2;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f7f7f7;

  .uploaded-item-preview-img,
  .uploaded-item-preview-video {
    max-width: 100%;
    max-height: 100%;
    object-fit: cover;
  }

  .uploaded-item-file-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 5px;

    .file-name-ellipsis {
      font-size: 12px;
      color: #515a6e;
      width: 90px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-top: 5px;
    }
  }

  .uploaded-item-actions {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    align-items: center;
    justify-content: space-around;
    padding: 5px;

    i {
      font-size: 20px;
      cursor: pointer;
    }
  }

  &:hover .uploaded-item-actions {
    display: flex;
  }
}

.multi-upload-drag-area {
  display: inline-block;
  border-radius: 4px;
  transition: border-color 0.2s ease;
  flex: 0 0 auto;

  &:hover {
    border-color: #5cadff;
  }

  .upload-drag-content {
    width: 100px;
    height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #999;
    position: relative;

    p {
      font-size: 12px;
      margin-top: 5px;
    }
  }

  .name-upload-trigger,
  .extips-upload-trigger {
    display: block;
    text-align: center;
    margin-top: 5px;
    font-size: 12px;
    cursor: default;
  }

  .name-upload-trigger {
    color: #515a6e;
  }

  .extips-upload-trigger {
    color: #999;
  }
}

.upload-rules-hint {
  font-size: 12px;
  color: #999999;
  margin-top: 8px;
  line-height: 1.5;
  text-align: left;
}

$wh: 130px;
$border-color-upload: 1px solid $theme-sea-main;

.ellipsis {
  margin: 8px;
  padding: 0 !important;
  display: -webkit-box;
  overflow: hidden;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.file-txt {
  @extend .flex-row-center-center;
  padding: 8px;
  max-width: $wh;
  width: 100%;
  font-size: 12px;
  line-height: 1.2;
  color: $theme-sea-heavy;
  text-align: center;
}

.name {
  @extend .file-txt;
  background: $theme-sea-main;
  border-radius: 0 0 6px 6px;
  border: $border-color-upload;
  border-top: 0;
}

.extips {
  @extend .file-txt;
  display: inline-block;
  text-align: justify;
  font-size: 11px;
  color: #999;
  line-height: 1.5;
}

.file_pics {
  @extend .flex-row-center-center;
  position: relative;
  min-width: $wh;
  width: $wh;
  height: $wh;
  border-radius: 6px 6px 0 0;
  background: rgb(223 244 252 / 20%);
  backdrop-filter: blur(1px);
  overflow: hidden;
  border: $border-color-upload !important;

  &.noname {
    border-radius: 6px;
  }

  .aim {
    width: 100%;
    height: 100%;
    cursor: pointer;

    &.disabled {
      cursor: not-allowed;
      filter: grayscale(1) opacity(0.5);
    }
  }

  .demo-upload-list {
    @extend .flex-row-center-center;
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .fileshow {
      padding: 0 20px;
    }

    &:hover .demo-upload-list-cover {
      display: flex;
    }
  }

  .demo-upload-list-cover {
    @extend .flex-row-center-around;
    display: none;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, .5);
    z-index: 999;

    i {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      width: 100%;
      cursor: pointer;

      &:hover {
        backdrop-filter: blur(3px);
      }
    }
  }
}

.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
  color: transparentize($theme-sea-heavy, 0.35);
}

@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(180deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.load-txt {
  font-size: 12px;
  color: transparentize($theme-sea-heavy, 0.35);
}

:deep(.vertical-center-modal) {
  .ivu-modal {
    display: flex;
    justify-content: center;
  }

  .ivu-modal-content {
    width: fit-content !important;
  }

  .ivu-tooltip {
    font-size: 0;
  }

  .ivu-modal-body {
    @extend .flex-row-center-center;
    padding: 0;
    max-width: 100%;
    min-width: 360px;
    min-height: 40vh;
    max-height: 75vh;
    overflow-y: auto;
    overflow-x: hidden;

    .preview {
      max-width: 100%;
      max-height: 75vh;
      cursor: pointer;
    }
  }
}
</style>
