<script setup>
const {version, description, timestamp} = defineProps({
    version: [Number,String],
    description: String,
    timestamp: [String,Date]
})
</script>

<template>
    <div class="card">
        <div class="card-name">
            <svg t="1721633605201" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12445" width="30" height="30"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#1687E8" fill-opacity="0.1" p-id="12446"></path><path d="M494.784 745.344a46.592 46.592 0 0 1-51.84 22.208 50.048 50.048 0 0 1-39.488-39.488l-64-330.624h-84.032v-98.688h123.392a50.432 50.432 0 0 1 49.344 39.488l49.344 249.216 207.232-330.624 83.904 51.84-273.856 436.736z" fill="#1687E8" p-id="12447"></path></svg>
            &nbsp; {{version}}
        </div>
        <div class="quote">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 330 307" height="80" width="80">
                <path fill="currentColor"
                      d="M302.258 176.221C320.678 176.221 329.889 185.432 329.889 203.853V278.764C329.889 297.185 320.678 306.395 302.258 306.395H231.031C212.61 306.395 203.399 297.185 203.399 278.764V203.853C203.399 160.871 207.902 123.415 216.908 91.4858C226.323 59.1472 244.539 30.902 271.556 6.75027C280.562 -1.02739 288.135 -2.05076 294.275 3.68014L321.906 29.4692C328.047 35.2001 326.614 42.1591 317.608 50.3461C303.69 62.6266 292.228 80.4334 283.223 103.766C274.626 126.69 270.328 150.842 270.328 176.221H302.258ZM99.629 176.221C118.05 176.221 127.26 185.432 127.26 203.853V278.764C127.26 297.185 118.05 306.395 99.629 306.395H28.402C9.98126 306.395 0.770874 297.185 0.770874 278.764V203.853C0.770874 160.871 5.27373 123.415 14.2794 91.4858C23.6945 59.1472 41.9106 30.902 68.9277 6.75027C77.9335 -1.02739 85.5064 -2.05076 91.6467 3.68014L119.278 29.4692C125.418 35.2001 123.985 42.1591 114.98 50.3461C101.062 62.6266 89.6 80.4334 80.5942 103.766C71.9979 126.69 67.6997 150.842 67.6997 176.221H99.629Z"></path>
            </svg>
        </div>
        <div class="body-text" v-html="description"></div>
        <div class="author">更新于 <Time :time="timestamp" /> </div>
    </div>
</template>

<style scoped lang="scss">
.card {
    width: 100%;
    min-height: 200px;
    background: $theme-sea-main;
    position: relative;
    border-radius: 8px;
    * {
        font-family: "JetBrains Mono",monospace;
    }
}

.quote {
    color: rgba($theme-sea-light, 0.3);
    padding-left: 30px;
    position: absolute;
    bottom: 50px;
}

.card-name {
    display: flex;
    align-items: center;
    text-transform: uppercase;
    font-weight: 700;
    color: $theme-sea-heavy;
    padding: 35px;
    line-height: 23px;
    font-size: 24px;
}

.body-text {
    font-size: 14px;
    font-weight: 900;
    padding: 0 40px;
    color: #465512;
    line-height: 23px;
    max-height: 30vh;
    overflow-y: auto;
}

.author {
    margin-top: 5px;
    transition: 0.5s;
}

.pic {
    width: 50px;
    height: 50px;
    background-color: rgb(158, 196, 21);
    border-radius: 50%;
}

.author-container {
    display: flex;
    align-items: center;
}

.author {
    font-weight: 700;
    color: rgb(127, 155, 29);
    padding: 20px 40px 40px;
}

.card .author svg {
    display: inline;
    font-size: 12px;
    color: rgba(128, 155, 29, 0.452);
}
</style>
