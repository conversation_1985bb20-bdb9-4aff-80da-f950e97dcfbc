<script setup>
import { I<PERSON>, Modal, Tooltip, Message, Spin, Upload } from "view-design";
import { ref, defineProps, defineEmits, computed } from "vue";

const props = defineProps({
    // * 图片链接
    imgUrl: {
        type: String,
        default: ""
    },
    // * 图片名称
    imgName: {
        type: String,
        default: ""
    },
    // * 额外窗口外信息
    imgTips: {
        type: String,
        default: ""
    },
    // * 是否必填
    required: {
        type: Boolean | Number,
        default: false
    },
    // * 是否展示
    show: {
        type: Boolean,
        default: true
    },
    // * 额外的自定义 class
    className: {
        type: String,
        default: ""
    },
    // * 上传的文件类型
    type: {
        type: String,  // * [image,pdf,file,video]
        default: "image"
    },
    // * 是否禁用
    disabled: {
        type: Boolean,
        default: false
    },
    // * 是否展示列表
    showList: {
        type: Boolean,
        default: false
    },
    // * 上限
    maxSize: {
        type: Number,
        default: 18432
    },
    // * 删除按钮
    canDel: {
        type: <PERSON><PERSON><PERSON>,
        default: true
    }
});

let showSpin = ref(false);
let previewUpload = ref(false);
let fileName = ref("");

const watchFile = () => {
    if (isFile.value || props.type === "CAD") {
        if (isPdf.value) {
            window.open(props.imgUrl);
        } else {
            Modal.confirm({
                title: '提醒',
                content: '<p>该文件可能需下载后查看</p>',
                okText: '知晓并确认',
                onOk: () => {
                    window.open(props.imgUrl);
                }
            });
        }
    } else {
        previewUpload.value = true
    }
}

const toTarget = (url) => {
    window.open(url);
};

const emit = defineEmits(["update:imgUrl"]);

const headers = { Authorization: `Bearer ${JSON.parse(window.localStorage.getItem("logins")).access_token}` }

// * 多合一上传接口，历史接口尽可能少用 接口更新
// const actionUrl = `${process.env.VUE_APP_BASE_API}/merchant/oss/file/upload.do`
const actionUrl = `${process.env.VUE_APP_BASE_API}/merchant/oss/image/uploadFile.do`

const upProgress = () => {
    showSpin.value = true;
};

const upSuccess = (response) => {
    if (response.success) {
        const newImageUrl = response.result[0]?.imageUrl || response.result[0]?.pdfUrl || response.result[0]?.fileUrl;
        emit("update:imgUrl", newImageUrl);
        emit("change", newImageUrl);
        fileName.value = response.result[0]?.pdfName || response.result[0]?.fileName;
    } else {
        Message.error(response?.error);
    }
    setTimeout(() => {
        showSpin.value = false;
    }, 300);
};

const upFail = () => {
    setTimeout(() => {
        showSpin.value = false;
        Message.error("上传失败");
    }, 300);
};

const formatError = (_file) => {
    Message.warning({
        content: "上传格式不正确，请上传正确的格式文件",
        duration: 5
    });
};

const maxSizeError = (_file) => {
    Message.warning({
        content: `${_file.name}文件太大，请压缩后低于${(dynaicMaxSize.value) / 1024} MB上传.`,
        duration: 5
    });
};

const delImg = () => {
    emit("update:imgUrl", "");
    fileName.value = "";
};

const isImage = computed(() => {
    let type = props.type;
    return ["image"].includes(type);
});

const isVideo = computed(() => {
    let type = props.type;
    return ["video"].includes(type);
});

const isPdf = computed(() => {
    let type = props.type;
    return ["pdf"].includes(type);
});

const isFile = computed(() => {
    let type = props.type;
    return !["image", "video"].includes(type);
});

const isCad = computed(() => {
    let type = props.type;
    return ["file", "image"].includes(type);
});

const hasCover = computed(() => {
    let imgUrl = props.imgUrl;
    if (!imgUrl) return false;
    return imgUrl && (isImage || isVideo);
});

const acceptType = computed(() => {
    const resObj = {
        "image": ".jpg, .jpeg, .png",
        "video": ".mp4",
        "pdf": ".pdf",
        "file": ".zip, .rar, .dwg, .dws, .dwt, .dxf"  // * 文件类拓展按需添加，接口也需支持
    };
    return resObj[props.type] || ".png,.jpg,.jpeg,.gif,.bmp,.pdf,.rar,.zip,.mp4,.dwg,.dws,.dwt,.dxf";
});

const formatType = computed(() => {
    const resObj = {
        "image": ["jpg", "jpeg", "png"],
        "video": ["mp4"],
        "pdf": ["pdf"],
        "file": ["zip", "rar", "dwg", "dws", "dwt", "dxf"]
    };
    return resObj[props.type] || ["png", "jpg", "jpeg", "gif", "bmp", "pdf", "rar", "zip", "mp4", "dwg", "dws", "dwt", "dxf"];
});

const dynaicMaxSize = computed(() => {
    const resObj = {
        "image": 40960,   // * 40MB
        "video": 102400,  // * 100MB
        "pdf": 102400,   // * 100MB
        "file": 102400   // * 100MB
    };
    return resObj[props.type] || props.maxSize;
});

</script>

<template>
    <div v-if="show">
        <Upload :accept="acceptType" :format="formatType" :disabled="disabled" :show-upload-list="showList"
            :on-progress="upProgress" :on-error="upFail" :max-size="dynaicMaxSize" :on-success="upSuccess"
            :headers="headers" :action="actionUrl" :on-format-error="formatError" :on-exceeded-size="maxSizeError">
            <div class="file_pics" :class="{ 'noname': !imgName, 'fileup': isFile || isCad, }">
                <Spin v-if="showSpin" fix style="background: #fff">
                    <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
                    <span class="load-txt">上传中</span>
                </Spin>
                <div v-if="hasCover" class="demo-upload-list">
                    <!--image-->
                    <img v-if="imgUrl?.includes('.png') || imgUrl?.includes('.jpg') || imgUrl?.includes('.jpeg') || imgUrl?.includes('.JPG')"
                        :src="imgUrl + '?x-oss-process=image/resize,m_lfit,h_200,w_200'" width="100%" class="imagefit"
                        alt="" />
                    <!--video-->
                    <img v-else-if="imgUrl?.includes('.mp4')"
                        :src="imgUrl + '?x-oss-process=video/snapshot,t_1,f_jpg,w_0,h_0,m_fast'" width="100%"
                        class="imagefit" alt="" />
                    <!--file:pdf,zip...-->
                    <div v-else class="flex-column-center fileshow">
                        <img src="../../assets/icon/uploaded.svg" width="24px" alt="" />
                        <span v-if="fileName" class="file-txt ellipsis">{{ fileName }}</span>
                    </div>
                    <div class="demo-upload-list-cover" @click.stop>
                        <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="watchFile"></Icon>
                        <Icon v-if="canDel" type="ios-trash-outline" size="20" color="#fff" @click.stop="delImg"></Icon>
                    </div>
                </div>
                <div v-else class="flex-column-center-center aim" :class="{ 'disabled': disabled }">
                    <img v-if="isFile || props.type === 'CAD'" src="../../assets/icon/file.svg" width="22px" alt="upload">
                    <img v-else-if="isVideo" src="../../assets/icon/video.svg" width="20px" alt="upload">
                    <img v-else src="@/assets/icon/image.svg" width="36px" alt="upload">
                    <slot></slot>
                </div>
            </div>
            <div v-if="imgName" class="name" @click.stop>{{ required ? "*" : "" }} {{ imgName }}</div>
            <div v-if="imgTips" class="extips" @click.stop>
                <Icon type="md-help-circle" /> {{ imgTips }}
            </div>
        </Upload>
        <Modal width="99" v-model="previewUpload" reset-drag-position draggable sticky :title="imgName || '图片详情'"
            footer-hide class-name="vertical-center-modal">
            <Tooltip content="点击查看源文件" theme="light">
                <video v-if="isVideo" class="preview" controls autoplay :src="imgUrl" @click="toTarget(imgUrl)"></video>
                <img v-else-if="isImage" class="preview" :src="imgUrl" @click="toTarget(imgUrl)" alt="visibleImg">
                <!--                暂时不在内部展示-->
                <!--                <iframe v-if="isPdf" style="max-width: 1000px;width: 80vw;min-height: 75vh" frameborder="no" marginwidth="0" marginheight="0" :src="imgUrl"></iframe>-->
            </Tooltip>
        </Modal>
    </div>
</template>

<style scoped lang="scss">
$wh: 130px;
$border-color-upload: 1px solid $theme-sea-main;

.ellipsis {
    margin: 8px;
    padding: 0 !important;
    display: -webkit-box;
    overflow: hidden;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.file-txt {
    @extend .flex-row-center-center;
    padding: 8px;
    max-width: $wh;
    width: 100%;
    font-size: 12px;
    line-height: 1.2;
    color: $theme-sea-heavy;
    text-align: center;
}

.name {
    @extend .file-txt;
    background: $theme-sea-main;
    border-radius: 0 0 6px 6px;
    border: $border-color-upload;
    border-top: 0;
    border-top: 0;
}

.extips {
    @extend .file-txt;
    display: inline-block;
    text-align: justify;
    font-size: 11px;
    color: #999;
    line-height: 1.5;
}

.file_pics {
    @extend .flex-row-center-center;
    position: relative;
    min-width: $wh;
    width: $wh;
    height: $wh;
    border-radius: 6px 6px 0 0;
    background: rgb(223 244 252 / 20%);
    backdrop-filter: blur(1px);
    overflow: hidden;
    border: $border-color-upload !important;

    &.noname {
        border-radius: 6px;
    }

    //&.fileup {
    //    width: auto;
    //}

    .aim {
        width: 100%;
        height: 100%;
        cursor: pointer;

        &.disabled {
            cursor: not-allowed;
            filter: grayscale(1) opacity(0.5);
        }
    }

    .demo-upload-list {
        @extend .flex-row-center-center;
        position: relative;
        width: 100%;
        height: 100%;
        overflow: hidden;

        .fileshow {
            padding: 0 20px;
        }

        &:hover .demo-upload-list-cover {
            display: flex;
        }
    }

    .demo-upload-list-cover {
        @extend .flex-row-center-around;
        display: none;
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(0, 0, 0, .5);
        z-index: 999;

        i {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            width: 100%;
            cursor: pointer;


            &:hover {
                backdrop-filter: blur(3px);
            }
        }
    }
}

.demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
    color: transparentize($theme-sea-heavy, 0.35);
}

@keyframes ani-demo-spin {
    from {
        transform: rotate(0deg);
    }

    50% {
        transform: rotate(180deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.load-txt {
    font-size: 12px;
    color: transparentize($theme-sea-heavy, 0.35);
}

:deep(.vertical-center-modal) {
    .ivu-modal {
        display: flex;
        justify-content: center;
    }

    .ivu-modal-content {
        width: fit-content !important;
    }

    .ivu-tooltip {
        font-size: 0;
    }

    .ivu-modal-body {
        @extend .flex-row-center-center;
        padding: 0;
        max-width: 100%;
        min-width: 360px;
        min-height: 40vh;
        max-height: 75vh;
        overflow-y: auto;
        overflow-x: hidden;

        .preview {
            max-width: 100%;
            max-height: 75vh;
            cursor: pointer;
        }
    }
}
</style>
