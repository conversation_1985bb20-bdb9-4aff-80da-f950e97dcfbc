<script setup>
import { ref } from 'vue'
let loading = ref(true)

const hide = () => {
    loading.value = false
}

const show = () => {
    loading.value = true
}

// * 暴露函数
defineExpose({ show, hide });
</script>

<template>
    <div>
        <Spin v-if="loading" class="flex-row-center-center" style="margin-bottom: 0.2rem;" />
        <div style="color: #999">{{ loading ? '查询中' : '暂无数据' }}</div>
    </div>
</template>
