<template>
    <div id="inverterCont" class="model_view">
        <div style="margin-bottom: 20px;color: #666;">
            <Icon type="ios-sync" />
            数据更新时间：{{ commObj.data && commObj.data.dataTimeAt.substr(0, 10) }}
        </div>
        <div class="currentWeather">
            <div style="width: 400px;">
                <p style="font-weight: bolder;">天气信息：{{ time }}</p>
                <div style="display: flex; align-items: center;">
                    <p style="width: 150px;">
                        天气：
                        <img :src=currentWeather.weather1.day_weather_pic
                            style="width: 20px; height: 20px; margin: 0 5px;">
                        <span>{{ currentWeather.weather1.day_weather }}</span>
                    </p>
                    <p>
                        日出日落：
                        <span>{{ currentWeather.weather1.sun_begin_end }}</span>
                    </p>
                </div>
                <div style="display: flex; align-items: center;">
                    <p style="width: 150px;">
                        温度：
                        <span>{{ currentWeather.weather1.night_air_temperature }}</span>
                        <span>~</span>
                        <span>{{ currentWeather.weather1.day_air_temperature }}</span>℃
                    </p>
                    <p>
                        风向/风速：
                        <span>{{ currentWeather.weather1.night_wind_power }}</span>
                    </p>
                </div>
            </div>
            <div style="display: flex; margin-left: 30px;">
                <div v-for="(weather, index) in [currentWeather.weather2, currentWeather.weather3, currentWeather.weather4]"
                    :key="index" style="border-left: 1px solid #ccc; padding: 0 30px; margin: 0 30px;">
                    <p style="font-weight: bolder;">{{ formattedDay(weather.day) }}</p>
                    <p>
                        <img :src="weather.day_weather_pic" style="width: 20px; height: 20px; margin: 0 5px;">
                        <span>{{ weather.day_weather }}</span>
                    </p>
                    <p>
                        <span>{{ weather.night_air_temperature }}</span>
                        <span>~</span>
                        <span>{{ weather.day_air_temperature }}</span>℃
                    </p>
                </div>
            </div>
        </div>
        <div class="view_title sub">基本信息</div>
        <div class="flex-row-center-between" style="flex-wrap: wrap;">
            <table class="form_table" border="0" cellspacing="0" cellpadding="0" width="100%">
                <tr>
                    <td align="center" class="flabel">SN</td>
                    <td align="left" width="200">{{ commObj.data.inveterSn || '-' }}</td>
                    <td align="center" class="flabel">状态</td>
                    <td align="left" width="200">{{ inveterStateList[commObj.data.inveterState] || '-' }}</td>
                    <td align="center" class="flabel">型号</td>
                    <td align="left" width="200">-</td>
                </tr>
                <tr>
                    <td align="center" class="flabel">额定功率</td>
                    <td align="left" width="200">{{ commObj.data.power || '0' }} kWp</td>
                    <td align="center" class="flabel">所属采集器</td>
                    <td align="left" width="200">{{ commObj.data.collectorSn || '-' }}</td>
                    <td align="center" class="flabel">国家标准</td>
                    <td align="left" width="200">{{ commObj.inveter.nationalStandardstr || '-' }}</td>
                </tr>
                <tr>
                    <td align="center" class="flabel">状态</td>
                    <td align="left" width="200">{{ commObj.data.nowStatus || '-' }}</td>
                    <td align="center" class="flabel">启用时间</td>
                    <td align="left" width="200">{{ commObj.data.startAt && commObj.data.startAt.replace('T', ' ') ||
                '-' }}</td>
                    <td align="center" class="flabel">停用时间</td>
                    <td align="left" width="200">{{ commObj.data.stopAt && commObj.data.stopAt.replace('T', ' ') || '-'
                        }}</td>
                </tr>
                <tr>
                    <td align="center" class="flabel">逆变器版本</td>
                    <td align="left" width="200" colspan="5">-</td>
                </tr>
            </table>

            <table class="form_table" border="0" cellspacing="0" cellpadding="0" width="40%">
                <tr>
                    <td align="center" width="50%">
                        <label class="flabel">实时功率</label>
                        <br />
                        {{ commObj.data.pac || '0' }} kWp
                    </td>
                    <td align="center" width="50%">
                        <label class="flabel">当日发电</label>
                        <br />
                        {{ commObj.data.elecDay || '0' }} kWh
                    </td>
                </tr>
                <tr>
                    <td align="center" width="50%">
                        <label class="flabel">满发小时数</label>
                        <br />
                        {{ commObj.data.fullHour || '0' }} h
                    </td>
                    <td align="center" width="50%">
                        <label class="flabel">当月发电</label>
                        <br />
                        {{ commObj.data.elecMonth || '0' }} kWh
                    </td>
                </tr>
                <tr>
                    <td align="center" width="50%">
                        <label class="flabel">报警信息</label>
                        <br />
                        {{ warnRes }}
                    </td>
                    <td align="center" width="50%">
                        <label class="flabel">当年发电</label>
                        <br />
                        {{ commObj.data.elecYear || '0' }} kWh
                    </td>
                </tr>
                <tr>
                    <td align="center" width="50%">
                        <label class="flabel">IGBT温度</label>
                        <br />
                        {{ commObj.inveter.inverterTemperature || '0' }} ℃
                    </td>
                    <td align="center" width="50%">
                        <label class="flabel">累计发电</label>
                        <br />
                        {{ commObj.data.elecTotal || '0' }} kWh
                    </td>
                </tr>
            </table>

            <div class="flex-row-start-between pvbox">
                <div style="width: 50%;padding: 0 10px;">
                    <table class="form_table_pv_th" border="0" cellspacing="0" cellpadding="0" width="100%">
                        <tr>
                            <th align="left" colspan="4">直流信息</th>
                        </tr>
                        <tr>
                            <td align="center" width="25%"></td>
                            <td align="center" width="25%">电压(V)</td>
                            <td align="center" width="25%">电流(A)</td>
                            <td align="center" width="25%">功率(W)</td>
                        </tr>
                    </table>

                    <div class="inbox">
                        <table class="form_table_pv" border="0" cellspacing="0" cellpadding="0" width="100%">
                            <tr v-for="(v, i) in 32" :key="i" v-show="commObj.inveter[`upv` + `${i + 1}`]">
                                <td align="center" width="25%">PV{{ i + 1 }}</td>
                                <td align="center" width="25%">{{ commObj.inveter[`upv` + `${i + 1}`] }}</td>
                                <td align="center" width="25%">{{ commObj.inveter[`ipv` + `${i + 1}`] }}</td>
                                <td align="center" width="25%">{{ parseFloat(commObj.inveter[`upv` + `${i + 1}`] *
                commObj.inveter[`ipv` + `${i + 1}`]).toFixed(2) }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                <div style="width: 50%;padding: 0 10px;">
                    <table class="form_table_pv_th" border="0" cellspacing="0" cellpadding="0" width="100%">
                        <tr>
                            <th align="left" colspan="4">交流信息</th>
                        </tr>
                        <tr>
                            <td align="center" width="25%"></td>
                            <td align="center" width="25%">电压(V)</td>
                            <td align="center" width="25%">电流(A)</td>
                            <td align="center" width="25%">频率(W)</td>
                        </tr>
                    </table>

                    <div class="inbox">
                        <table class="form_table_pv" border="0" cellspacing="0" cellpadding="0" width="100%">
                            <tr v-for="(v, i) in 3" :key="i" v-show="commObj.inveter[`uac` + `${i + 1}`]">
                                <td align="center" v-if="i === 0">U</td>
                                <td align="center" v-if="i === 1">V</td>
                                <td align="center" v-if="i === 2">W</td>
                                <td align="center">{{ commObj.inveter[`uac` + `${i + 1}`] }}</td>
                                <td align="center">{{ commObj.inveter[`iac` + `${i + 1}`] }}</td>
                                <td align="center">{{ commObj.inveter.fac }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="view_title sub">逆变器图表</div>
        <div class="chartbox">
            <div class="flex-row-center-between" style="padding: 0 100px;">
                <ButtonGroup style="margin-right: 50px;">
                    <Button :type="btn === 0 ? 'primary' : 'default'" @click="getDayData">日</Button>
                    <Button :type="btn === 1 ? 'primary' : 'default'" @click="getMonthData">月</Button>
                    <Button :type="btn === 2 ? 'primary' : 'default'" @click="getYearData">年</Button>
                    <Button :type="btn === 3 ? 'primary' : 'default'" @click="getTotalData">累计</Button>
                </ButtonGroup>
                <div class="historicalWeather">
                    <span style="margin-right: 5px;">{{ historicalWeather.area }} </span>
                    <img :src=historicalWeather.weatherUrl style="width: 20px; height: 20px; margin: 0 5px;">
                    <span>{{ historicalWeather.weather }}</span>
                    <span style="margin-left: 5px;">{{ historicalWeather.max_temperature + '℃' }}</span>
                    <span style="margin: 0 5px">~</span>
                    <span>{{ historicalWeather.min_temperature + '℃' }}</span>
                </div>
                <DatePicker v-if="btn === 0" type="date" :value="dayTime" :options="options" placeholder="选择日期"
                    style="width: 200px" @on-change="seleDate" />
                <DatePicker v-if="btn === 1" type="month" :value="monthTime" :options="options" placeholder="选择月份"
                    style="width: 200px" @on-change="seleDate" />
                <DatePicker v-if="btn === 2" type="year" :value="yearTime" :options="options" placeholder="选择年"
                    style="width: 200px" @on-change="seleDate" />
            </div>
            <div id="lineChart" style="width: 100%;height: 500px;"></div>
        </div>
    </div>
</template>
<script>
import * as echarts from 'echarts';
import _data from '@/views/apv/_edata.js'; // 数据字典
import API from '@/api/apv';
import _API from '@/api/osp';
export default {
    data() {
        return {
            time: '',
            bindStatusList: _data.bindStatusList,
            collectorStateList: _data.collectorStateList,
            elecStatusList: _data.elecStatusList,
            inveterStateList: _data.inveterStateList,
            dayTime: '',
            monthTime: '',
            yearTime: '',
            dayData: {},
            monthData: {},
            yearData: {},
            totalData: {},
            options: {},
            currentWeather: {},
            historicalWeather: {},
            btn: 0,
            chartObj: null
        };
    },

    props: {
        commObj: Object
    },

    created() {
        let mln = this.commObj.monthList.length;
        this.dayTime = this.commObj.dayList[0];
        this.monthTime = this.commObj.monthList[mln - 1];
        this.yearTime = this.commObj.yearList[0];
        this.currentWeather = this.commObj.weatherResponse
    },

    mounted() {
        this.dateOption();
        this.getDayData();
        this.getHistoryWeather()
        this.getTime()
    },

    unmounted() {
        if (this.chartObj !== null && this.chartObj !== '' && this.chartObj !== undefined) {
            this.chartObj.dispose(); // 销毁
        }
    },

    computed: {
        warnRes() {
            let inveter = this.commObj.inveter
            let wn
            if (inveter.dataSource === 'AISWEI') {
                if (inveter.wn0 !== 0 && inveter.wn0 !== 30) {
                    wn = "警告码:" + inveter.wn0
                } else {
                    wn = "警告码: --"
                }
                if (inveter.er !== 0) {
                    return "故障码:" + inveter.er + "，" + wn
                } else {
                    return "故障码: --" + "，" + wn
                }
            } else {
                if (inveter.erMsg && inveter.state === 3) {
                    return "警告码:" + inveter.erMsg
                } else {
                    return "警告码: --"
                }
            }
        },
        formattedDay() {
            return (date) => {
                let year = date.slice(0, 4);
                let month = date.slice(4, 6).replace(/^0+/, '');
                let dayOfMonth = date.slice(6, 8);
                return `${year}-${month}-${dayOfMonth}`;
            };
        }
    },

    methods: {
        getTime() {
            let currentDate = new Date();
            let year = currentDate.getFullYear();
            let month = currentDate.getMonth() + 1; 
            let day = currentDate.getDate();
            this.time = `${year}-${month}-${day}`
            console.log(this.time);
        },
        seleDate(e) {
            // console.log(e);
            if (this.btn === 0) {
                this.dayTime = e;
                this.getDayData();
                this.getHistoryWeather()
            } else if (this.btn === 1) {
                this.monthTime = e;
                this.getMonthData();
            } else if (this.btn === 2) {
                this.yearTime = e;
                this.getYearData();
            }
            this.dateOption();
        },

        dateOption() {
            let that = this;
            let dln = this.commObj.dayList.length;
            let dateStart = new Date(this.commObj.dayList[dln - 1]);
            let dateEnd = new Date(this.commObj.dayList[0]);

            let mln = this.commObj.monthList.length;
            let monthStart = new Date(this.commObj.monthList[0]);
            let monthEnd = new Date(this.commObj.monthList[mln - 1]);

            let yln = this.commObj.yearList.length;
            let yearStart = new Date(this.commObj.yearList[0]);
            let yearEnd = new Date(this.commObj.yearList[yln - 1]);

            this.options = {
                disabledDate(date) {
                    if (that.btn === 0) {
                        return date && (date.valueOf() < dateStart - 86400000 || date.valueOf() > dateEnd);
                    } else if (that.btn === 1) {
                        return date && (date.valueOf() < monthStart - 86400000 || date.valueOf() > monthEnd);
                    } else if (that.btn === 2) {
                        return date && (date.valueOf() < yearStart - 86400000 || date.valueOf() > yearEnd);
                    }
                }
            };
        },

        getDayData() {
            let datas = {
                dataTime: this.dayTime,
                inveterSn: this.commObj.data.inveterSn
            };
            API.dayChart(datas).then(res => {
                if (res.data.success) {
                    this.dayData = res.data.result;
                    this.btn = 0;
                    this.chartRender(res.data.result);
                } else {
                    this.$Message.error(res.error);
                }
            });
        },
        getHistoryWeather() {
            let params = {
                givenTime: this.dayTime,
                stationCode: this.commObj.data.stationCode
            }
            _API.historyWeather(params).then(res => {
                if (res.data.success) {
                    this.historicalWeather = res.data.result;
                } else {
                    this.$Message.error(res.error);
                }
            })
        },
        getMonthData() {
            let datas = {
                dataTime: this.monthTime,
                inveterSn: this.commObj.data.inveterSn
            };
            API.monthChart(datas).then(res => {
                if (res.data.success) {
                    this.monthData = res.data.result;
                    this.btn = 1;
                    this.chartRenderBar(res.data.result);
                } else {
                    this.$Message.error(res.error);
                }
            });
        },

        getYearData() {
            let datas = {
                year: this.yearTime,
                inveterSn: this.commObj.data.inveterSn
            };
            API.yearChart(datas).then(res => {
                if (res.data.success) {
                    this.yearData = res.data.result;
                    this.btn = 2;
                    this.chartRenderBar(res.data.result);
                } else {
                    this.$Message.error(res.error);
                }
            });
        },

        getTotalData() {
            let datas = {
                inveterSn: this.commObj.data.inveterSn
            };
            API.totalChart(datas).then(res => {
                if (res.data.success) {
                    this.totalData = res.data.result;
                    this.btn = 3;
                    this.chartRenderBar(res.data.result);
                } else {
                    this.$Message.error(res.error);
                }
            });
        },

        chartRender(res) {
            if (this.chartObj !== null && this.chartObj !== '' && this.chartObj !== undefined) {
                this.chartObj.dispose(); // 销毁
            }
            var chartDom = document.getElementById('lineChart');
            this.chartObj = echarts.init(chartDom);
            var option;

            option = {
                xAxis: {
                    type: 'category',
                    data: res.hourList
                },
                yAxis: {
                    name: '发电量（kwh）',
                    type: 'value'
                },
                series: [
                    {
                        data: res.dayElecList,
                        type: 'line'
                    }
                ]
            };

            option && this.chartObj.setOption(option);
        },

        chartRenderBar(res) {
            if (this.chartObj !== null && this.chartObj !== '' && this.chartObj !== undefined) {
                this.chartObj.dispose(); // 销毁
            }
            var chartDom = document.getElementById('lineChart');
            this.chartObj = echarts.init(chartDom);
            var option;

            option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '8%',
                    right: '8%',
                    bottom: '8%',
                    containLabel: true
                },
                xAxis: [
                    {
                        type: 'category',
                        data: res.dayList || res.monthList || res.yearList,
                        axisTick: {
                            alignWithLabel: true
                        }
                    }
                ],
                yAxis: [
                    {
                        name: '发电量（kWh）',
                        type: 'value'
                    }
                ],
                series: [
                    {
                        name: '发电量（kWh）',
                        type: 'bar',
                        barWidth: '60%',
                        barMaxWidth: '50',
                        data: res.dayELecList || res.monthElecList || res.yearElecList
                    }
                ]
            };

            option && this.chartObj.setOption(option);
        }
    }
};
</script>

<style lang="scss" scoped>
@import '@/style/_cus_list.scss';

.form_table {
    margin-bottom: 20px;
    page-break-after: always;
    background: #f6f7f8;

    td {
        padding: 10px;
        height: 50px;
        border: 1px solid $border-color-1 !important;
        font-size: 12px;
        line-height: 1.8;
    }

    .flabel {
        color: #888;
        font-weight: bolder;
        // background: #fff;
    }
}

.pvbox {
    position: relative;
    margin-bottom: 20px;
    width: 58%;
    height: 257px;
    background: #f6f7f8;
    border: 1px solid $border-color-1 !important;

    .inbox {
        width: 100%;
        height: 180px;
        overflow-y: auto;
    }

    &::after {
        position: absolute;
        top: 50%;
        left: 50%;
        content: '';
        width: 1px;
        height: 65%;
        background: $border-color-1;
        transform: translateY(-50%);
    }
}

.form_table_pv,
.form_table_pv_th {
    page-break-after: always;

    th {
        padding: 10px;
    }

    td {
        padding: 5px 10px;
        font-size: 12px;
        line-height: 1.8;
    }
}

.form_table_pv {
    tr:nth-child(even) {
        background: #eee;
    }
}

.view_title {
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    line-height: 2.4;
    font-size: 16px;
    font-weight: bolder;
    border-bottom: 1px solid #f5f7f9;

    @extend.sub-dot-custom;

    &.sub {
        margin-bottom: 20px;
        font-size: 14px;
    }
}

.view_label {
    margin-bottom: 20px;
    font-size: 12px;
}

.view_table td {
    border: none;
    vertical-align: text-top;

    img {
        cursor: pointer;
        background: #fafafa;
        border-radius: 8px;
        vertical-align: text-top;
    }
}

.historicalWeather {
    height: 32px;
    display: flex;
    align-content: center;
}

.currentWeather {
    width: 100%;
    margin: 10px 20px;
    display: flex;
    align-content: center;
}
</style>
