<script>
import menus from "@/store/modules/index/_type_menus"

export default {
    name: "sliderMenus",
    data() {
        return {
            menuList: [],
            userArr: localStorage.getItem("userArr")
        };
    },

    computed: {
        appSubName() {
            return process.env.VUE_APP_SUBNAME
        },
    },

    created() {
        this.getMenuList()
    },

    methods: {
        showChildFun(index, item) {
            // 2023-12-12 适配单一级目录选中
            if (item.isSingle) {
                this.$router.push({path: item.path}).catch(err => err)
                // 解决 vue-router 3.0 以上版本的重复路由报错问题
            } else {
                this.menuList[index].showChild = !this.menuList[index].showChild;
            }
        },

        async getMenuList() {
            let arr = menus; // 根据角色判断展示哪个 tab 跟 子 tab
            await this.changeType(arr)
            this.$nextTick(async () => {
                if (arr instanceof Array) {
                    this.menuList = arr
                }
            })
        },

        // 优化递归函数，根据权限判断菜单显隐和展开 AT 2022-11-02
        async changeType(arr, father) {
            if (arr instanceof Array) {
                arr.map(e => {
                    e.path = e.path?.replace(`/${this.appSubName}`, '')
                    if (father && this.$route.path.includes(e.path)) {
                        father.showChild = true
                    }
                    if (![0, 1].includes(e.type)) {
                        //子账号设置哪个级别展示哪个级别
                        //员工账号是根据不同的角色和等级展示的不用的菜单
                        const qxArr = e.type && e.type?.split(",") || '';
                        qxArr.length && qxArr.map(qx => {
                            if (this.userArr.includes(qx)) e.type = 1
                        });
                    }

                    this.changeType(e.child, e)
                })
            }
        },

        jumpTo(path) {
            this.$router.push({
                path: path
            }).catch(err => err)
        },

        // * 侧边导航选中逻辑
        currentSign(path, child = false) {
            const routePath = this.$route.path
            return child ? routePath.includes(path) : routePath === path
        }
    }
}
</script>

<template>
    <ul class="left_ul">
        <li v-for="(item, index) in menuList" :key="index">
            <p @click="showChildFun(index, item)" class="li_p"
               :class="{ 'colors-single': currentSign(item.path) }" v-if="item.type === 1">
                <span>{{ item.name }}</span>
                <img
                    v-if="item.child.length"
                    class="left_img"
                    :src="item.showChild ? require('@/assets/img/top.png') : require('@/assets/img/bottom.png')"
                    :alt="item.showChild ? 'Top' : 'Bottom'"
                />
            </p>
            <div class="li_div" v-show="item.showChild">
                <p v-for="(itm, inx) in item.child.filter(e => e.type === 1)" :key="inx"
                   @click="jumpTo(itm.path.replace('/zch/',''))">
                       <span :class="{ colors: currentSign(itm.path,itm.child === 1) }">
                           {{ itm.name }}{{ item.subColor }}
                           <Tag v-if="itm.subName"
                                :type="currentSign(itm.path,itm.child === 1) ? null : 'border'"
                                :color="itm.subColor || 'cyan'" style="margin-left:10px;height: auto;">{{
                                   itm.subName
                               }}</Tag>
                       </span>
                </p>
            </div>
        </li>
    </ul>
</template>

<style scoped lang="scss">
.left_ul {
    padding: 15px 15px;
    min-width: 210px;
    width: 100%;
    height: auto;
    overflow: hidden;
    user-select: none;
}

.left_ul > li {
    width: 100%;
    height: auto;
    overflow: hidden;
}

.li_p {
    text-align: left;
    padding-left: 10px;
    width: 100%;
    overflow: hidden;
    cursor: pointer;
}

.li_p > span {
    font-size: 15px;
    font-weight: bolder;
    color: #333;
    line-height: 50px;
    vertical-align: middle;
    user-select: none;
}

.li_div {
    padding: 10px 0;
    width: 100%;
    height: auto;
    overflow: hidden;
}

.li_div > p {
    color: $theme-sea-light;
    font-size: 14px;
    overflow: hidden;
    text-align: left;
    cursor: pointer;
    user-select: none;

    span {
        display: inline-block;
        padding: 12px 15px;
        border-radius: 6px;
        transition: all 500ms;
        user-select: none;
    }
}

.colors {
    color: $theme-sea-heavy !important;
    background: $theme-sea-main;
    font-weight: bold;
}

.colors-single {
    color: #01879e !important;
    background: $theme-sea-main;
    border-radius: 6px;
}

.left_img {
    float: right;
    width: 12px;
    margin: 18px 10px;
}

.left_img1 {
    width: 16px;
    margin-right: 10px;
    vertical-align: middle;
}
</style>
