<template>
    <div class="header">
        <div class="head_son_three">
            <div class="head_div_three app-name">{{appName}}</div>
            <div class="head_div_four">
                <p class="head_p_five">
                    <span>{{ userName }}</span>
                </p>
                <Dropdown @on-click="selectMenu" transfer class="head_p_five dropClass">
                    <div class="flex-row-center-center">
                        <svg t="1722057675170" class="icon" viewBox="0 0 1032 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3917" width="30" height="30"><path d="M0.473 508.972c0 280.987 229.945 508.779 513.584 508.779 283.648 0 513.575-227.792 513.575-508.78C1027.641 227.977 797.714 0.176 514.066 0.176 230.418 0.184 0.48 227.975 0.48 508.972z" fill="#EDEEF5" p-id="3918"></path><path d="M669.486 413.573c96.291-129.706 77.71 85.351-11.833 93.718-141.067 46.028 11.833-93.718 11.833-93.718z" fill="#EFE7E6" p-id="3919"></path><path d="M389.032 511.475c4.228 15.903 35.482 81.176-34.623 183.27-70.105 102.085 161.337 157.32 161.337 157.32s219.626-73.641 231.45-109.63c72.643-61.09-135.15 0.84-95.45-224.265-94.611-59.41-262.705-6.695-262.705-6.695z" fill="#EDE7E6" p-id="3920"></path><path d="M514.057 1017.751a514.24 514.24 0 0 0 440.407-246.985l-0.035-0.71c-25.906-39.051-203.863-98.198-273.688-98.198-138.529 239.896-325.492 41.292-322.113 2.24-96.747-6.581-213.334 62.63-278.03 107.608a514.223 514.223 0 0 0 433.459 236.045z" fill="#586ABA" p-id="3921"></path><path d="M394.538 550.807c5.697 20.086 153.162 156.488 286.78 123.011-39.112-47.699-25.258-122.18-22.816-166.527 13.846-30.125-259.903 16.743-263.964 43.516z" fill="#E3DCDB" p-id="3922"></path><path d="M532.638 167.55c54.91 10.04 131.772 15.055 141.915 91.207 107.275 443.514-372.517 505.436-310.858 40.172 31.254-164.855 168.943-131.378 168.943-131.378z" fill="#F7EDEB" p-id="3923"></path><path d="M416.812 227.8c29.74 20.087 89.788 85.912 235.362 53.564 12.455 47.97 6.223 47.97 39.927 98.733 24.638 8.367 96.011-231.792-113.007-264.429-244.692-37.66-276.13 175.726-213.255 252.71 15.29-15.903 3.396-102.926 50.973-140.586z" fill="#586ABA" p-id="3924"></path></svg>
                        &nbsp;<Icon color="#fff" size="18" type="ios-arrow-down"></Icon>
                    </div>
                    <DropdownMenu slot="list">
                        <DropdownItem v-for="item in dropdownItems" :key="item.name" :name="item.name" :divided="item.divided">
                            <Icon :type="item.icon" /> {{ item.label }}
                        </DropdownItem>
                    </DropdownMenu>
                </Dropdown>
            </div>
        </div>
    </div>
</template>
<script>
import {
    getUserInfo,
    signOut,
} from '@/api';
import {
    mapState
} from 'vuex';
import { storageClear } from "@/utils/common";

export default {
    data() {
        return {
            userName: '',
            userInfo: '',
            mobile: '', // 手机号
            password: '', // 密码
            dropdownItems: [
                { name: "2", icon: "md-log-out", label: "退出", divided: false }
            ]
        };
    },

    computed: {
        appName() {
            return process.env.VUE_APP_NAME;
        }
    },

    mounted() {
        this.getInit();
    },

    methods: {
        selectMenu(name) {
            // 操作映射表
            const operations = {
                '2': () => this.outLogin(),
            };

            // 执行对应的操作，如果找不到对应的操作，则发出警告
            const operation = operations[name];
            if (operation) {
                operation();
            } else {
                console.warn('Unknown menu option:', name);
            }
        },

        // * 退出
        outLogin() {
            storageClear()
            this.$Message.success('退出成功');
            this.$router.push({
                path: '/login'
            });
        },

        // * 菜单处理初始化准备
        async getInit() {
            await this.getName()
        },

        // * 获取姓名和角色信息
        getName() {
            return new Promise((resolve, reject) => {
                getUserInfo().then(res => {
                    this.userName = res.data.username;
                }).catch(() => {
                    reject([])
                })
            })
        }
    }
};
</script>
<style lang="scss" scoped>
@import '@/style/header.scss';

.app-name {
    font-size: 18px;
    color: #fff;
    font-weight: bolder;
}

.dropClass {
    margin-left: 20px;
    cursor: pointer;
    font-size: 14px;
    line-height: 68px;
}

.skuconten_span {
    display: inline-block;
    width: 100px;
    color: #717171;
    font-size: 12px;
    line-height: 32px;
    float: left;
}

.express_p {
    margin-bottom: 20px;
}

.pline {
    padding: 8px 0;

    * {
        font-family: "JetBrains Mono", monospace;
    }
}
</style>
