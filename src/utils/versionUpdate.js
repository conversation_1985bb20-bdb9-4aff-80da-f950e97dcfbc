import axios from "axios";
import {throttle} from "lodash";
import { vensionCard } from "@/utils/versionCard"

export const checkForNewVersion = throttle((routerTo) => {
    let url = `//${window.location.host}/mch/version.json?t=${new Date().getTime()}`;
    axios.get(url).then(res => {
        if (res.status === 200) {
            const lastInfo = res.data[0];
            if (!lastInfo) {
                return;
            }

            let { version, description, routerKey, timestamp } = lastInfo
            let localVersion = JSON.parse(localStorage.getItem("v"))?.version;
            if (version && localVersion && compareVersions(version, localVersion)) {
                // 指定路由提示，未设置默认全局提示
                if (!routerKey || routerTo.path?.includes(routerKey)) {
                    localStorage.setItem("v", JSON.stringify(lastInfo));
                    vensionCard({ version, description, timestamp});
                }
            } else {
                localStorage.setItem("v", JSON.stringify(lastInfo));
            }
        }
    }).catch(() => {})
}, 1000*60*5,{ 'trailing': false });

function compareVersions(v1, v2) {
    const parts1 = v1.split('.').map(Number);
    const parts2 = v2.split('.').map(Number);

    for (let i = 0; i < Math.max(parts1.length, parts2.length); i++) {
        const num1 = i < parts1.length ? parts1[i] : 0;
        const num2 = i < parts2.length ? parts2[i] : 0;

        if (num1 > num2) return true;
        if (num1 < num2) return false;
    }

    return false;
}
