import _ from 'lodash';

export default function install(Vue) {
    Vue.directive('trim', {
        bind(el, binding, vnode) {
            let isComposing = false;
            // 使用lodash的debounce包装处理函数
            const trimAndAssign = _.debounce((e) => {
                e.target.value = e.target.value.replace(/\s+/g, '');
                vnode.context[binding.expression] = e.target.value;
            }, 500);

            // 绑定到input事件以实现实时防抖处理
            // * el.addEventListener('input', trimAndAssign);
            el.addEventListener('compositionstart', () => {
                isComposing = true;
            });

            el.addEventListener('compositionend', (e) => {
                isComposing = false;
                trimAndAssign(e); // 在IME输入结束后去空格
            });

            // 绑定到input事件，实时更新缓存值并在非中文输入时去空格
            el.addEventListener('input', (e) => {
                if (!isComposing) {
                    trimAndAssign(e);
                } else {
                    // * console.log('中文输入中');// 中文输入时仅缓存值
                }
            });


            // 保存防抖函数引用，以便在unbind时清理
            vnode.context._trimListener = trimAndAssign;
        },
        unbind(el, binding, vnode) {
            // 移除事件监听器，同时清理防抖定时器（由lodash的debounce自动管理，此处无需显式清除）
            el.removeEventListener('compositionstart', vnode.context._compositionStartHandler);
            el.removeEventListener('compositionend', vnode.context._compositionEndHandler);
            el.removeEventListener('input', vnode.context._trimListener);
            delete vnode.context._trimListener; // 清理引用
        },
    });
}
