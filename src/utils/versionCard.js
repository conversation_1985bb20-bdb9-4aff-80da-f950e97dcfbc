import { Modal } from "view-design";
import versionCard from "@/components/public/versionCard.vue";

export const vensionCard = ({ version, description, timestamp }, hideFooter = false) => {
    const props = { version, description, timestamp }
    if (!version) {
        version = '暂无版本'
    }
    if (hideFooter) {
        Modal.info({
            render: (h) => h(versionCard, { props }, []),
            width: "500px",
        });
    } else {
        Modal.confirm({
            render: (h) => h(versionCard, { props }, []),
            width: "500px",
            okText: "立即更新",
            cancelText: "稍后处理",
            onOk: () => location.reload(),
        });
    }

    let element = document.getElementsByClassName("ivu-modal-wrap")[0];
    let eleconf = document.getElementsByClassName("ivu-modal-confirm")[0];
    // element && Object.assign(element.style, {
    //     "display": "flex",
    //     "align-items": "center",
    //     "justify-content": "center"
    // });
    eleconf && Object.assign(eleconf.style, {
        "padding": 0
    });
    // element.childNodes[0] && Object.assign(element.childNodes[0].style, {
    //     "top": "0"
    // });
};
