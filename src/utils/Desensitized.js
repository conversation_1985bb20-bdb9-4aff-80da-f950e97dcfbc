/**
 * 脱敏相关操作
 */
export class Desensitized {

    static phoneReg = /^(13[0-9]|14[********]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{4}(\d{4})$/; // 手机号正则校验
    static idNoReg = /(\d{6})(\d*)(\w{3})/; // 简易身份证号码正则
    //   static idNoReg = /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[X])$)$/; 
    static addrReg = /(.{9})(.*)/; // 地址正则
    static bankCardReg = /^([1-9]{1})(\d{15}|\d{18})$/; // 银行卡号正则
    static emailReg = /^([A-Za-z0-9_\-.])+@([A-Za-z0-9_\-.])+\.([A-Za-z]{2,4})$/; //邮箱正则
    static nameReg = /^[\u4e00-\u9fa5]{2,20}$/; // 验证中文名的正则

    /**
     * 名字脱敏 保留首位
     * @param fullName
     * @returns {string}
     */
    static desensitizedName(fullName) {
        if (!fullName) {
            return "";
        }

        const length = fullName.length;

        if (length === 2) {
            // 两个字只脱敏第二个字
            return fullName.charAt(0) + '*';
        } else if (length === 3) {
            // 三个字脱敏中间的字
            return fullName.charAt(0) + '*' + fullName.charAt(2);
        } else {
            // 对于其他情况（例如四个及以上字）
            // 保留首位和最后一位，其他脱敏
            let leftStr = fullName.charAt(0);
            let rightStr = fullName.charAt(length - 1);
            let midStr = '*'.repeat(length - 2); // 中间脱敏部分
            return leftStr + midStr + rightStr;
        }
    }

    /**
     * 脱敏公用
     * @param str 脱敏字符串
     * @param begin 起始保留长度，从0开始
     * @param end 结束保留长度，到str.length结束
     * @returns {string}
     */
    static desensitizedCommon(str, begin, end) {
        if (!str && (begin + end) >= str.length) {
            return "";
        }

        let leftStr = str.substring(0, begin);
        let rightStr = str.substring(str.length - end, str.length);

        let strCon = ''
        for (let i = 0; i < str.length - end - begin; i++) {
            strCon += '*';
        }
        return leftStr + strCon + rightStr;
    }

    /**
     * 手机号脱敏
     * @param str
     * @returns {string|*|string}
     */
    static desensitizedPhone(str) {
        if (!str) {
            return "";
        }
        return str.replace(this.phoneReg, '$1****$2');
    }

    /**
     * 身份证号脱敏
     * @param str
     * @returns {string|*|string}
     */
    static desensitizedIdNo(str) {
        if (!str) {
            return "";
        }
        if (this.idNoReg.test(str)) {
            let text1 = RegExp.$1;
            let text3 = RegExp.$3;
            let text2 = RegExp.$2.replace(/./g, "*");
            return text1 + text2 + text3;
        }
        return str;
    }

    /**
     * 地址脱敏
     * @param str
     * @returns {string|*|string}
     */
    static desensitizedAddr(str) {
        if (!str) {
            return "";
        }
        if (this.addrReg.test(str)) {
            let text1 = RegExp.$1;
            let text2 = RegExp.$2.replace(/./g, "*");
            return text1 + text2;
        }
        return str;
    }

    /**
     * 银行卡号脱敏
     */
    static desensitizedBankCard(str) {
        if (!str) {
            return "";
        }
        if (this.bankCardReg.test(str)) {
            return str.replace(/^(.{4})(?:\d+)(.{4})$/, "$1 **** **** $2")
        }
        return str;
    }

    /**
     * 邮箱脱敏
     */
    static desensitizedEmail(str) {
        if (!str) {
            return "";
        }
        if (this.emailReg.test(str)) {
            let avg;
            let splitted;
            let email1;
            let email2;
            splitted = str.split('@');
            email1 = splitted[0];
            avg = email1.length / 2;
            email1 = email1.substring(0, email1.length - avg);
            email2 = splitted[1];
            return email1 + '***@' + email2; // 输出为81226***@qq.com
        }
        return str;
    }

    /**
     * 判断当前字段是身份证号、手机号、地址还是其他类型
     */
    static judgeType(str) {
        if (!str) {
            return "";
        }
        if (this.nameReg.test(str)) {
            return this.desensitizedName(str);
        } else if (this.phoneReg.test(str)) {
            return this.desensitizedPhone(str);
        } else if (this.bankCardReg.test(str)) {
            return this.desensitizedBankCard(str);
        } else if (this.emailReg.test(str)) {
            return this.desensitizedEmail(str);
        } else if (this.idNoReg.test(str)) {
            return this.desensitizedIdNo(str);
        } else if (this.addrReg.test(str)) {
            // return this.desensitizedAddr(str);
            return str;
        }
        return str;
    }
}
