import { Message, Notice } from "view-design";
/*
 * @Author: lny
 * @FilePath: /src/utils/index.js
 * @Description: 公共方法组件（2023）
 * @remark: null
 */

// 封装提示语方法
export const commonNotice = ({ title = "提示", desc = "", render, duration = 0, name, onClose = () => {}, type = "open" }) => {
  const config = {
    title,
    desc,
    duration,
    onClose,
  };
  render && Object.assign(config, { render });
  name && Object.assign(config, { name });
  Notice[type](config);
};

//封装导出方法
export const downloadFile = (res, fileName) => {
  let binaryData = [];
  let link = document.createElement("a");
  binaryData.push(res.data);
  link.style.display = "none";
  link.href = window.URL.createObjectURL(new Blob(binaryData));
  link.setAttribute("download", `${fileName}.xlsx`);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 封装js计算结果保留方法 num: 处理数字 power: 保留位数 force：是否强制保留位数  round：取整类型(1：四舍五入, 2: 向上取整, 3: 向下取整)
// 输出结果为字符串类型
export const preserveResult = (num, power = 0, force = false, round = 1) => {
    let f = parseFloat(num);
    if (isNaN(f)) {
        return false;
    } else {
        let t = 0
        switch (round) {
            case 1:
                t = Math.round(num * Math.pow(10, power)) / Math.pow(10, power); // 四舍五入
                break;
            case 2:
                t = Math.ceil(num * Math.pow(10, power)) / Math.pow(10, power); // 向上取整
                break;
            case 3:
                t = Math.floor(num * Math.pow(10, power)) / Math.pow(10, power); // 向下取整
                break;
        }
        let s = t.toString();
        let rs = s.indexOf(".");
        if (rs < 0 && power > 0 && force) {
            rs = s.length;
            s += ".";
        }
        while (force && s.length <= rs + power) {
            s += "0";
        }
        return s;
    }
}
