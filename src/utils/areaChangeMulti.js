import { getRegionList } from "@/api/index";
import API from "@/api/apv";
import _ from "lodash";
class AreaChangeMulti {
  addressOneList = [];
  addressTwoList = [];
  addressThreeList = [];
  addressList = [];
  areaList = [];
  regionList = [
    {
      provinceId: "",
      cityId: "",
      regionId: "",
      addressTwoList: [],
      addressThreeList: [],
    },
  ];
  provinceId = "";
  cityId = "";
  regionId = "";

  // 获取主区域信息
  getInfo = (editId, orign) => {
    API.getAuths().then((res) => {
      if (res.data.result.length) {
        this.areaList = res.data.result;
        const master = res.data.result;
        let arr = [];
        res.data.result.filter((e) => {
          let obj = {
            id: String(e?.provinceId),
            name: e?.provinceName,
          };
          arr.push(obj);
        });
        this.addressOneList = _.uniqWith(arr, _.isEqual);

        if (editId) {
          // 修改
          orign.getEdit(editId);
        } else if (orign.nannyObj?.nannyId) {
          // 小管家获单（仅考虑户用）
          orign.nannyView();
        } else {
          // 普通录入
          this.provinceId = master?.provinceId;
          this.provinceName = master?.provinceName;
          this.typeOneChange(master?.provinceId);
        }
      } else {
        this.areaList = [];
      }
    });
  };

  // 获取省市区
  getaddressList = (editId, orign, limit = true) => {
    getRegionList()
      .then((res) => {
        this.addressList = res.data.result;
      })
      .then(() => {
        if (limit) {
          this.getInfo(editId, orign);
        } else {
          this.getCommon(editId, orign);
        }
      });
  };

  // 省改变   // 查询授权区域，业主地址受限调整  2022-07-11
  typeOneChange = (item, index) => {
    this.regionList.forEach((item, ind) => {
      this.regionList[ind].addressTwoList = [];
    });
    this.cityId = "";
    this.regionId = "";
    if (!item) return;
    let str = String(item).substring(0, 2);
    let areaList = this.areaList;
    if (areaList.length) {
      areaList.map((e) => {
        if (str === String(e.cityId).substring(0, 2)) {
          let obj = {
            id: e.cityId,
            name: e.cityName,
          };
          this.regionList.forEach((item, ind) => {
            // 去重
            let hasIt = this.regionList[ind].addressTwoList.some((n) => n.id === obj.id);
            // console.log(hasIt);
            !hasIt && this.regionList[ind].addressTwoList.push(obj);
          });
        }
      });
    }
  };

  // 市变化
  typeTwoChange = (item, index) => {
    this.regionList[index].addressThreeList = [];
    this.regionId = "";
    if (!item) return;
    let str = String(item).substring(0, 4);
    let areaList = this.areaList;
    if (areaList.length) {
      areaList.map((e) => {
        if (str === String(e.regionId).substring(0, 4)) {
          let obj = {
            id: String(e.regionId),
            name: e.regionName,
          };
          // 去重
          let hasIt = this.regionList[index].addressThreeList.some((n) => n.id === obj.id);
          !hasIt && this.regionList[index].addressThreeList.push(obj);
        }
      });
    }
  };

  // 获取一般省
  getCommon = (editId, orign) => {
    for (let i in this.addressList.province_list) {
      let obj = {
        id: i,
        name: this.addressList.province_list[i],
      };
      this.addressOneList.push(obj);
    }
    if (editId) {
      orign.getEdit(editId);
    }
  };

  // 省改变   // 未限制地址
  typeOneChangeCommon = (item, index = 0, isClear = true) => {
    // this.addressTwoList = [];
    // this.cityId = '';
    // this.regionId = '';
    this.regionList.forEach((item, ind) => {
      this.regionList[ind].addressTwoList = [];
      if (isClear) {
        this.regionList[ind].cityId = "";
        this.regionList[ind].regionId = "";
      }
    });
    if (!item) return;
    let str = String(item).substring(0, 2);
    for (let i in this.addressList.city_list) {
      if (str === i.substring(0, 2)) {
        let obj = {
          id: i,
          name: this.addressList.city_list[i],
        };
        this.regionList.forEach((item, ind) => {
          this.regionList[ind].addressTwoList.push(obj);
        });
      }
    }
  };

  // 市变化
  typeTwoChangeCommon = (item, index = 0, isClear = true) => {
    // this.addressThreeList = [];
    // this.regionId = '';
    this.regionList[index].addressThreeList = [];
    if (isClear) {
      this.regionList[index].regionId = "";
    }
    if (!item) return;
    let str = String(item).substring(0, 4);
    for (let i in this.addressList.county_list) {
      if (str === i.substring(0, 4)) {
        let obj = {
          id: i,
          name: this.addressList.county_list[i],
        };
        this.regionList[index].addressThreeList.push(obj);
      }
    }
  };

  // 查询选择项赋值
  cityInit = (e, limit = true) => {
    if (e.regionList && e.regionList.length) {
      this.regionList = [];
    } else {
      this.regionList = [
        {
          provinceId: "",
          cityId: "",
          regionId: "",
          addressTwoList: [],
          addressThreeList: [],
        },
      ];
    }
    const typeOne = limit ? "typeOneChange" : "typeOneChangeCommon";
    const typeTwo = limit ? "typeTwoChange" : "typeTwoChangeCommon";
    for (let i = 0; i < e.regionList.length; i++) {
      const item = e.regionList[i];
      const { provinceId, cityId, regionId } = item;
      Object.assign(item, {
        provinceId: String(provinceId),
        cityId: cityId,
        addressTwoList: [],
        addressThreeList: [],
      });
      this.regionList.push(item);
      this[typeOne](provinceId, i, false);
      //   setTimeout(() => , 100)
      setTimeout(() => {
        this[typeTwo](cityId, i, false);
        Object.assign(item, {
          regionId: String(regionId),
        });
      }, 100);
    }
  };

  // 添加多个区域
  addRegion = (index) => {
    this.regionList.push({
      provinceId: this.regionList[index].provinceId,
      cityId: "",
      regionId: "",
      addressTwoList: this.regionList[index].addressTwoList,
      addressThreeList: [],
    });
  };

  // 删除一个
  deleteRegion = (index) => {
    this.regionList.splice(index, 1);
  };
}
export default new AreaChangeMulti();
