//个人信息脱敏处理
export default {
  encryptData(originalData, type) {
    //定义一个encryptData的函数，接受两个参数originalData和type
    let encryptedData = ""; //声明一个变量encryptedData，用于存储加密后的数据。
    switch (
      type //根据type的取值进行不同的处理
    ) {
      case "phone": {
        //手机号
        const phoneRegex = /^(1[3-9]\d)(\d{4})(\d{4})$/; //校验电话号码格式
        const phoneMatch = originalData.match(phoneRegex); //使用正则表达式匹配原始数据。
        if (phoneMatch) {
          encryptedData = phoneMatch[1] + "****" + phoneMatch[3]; //将匹配到的电话号码部分进行部分隐藏处理
        } else {
          encryptedData = originalData;
          // console.error("无效的电话号码格式。请提供有效的电话号码");
        }
        break;
      }
      case "idCard": {
        //身份证
        const idCardRegex = /^(\d{6})(\d{8})(\d{3}[\dXx])$/;
        const idCardMatch = originalData.match(idCardRegex);
        if (idCardMatch) {
          encryptedData = idCardMatch[1] + "************"; //只展示前6位
          // encryptedData = idCardMatch[1] + "********" + idCardMatch[3].toUpperCase();//展示前6位和后4位
        } else {
          encryptedData = originalData;
          // console.error("无效的身份证格式。请提供有效的18位身份证号码");
        }
        break;
      }
      case "bankAccount": {
        //银行账号
        const bankAccountRegex = /^(\d{4})\d{8}(\d{4})\d{0,3}$/;
        const bankAccountMatch = originalData.match(bankAccountRegex);
        if (bankAccountMatch) {
          if (originalData.length === 16) {
            encryptedData = originalData.substring(0, 4) + " **** **** " + originalData.substring(originalData.length - 4);
          } else if (originalData.length >= 17 && originalData.length <= 19) {
            encryptedData = originalData.substring(0, 4) + " **** **** " + originalData.substring(originalData.length - 3);
          } else {
            encryptedData = originalData;
            // console.error("无效的银行账号格式。请提供有效的银行账号");
          }
        } else {
          encryptedData = originalData;
          // console.error("无效的银行账号格式。请提供有效的银行账号");
        }
        break;
      }
      default: //默认情况下执行以下代码块。
        console.error(
          "不支持的数据类型。请指定有效的数据类型（电话、身份证或银行账户）"
        );
        break;
    }
    return encryptedData; //返回加密后的数据
  },
};