import {
    getRegionList
} from "@/api";
import API from '@/api/apv';
import _ from "lodash";

class AreaChange {
    addressOneList = []
    addressTwoList = []
    addressThreeList = []
    addressList = []
    areaList = []
    provinceId = ''
    cityId = ''
    regionId = ''

    // 获取主区域信息
    getInfo = (editId, orign, spId) => {
        API.findSpRegion().then(res => {
            if (res.data.length) {
                this.areaList = res.data.filter(e => e.status === 'ENABLE')
                const master = res.data.find(e => e.status === 'ENABLE' && e.regionType === 'MASTER')
                // * 放开跨省建站，适配大客户 At 2024-01-08
                let arr = []
                res.data.filter(e => {
                    if (e.status === 'ENABLE') {
                        let obj = {
                            id: String(e?.provinceId),
                            name: e?.provinceName
                        }
                        arr.push(obj);
                    }
                })
                this.addressOneList = _.uniqWith(arr, _.isEqual)
                if (editId) {
                    // 修改
                    orign.getEdit(editId);
                } else if (orign?.nannyObj?.nannyId) {
                    // 小管家获单（仅考虑户用）
                    orign.nannyView()
                } else if (spId) {
                    orign.getStagingDetail(spId)
                } else {
                    // 普通录入
                    this.provinceId = master?.provinceId;
                    this.provinceName = master?.provinceName;
                    this.typeOneChange(master?.provinceId).then()
                }
            } else {
                this.areaList = []
            }
        })
    }

    // 获取省市区
    getaddressList = async (editId, orign, limit = true, spId) => {
        try {
            this.provinceId = ''
            this.cityId = '';
            this.regionId = '';
            const res = await getRegionList()
            this.addressList = res.data.result;
            if (limit) {
                this.getInfo(editId, orign, spId)
            } else {
                this.getCommon(editId, orign, spId)
            }
        } catch (error) {}
    }

    // 省改变   // 查询授权区域，业主地址受限调整  2022-07-11
    typeOneChange = async (item) => {
        try {
            this.addressTwoList = [];
            this.cityId = '';
            this.regionId = '';
            if (!item) return;
            let str = String(item).substring(0, 2);
            let areaList = this.areaList
            if (areaList.length) {
                areaList.map((e) => {
                    if (str === String(e.cityId).substring(0, 2)) {
                        let obj = {
                            id: String(e.cityId),
                            name: e.cityName
                        }
                        // 去重
                        let hasIt = this.addressTwoList.some(n => n.id === obj.id)
                        // console.log(hasIt);
                        !hasIt && this.addressTwoList.push(obj)
                    }
                })
            }
        } catch (error) {}
    }

    // 市变化
    typeTwoChange = async (item) => {
        try {
            this.addressThreeList = [];
            this.regionId = '';
            if (!item) return;
            let str = String(item).substring(0, 4);
            let areaList = this.areaList
            if (areaList.length) {
                let noRegion = areaList.every(e => !e.regionId)
                if (!noRegion) {
                    areaList.map((e) => {
                        if (str === String(e.regionId).substring(0, 4)) {
                            let obj = {
                                id: String(e.regionId),
                                name: e.regionName
                            };
                            // 去重
                            let hasIt = this.addressThreeList.some(n => n.id === obj.id)
                            !hasIt && this.addressThreeList.push(obj)
                        }
                    })
                } else {
                    // * 适配到市级区域全量选择 At 2024-01-10 奥克隆定制
                    await this.typeTwoChangeCommon(item)
                }
            }
        } catch (error) {}
    }

    // 获取一般省
    getCommon = (editId, orign, spId) => {
        for (let i in this.addressList.province_list) {
            let obj = {
                id: String(i),
                name: this.addressList.province_list[i]
            };
            this.addressOneList.push(obj);
        }
        if (editId) {
            orign.getEdit(editId);
        } else if (orign?.nannyObj?.nannyId) {
            // 小管家获单（仅考虑户用）
            orign.nannyView()
        } else if (spId) {
            orign.getStagingDetail(spId)
        }
    }

    // 省改变   // 未限制地址
    typeOneChangeCommon = async (item) => {
        try {
            this.addressTwoList = [];
            this.cityId = '';
            this.regionId = '';
            if (!item) return;
            let str = String(item).substring(0, 2);
            for (let i in this.addressList.city_list) {
                if (str === i.substring(0, 2)) {
                    let obj = {
                        id: String(i),
                        name: this.addressList.city_list[i]
                    };
                    this.addressTwoList.push(obj);
                }
            }
        } catch (error) {}
    }

    // 市变化
    typeTwoChangeCommon = async (item) => {
        try {
            this.addressThreeList = [];
            this.regionId = '';
            if (!item) return;
            let str = String(item).substring(0, 4);
            for (let i in this.addressList.county_list) {
                if (str === i.substring(0, 4)) {
                    let obj = {
                        id: String(i),
                        name: this.addressList.county_list[i]
                    };
                    this.addressThreeList.push(obj);
                }
            }
        } catch (error) {}
    }

    // 查询选择项赋值
    cityInit = async (e, limit = true) => {
        try {
            if (limit) {
                await this.typeOneChange(e.provinceId);
                await this.typeTwoChange(e.cityId)
            } else {
                await this.typeOneChangeCommon(e.provinceId);
                await this.typeTwoChangeCommon(e.cityId)
            }
        } catch (error) {}
    }
}

export default new AreaChange()
