import { getRegionList } from "@/api/index";
import API from "@/api/apv";

class AreaChanges {
  addressOneList = [];
  addressTwoList = [];
  addressThreeList = [];
  addressList = [];
  areaList = [];
  regionList = [
    {
      serviceProvinceId: "", //服务省地址
      serviceProvince: "", //服务省地址名称
      serviceCityId: "", //服务市地址ID
      serviceCity: "", //服务市地址名称
      serviceAreaId: "", //服务区域ID
      serviceArea: "", //服务区域
      addressTwoList: [],
      addressThreeList: [],
    },
  ];
  serviceProvinceId = "";
  serviceCityId = "";
  serviceAreaId = "";

  // 获取主区域信息
  getInfo = (editId, orign) => {
    API.findSpRegion().then((res) => {
      if (res.data.length) {
        this.areaList = res.data.filter((e) => e.status === "ENABLE");
        const master = res.data.find((e) => e.status === "ENABLE" && e.regionType === "MASTER");
        let obj = {
          id: master?.provinceId,
          name: master?.provinceName,
        };
        let arr = [];
        arr.push(obj);
        this.addressOneList = [...new Set(arr)];

        if (editId) {
          // 修改
          orign.getEdit(editId);
        } else if (orign.nannyObj?.nannyId) {
          // 小管家获单（仅考虑户用）
          orign.nannyView();
        } else {
          // 普通录入
          this.serviceProvinceId = master?.provinceId;
          this.serviceProvince = master?.provinceName;
          this.typeOneChange(master?.provinceId);
        }
      } else {
        this.areaList = [];
      }
    });
  };

  // 获取省市区
  getaddressList = (editId, orign, limit = true) => {
    getRegionList()
      .then((res) => {
        this.addressList = res.data.result;
      })
      .then(() => {
        if (limit) {
          this.getInfo(editId, orign);
        } else {
          this.getCommon(editId, orign);
        }
      });
  };

  // 省改变   // 查询授权区域，业主地址受限调整  2022-07-11
  typeOneChange = (item, index) => {
    this.regionList.forEach((item, ind) => {
      this.regionList[ind].addressTwoList = [];
    });
    this.cityId = "";
    this.regionId = "";
    if (!item) return;
    let str = String(item).substring(0, 2);
    let areaList = this.areaList;
    if (areaList.length) {
      areaList.map((e) => {
        if (str === String(e.cityId).substring(0, 2)) {
          let obj = {
            id: e.cityId,
            name: e.cityName,
          };
          this.regionList.forEach((item, ind) => {
            // 去重
            let hasIt = this.regionList[ind].addressTwoList.some((n) => n.id === obj.id);
            !hasIt && this.regionList[ind].addressTwoList.push(obj);
          });
        }
      });
    }
  };

  // 市变化
  typeTwoChange = (item, index) => {
    this.regionList[index].addressThreeList = [];
    this.regionId = "";
    if (!item) return;
    let str = String(item).substring(0, 4);
    let areaList = this.areaList;
    if (areaList.length) {
      areaList.map((e) => {
        if (str === String(e.regionId).substring(0, 4)) {
          let obj = {
            id: e.regionId,
            name: e.regionName,
          };
          // 去重
          let hasIt = this.regionList[index].addressThreeList.some((n) => n.id === obj.id);
          !hasIt && this.regionList[index].addressThreeList.push(obj);
        }
      });
    }
  };

  // 获取一般省
  getCommon = (editId, orign) => {
    this.addressOneList=[]
    for (let i in this.addressList.province_list) {
      let obj = {
        id: i,
        name: this.addressList.province_list[i],
      };
      this.addressOneList.push(obj);
    }
    if (editId) {
      orign.getEdit(editId);
    }
  };

  // 省改变   // 未限制地址
  typeOneChangeCommon = (item, index = 0, isClear = true) => {
    this.regionList[index].addressTwoList = [];
    if (!item) return;
    let str = String(item).substring(0, 2);
    let arr = [];
    for (let i in this.addressList.city_list) {
      if (str === i.substring(0, 2)) {
        let obj = {
          id: i,
          name: this.addressList.city_list[i],
        };
        arr.push(obj);
      }
    }
    this.regionList[index].addressTwoList = arr;
  };

  // 市变化
  typeTwoChangeCommon = (item, index = 0, isClear = true) => {
    this.regionList[index].addressThreeList = [];
    if (!item) return;
    let str = String(item).substring(0, 4);
    for (let i in this.addressList.county_list) {
      if (str === i.substring(0, 4)) {
        let obj = {
          id: i,
          name: this.addressList.county_list[i],
        };
        this.regionList[index].addressThreeList.push(obj);
      }
    }
  };

  // 查询选择项赋值
  cityInit = (e, limit = true) => {
    if (e.regionDtoList && e.regionDtoList.length) {
      this.regionList = [];
    } else {
      this.regionList = [
        {
          serviceProvinceId: "", //服务省地址
          serviceProvince: "", //服务省地址名称
          serviceCityId: "", //服务市地址ID
          serviceCity: "", //服务市地址名称
          serviceAreaId: "", //服务区域ID
          serviceArea: "", //服务区域
          addressTwoList: [],
          addressThreeList: [],
        },
      ];
    }

    const typeOne = limit ? "typeOneChangeCommon" : "typeOneChange";
    const typeTwo = limit ? "typeTwoChangeCommon" : "typeTwoChange";
    for (let i = 0; i < e.regionDtoList.length; i++) {
      const item = e.regionDtoList[i];
      const { serviceProvinceId, serviceCityId, serviceAreaId } = item;
      Object.assign(item, {
        serviceProvinceId: String(serviceProvinceId),
        serviceCityId: String(serviceCityId),
        addressTwoList: [],
        addressThreeList: [],
      });
      this.regionList.push(item);
      this[typeOne](serviceProvinceId, i, false);
      setTimeout(() => this[typeTwo](serviceCityId, i, false), 100);
      setTimeout(
        () =>
          Object.assign(item, {
            serviceAreaId: String(serviceAreaId),
          }),
        100
      );
    }
  };
  // 添加多个区域
  addRegion = (index) => {
    this.regionList.push({
      serviceProvinceId: "", //服务省地址
      serviceProvince: "", //服务省地址名称
      serviceCityId: "", //服务市地址ID
      serviceCity: "", //服务市地址名称
      serviceAreaId: "", //服务区域ID
      serviceArea: "", //服务区域
      addressOneList: [],
      addressTwoList: [],
      addressThreeList: [],
    });
  };

  // 删除一个
  deleteRegion = (index) => {
    this.regionList.splice(index, 1);
  };
}
export default new AreaChanges();
