import * as XLSX from 'xlsx'

// excel导入方法
export const file2XLSX = (file) => {
    return new Promise(function (resolve, reject) {
        // 通过FileReader对象读取文件
        const reader = new FileReader()
        // 读取为二进制字符串
        reader.readAsBinaryString(file)
        reader.onload = function (e) {
            // 获取读取文件成功的结果值
            const data = e.target.result
            // XLSX.read解析数据，按照type 的类型解析
            let wb = XLSX.read(data, {
                type: 'binary' // 二进制
            })
            // 存储获取到的数据
            const result = []
            // 工作表名称的有序列表
            wb.SheetNames.forEach(sheetName => {
                result.push({
                    // 工作表名称
                    sheetName: sheetName,
                    // 利用 sheet_to_json 方法将 excel 转成 json 数据
                    sheet: XLSX.utils.sheet_to_json(wb.Sheets[sheetName]),
                })
            })
            resolve(result)
        }
    })
}
