import { Message, Notice, Spin } from "view-design";
import { pick, assign } from "lodash"
/*
 * @Author: lufy
 * @FilePath: /src/utils/common.js
 * @Description: 公共方法组件（2023）
 * @remark: null
 */

// * 缓存清理保留个别数据
export const storageClear = () => {
    const save_Storage = {
        v: localStorage.getItem('v')
    }
    localStorage.clear();
    save_Storage.v && localStorage.setItem('v', save_Storage.v)  // * 重新填充历史版本号
}

// * 对象交集更新事件
export const updateObjectWithLodashIntersection = (obj1, obj2) => {
    // 使用 _.pick 方法选取 obj2 中 obj1 存在的键值对
    const intersectionObj = pick(obj2, Object.keys(obj1));

    // 使用 _.assign 方法将交集的键值对赋值给 obj1，达到更新的目的
    return assign(obj1, intersectionObj);
}

// 创建一个通用的函数来映射图片URL
export const mapImageUrls = (list, imgUrlMap) => {
    return list?.map(item => ({
        ...item,
        imgUrl: imgUrlMap.get(item.imgName)?.gridCheckImg || imgUrlMap.get(item.imgName)?.completeConfirmImg || imgUrlMap.get(item.imgName)?.imgUrl // 保持原始imgUrl作为最后的备选，如果上述都未定义
    }));
}

// * 公共详情方法：打开详情
export const getView = (id, mode, stationType, origin) => {
    // * 所有模式统一化 At 2023-12-12
    if (['DH_EPC'].includes(mode)) {
        origin.$refs.stationInfoDH.getInfoView(id);
    } else {
        origin.$refs.stationInfoYX.getInfoView(id);
    }
    // * 电站模式去向
    // if (['YX','ZH'].includes(mode)) {
    //     this.$refs.stationInfoYX.getInfoView(id)
    // } else if(mode === 'HR') {
    //     this.$refs.stationInfo.getInfoView(id)
    // } else {
    //     this.$Message.error('电站模式异常，请联系管理员查询');
    // }
};

// 下载cdn需要重名的文件，跨域需设置代理
export const downFileHandle = (url, name) => {
    let request = new XMLHttpRequest();
    url = url.replace("https://cdn01.rrsjk.com", "/down");
    request.open("GET", url, true);
    request.responseType = "blob";
    request.onload = () => {
        const url = window.URL.createObjectURL(request.response);
        const link = document.createElement("a");
        link.href = url;
        link.download = name;
        link.click();
    };
    request.send();
};

// 封装上传公共事件
export const UploadClass = class UploadClass {
    static headers = { Authorization: `Bearer ${JSON.parse(localStorage.getItem("logins"))?.access_token}` };

    static actionUrl = (type) => {
        const Action = {
            image: `${process.env.VUE_APP_BASE_API}/merchant/oss/image/upload.do`,
            pdf: `${process.env.VUE_APP_BASE_API}/merchant/oss/image/uploadPdf.do`,
            file: `${process.env.VUE_APP_BASE_API}/merchant/oss/file/upload.do`,
            video: `${process.env.VUE_APP_BASE_API}/merchant/oss/file/upload.do`,
        };
        return Action[type] || Action['file'];
    };

    static upSuccess = () => {
        setTimeout(() => {
            Message.destroy();
        }, 500);
    };

    static progress = () => {
        Message.loading('正在上传，较大文件可能上传缓慢，请耐心等待');
        // Spin.show({
        //     render: (h) => {
        //         return h('div', [
        //             h('Icon', {
        //                 'class': 'spin-icon-load',
        //                 props: {
        //                     type: 'ios-loading',
        //                     size: 18
        //                 }
        //             }),
        //             h('div', '正在上传，较大文件可能上传缓慢，请耐心等待')
        //         ])
        //     }
        // });
    };

    static formatError = (_file) => {
        Notice.warning({
            title: "上传格式不正确",
            desc: "请上传正确的格式文件"
        });
    };

    static maxSizeError = (_file) => {
        Notice.warning({
            title: "上传文件过大",
            desc: `${_file.name}文件太大，请压缩后上传.<br><span style="color: red;font-weight:bolder">${_file.size}</span>/ 20480`
        });
    };

    static upError = (error) => {
        Message.error({
            content: `上传失败：${error}`
        });
    };
};
