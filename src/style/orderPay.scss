.orderPay {
  width: 100%;
  min-height: $min-height;
  background: white;
  .content {
    padding: 20px;
    width: 100%;
    height: auto;
    overflow: hidden;
  }
  .success-sub {
    clear: both;
    width: 100%;
    background: $background-main;
    border: 1px solid #ddd;
    padding: 15px;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
    *position: relative;
    margin: 0 auto;
  }
  .h1-success-sub {
    font-size: 16px;
    font-weight: lighter;
    padding-left: 55px;
    background-position: 0 0;
    height: 34px;
    line-height: 34px;
    margin-top: 10px;
    margin-bottom: 10px;
    margin-left: 30px;
  }
  .success-sub-mind {
    color: #cd0000;
    font-size: 12px;
  }
  .h1-success-sub {
    background: url(/assets/img/success.png) no-repeat;
    background-position: 15px 3px;
    background-size: 30px 30px;
  }
  .tabs {
    box-sizing: border-box;
    margin: 30px auto;
    border-top: none;
  }
  .tabs_p {
    line-height: 30px;
    font-size: 14px;
    color: #333333;
    font-weight: 600;
    width: 94%;
    margin: 20px auto;
  }
  .tabs_div {
    width: 100%;
    height: auto;
    padding: 0 3%;
    overflow: hidden;
    border: 1px solid #ccc;
    border-top: none;
    margin-top: -16px;
    padding-bottom: 50px;
  }
  .tabs_div_son {
    width: 200px;
    height: 35px;
  }
  .tabs_div_son > p {
    width: 170px;
    height: 35px;
    float: right;
    border: 1px solid #dcdee2;
  }
  .bg1 {
    background: url(/assets/img/pay-bank-l.png) no-repeat 15px -4963px;
  }
  .tabs_div_son_one {
    width: 670px;
    height: 300px;
    margin: 50px auto;
  }
  .tabs_p_one {
    font-size: 26px;
    color: #666;
    text-align: center;
  }
  .tabs_left {
    width: 50%;
    float: left;
    height: 100%;
  }
  .code_div {
    width: 180px;
    height: 180px;
    border: 1px solid #ccc;
    margin: 10px auto;
    position: relative;
    text-align: center;
  }
  .code_div > img {
    width: 170px;
    height: 170px;
    margin-top: 5px;
  }
  .tabs_p_two {
    width: 90%;
    margin: 0 auto;
    line-height: 20px;
    margin-top: 10px;
    font-size: 14px;
    color: #333333;
    text-align: center;
  }
  .tabs_img {
    height: 100%;
  }
  .wimportant {
    background: #f6f6f6;
    border: 1px solid #e5e5e5;
    margin: 10px;
    line-height: 24px;
    font-size: 14px;
  }
  .pda20 {
    padding: 10px 20px;
  }
  .liheight36 {
    line-height: 36px;
  }
  .wimport li {
    font-family: "微软雅黑", Verdana, Tahoma, Arial, Helvetica, "宋体", sans-serif;
    font-weight: 700;
    line-height: 38px;
    font-size: 16px;
    font-style: normal;
    color: #333333;
  }
  .wredco {
    color: #c3002a;
  }
  .pda40 {
    padding: 10px 40px;
  }
}
#qrcode {
  text-align: center;

  img {
    width: 170px;
    height: 170px;
    margin-top: 5px;
    margin-left: 5px;
  }
}

.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}

@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(180deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.demo-spin-col {
  height: 100px;
  position: relative;
  border: 1px solid #eee;
}

.new_tabs_p_two {
  width: 100%;
  margin: 0 auto;
  padding: 20px;
  line-height: 20px;
  margin-top: 10px;
  font-size: 14px;
  font-weight: 600;
  color: #333333;
  text-align: left;
}

.tabs_span1 {
  display: inline-block;
  width: 80px;
}

.tabs_span2 {
  margin-right: 10px;
}

.radio-ment {
  display: flex;
  align-items: center;
  padding-top: 20px;
}

.radio-img {
  margin: 0 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
}

.btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 50px;
}
