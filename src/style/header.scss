.header {
	width: 100%;
	margin-bottom: 15px;
	background: $theme-sea-thin;
	// overflow: hidden;
	.fl_ri {
		float: right;
	}
	.fl_le {
		float: left;
	}
	.head_son {
		width: 100%;
		height: 100%;
		margin: 0 auto;
	}
	.head_son_one {
		width: 300px;
		height: 36px;
		float: right;
		margin-top: 10px;
	}
	.head_p_one {
		height: 100%;
		margin-right: 10px;
		cursor: pointer;
	}
	.head_p_one > span {
		font-size: 14px;
		color: #af99af;
		line-height: 36px;
		vertical-align: middle;
	}
	.head_p_one > i {
		vertical-align: middle;
	}
	.head_son_two {
		width: 100%;
		height: 100px;
		padding: 0 6%;
	}
	.logo_img {
		height: 22px;
		float: left;
		cursor: pointer;
	}
	.head_p_two {
		color: #fff;
		line-height: 24px;
		font-size: 16px;
		float: left;
		margin-top: 23px;
		border-left: 1px solid #cccccc;
		margin-left: 15px;
		padding-left: 15px;
	}
	.head_son_three {
		width: 100%;
		min-width: 1200px;
		max-width: 1920px;
		margin: 0 auto;
		padding: 0 3%;
		overflow: hidden;
	}
	.head_p_three {
		padding: 0 15px;
		color: $theme-sea-main;
		font-size: 15px;
		line-height: 70px;
		text-align: center;
		float: left;
		cursor: pointer;
		user-select: none;

		&:hover {
			background: $theme-sea-thiner;
		}
	}
	.backSign {
		background: $theme-sea-heavy;
		&:hover {
			background: $theme-sea-heavy;
		}
	}
	.head_div_three {
		display: flex;
		align-items: center;
		width: 200px;
		height: 70px;
		float: left;
	}
	.head_div_four {
		display: flex;
		height: 70px;
		float: right;
	}
	.head_p_five {
		display: inline-flex;
		align-items: center;
		height: 70px;
		margin: 0 10px;
		cursor: pointer;

		.user-head {
			width: 40px;
			height: 40px;
			background: $theme-sea-heavy;
			display: inline-flex;
			align-items: center;
			justify-content: center;
			border-radius: 6px;
		}
	}
	.head_p_five > span {
		font-size: 14px;
		color: $theme-sea-main;
		user-select: none;
		display: inline-flex;
		align-items: center;
		border-radius: 6px;
		font-family: monospace;
	}

	.head_mode {
		position: relative;
		padding: 0 3%;
		padding-left: calc(3% + 290px);
		background: #fff;
		width: 100%;
		height: 0;
		opacity: 0;
		transition: height 0.35s;
	}

	.head_mode.pull {
		height: 40px;
		opacity: 1;

		.mode_arrow {
			position: absolute;
			top: -14px;
			left: calc(3% + 330px);
		}
	}

	.mode_child {
		display: inline-block;
		padding: 0 15px;
		min-width: 105px;
		height: 100%;
		text-align: center;
		color: #76A3B7;
		font-size: 13px;
		line-height: 40px;
		cursor: pointer;
		user-select:none;

		&.actived {
			background: $theme-sea-main;
			font-weight: bolder;
			color: #315F8A;
		}
	}

	:deep(.ivu-tooltip-popper) {
		z-index: 1000 !important;
	}
}

// 自定义tooltip
.tooltip {
	:deep(.ivu-tooltip-inner) {
		align-items: center;
		appearance: none;
		background-color: #FCFCFD;
		border-radius: 4px;
		border-width: 0;
		box-shadow: rgba(45, 35, 66, 0.4) 0 2px 4px,rgba(45, 35, 66, 0.3) 0 7px 13px -3px,#D6D6E7 0 -3px 0 inset;
		box-sizing: border-box;
		color: #36395A;
		display: inline-flex;
		font-family: "JetBrains Mono",monospace;
		height: 48px;
		justify-content: center;
		line-height: 1;
		list-style: none;
		overflow: hidden;
		padding-left: 16px;
		padding-right: 16px;
		position: relative;
		text-align: left;
		text-decoration: none;
		transition: box-shadow .15s,transform .15s;
		user-select: none;
		-webkit-user-select: none;
		touch-action: manipulation;
		white-space: nowrap;
		will-change: box-shadow,transform;

		&:focus {
			box-shadow: #D6D6E7 0 0 0 1.5px inset, rgba(45, 35, 66, 0.4) 0 2px 4px, rgba(45, 35, 66, 0.3) 0 7px 13px -3px, #D6D6E7 0 -3px 0 inset;
		}

		&:hover {
			box-shadow: rgba(45, 35, 66, 0.4) 0 4px 8px, rgba(45, 35, 66, 0.3) 0 7px 13px -3px, #D6D6E7 0 -3px 0 inset;
			transform: translateY(-2px);
		}

		&:active {
			box-shadow: #D6D6E7 0 3px 7px inset;
			transform: translateY(2px);
		}
	}
}
