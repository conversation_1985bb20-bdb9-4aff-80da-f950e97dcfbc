/*
* @Author: lufy
* @FilePath: /src/style/_variables.scss
* @Description:  sass通用配置
*/

@import "iview";

/*
* 布局配置
*/
$min-height: calc(100vh - 100px); // 页面最低高度
$border-raius-6: 6px;
$border-raius-10: 10px;

/*
* 颜色配置
*/
$color-main: #c7003a; // 项目主色
$background-main: #f5f7f9; // 背景主色
$background-header: #03b8cc; // header主色
$border-color-1: #e8eaec; // 通用边框颜色
$border-color-2: #f2f2f2;
$border-color-3: #f5f5f5;
$border-color-4: #f7f7f7;

// 海洋之滨 配色
$theme-sea-main: #DFF4FC;
$theme-sea-heavy: #315F8A;
$theme-sea-thin: #3B6C99;
$theme-sea-thiner: #638EB3;
$theme-sea-light: #76A3B7;

// 通用处理
// 处理弹框 body pr 问题
body {
	padding-right: 0 !important;
}

.imagefit {
	object-fit: contain;
	height: inherit;
	transition: transform 300ms ease;

	&:hover {
		transform: scale(1.05);
	}
}

.item_large {
	width: 300px;
}

.item_small {
	width: 200px;
}

// 自定义背景样式类
.back-style-1{
	padding: 15px;
	background: transparent;
	background-image: radial-gradient(#e9e9e9 0.5px, #fff 0.5px);
	background-size: 4px 4px;
}

.sub-dot-custom {
	&::before {
		margin-right: 8px;
		content: '';
		width: 10px;
		height: 10px;
		// background: #03b8cc;
		background-image: linear-gradient( 135deg, #ABDCFF 10%, #0396FF 100%);
		border-radius: 3px;
	}

	&.sub{
		margin-bottom: 20px;
		font-size: 14px;

		&::before {
			width: 8px;
			height: 8px;
			background-image: linear-gradient( 45deg, #99c2ed 10%, #e3ebf3 100%);
		}
	}
}

.status-dot {
	&::before {
		display: inline-block;
		margin-right: 6px;
		content: '';
		width: 8px;
		height: 8px;
		background-image: linear-gradient( 45deg, #dd3c3c 10%, #e3ebf3 100%);
		border-radius: 3px;
		animation: twinkle 1s infinite alternate;
	}
}

.text-ellipsis {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
}

// 全局抽屉最小宽度限制
.ivu-drawer {
	min-width: 700px;
}

@keyframes twinkle {
	0%{
		opacity:.1;
	}
	100%{
		opacity:0.5;
	}
}

.spin-icon-load{
	animation: ani-demo-spin 1s linear infinite;
}
@keyframes ani-demo-spin {
	from { transform: rotate(0deg);}
	50%  { transform: rotate(180deg);}
	to   { transform: rotate(360deg);}
}

// 自动移滚动条样式
::-webkit-scrollbar {
    width: 12px;
	height: 12px;
	border: 1px solid rgba(0, 0, 0, 0.05);
	border-radius: 3px;
}

// 定义滑块：内阴影+圆角
::-webkit-scrollbar-thumb {
    border-radius: 6px;
    border: 2px solid rgba(0, 0, 0, 0);
    box-shadow: 9999px 0 0 rgba(0, 0, 0, 0.25) inset;
}

::-webkit-scrollbar-thumb:hover {
	box-shadow: 9999px 0 0 rgba(0, 0, 0, 0.5) inset;
}
