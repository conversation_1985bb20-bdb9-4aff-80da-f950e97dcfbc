%pt {
	padding: 20px 0;
}
// list
.cus_list {
	width: 100%;
	min-height: $min-height;
	background: white;

	.step_div {
		width: 60%;
		min-width: 850px;
		margin: 0 auto;
		@extend %pt;
	}

	.list_son {
		width: 100%;
		height: auto;
		overflow: hidden;
		margin: 0 auto;

		.list_but {
			padding: 0 22px;
			width: 100%;
			height: auto;
			overflow: hidden;
			border-top: 1px solid transparent;

			.condition {
				margin: 10px 0;
				margin-right: 20px;
				width: auto;
				float: left;
			}

			.condition_p {
				float: left;
				line-height: 32px;
				font-size: 14px;
				color: #333;
				margin-right: 10px;
			}

			.condition > button {
				margin-right: 20px;
			}
		}

		.commListDiv {
			width: calc(100% - 40px);
			height: auto;
			overflow-y: hidden;
			overflow-x: auto;
			margin: 20px;
		}
	}
}

// table
.cus_table {
	width: 100%;

	th,
	td {
		-webkit-box-sizing: border-box;
		box-sizing: border-box;
		padding: 15px 18px;
	}

	th {
		font-size: 12px;
		font-weight: bolder;
		height: 100%;
		white-space: nowrap;
		overflow: hidden;
		background-color: #f2f2f2;
		text-overflow: ellipsis;
		vertical-align: middle;
		border-bottom: 1px solid $border-color-1;
	}

	td {
		font-size: 12px;
		min-width: 75px;
		height: 100%;
		text-overflow: ellipsis;
		vertical-align: middle;
		border-bottom: 1px solid $border-color-1;
		background: #fff;
	}

	%border_cus {
		box-sizing: border-box;
		border: 1px solid $border-color-2;

		td { border: 0 }
	}

	tr.border_nb {
		@extend %border_cus;
		border-bottom: 0;
	}

	tr.border_nt {
		@extend %border_cus;
		border-top: 0;
	}

	.cus_td {
		border-bottom: 0;
		padding: 20px 0 0;
	}

	.cus_td_line {
		padding: 8px 10px;
		background: $border-color-3;

		.flask {
		 	margin: 0 20px;
		}
	}

	tbody:hover {
		tr.border_nb, tr.border_nt {
			border-color: #ddd;
		}
		td {
			//font-weight: bold;
			color: #000;
		}
	}

	div.back-filter {
		background: inherit;
	}

	.fixedHeader {
		position: sticky;
		top: 0;
		z-index: 2;
		@extend .back-filter;
	}

	.fixedLeft {
		position: sticky;
		left: 0;
		@extend .back-filter;
	}

	.fixedRight {
		position: sticky;
		right: 0;
		@extend .back-filter;
	}

	&.shop_order {
		tbody {border-bottom: 1px solid #e8eaec}
		td {border-bottom: 1px dashed #f5f5f5}
	}
}

.cus_table_info {
	@extend .cus_table;
	th {width: 240px;}
	th,td{
	    border: 1px solid #e8eaec;
	}
}

// linetxt
.line_txt {
	display: inline-block;
	margin: 3px;
	padding: 4px 10px;

	&:last-of-type {
		margin-right: 0;
	}
}

// perfect-scroll 自定义浮动
::v-deep(.ps) {
	.ps__rail-x {
		position: fixed;
		bottom: 5% !important;
		left: 50% !important;
		transform: translateX(-50%) scaleX(0.8) scaleY(0.8);
		background: #eee;
		border-radius: 4px;
		z-index: 99;

		&:hover {
			cursor: grab;
		}

		&:active {
			cursor: grabbing;

			::after {
				position: absolute;
				top: 25px;
				left: 50%;
				padding: 8px 15px;
				content: '支持键盘 ⬅️ ➡️ 控制';
				transform: translateX(-50%) scaleX(1.25) scaleY(1.25);
				border-radius: 4px;
				background: #eee;
				border: 1px solid #ddd;
			}
		}

		.ps__thumb-x {
			border-radius: 4px;
		}
	}
}
