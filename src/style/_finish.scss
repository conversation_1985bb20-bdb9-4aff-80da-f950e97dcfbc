.cus_form {
    $wh: 130px;
    $border-color-upload: 1px dashed #dcdee2;
    .form_table {
        margin-bottom: 20px;
        td {
            padding: 10px;
            min-width: 100px;
            height: 55px;
            border: 1px solid $border-color-1 !important;

            &.flabel {
                background: #f8f8f9;
            }
        }
    }

    .form {
        margin: 0 auto;
        width: 100%;
        box-sizing: border-box;

        .item_large {
            width: 300px;
        }

        .item_small {
            width: 200px;
        }

        .item_smaller {
            width: 100px;
        }

        .tips {
            @extend .flex-row-center-center;
            padding: 8px;
            max-width: $wh !important;
            width: 100%;
            font-size: 12px;
            line-height: 1.2;
            color: $theme-sea-heavy;
            text-align: center;
            background: $theme-sea-main;
            border-radius: 0 0 6px 6px;
            border: $border-color-upload;
            border-top: 0;
            border-top: 0;
        }
		
		.cus_title_sub {
			margin: 15px 0;
            font-size: inherit;
            font-weight: inherit;
		}
    }

    .file_pics {
        @extend .flex-row-center-center;
        width: $wh !important;
        height: $wh !important;
        border-radius: 6px 6px 0 0 !important;
        background: rgb(223 244 252 / 20%);
        backdrop-filter: blur(1px);
        overflow: hidden;
        cursor: pointer;
        border: $border-color-upload !important;

        i,img {cursor: pointer;}

        .demo-upload-list {
            @extend .flex-row-center-center;
            position: relative;
            width: 100%;
            height: 100%;
            overflow: hidden;

            &:hover .demo-upload-list-cover {
                display: flex;
            }
        }

        .demo-upload-list-cover {
            @extend.flex-row-center-around;
            display: none;
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0,0,0,.5);
            z-index: 999;

            i {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                height: 100%;
                width: 100%;

                &:hover {
                    backdrop-filter: blur(3px);
                }
            }
        }
    }

    .inlinebox {
        @extend .flex-column-center-center;
        margin-right: 20px;
        margin-bottom: 20px;
        display: inline-flex;
    }

    .info_tip {
        margin: 15px 0;
        padding: 15px;
        background: #f8f8f9;
        border-radius: 6px;
        font-size: 12px;
        color: #dd3c3c;
    }
	
	.add_more_btn {
		margin: 15px 0;
	}
}

:deep(.vertical-center-modal) {
    .ivu-modal {
        display: flex;
        justify-content: center;
    }

    .ivu-modal-content {
        width: fit-content !important;
    }

    .ivu-modal-body {
        @extend .flex-row-center-center;
        padding: 0;
        max-width: 100%;
        min-width: 360px;
        min-height: 40vh;
        max-height: 75vh;

        .preview {
            max-width: 100%;
            max-height: 75vh;
            cursor: pointer;
        }
    }
}

:deep(.ivu-form) {
    .ivu-form-item-label,.ivu-input,.ivu-select-selected-value {
        font-size: 12px ;
    }
}