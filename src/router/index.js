import { vueRouterBefore, vueScrollBehavior } from "@/router/utils/guard";
import Vue from "vue";
import Router from "vue-router";
import { configRoutes } from "@/router/utils";
const moduleFiles = require.context('@/router/modules/', false, /\.js$/);
const routerArr = configRoutes(moduleFiles)

const microEnv = window.__POWERED_BY_QIANKUN__;

Vue.use(Router);
const subName = process.env.VUE_APP_SUBNAME

const router = new Router({
    base: microEnv ? `/mch/${subName}`:`/sub/${subName}`,
    mode: 'history',
    routes: [
        ...routerArr,
        {
            path: "/login",
            name: "login",
            component: () => import("@/views/login.vue"),
            meta: {
                auth: false,
                keepAlive: false
            }
        },
        {
            path: "*", // 未匹配到路由时重定向
            redirect: () => '/',
        }
    ],
    scrollBehavior(to, from, savedPosition) {
        vueScrollBehavior(to, from, savedPosition)
    }
});

// 全局路由钩子函数 对全局有效
router.beforeEach((to, _from, next) => vueRouterBefore(to, _from, next));

// 解决导航栏中的vue-router在3.0版本以上重复点菜单报错问题
const originalPush = Router.prototype.push;
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err);
};

export default router;
