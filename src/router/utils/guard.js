import { checkForNewVersion } from "@/utils/versionUpdate";
import { Message } from 'view-design';
const microEnv = window.__POWERED_BY_QIANKUN__

/**
* @description 路由守卫
* @time 2022/4/23 分离
* <AUTHOR>
*/
export const vueRouterBefore = (to, _from, next) => {
    // * checkForNewVersion(to)   // 更新面板   // * At 2024-07-23 优化生产发版流程更细卡片
    let auth = to.meta.auth;
    let token = JSON.parse(window.localStorage.getItem("logins")) || 0;
    let isHttp = document.location.protocol;
    let hostname = document.location.hostname;
    // if (isHttp === "http:" && process.env.NODE_ENV === "production" && hostname !== "127.0.0.1") {
    //     // 判断 http 自动转成 https:
    //     let url = window.location.href;
    //     url = url.replace("http:", "https:");
    //     window.location.href = url;
    // }

    /**
     * @description: 路由权限  2022/4/12
     * @param {*} limits：权限角色
     * @return: {*}  flag：符合权限
     * @author: lufy
     */
    if (to.meta.limits) {
        let userArr = localStorage.getItem("userArr")?.split(",");
        let flag = to.meta.limits.some((e) => userArr?.includes(e));
        if (!flag) {
            Message.warning({
                content: "您无权限打开此页面",
                duration: 1,
                onClose: () => microEnv? window?.microUtils?.baseRouterTo("/") : next('/index')
            });
            return
        }
    }

    if (auth) {
        // 需要登录
        if (token) {
            next();
        } else {
            next({ path: "/login"});
        }
    } else {
        next();
    }
};

export const vueScrollBehavior = (to, from, savedPosition) => {
    // savedPosition 会在你使用浏览器前进或后退按钮时候生效
    // 这个跟你使用 router.go() 或 router.back() 效果一致
    // 这也是为什么我在 tab 栏结构中放入了一个 点击回退 的按钮
    if (savedPosition) {
        // * window.scrollTo(savedPosition.x,savedPosition.y)
        // * 暂时默认全部回顶，后期继续优化
        window.scrollTo(0,0)
    } else {
        // 如果不是通过上述行为切换组件，就会让页面回到顶部
        window.scrollTo(0,0)
    }
}
