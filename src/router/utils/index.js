/**
 * @description: 路由工具包
 * @author: lufy
 * @date: 2024-01-17
 */

// * 自动导入模块
export const configRoutes = (moduleFiles) => {
    return moduleFiles.keys().reduce((routes, filepath) => {
        // 因为moduleFiles是一个函数，那么可以接受一个参数（string：文件的相对路径），调用其从而获取到对应路径下的模块的导出对象
        // 导出的对象中有一个属性：default，可以获取到默认导出的所有内容
        const value = moduleFiles(filepath).default;
        // 我们判断导出的是不是数组，是则进行拓展解构
        if (Array.isArray(value)) {
            routes.push(...value);
        } else {
            // 否则直接加到routes中
            routes.push(value);
        }
        return routes;
    }, []);
}
