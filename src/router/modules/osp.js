import Home from "@/views/index.vue";

export default [
    {
        path: "/",
        name: "osp",
        component: Home,
        redirect: "/ospContract",
        meta: {
            auth: false, // 是否需要登录
            keepAlive: true, // 是否缓存组件
            limits: ["light_osp", "light_osp_warranty"] // 权限码
        },
        children: [
            {
                path: "/ospReg",
                name: "ospReg", // 在线签约
                component: () => import("@/views/osp/account/ospReg.vue"),
                meta: {
                    auth: true,
                    keepAlive: false
                }
            },
            // {
            //   path: "/thirdParty",
            //   name: "thirdParty",
            //   component: () => import("@/views/osp/account/thirdParty.vue"),
            //   meta: {
            //     auth: true,
            //     keepAlive: false,
            //   },
            // },
            {
                path: "depositList",
                name: "osp_depositList", // 备件保证金
                component: () => import("@/views/osp/account/depositList.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp"]
                }
            },
            {
                path: "depositList/bailPay",
                name: "osp_bailPay", // 支付保证金
                component: () => import("@/views/osp/account/bailPay.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp"]
                }
            },
            {
                path: "personnelList",
                name: "personnelList", // 人员管理
                component: () => import("@/views/osp/attendant/personnelList.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp", "light_osp_warranty"]
                }
            },
            {
                path: "jobQualification",
                name: "jobQualification", // 上岗资格管理
                component: () => import("@/views/osp/attendant/jobQualification.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp", "light_osp_warranty"]
                }
            },
            {
                path: "operationList",
                name: "operationList", // 运维工单
                component: () => import("@/views/osp/operation/operationList.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp", "light_osp_warranty"]
                }
            },
            {
                path: "knowledge",
                name: "knowledge", // 运维知识库
                component: () => import("@/views/osp/operation/knowledge.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp", "light_osp_warranty"]
                }
            },
            {
                path: "pStationChanges",
                name: "pStationChanges", // 电站数据变更
                component: () => import("@/views/osp/operation/pStationChanges.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp", "light_osp_warranty"]
                }
            },
            {
                path: "operationList/details",
                name: "ospDetails", // 运维工单详情
                component: () => import("@/views/osp/operation/details.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp", "light_osp_warranty"]
                }
            },
            {
                path: "powerStation",
                name: "powerStation", // 电站列表
                component: () => import("@/views/osp/operation/powerStation.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp", "light_osp_warranty"]
                }
            },
            {
                path: "operationInverter",
                name: "operationInverter", // 逆变器列表
                component: () => import("@/views/osp/operation/operationInverter.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp", "light_osp_warranty"]
                }
            },
            {
                path: "settleBills",
                name: "settleBills", // 结算账单
                component: () => import("@/views/osp/finalAccount/settleBills.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp"]
                }
            },
            {
                path: "oamSettleBills",
                name: "oamSettleBills", // 结算账单(工商业)
                component: () => import("@/views/osp/finalAccount/oamSettleBills.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp"]
                }
            },
            {
                path: "specialCharges",
                name: "specialCharges", // 特殊费用列表(户用)
                component: () => import("@/views/osp/finalAccount/specialCharges.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp"]
                }
            },
            {
                path: "settleBills/detail",
                name: "settleBillsDetail", // 账单内电站列表
                component: () => import("@/views/osp/finalAccount/settleBillsDetail.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp"]
                }
            },
            {
                path: "settPositiveMotivation",
                name: "settPositiveMotivation", // 运维商政策兑现+(户用)列表
                component: () => import("@/views/osp/finalAccount/settPositiveMotivation.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp"]
                }
            },
            {
                path: "settNegativeIncentive",
                name: "settNegativeIncentive", // 运维商政策兑现-(户用)列表
                component: () => import("@/views/osp/finalAccount/settNegativeIncentive.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp"]
                }
            },
            {
                path: "oamPositiveMotivation",
                name: "oamPositiveMotivation", // 运维商政策兑现+(工商业)
                component: () => import("@/views/osp/finalAccount/oamPositiveMotivation.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp"]
                }
            },
            {
                path: "oamNegativeIncentive",
                name: "oamNegativeIncentive", // 运维商政策兑现-(工商业)
                component: () => import("@/views/osp/finalAccount/oamNegativeIncentive.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp"]
                }
            },
            {
                path: "ospContract",
                name: "ospContract", // 合同签署管理
                component: () => import("@/views/osp/contractManage/ospContract.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp", "light_osp_warranty"]
                }
            },
            {
                path: "ospRegister",
                name: "ospRegister", // 电签认证
                component: () => import("@/views/osp/contractManage/ospRegister.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp", "light_osp_warranty"]
                }
            },
            {
                path: "serviceQualification",
                name: "serviceQualification", // 服务资质管理
                component: () => import("@/views/osp/contractManage/serviceQualification.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp", "light_osp_warranty"]
                }
            },
            {
                path: "ospRegion",
                name: "ospRegion", // 服务区域
                component: () => import("@/views/osp/serviceArea/ospRegion.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp", "light_osp_warranty"]
                }
            },
            {
                path: "ospRegion/ospRegionInfo",
                name: "ospRegionInfo", // 多区域申请信息页
                component: () => import("@/views/osp/serviceArea/ospRegionInfo.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp"]
                }
            },
            {
                path: "ospMargin",
                name: "ospMargin", // 账户管理
                component: () => import("@/views/osp/accountManagement/ospMargin.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp", "light_osp_warranty"]
                }
            },
            {
                path: "ospMargin/ospPayment",
                name: "ospPayment", // 保证金
                component: () => import("@/views/osp/accountManagement/ospPayment.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp", "light_osp_warranty"]
                }
            },
            {
                path: "rentQuery",
                name: "rentQuery", // 电站租金查询列表
                component: () => import("@/views/osp/operation/rentQuery.vue"),
                meta: {
                    auth: true,
                    keepAlive: true,
                    limits: ["light_osp", "light_osp_warranty"]
                }
            },
            {
                path: "rentQuery/rentQueryDetail",
                name: "rentQueryDetail", // 电站租金查询详情
                component: () => import("@/views/osp/operation/rentQueryDetail.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp", "light_osp_warranty"]
                }
            },
            {
                path: "policyNumber",
                name: "policyNumber", // 保险管理-保单号查询
                component: () => import("@/views/osp/insurance/policyNumber.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp"]
                }
            },
            {
                path: "property",
                name: "property", // 保险管理-保险报案(财产一切险)
                component: () => import("@/views/osp/insurance/property.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp"]
                }
            },
            {
                path: "public",
                name: "public", // 保险管理-保险报案(公众责任险)
                component: () => import("@/views/osp/insurance/public.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp"]
                }
            },
            {
                path: "maintainance/list",
                name: "maintainanceList",
                component: () => import("@/views/osp/maintainance/list/index.vue"),
                meta: {
                    auth: true,
                    keepAlive: true,
                    limits: ["light_osp"]
                }
            },
            {
                path: "maintainance/apply",
                name: "maintainanceApply",
                component: () => import("@/views/osp/maintainance/list/apply.vue"),
                meta: {
                    auth: true,
                    keepAlive: true,
                    limits: ["light_osp"]
                }
            },
            {
                path: "maintainance/my",
                name: "maintainanceListMy",
                component: () => import("@/views/osp/maintainance/list/my.vue"),
                meta: {
                    auth: true,
                    keepAlive: true,
                    limits: ["light_osp"]
                }
            },
            {
                path: "maintainance/detail",
                name: "maintainanceDetail",
                component: () => import("@/views/osp/maintainance/detail.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp"]
                }
            },
            {
                path: "maintainance/solution/list",
                name: "maintainanceSolutionList",
                component: () => import("@/views/osp/maintainance/solution/list.vue"),
                meta: {
                    auth: true,
                    keepAlive: true,
                    limits: ["light_osp"]
                }
            },
            {
                path: "maintainance/solution/view",
                name: "maintainanceSolutionView",
                component: () => import("@/views/osp/maintainance/solution/view.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp"]
                }
            },
            {
                path: "maintainance/inspect/plan/list",
                name: "maintainanceInspectPlanList",
                component: () => import("@/views/osp/maintainance/inspect/plan/list.vue"),
                meta: {
                    auth: true,
                    keepAlive: true,
                    limits: ["light_osp"]
                }
            },
            {
                path: "maintainance/inspect/task/list",
                name: "maintainanceInspectTaskList",
                component: () => import("@/views/osp/maintainance/inspect/task/list.vue"),
                meta: {
                    auth: true,
                    keepAlive: true,
                    limits: ["light_osp"]
                }
            },
            {
                path: "maintainance/inspect/task/detail",
                name: "maintainanceInspectTaskDetail",
                component: () => import("@/views/osp/maintainance/inspect/task/detail.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp"]
                }
            },
            {
                path: "maintainance/report/index",
                name: "maintainanceReportIndex",
                component: () => import("@/views/osp/maintainance/report/index.vue"),
                meta: {
                    auth: true,
                    keepAlive: true,
                    limits: ["light_osp"]
                }
            },
            {
                path: "maintainance/message/index",
                name: "maintainanceMessageIndex",
                component: () => import("@/views/osp/maintainance/message/index.vue"),
                meta: {
                    auth: true,
                    keepAlive: true,
                    limits: ["light_osp"]
                }
            },
            {
                path: "maintainance/station/index",
                name: "maintainanceStationIndex",
                component: () => import("@/views/osp/maintainance/station/index.vue"),
                meta: {
                    auth: true,
                    keepAlive: true,
                    limits: ["light_osp"]
                }
            },
            {
                path: "maintainance/station/detail",
                name: "maintainanceStationDetail",
                component: () => import("@/views/osp/maintainance/station/detail.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp"]
                }
            },
            {
                path: "maintainance/exam/my",
                name: "maintainanceExamMy",
                component: () => import("@/views/osp/maintainance/exam/my.vue"),
                meta: {
                    auth: true,
                    keepAlive: true,
                    title: "我的考试",
                    limits: ["light_osp"]
                },
            },
            {
                path: "maintainance/exam/do",
                name: "maintainanceExamDo",
                component: () => import("@/views/osp/maintainance/exam/exam.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    title: "在线考试",
                    limits: ["light_osp"]
                },
            },
            {
                path: "maintainance/exam/score",
                name: "maintainanceExamScore",
                component: () => import("@/views/osp/maintainance/exam/score.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    title: "考试结果",
                    limits: ["light_osp"]
                },
            },
        ]
    },
    {
        path: "/discontinue",
        name: "discontinue", // 在线签约src\views\osp\contractManage\discontinue.vue
        component: () => import("@/views/osp/contractManage/discontinue.vue")
    }
];
