import Home from "@/views/index.vue";
export default [
    {
        path: "/",
        name: "ospSpare",
        component: Home,
        meta: {
            auth: false, // 是否需要登录
            keepAlive: true, // 是否缓存组件
            limits: ["light_osp"] // 权限码
        },
        children: [
            /* ============ 订单管理 ================== */
            {
                path: "serviceProviderOrder",
                name: "serviceProviderOrder", // 服务商订单
                component: () => import("@/views/ospSpare/orderManage/serviceProviderOrder/index.vue"),
                meta: {
                    auth: true,
                    keepAlive: false
                }
            },
            {
                path: "serviceProviderOrder/details",
                name: "details", // 服务商订单详情
                component: () => import("@/views/ospSpare/orderManage/serviceProviderOrder/details.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    limits: ["light_osp"]
                }
            },
            // {
            //     path: "PO",
            //     name: "PO", // 入库订单
            //     component: () => import("@/views/ospSpare/orderManage/PO/index.vue"),
            //     meta: {
            //         auth: true,
            //         keepAlive: false
            //     }
            // },
            {
                path: "lendOrder",
                name: "lendOrder", // 借件订单
                component: () => import("@/views/ospSpare/orderManage/lendOrder/index.vue"),
                meta: {
                    auth: true,
                    keepAlive: false
                }
            },
            {
                path: "lendOrderSpecial",
                name: "lendOrderSpecial", // 借件订单
                component: () => import("@/views/ospSpare/orderManage/lendOrderSpecial/index.vue"),
                meta: {
                    auth: true,
                    keepAlive: false
                }
            },
            // {
            //     path: "orderExecutionView",
            //     name: "orderExecutionView", // 订单执行查看
            //     component: () => import("@/views/ospSpare/orderManage/orderExecutionView/index.vue"),
            //     meta: {
            //         auth: true,
            //         keepAlive: false
            //     }
            // },
            {
                path: "serviceAllot",
                name: "serviceAllot", // 服务商调拨
                component: () => import("@/views/ospSpare/orderManage/serviceAllot/index.vue"),
                meta: {
                    auth: true,
                    keepAlive: false
                }
            },
            {
                path: "serviceAllotConfirm",
                name: "serviceAllotConfirm", // 服务商调拨-确认
                component: () => import("@/views/ospSpare/orderManage/serviceAllotConfirm/index.vue"),
                meta: {
                    auth: true,
                    keepAlive: false
                }
            },
            /* ============ 入库管理 ================== */
            {
                path: "serviceStock",
                name: "serviceStock", // 服务商入库
                component: () => import("@/views/ospSpare/stockManage/serviceStock/index.vue"),
                meta: {
                    auth: true,
                    keepAlive: false
                }
            },
            // {
            //     path: "returnSigned",
            //     name: "returnSigned", // 退返件签收
            //     component: () => import("@/views/ospSpare/stockManage/returnSigned/index.vue"),
            //     meta: {
            //         auth: true,
            //         keepAlive: false
            //     }
            // },
            // {
            //     path: "supplierStock",
            //     name: "supplierStock", // 供应商入库
            //     component: () => import("@/views/ospSpare/stockManage/supplierStock/index.vue"),
            //     meta: {
            //         auth: true,
            //         keepAlive: false
            //     }
            // },
            {
                path: "damageList",
                name: "damageList", // 货损货差列表
                component: () => import("@/views/ospSpare/stockManage/damageList/index.vue"),
                meta: {
                    auth: true,
                    keepAlive: false
                }
            },
            {
                path: "engineerStock",
                name: "engineerStock", // 工程师备件入库
                component: () => import("@/views/ospSpare/stockManage/engineerStock/index.vue"),
                meta: {
                    auth: true,
                    keepAlive: false
                }
            },
            /* ============ 出库管理 ================== */
            {
                path: "returnStockOut",
                name: "returnStockOut", // 退返出库
                component: () => import("@/views/ospSpare/stockOutManage/returnStockOut/index.vue"),
                meta: {
                    auth: true,
                    keepAlive: false
                }
            },
            // {
            //     path: "supplierStockOut",
            //     name: "supplierStockOut", // 供应商出库
            //     component: () => import("@/views/ospSpare/stockOutManage/supplierStockOut/index.vue"),
            //     meta: {
            //         auth: true,
            //         keepAlive: false
            //     }
            // },
            {
                path: "expressCheck",
                name: "expressCheck", // 快递路由查询
                component: () => import("@/views/ospSpare/stockOutManage/expressCheck/index.vue"),
                meta: {
                    auth: true,
                    keepAlive: false
                }
            },
            {
                path: "serviceStockOut",
                name: "serviceStockOut", // 服务商出库
                component: () => import("@/views/ospSpare/stockOutManage/serviceStockOut/index.vue"),
                meta: {
                    auth: true,
                    keepAlive: false
                }
            },
            /* ============ 库存管理 ================== */
            {
                path: "repertoryResize",
                name: "repertoryResize", // 库存调整
                component: () => import("@/views/ospSpare/repertoryManage/repertoryResize/index.vue"),
                meta: {
                    auth: true,
                    keepAlive: false
                }
            },
            // {
            //     path: "currentRepertoryView",
            //     name: "currentRepertoryView", // 现有库存查看-供应商
            //     component: () => import("@/views/ospSpare/repertoryManage/currentRepertoryView/index.vue"),
            //     meta: {
            //         auth: true,
            //         keepAlive: false
            //     }
            // },
            {
                path: "currentRepertoryViewService",
                name: "currentRepertoryViewService", // 现有库存查看-服务商
                component: () => import("@/views/ospSpare/repertoryManage/currentRepertoryViewService/index.vue"),
                meta: {
                    auth: true,
                    keepAlive: false
                }
            },
            {
                path: "repertorySet",
                name: "repertorySet", // 安全库存设置
                component: () => import("@/views/ospSpare/repertoryManage/repertorySet/index.vue"),
                meta: {
                    auth: true,
                    keepAlive: false
                }
            },
            /* ============ 财务管理 ================== */
            {
                path: "advanceManage",
                name: "advanceManage", // 备件保证金
                component: () => import("@/views/ospSpare/financeManage/advanceManage/index.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                }
            },
            {
                path: "billFlowQuery",
                name: "billFlowQuery", // 账单流水查询
                component: () => import("@/views/ospSpare/financeManage/billFlowQuery/index.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                }
            },
            {
                path: "buybackManage",
                name: "buybackManage", // 运维商备件回购
                component: () => import("@/views/ospSpare/financeManage/buybackManage/index.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                }
            },
        ]
    }
];
