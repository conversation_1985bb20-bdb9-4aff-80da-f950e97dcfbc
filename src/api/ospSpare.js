const {
    get,
    post
} = require("@/axios/http.js");
/* 光伏备案管理公用接口 */
export default {
    // 获取服务商
    getWarehouse: (params) => {
        return post("/merchant/repairs/cdWarehouse/list", params, 1)
    },
    // 查询当前登录对应的供应商id
    querySupp: (url) => {
        return get("/merchant/repairs/cdWarehouse/getCdWarehouseByMemberID?type=1&memberId=" + url)
    },
    // 服务订单新增明细列表
    getDetailList: (url, params) => {
        return post("/merchant/repairs/cdMaterial/getPage?page=" + url.page + "&rows=" + url.rows, params, 1)
    },
    // 查询所有备件信息
    getMaterialList: (params) => {
        return post("/merchant/repairs/cdMaterial/getList", params, 1, true)
    }
}
