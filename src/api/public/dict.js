import { Message } from "view-design";
import { get } from "@/axios/http.js";
import axios from "axios";

// * 通用字典请求
export const getDicts = async (logic, name) => {
    try {
        axios.defaults.headers["scope"] = "nh_pv_h5";

        const res = await get(`/dictapi`, {logic, name});
        if (res.data.success) {
            return res.data.result
        } else {
            Message.error(res.message);
        }
    } catch (error) {
        Message.error(name + " 数据获取失败，请联系管理员处理");
    }
};
