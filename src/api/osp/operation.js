const {
    get,
    post,
    put,
    deletes
} = require("@/axios/http.js");

// 工单备件
export default {
    // 获取列表
    getList: (pageText, params) => {
        return post("/merchant/repairs/woPart/orderWoPartPage" + pageText, params, 1)
    },
    // 申请前校验
    applyPartCheck: (params) => {
        return post("/merchant/repairs/woPart/applyPartCheck", params, 1)
    },
    // 申请
    applyPart: (params) => {
        return post("/merchant/repairs/woPart/applyPart ", params, 1)
    },
    // 登记校验
    sparePartsRegistrationCheck: (params) => {
        return post("/merchant/repairs/woPart/sparePartsRegistrationCheck", params, 1)
    },
    // 登记 -保存
    sparePartsRegistration: (params) => {
        return post("/merchant/repairs/woPart/sparePartsRegistration", params, 1)
    },
    //登记-提交
    sparePartsRegistrationSubmit: (params) => {
        return post("/merchant/repairs/woPart/sparePartsRegistrationSubmit", params, 1)
    },
    // 备件结单
    newAndOldPartsCreate: (url) => {
        return get("/merchant/repairs/spTranSn/newAndOldPartsCreate?serviceInfoId=" + url)
    },
    //快递路由查询
    ExpressRouting: (params) => {
        return post("/merchant/repairs/spExpressRouting/list",params,1)
    },
    // 服务订单新增加载
    addAddressLoading: (url) => {
        return get("/merchant/repairs/cdWarehouseAddress/getCdWarehouseAndAddressByMemberID?type=3&memberId=" + url)
    },
    //上传附件提交
    saveOrderWoPartFile: (params) => {
        return post("/merchant/repairs/woPart/saveOrderWoPartFile",params,1)
    },
}
