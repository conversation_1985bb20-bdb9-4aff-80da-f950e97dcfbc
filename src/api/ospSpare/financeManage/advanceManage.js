const {
    get,
    post,
    put,
    deletes
} = require("@/axios/http.js");

export default {
    // 获取列表
    getList: (pageText, params) => {
        return post("/merchant/repairs/spAdvancePayment/getPage" + pageText, params, 1)
    },
    // 新增
    add: (params) => {
        return post("/merchant/repairs/spAdvancePayment/addSpAdvancePayment", params, 1)
    },
    // 编辑
    update: (params) => {
        return post("/merchant/repairs/spAdvancePayment/updateSpAdvancePayment", params, 1)
    },
    // 提交
    submit: (params) => {
        return post("/merchant/repairs/spAdvancePayment/submitSpAdvancePayment", params, 1)
    },
    // 删除
    delete: (params) => {
        return post("/merchant/repairs/spAdvancePayment/delSpAdvancePayment", params, 1)
    },
    // 导出
    export: (params) => {
        return post("/merchant/repairs/spAdvancePayment/export", params, 3)
    }
}
