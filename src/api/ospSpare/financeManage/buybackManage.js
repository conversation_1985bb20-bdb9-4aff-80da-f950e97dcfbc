const {
    get,
    post
} = require("@/axios/http.js");

export default {
    // 获取列表
    getList: (url, params) => {
        return post("/merchant/repairs/spOrderBack/getPage?page=" + url.page + "&rows=" + url.rows, params, 1)
    },
    // 提交
    submitApi: (params) => {
        return post("/merchant/repairs/spOrderBack/submitOrder", params, 1)
    },
    // 修改
    editApi: (params) => {
        return post("/merchant/repairs/spOrderBack/update", params, 1)
    },
    uploadFileApi: (params) => {
        return post("/merchant/repairs/spOrderBack/submitOrderPay", params, 1)
    },
    // 详情
    getDetailApi: (params) => {
        return get("/merchant/repairs/spOrderBack/getInfoById", params, 3)
    },
    //     导出
    exportList: (params) => {
        return get("/merchant/repairs/spOrderBack/getPage/export", params, 3)
    },
}
