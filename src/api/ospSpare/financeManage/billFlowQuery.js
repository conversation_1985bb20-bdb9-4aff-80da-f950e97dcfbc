const {
    get,
    post,
    deletes
} = require("@/axios/http.js");

export default {
    // 获取服务商下拉数据接口
    queryRoute: (params) => {
        return post("/merchant/repairs/cdWarehouse/list", params, 1)
    },
    // 获取列表
    getList: (url, params) => {
        return post("/merchant/repairs/spForegift/getPage?page=" + url.page + "&rows=" + url.rows, params, 1)
    },
    // 导出
    exportList: (params) => {
        return get("/merchant/repairs/spForegift/export", params, 3)
    }
}
