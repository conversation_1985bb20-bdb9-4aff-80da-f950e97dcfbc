const {
    get,
    post,
    put,
    deletes
} = require("@/axios/http.js");

export default {
    // 供应商
    getWarehouse: (params, pageStr) => {
        return post("/merchant/repairs/cdWarehouse/getPage" + pageStr, params, 1)
    },
    // 备件列表
    getMaterial: (params, pageStr) => {
        return post("/merchant/repairs/cdMaterial/getPage" + pageStr, params, 1)
    },
    // 数据字典
    getDict: (params) => {
        return post("/merchant/repairs/cdDictData/getList", params, 1)
    },
}
