const {
    get,
    post,
    put,
    deletes
} = require("@/axios/http.js");

export default {
    // 获取列表
    getList: (url, params) => {
        return post("/merchant/repairs/spTranSnDetailSh/getPage?page=" + url.page + "&rows=" + url.rows, params, 1)
    },
    export: (params) => {
        return post("/merchant/repairs/spTranSnDetailSh/export?" , params, 3)
    },
    //货损审批
    approve: (params) => {
        return post("/merchant/repairs/spTranSnDetailSh/approve", params, 1);
    },
    //货损编辑
    update: (params) => {
        return post("/merchant/repairs/spTranSnDetailSh/update", params, 1);
    },
    //撤销申诉
    revokeAppeal: (params) => {
        return post("/merchant/repairs/spTranSnDetailSh/revokeAppeal", params, 1);
    },
}
