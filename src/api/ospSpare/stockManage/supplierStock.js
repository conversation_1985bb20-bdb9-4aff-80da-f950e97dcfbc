const {
    get,
    post,
    put,
    deletes
} = require("@/axios/http.js");

export default {
    // 获取列表
    getList: (pageText, params) => {
        return post("/merchant/repairs/spTran/getPage" + pageText, params, 1)
    },
    // 编辑
    update: (params) => {
        return post("/merchant/repairs/spTran/update", params, 1)
    },
    // 关单入库
    customsDocumentsWarehouse: (params) => {
        return post("/merchant/repairs/spTran/customsDocumentsWarehouse", params, 1)
    },
    // 详情
    getInfo: (params) => {
        return post("/merchant/repairs/spTran/getInfo", params, 1)
    },
    //     导出
    exportList: (params) => {
        return get("/merchant/repairs/spTran/getPage/export", params, 3)
    },
}
