const {
    get,
    post,
    deletes
} = require("@/axios/http.js");
export default {
    // 工程师备件入库 获取列表
    getList: (url, params) => {
        return post("/merchant/repairs/spTranSn/getNewAndOldPartsPage?page=" + url.page + "&rows=" + url.rows, params, 1)
    },
    //工程师备件入库 -详情
    orderDetail: (params) => {
        return post("/merchant/repairs/spTranSn/getInfo",params,1)
    },
    //工程师备件入库 关单入库
    closeTranSnIn: (params) => {
        return post("/merchant/repairs/spTranSn/newAndOldPartsClose",params,1)
    },
}
