const {
    get,
    post,
    deletes
} = require("@/axios/http.js");
export default {
    // 退返件签收 获取列表
    getList: (url, params) => {
        return post("/merchant/repairs/spOldbackLists/getQsPage?page=" + url.page + "&rows=" + url.rows, params, 1)
    },
    signIn: (params) => {
        return post("/merchant/repairs/spOldbackLists/coreReceipt", params, 1)
    },
    //退返单拒收
    rejectOldback: (params) => {
        return post("/merchant/repairs/spOldbackLists/rejectOldback", params, 1)
    },
    //服务商库存调拨 -详情
    orderDetail: (params) => {
        return get("/merchant/repairs/spOldbackLists/getInfoById", params);
    },
    //  导出
    exportList: (params) => {
        return get("/merchant/repairs/spOldbackLists/coreReceiptExport", params, 3)
    },

}

