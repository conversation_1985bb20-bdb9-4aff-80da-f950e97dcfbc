const {
    get,
    post,
    deletes
} = require("@/axios/http.js");

export default {
    // 获取列表
    getList: (url, params) => {
        return post("/merchant/repairs/spOldbackLists/getPage?page=" + url.page + "&rows=" + url.rows, params, 1)
    },
    // 退返件出库-订单详情
    orderDetail: (url) => {
        return get("/merchant/repairs/spOldbackLists/getInfoById?id=" + url)
    },
    //退返件出库-确认退返提交
    subStockOut: (url) => {
        return get("/merchant/repairs/spOldbackLists/confirmReturn?id=" + url)
    },
    //退返件出库-编辑
    updateDetail: (params) => {
        return post("/merchant/repairs/spOldbackLists/update",params,1)
    },
    updateTransportDetail: (params) => {
        return post("/merchant/repairs/spOldbackLists/updateTransport",params,1)
    },
    //物流公司查询
    getExpressCompany: (params) => {
        return post("/merchant/repairs/spExpressCompany/list",params,1)
    },
//     导出
    exportList: (params) => {
        return get("/merchant/repairs/spOldbackLists/export",params,3)
    },
}
