const {
    get,
    post,
    deletes
} = require("@/axios/http.js");
export default {
    // 服务商出库 获取列表
    getList: (url, params) => {
        return post("/merchant/repairs/spTranSn/getPage?page=" + url.page + "&rows=" + url.rows, params, 1)
    },
    //服务商出库 -详情
    orderDetail: (params) => {
        return post("/merchant/repairs/spTranSn/getInfo",params,1)
    },
    //服务商出库 关单入库
    closeTranSnIn: (params) => {
        return post("/merchant/repairs/spTranSn/closeTranSnOut",params,1)
    },

}
