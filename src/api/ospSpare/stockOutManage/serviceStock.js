const {
    get,
    post,
    deletes
} = require("@/axios/http.js");

export default {
    //服务商入库- 获取列表
    getList: (url, params) => {
        return post("/merchant/repairs/spTranSn/queryPageTranSnIn?page=" + url.page + "&rows=" + url.rows, params, 1)
    },
    //服务商入库-详情
    orderDetail: (params) => {
        return post("/merchant/repairs/spTranSn/getInfo",params,1)
    },
    // 服务商入库 修改
    updateDetail: (params) => {
        return post("/merchant/repairs/spTranSn/update",params,1)
    },
    //服务商入库 关单入库
    closeTranSnIn: (params) => {
        return post("/merchant/repairs/spTranSn/closeTranSnIn",params,1)
    },
    // 借件入库
    lendOrderPutInApi: (params) => {
        return post("/merchant/repairs/spTranSn/updateButchNum",params,1)
    },
    //     导出
    exportList: (params) => {
        return get("/merchant/repairs/spTranSn/queryPageTranSnIn/export", params, 3)
    },
}
