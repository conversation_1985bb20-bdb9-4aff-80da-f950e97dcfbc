const {
    get,
    post,
    deletes
} = require("@/axios/http.js");

export default {
    // 获取列表
    getList: (url, params) => {
        return post("/merchant/repairs/spTran/out/getPage?page=" + url.page + "&rows=" + url.rows, params, 1)
    },
    // 供应商出库订单详情
    orderDetail: (url) => {
        return get("/merchant/repairs/spTran/out/outInfoBytranId?id=" + url)
    },
    // 供应商出库 编辑
    updateDetail: (params) => {
        return post("/merchant/repairs/spTran/updateDetail",params,1)
    },
    // 供应商出库-出库关单 提交（单条操作）
    subStockOut: (url) => {
        return get("/merchant/repairs/spTran/out/outbound?id=" + url)
    },
    // 供应商出库-更新物流 提交（单条操作）
    changeAddress: (params) => {
        return post("/merchant/repairs/spTran/updateInfo",params,1)
    },
    //  供应商出库-取消出库
    cancelStockOut: (url) => {
        return get("/merchant/repairs/spTran/out/cancelOutbound?id=" + url)
    },
    //供应商出库-路由信息查询
    ExpressRouting: (params) => {
        return post("/merchant/repairs/spExpressRouting/list",params,1)
    },
    //供应商出库-导出
    exportList: (params) => {
        return get("/merchant/repairs/spTran/out/export",params,3)
    },
    //物流公司查询
    getExpressCompany: (params) => {
        return post("/merchant/repairs/spExpressCompany/list",params,1)
    },
    // 获取服务商
    getWarehouse: (params) => {
        return post("/merchant/repairs/cdWarehouse/list", params, 1)
    },
}
