const {
    get,
    post
} = require("@/axios/http.js");

export default {
    // 列表
    getList: (url, params) => {
        return post("/merchant/repairs/spOrderBorrow/getPage?page=" + url.page + "&rows=" + url.rows, params, 1)
    },
    getListSupplier: (url, params) => {
        return post("/merchant/repairs/spOrderBorrow/getPageSupplier?page=" + url.page + "&rows=" + url.rows, params, 1)
    },
    // 详情
    getOrderDetail: (id) => {
        return get("/merchant/repairs/spOrderBorrow/getInfoById?id=" + id)
    },
    // 新增
    addOrder: (data) => {
        return post("/merchant/repairs/spOrderBorrow/save", data, 1)
    },
    // 编辑订单
    updateOrder: (data) => {
        return post("/merchant/repairs/spOrderBorrow/update", data, 1)
    },
    // 取消订单
    cancelOrder: (data) => {
        return post("/merchant/repairs/spOrderBorrow/cancel", data, 1)
    },
    // 删除订单
    delOrder: (data) => {
        return post("/merchant/repairs/spOrderBorrow/delete", data, 1)
    },
    //提交订单
    submitOrder: (data) => {
        return post("/merchant/repairs/spOrderBorrow/submitOrder", data, 1)
    },
    // 查询建站服务站
    getSupplierList: (data) => {
        return post("/merchant/repairs/spOrderBorrow/findProviderList", data, 1)
    },
    // 运维商认责
    operationAccountApi: (data) => {
        return post("/merchant/repairs/spOrderBorrow/ywsApproval", data, 1)
    },
    // 供应商认责
    supplierAccountApi: (data) => {
        return post("/merchant/repairs/spOrderBorrow/gysApproval", data, 1)
    },
    // 查询备件名称
    getmaterialListApi: (params) => {
        return post("/merchant/repairs/cdMaterial/getPage?page=" + 1 + "&rows=" + 100, params, 1)
    },
    // 运维单号查询
    getWoIdDetail: (pararm) => {
        return get("/merchant/repairs/woPart/getLightWorkOrderBySn", pararm)
    },
    //     导出
    exportList: (params) => {
        return get("/merchant/repairs/spOrderBorrow/orderBorrow/export", params, 3)
    },
    //     导出
   supplierExportList: (params) => {
        return get("/merchant/repairs/spOrderBorrow/orderBorrow/supplierExport", params, 3)
    },
    //sn单号校验
    findLinghtModelSn: (params) => {
        return post("/merchant/repairs/spOrderBorrow/findLinghtModelSn", params, 1)
    }
}
