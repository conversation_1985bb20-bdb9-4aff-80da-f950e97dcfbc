const {
    get,
    post,
    deletes
} = require("@/axios/http.js");

export default {
    // 服务商库存调拨 获取列表
    getList: (url, params) => {
        return post("/merchant/repairs/spOrder/getServicePITPage?page=" + url.page + "&rows=" + url.rows, params, 1)
    },
    //服务商库存调拨 -详情
    orderDetail: (params) => {
        return post("/merchant/repairs/spOrder/getInfoServicePIT",params,1)
    },
    //新增-调出仓库下拉数据 接口
    queryRoute: (params) => {
        return post("/merchant/repairs/cdWarehouseRoute/queryRoute",params,1)
    },
    //新增 列表查询接口
    cdMaterial: (params) => {
        return post("/merchant/repairs/spStoreage/getStoreageTotal",params,1)
    },
    // 新增提交
    addServicePIT: (params) => {
        return post("/merchant/repairs/spOrder/addServicePIT",params,1)
    },
    //编辑提交
    updateServicePIT: (params) => {
        return post("/merchant/repairs/spOrder/updateServicePIT",params,1)
    },
    // 列表提交接口
    submitServicePIT: (params) => {
    return post("/merchant/repairs/spOrder/submitServicePIT",params,1)
    },
    //删除接口
    delServicePIT: (params) => {
        return post("/merchant/repairs/spOrder/delServicePIT",params,1)
    },
    // 查询当前登录对应的仓库id type1供应商 3服务商
    queryStoreId: (url) => {
        return get("/merchant/repairs/cdWarehouse/getCdWarehouseByMemberID?type=3&memberId=" + url)
    },
    // 调拨确认 提交
    approvalServicePIT: (params) => {
        return post("/merchant/repairs/spOrder/approvalServicePIT", params, 1)
    },
    //     导出
    exportList: (params) => {
        return get("/merchant/repairs/spOrder/getServicePITPage/export", params, 3)
    },
    //     导出 服务商调拨-确认
    exportListConfirm: (params) => {
        return get("/merchant/repairs/spOrder/getServicePITPage/export", params, 3)
    },
}
