const {
    get,
    post,
    deletes
} = require("@/axios/http.js");

export default {
    // 获取列表
    getList: (url, params) => {
        return post("/merchant/repairs/spOrder/orderExecutionPage?page=" + url.page + "&rows=" + url.rows, params, 1)
    },
    // 取消订单
    cancelOrder: (params) => {
        return post("/merchant/repairs/spOrder/singleCancel", params, 1)
    },
    // 匹配订单
    orderMatching: (url) => {
        return get("/merchant/repairs/spOrder/ordeMatching?id=" + url)
    },
    // 查询当前登录对应的供应商id
    querySupp: (url) => {
        return get("/merchant/repairs/cdWarehouse/getCdWarehouseByMemberID?type=1&memberId=" + url)
    },
//     导出
    exportList: (params) => {
        return get("/merchant/repairs/spOrder/orderExecution/export", params, 3)
    },
}
