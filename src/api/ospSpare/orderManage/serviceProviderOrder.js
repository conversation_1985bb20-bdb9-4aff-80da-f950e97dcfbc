const {
    get,
    post,
    deletes
} = require("@/axios/http.js");

export default {
    // 获取列表
    getList: (url, params) => {
        return post("/merchant/repairs/spOrder/getPage?page=" + url.page + "&rows=" + url.rows, params, 1)
    },
    // 订单删除
    delOrder: (params) => {
        return post("/merchant/repairs/spOrder/delete", params, 1)
    },
    // 整单取消
    cancelOrder: (params) => {
        return post("/merchant/repairs/spOrder/serviceCancelOrder", params, 1)
    },
    // 订单提交
    subOrder: (params) => {
        return post("/merchant/repairs/spOrder/submitOrder", params, 1)
    },
    // 服务订单新增加载
    addLoading: (url) => {
        return get("/merchant/repairs/cdWarehouse/getCdWarehouseByMemberID?type=3&memberId=" + url)
    },
    // 服务订单新增加载
    addAddressLoading: (url) => {
        return get("/merchant/repairs/cdWarehouseAddress/getCdWarehouseAndAddressByMemberID?type=3&memberId=" + url)
    },
    // 服务订单新增明细列表
    getDetailList: (url, params) => {
        return post("/merchant/repairs/cdMaterial/getPage?page=" + url.page + "&rows=" + url.rows, params, 1)
    },
    // 服务订单新增保存
    addOrderSave: (params) => {
        return post("/merchant/repairs/spOrder/save", params, 1)
    },
    // 服务订单编辑保存
    editOrderSave: (params) => {
        return post("/merchant/repairs/spOrder/update", params, 1)
    },
    // 服务订单编辑加载
    editLoading: (url) => {
        return get("/merchant/repairs/spOrder/getUpdateInfoById?id=" + url)
    },
    // 服务订单详情
    orderDetail: (url) => {
        return get("/merchant/repairs/spOrder/getInfoById?id=" + url)
    },
    // 导出
    exportList: (params) => {
        return get("/merchant/repairs/spOrder/export", params, 3)
    },
    // 导入模板下载
    exportTemplateList: (params) => {
        return get("/merchant/repairs/spOrder/downTemplate", null, 3)
    }
}
