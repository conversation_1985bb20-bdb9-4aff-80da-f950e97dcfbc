const {
    get,
    post,
    put,
    deletes
} = require("@/axios/http.js");

export default {
    // 获取列表
    getList: (pageText, params) => {
        return post("/merchant/repairs/spPurchase/getPage" + pageText, params, 1)
    },
    // 新增
    add: (params) => {
        return post("/merchant/repairs/spPurchase/addSpPurchase", params, 1)
    },
    // 编辑
    update: (params) => {
        return post("/merchant/repairs/spPurchase/updateSpPurchase", params, 1)
    },
    // 采购订单提交
    submit: (params) => {
        return post("/merchant/repairs/spPurchase/submit", params, 1)
    },
    // 详情
    getInfo: (params) => {
        return post("/merchant/repairs/spPurchase/getInfo", params, 1)
    },
    // 删除
    delete: (params) => {
        return post("/merchant/repairs/spPurchase/delete", params, 1)
    },
    // 导出
    export: (params) => {
        return get("/merchant/repairs/spPurchase/export", params, 3)
    }
}
