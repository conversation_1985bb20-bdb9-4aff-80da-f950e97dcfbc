const {
    get,
    post,
    deletes
} = require("@/axios/http.js");
export default {
    //查询所有地址
    getList: ( params) => {
        return post("/merchant/repairs/cdWarehouseAddress/getAllList", params, 1)
    },
    //新增地址
    addList: ( params) => {
        return post("/merchant/repairs/cdWarehouseAddress/save", params, 1)
    },
    // 编辑加载
    editLoading: (url) => {
        return get("/merchant/repairs/cdWarehouseAddress/getInfoById?id=" + url)
    },
    //设为默认接口
    updateDefault: ( params) => {
        return post("/merchant/repairs/cdWarehouseAddress/updateDefault", params, 1)
    },
    //删除地址
    delete: ( params) => {
        return post("/merchant/repairs/cdWarehouseAddress/delete", params, 1)
    },
}
