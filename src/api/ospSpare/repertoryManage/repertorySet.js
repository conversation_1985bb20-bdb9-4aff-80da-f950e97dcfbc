const {
    get,
    post,
    put,
    deletes
} = require("@/axios/http.js");

export default {
    // 获取列表
    getList: (url, params) => {
        return post("/merchant/repairs/spWarehouseAttach/page?page=" + url.page + "&rows=" + url.rows, params, 1)
    },
    // 导出
    exportList: (params) => {
        return get("/merchant/repairs/spWarehouseAttach/export", params, 3)
    },
    // 导入模板
    exportTemplateList: () => {
        return get("/merchant/repairs/spWarehouseAttach/downTemplate", null, 3)
    },
    // 新增
    addList: (params) => {
        return put("/merchant/repairs/spWarehouseAttach/save", params)
    },
    // 查询当前登录对应的仓库id type1供应商 3服务商
    queryStoreId: (url) => {
        return get("/merchant/repairs/cdWarehouse/getCdWarehouseByMemberID?type=3&memberId=" + url)
    },
    // 获取列表详情
    getDetail: (url) => {
        return get("/merchant/repairs/spWarehouseAttach/getInfoById?id=" + url)
    },
    // 编辑订单保存
    editSave: (params) => {
        return post("/merchant/repairs/spWarehouseAttach/update", params, 1)
    },
    // 删除
    delList: (url) => {
        return get("/merchant/repairs/spWarehouseAttach/delete?id=" + url)
    }
}
