const {
    get,
    post,
    deletes
} = require("@/axios/http.js");

export default {
    //列表查询
    getList: (url,params) => {
        return post("/merchant/repairs/spStoreage/getPage?page=" + url.page + "&rows=" + url.rows, params, 1)
    },
    //现有库存查看-详情
    orderDetail: (params) => {
        return post("/merchant/repairs/spStoreage/getInfoById" , params, 1);
    },
    //  导出
    exportList: (params) => {
        return get("/merchant/repairs/spStoreage/export", params, 3)
    },
}
