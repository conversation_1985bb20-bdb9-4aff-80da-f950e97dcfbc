const {
    get,
    post,
    put,
    deletes
} = require("@/axios/http.js");

export default {
    // 获取列表
    getList: (url, params) => {
        return post("/merchant/repairs/stockAdjustment/getPage?page=" + url.page + "&rows=" + url.rows, params, 1)
    },
    // 新增明细列表
    getDetailList: (url, params) => {
        return post("/merchant/repairs/cdMaterial/getPage?page=" + url.page + "&rows=" + url.rows, params, 1)
    },
    // 新增加载
    addLoading: (url) => {
        return get("/merchant/repairs/cdWarehouse/getCdWarehouseByMemberID?type=3&memberId=" + url)
    },
    // 供应商库存调整新增加载
    addLoadingSupplier: (url) => {
        return get("/merchant/repairs/cdWarehouse/getCdWarehouseByMemberID?type=1&memberId=" + url)
    },

    //新增 列表查询接口
    cdMaterial: (params) => {
        return post("/merchant/repairs/spStoreage/getStoreage",params,1)
    },
    // 新增保存
    addSave: (params) => {
        return post("/merchant/repairs/stockAdjustment/addStockAdjustment", params, 1)
    },
    // 编辑保存
    editSave: (params) => {
        return post("/merchant/repairs/stockAdjustment/updateStockAdjustment", params, 1)
    },
    // 详情接口
    getInfo: (params) => {
        return post("/merchant/repairs/stockAdjustment/getInfo", params, 1)
    },
    //提交
    submitStockAdjustment: (params) => {
        return post("/merchant/repairs/stockAdjustment/submitStockAdjustment", params, 1)
    },
    // 删除
    delStockAdjustment: (params) => {
        return post("/merchant/repairs/stockAdjustment/delStockAdjustment", params, 1)
    },
    //     导出
    exportList: (params) => {
        return get("/merchant/repairs/stockAdjustment/getPage/export", params, 3)
    },
    //     导出-供应商
    exportListGYS: (params) => {
        return get("/merchant/repairs/stockAdjustment/getPage/export", params, 3)
    },
}
