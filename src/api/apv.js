const { get, post } = require("@/axios/http.js");

// * 项目签约校验(在该项目下必须存在已经预审通过的承租人)

export const checkBeforeSign = (params) => {
    return get("/merchant/light/project/checkBeforeSign.do", params);
};

// * TOB用户登记
export const addBusinessBody = (params) => {
    return post("/merchant/light/owner/addBusinessBody.do", params, 1);
};

// * 商户通提交现场验收整改信息
export const commitOffLineInfo = (params) => {
    return post("/merchant/OffLineStationFlow/commitOffLineInfo", params, 1);
};

// * 商户通修改现场验收整改信息
export const editOffLineInfo = (params) => {
    return post("/merchant/OffLineStationFlow/editOffLineInfo", params, 1);
};

// * 现场验收列表查询
export const queryMerchantPageOffLine = (params) => {
    return get("/merchant/OffLineStationFlow/queryMerchantPageOffLine.do", params);
};

// * 获取四件套方案设计接口
export const getAllPlanDesignByConfigType = (params) => {
    return get("/merchant/light/planDesign/getAllPlanDesignByConfigType", params);
};

// * 根据名称查询仓库
export const findStoreByName = (params) => {
    return get("/merchant/light/store/auclon/findByName.do", params);
};

// * 修改子账号服务区域
export const editSubRegion = (params) => {
    return post("/merchant/light/spAccount/editRegion.do", params, 1);
};

// * 查询子账号服务区域
export const subRegionList = (params) => {
    return get("/merchant/light/spAccount/regionList.do", params);
};

// * 安装提升政策
export const findInstallAdvancePolicy = (params) => {
    return get("/merchant/light/sp/findInstallAdvancePolicy", params);
};

// * 获取待下单服务区域
export const findOrderRegion = (params) => {
    return get("/merchant/light/sp/findOrderRegion", params);
};

// * 获取奥客隆待下单服务区域
export const findAuclonOrderRegion = (params) => {
    return get("/merchant/light/sp/auclon/findOrderRegion", params);
};

// * 获取图片组排版布局
export const getImgTemplate = (params) => {
    return get("/merchant/light/station/getImgTemplate", params);
};

// * 获取图片组排版布局pro
export const getImgTemplatePro = (params) => {
    return get("/merchant/light/station/getImgTemplatePro", params);
};

// * 基础政策
export const findSpPolicy = (params) => {
    return get("/merchant/light/sp/findPolicy", params);
};

// 判断服务商是否做整套
export const checkSpDoFourItem = (params) => {
    return get("/merchant/light/sp/planConfig/checkSpDoFourItem", params);
};

// * 结算汇总列表
export const settleSumList = (params) => {
    return get("/merchant/light/settle/sum/list.do", params);
};

// * 结算汇总列表导出
export const settleSumExport = (params) => {
    return get("/merchant/light/settle/sum/export.do", params, 3);
};

// * 饼状图查询
export const settleSpCount = (params) => {
    return get("/merchant/light/settle/sum/spCount.do", params);
};

// * 保证金发货额度
export const queryLimit = (params) => {
    return get("/merchant/light/deposit/queryLimit.do", params);
};

// * 原材料支付
export const materialPay = (params) => {
    return post("/merchant/sp/order/pay.do", params);
};

// 原材料销售列表
export const materialList = (params) => {
    return get("/merchant/sp/order/list.do", params);
};
// 原材料修改
export const materialEdit = (params) => {
    return post("/merchant/sp/order/edit.do", params, 1);
};
// 原材料取消订单
export const materialCancel = (params) => {
    return post("/merchant/sp/order/cancel.do", params);
};
// 原材料申请购买
export const materialApply = (params) => {
    return post("/merchant/sp/order/apply.do", params, 1);
};
// 原材料电站详情
export const materialStationInfo = (params) => {
    return get("/merchant/sp/order/stationInfo.do", params);
};
// 原材料够买详情
export const materialDetail = (params) => {
    return get("/merchant/sp/order/detail.do", params);
};

// 获取组件编码信息
export const moduleSnHistory = (params) => {
    return get("/merchant/light/moduleSn/getHistory", params);
};

// 组件编码导出
export const moduleSnExport = (params) => {
    return get("/merchant/light/moduleSn/export", params, 3);
};

// 获取组件编码信息
export const findSummaryInfo = (params) => {
    return get("/merchant/light/planConfig/findSummaryInfo", params);
};

// 获取组件编码信息
export const planConfigFlag = (params) => {
    return get("/merchant/light/sp/planConfig/findBy", params);
};
// 获取街道列表
export const getStreetList = (params) => {
    return get("/merchant/light/sp/findStreet", params);
};
// 新商政策
export const findNewMerchantPolicy = (params) => {
    return get("/merchant/light/newMerchantPolicy/list.do", params);
};
// 新商政策结算明细-汇总
export const findNewMerchantReward = (params) => {
    return get("/merchant/light/newMerchantReward/list.do", params);
};
// 新商政策结算汇总列表详情导出
export const exportNewMerchantReward = (params) => {
    return get("/merchant/light/newMerchantReward/export.do", params, 3);
};
// 新商政策结算明细-明细
export const findNewMerchantRewardDetail = (params) => {
    return get("/merchant/light/newMerchantRewardDetail/list.do", params);
};
// 新商政策结算明细列表详情导出
export const exportNewMerchantRewardDetail = (params) => {
    return get("/merchant/light/newMerchantRewardDetail/export.do", params, 3);
};
// orc识别
export const  getOcrTrigger= (params) => {
	return get("/merchant/light/owner/oCRTrigger", params);
}
// 记录身份证ORC识别结果
export const cresateOrUpdateIdCardOCR = (params) => {
	return post("/merchant/light/owner/cresateOrUpdateIdCardOCR", params, 1);
}
// 根据身份证号查询ORC识别记录
export const getOCRRecordByIdCard = (params) => {
	return get("/merchant/light/owner/getOCRRecordByIdCard", params);
}
// 根据银行卡号查询ORC识别记录
export const getOCRRecordByBankNo = (params) => {
	return get("/merchant/light/owner/getOCRRecordByBankNo", params);
}

// 电站回购-列表
export const getEnrolmentSupplyList = (params) => {
    return get("/merchant/stationRepurchase/list.do", params);
};
// 电站回购-详情
export const getEnrolmentSupplyDetail = (params) => {
    return get("/merchant/stationRepurchase/detail.do", params);
};
// 电站回购-取消订单
export const cancelEnrolmentSupply = (params) => {
    return post("/merchant/stationRepurchase/cancel.do", params);
};
// 电站回购-申请购买反显
export const getAddEnrolmentSupplyInfo = (params) => {
    return get("/merchant/stationRepurchase/addPrepareInfo.do", params);
};
// 电站回购-申请购买
export const addEnrolmentSupply = (params) => {
    return post("/merchant/stationRepurchase/add.do", params, 1);
};
// 电站回购-修改购买申请
export const updateAddEnrolmentSupply = (params) => {
    return post("/merchant/stationRepurchase/update.do", params, 1);
};
// 电站回购-查看签署购售协议
export const getBuyEnrolmentSupplyUrl = (params) => {
    return get("/merchant/stationRepurchase/buy/getUrl.do", params);
};
// 电站回购-获取签署购售协议地址
export const getBuyEnrolmentSupplySignLink = (params) => {
    return get("/merchant/stationRepurchase/buy/getSignLink.do", params);
};
// 电站回购-查看签署验收单
export const getAcceptBuyEnrolmentSupplyUrl = (params) => {
    return get("/merchant/stationRepurchase/acceptance/getUrl.do", params);
};
// 电站回购-获取签署验收单地址
export const getAcceptBuyEnrolmentSupplySignLink = (params) => {
    return get("/merchant/stationRepurchase/acceptance/getSignLink.do", params);
};
// 电站回购-去支付
export const payEnrolmentSupply = (params) => {
    return post("/merchant/stationRepurchase/pay.do", params, 1);
};

// CommonJS写法
export default {
    // 电站登记-ZH电站登记信息
    registCnnChnStation: (params) => {
        return post("/merchant/light/station/registCnnChnStation.do", params, 1);
    },
    // 电站登记-ZH电站登记信息修改
    updateCnChnStation: (params) => {
        return post("/merchant/light/station/updateCnChnStation.do", params, 1);
    },
    // 查询企业注册信息
    detail: (params) => {
        return get("/merchant/light/dcc/company/detail.do", params);
    },
    // 法大大企业认证
    fddAuth: (params) => {
        return post("/merchant/light/dcc/company/fddAuth.do", params);
    },
    // 法大大企业注册
    fddRegist: (params) => {
        return post("/merchant/light/dcc/company/fddRegist.do", params, 1);
    },
    // 服务商签合同
    sign: (params) => {
        return post("/merchant/light/dcc/company/sign.do", params);
    },
    // 开户行查询
    findMainBankInfo: (params) => {
        return get("/merchant/bank/findMainBankInfo.do", params);
    },
    // 支行省查询
    findProvinceBank: (params) => {
        return get("/merchant/bank/findProvinceBank.do", params);
    },
    // 支行市查询
    findCityBank: (params) => {
        return get("/merchant/bank/findCityBank.do", params);
    },
    // 支行县查询
    findRegionBank: (params) => {
        return get("/merchant/bank/findRegionBank.do", params);
    },
    // 支行查询
    findHouseBank: (params) => {
        return get("/merchant/bank/findHouseBank.do", params);
    },
    // 注册
    registSp: (params) => {
        return post("/merchant/light/sp/registSp.do", params, 1);
    },
    // 服务商修改
    updateSp: (params) => {
        return post("/merchant/light/sp/updateSp.do", params, 1);
    },
    // 工商业入驻注册
    registEPCSp: (params) => {
        return post("/merchant/light/sp/registEPCSp.do ", params, 1);
    },
    // 工商业入驻服务商修改
    updateEpcSp: (params) => {
        return post("/merchant/light/sp/updateEpcSp.do", params, 1);
    },
    // 获取服务商信息
    getSpInfo: (params) => {
        return get("/merchant/light/sp/getSpInfo.do", params);
    },
    // 获取户用服务商信息
    getCheckSpRegister: (params) => {
        return get("/merchant/light/sp/checkSpRegister", params);
    },
    // 获取工商业EPC服务商信息
    getCheckEpcSpRegister: (params) => {
        return get("/merchant/light/sp/checkEpcSpRegister", params);
    },
    // 服务商登记
    registStation: (params) => {
        return post("/merchant/light/sp/registStation.do", params, 1);
    },
    // 服务商登记修改
    updateStation: (params) => {
        return post("/merchant/light/sp/updateStation.do", params, 1);
    },
    // 服务商电站列表
    stationList: (params) => {
        return get("/merchant/light/sp/stationList.do", params);
    },
    // 服务商电站列表导出 (2023-11-22)
    stationListExport: (params) => {
        return get("/merchant/light/sp/stationListExport.do", params, 3);
    },
    // 电站各状态数量
    stationStatusSum: (params) => {
        return get("/merchant/light/sp/stationStatusSum.do", params);
    },
    //  电站详情
    getStationById: (params) => {
        return get("/merchant/light/sp/getStationById.do", params);
    },
    // 电站方案
    findPlanByStationId: (params) => {
        return get("/merchant/light/sp/findPlanByStationId.do", params);
    },
    // 查询过程信息
    findProcessRecord: (params) => {
        return get("/merchant/light/sp/findProcessRecord.do", params);
    },
    // 删除电站
    removeStation: (params) => {
        return post("/merchant/light/sp/removeStation.do", params, 1);
    },
    // 获取保证金
    getDeposit: (params) => {
        return get("/merchant/light/deposit/getDeposit.do", params);
    },
    // 服务商保证金记录分页查询
    depositList: (params) => {
        return get("/merchant/light/deposit/find.do", params);
    },
    // 交保证金
    addDeposit: (params) => {
        return post("/merchant/light/deposit/addDeposit.do", params, 1);
    },
    // 修改保证金
    modifyDeposit: (params) => {
        return post("/merchant/light/deposit/modifyDeposit.do", params, 1);
    },
    // 获取商品型号主数据
    findByType: (params) => {
        return get("/merchant/light/sp/findByType.do", params);
    },
    // 获取仓库信息
    // 2022-07-06 弃用，升级多区域列表
    // getStore: (params) => {
    //     return get("/merchant/light/makeOrder/getStore.do", params);
    // },
    // 获取仓库信息列表
    findStore: (params) => {
        return get("/merchant/light/makeOrder/findStore.do", params);
    },
    // 批量下单
    apvCreate: (params) => {
        return post("/merchant/light/makeOrder/create.do", params, 1);
    },
    // 奥客隆-批量下单
    apvVipCreate: (params) => {
        return post("/merchant/light/makeOrder/vip/create.do", params, 1);
    },
    // 辅料-批量下单
    batchCreate: (params) => {
        return post("/merchant/light/auxiliaryMakeOrder/batchCreate", params, 1);
    },
    // 合同预览
    previewContract: (params) => {
        // return get("/merchant/light/sp/previewContract.do", params);
        // 改为法大大签署 At 2023.4.13
        return get("/merchant/light/dcc/company/previewSpContract.do", params);
    },
    // 签订合同
    signContract: (params) => {
        // return post("/merchant/light/sp/signContract.do", params, 1);
        // 改为法大大签署 At 2023.4.13
        return post("/merchant/light/dcc/company/serviceSignMaster.do", params, 1);
    },
    // Epc合同预览
    previewSpEpcContract: (params) => {
        return get("/merchant/light/dcc/company/previewSpEpcContract.do", params);
    },
    // Epc签订合同
    serviceSignEpcMaster: (params) => {
        return post("/merchant/light/dcc/company/serviceSignEpcMaster.do", params, 1);
    },
    // 查看合同
    showContract: (params) => {
        return get("/merchant/light/sp/showContract.do", params);
    },
    // 查询仓库
    getStore: (params) => {
        return get("/merchant/light/makeOrder/getStore.do", params);
    },
    // 入库管理
    storeFindData: (params) => {
        return get("/merchant/light/purchase/find.do", params);
    },
    // 服务商入库
    inStore: (params) => {
        return post("/merchant/light/purchase/inStore.do", params, 1);
    },
    // 入库影像修改
    updateSign: (params) => {
        return post("/merchant/light/purchase/updateSign.do", params, 1);
    },
    // 批量入库
    inStoreBatch: (params) => {
        return post("/merchant/light/purchase/inStoreBatch.do", params, 1);
    },
    // 导出入库列表
    purchaseExport: (params) => {
        return get("/merchant/light/purchase/export.do", params, 3);
    },
    // 导出出库列表
    stockExport: (params) => {
        return get("/merchant/light/stock/export.do", params, 3);
    },
    // 导出库存列表
    stockSaveExport: (params) => {
        return get("/merchant/light/stock/stock/export.do", params, 3);
    },
    // 供应商采购列表
    preOrderList: (params) => {
        return get("/merchant/light/purchaseOrder/preOrderList.do", params);
    },
    // 导出光伏采购订单列表
    preOrderExport: (params) => {
        return get("/merchant/light/purchaseOrder/preOrderExport.do", params, 3);
    },
    // 采购单明细
    preOrderRelateList: (params) => {
        return get("/merchant/light/purchaseOrder/preOrderRelateList.do", params);
    },
    // 导出采购单明细
    preOrderRelateExport: (params) => {
        return get("/merchant/light/purchaseOrder/preOrderRelateExport.do", params, 3);
    },
    // 供应商订单列表
    apvSupplylist: (params) => {
        return get("/merchant/light/purchaseOrder/list.do", params);
    },
    // 光伏供应商配货单物流
    findExpressRecord: (params) => {
        return get("/merchant/light/purchaseOrder/findExpressRecord.do", params);
    },
    // 光伏订单发货
    deliver: (params) => {
        return post("/merchant/light/purchaseOrder/deliver.do", params, 1);
    },
    // 导出光伏订单列表
    deliverExport: (params) => {
        return get("/merchant/light/purchaseOrder/export.do", params, 3);
    },
    // 导出模版
    apvDeliverTemplate: (params) => {
        return get("/merchant/light/purchaseOrder/deliverTemplate.do", params);
    },
    // 供应商库存管理
    findStock: (params) => {
        return get("/merchant/light/stock/findStock.do", params);
    },
    // 供应商出库管理
    findStoreOut: (params) => {
        return get("/merchant/light/stock/findStoreOut.do", params);
    },
    // 服务商电站信息确认
    confirmStation: (params) => {
        return post("/merchant/light/sp/confirmStation.do", params, 1);
    },
    // 完工确认
    completeConfirm: (params) => {
        return post("/merchant/light/station/completeConfirm.do", params, 1);
    },
    // 修改完工确认
    editCompleteConfirm: (params) => {
        return post("/merchant/light/station/editCompleteConfirm.do", params, 1);
    },
    // 特殊需求接口：修改完工确认影像
    editCompleteConfirmImg: (params) => {
        return post("/merchant/light/station/editCompleteConfirmImg.do", params, 1);
    },
    // 查看完工确认拒绝原因
    getCompleteRejectReson: (params) => {
        return get("/merchant/light/station/getCompleteRejectReson.do", params);
    },
    // 查看完工确认详情
    getCompleteConfirmInfo: (params) => {
        return get("/merchant/light/station/getCompleteConfirmInfo.do", params);
    },
    // // 查看完工拒绝原因
    // getCompleteRejectReson: (params) => {
    //     return get("/merchant/light/station/getCompleteRejectReson.do", params);
    // },
    // 查看并网信息详情
    getAcceptanceInfo: (params) => {
        return get("/merchant/light/station/getAcceptanceInfo.do", params);
    },
    // 申请并网验收
    acceptanceApply: (params) => {
        return post("/merchant/light/station/acceptanceApply.do", params, 1);
    },
    // 修改并网验收
    editAcceptanceApply: (params) => {
        return post("/merchant/light/station/editAcceptanceApply.do", params, 1);
    },
    // 修改电站影像
    editConfirmImg: (params) => {
        return post("/merchant/light/station/editConfirmImg.do", params, 1);
    },
    // 查看初审拒绝原因
    getFirstRejectReson: (params) => {
        return get("/merchant/light/station/getFirstRejectReson.do", params);
    },
    // 查看终审拒绝原因
    getFinalRejectReson: (params) => {
        return get("/merchant/light/station/getFinalRejectReson.do", params);
    },
    // 查看验收报告
    showAcceptanceReport: (params) => {
        return get("/merchant/light/station/showAcceptanceReport.do", params);
    },
    // 合同列表   （需求改动 2022-05-09）
    stationContractRecordList: (params) => {
        return get("/merchant/light/station/stationContractRecordList.do", params);
    },
    // 触发短信
    sendSmsForSign: (params) => {
        return post("/merchant/light/station/sendSmsForSign.do", params);
    },
    // 合同列表导出  (2023-11-22)
    stationContractRecordExport: (params) => {
        return get("/merchant/light/station/stationContractRecordExport.do", params, 3);
    },
    // 划转用户查询
    transferList: (params) => {
        return get("/merchant/light/lightRent/list.do", params);
    },
    // 划转用户导出
    exportTransfer: (params) => {
        return get("/merchant/light/lightRent/export.do", params, 3);
    },
    /***
     * @description: 变更方案
     */
    // 新增变更方案
    addChange: (params) => {
        return get("/merchant/light/planChange/addChange.do", params);
    },
    // 保存方案变更申请
    savePlanChange: (params) => {
        return post("/merchant/light/planChange/savePlanChange.do", params, 1);
    },
    // 方案变更申请列表
    changeList: (params) => {
        return get("/merchant/light/planChange/changeList.do", params);
    },
    // 删除方案变更
    removePlanChange: (params) => {
        return post("/merchant/light/planChange/removePlanChange.do", params, 1);
    },
    // 查看方案变更详情
    showChangeDetail: (params) => {
        return get("/merchant/light/planChange/showChangeDetail.do", params);
    },
    // 查询原方案配置
    findOriPlanByStationId: (params) => {
        return get("/merchant/light/sp/findOriPlanByStationId.do", params);
    },
    // /light/sp/findAllStatusByStationId 查询电站方案信息(new)
    findAllStatusByStationId: (params) => {
        return get("/merchant/light/sp/findAllStatusByStationId", params);
    },
    // 修改方案变更
    updatePlanChange: (params) => {
        return post("/merchant/light/planChange/updatePlanChange.do", params, 1);
    },
    /***
     * @description: 账房管理
     */
    // 对账单查询
    lightSapPurchaseRecord: (params) => {
        return get("/merchant/light/lightSapPurchaseRecord/list.do", params);
    },
    // 对账单导出
    lightSapPurchaseRecordExport: (params) => {
        return get("/merchant/light/lightSapPurchaseRecord/export.do", params, 3);
    },
    // 结算单
    lightPurchaseApplySettle: (params) => {
        return get("/merchant/light/lightPurchaseApplySettle/list.do", params);
    },
    // 结算单导出
    lightPurchaseApplySettleExport: (params) => {
        return get("/merchant/light/lightPurchaseApplySettle/export.do", params, 3);
    },
    // 并网奖励列表
    lightSpGridAwardOrder: (params) => {
        return get("/merchant/light/lightSpGridAwardOrder/list.do", params);
    },
    // 并网奖励列表导出
    lightSpGridAwardOrderExport: (params) => {
        return get("/merchant/light/lightSpGridAwardOrder/export.do", params, 3);
    },
    // 并网奖励列表详情
    lightSpGridAwardOrderDetail: (params) => {
        return get("/merchant/light/lightSpGridAwardOrderDetail/list.do", params);
    },
    // 并网奖励列表详情导出
    lightSpGridAwardOrderDetailExport: (params) => {
        return get("/merchant/light/lightSpGridAwardOrderDetail/export.do", params, 3);
    },
    // 即时性激励列表
    lightStationEnableReward: (params) => {
        return get("/merchant/light/lightStationEnableReward/list.do", params);
    },
    // 即时性激励列表导出
    lightStationEnableRewardExport: (params) => {
        return get("/merchant/light/lightStationEnableReward/export.do", params, 3);
    },
    // 即时性激励列表详情
    lightStationEnableRewardDetail: (params) => {
        return get("/merchant/light/lightStationEnableReward/detail.do", params);
    },
    /***
     * @description: 子账户
     */
    // 系统管理，用户列表
    spAccountList: (params) => {
        return get("/merchant/light/spAccount/spAccountList.do", params);
    },
    // 系统管理，新增子账户
    addAccount: (params) => {
        return post("/merchant/light/spAccount/addAccount.do", params, 1);
    },
    // 系统管理，修改子账户
    editAccount: (params) => {
        return post("/merchant/light/spAccount/editAccount.do", params, 1);
    },
    // 系统管理，冻结子账户
    freezeAccount: (params) => {
        return post("/merchant/light/spAccount/freezeAccount.do", params);
    },
    // 系统管理，解冻子账户
    unFreezeAccount: (params) => {
        return post("/merchant/light/spAccount/unFreezeAccount.do", params);
    },
    // 检查子账号是否正常
    checkSubRole: (params) => {
        return get("/merchant/light/spAccount/checkSubRole.do", params);
    },
    // 获取子账号详情
    getAccountDetail: (params) => {
        return get("/merchant/light/spAccount/getAccountDetail.do", params);
    },
    /***
     * @description: 暂存
     */
    // 完工确认暂存
    completeConfirmCache: (params) => {
        return post("/merchant/light/station/completeConfirmCache.do", params, 1);
    },
    // 并网验收暂存
    acceptanceApplyCache: (params) => {
        return post("/merchant/light/station/acceptanceApplyCache.do", params, 1);
    },
    /***
     * @description: 多区域管理
     */
    // 多区域管理，获取主区域
    getMasterProvince: (params) => {
        return get("/merchant/light/sp/getMasterProvince.do", params);
    },
    // 多区域管理，申请服务区域
    applyAuthorityRegion: (params) => {
        return post("/merchant/light/sp/applyAuthorityRegion.do", params, 1);
    },
    // 多区域管理，修改服务区域
    editAuthorityRegion: (params) => {
        return post("/merchant/light/sp/editAuthorityRegion.do", params, 1);
    },
    // 多区域管理，服务区域列表
    listSpAuthorityZone: (params) => {
        return get("/merchant/light/sp/listSpAuthorityZone.do", params);
    },
    // 多区域管理，服务区域详情
    getAuthorityRegionDetail: (params) => {
        return get("/merchant/light/sp/getAuthorityRegionDetail.do", params);
    },
    // 多区域管理，查看合同
    previewRegionContract: (params) => {
        return get("/merchant/light/sp/previewRegionContract.do", params);
    },
    // 多区域管理，签订合同
    signRegionContract: (params) => {
        return post("/merchant/light/sp/signRegionContract.do", params);
    },
    // 多区域管理，批量下单查询服务区域
    findSpRegion: (params) => {
        return get("/merchant/light/sp/findSpRegion.do", params);
    },
    // 服务兵新增查询服务商的已授权区域
    getAuths: (params) => {
        return get("/merchant/light/staff/getAuth.do", params);
    },
    /***
     * @description: 终止管理
     */
    // 终止申请
    applyStop: (params) => {
        return post("/merchant/light/stopStation/applyStop.do", params);
    },
    // 终止管理列表
    stopStationList: (params) => {
        return get("/merchant/light/stopStation/stopStationList.do", params);
    },
    // 终止撤回
    revocationApply: (params) => {
        return post("/merchant/light/stopStation/revocationApply.do", params);
    },
    /***
     * @description: 逆变器列表
     */
    // 获取逆变器列表
    lightInveterList: (params) => {
        return get("/merchant/light/inveter/lightInveterList.do", params);
    },
    // 导出逆变器列表
    exportInveter: (params) => {
        return get("/merchant/light/inveter/exportInveter.do", params, 3);
    },
    // 逆变器详情
    inveterDetail: (params) => {
        return get("/merchant/light/inveter/inveterDetail.do", params);
    },
    // 查询日图表
    dayChart: (params) => {
        return get("/merchant/light/inveter/dayChart.do", params);
    },
    // 查询月图表
    monthChart: (params) => {
        return get("/merchant/light/inveter/monthChart.do", params);
    },
    // 查询年图表
    yearChart: (params) => {
        return get("/merchant/light/inveter/yearChart.do", params);
    },
    partnerRegistSendSms: (params) => {
        return post("/merchant/light/partner/partnerRegistSendSms.do", params);
    },
    // 查询累计图表
    totalChart: (params) => {
        return get("/merchant/light/inveter/totalChart.do", params);
    },
    /***
     * @description: 多项目模式 2022.08
     */
    //  项目确认管理
    confirmProjectList: (params) => {
        return get("/merchant/light/project/confirmProjectList.do", params);
    },
    // 确认合同
    confirmContract: (params) => {
        return post("/merchant/light/project/confirmContract.do", params);
    },
    // 查看项目
    findProject: (params) => {
        return get("/merchant/light/project/findProject.do", params);
    },
    // 户用项目租赁登记信息
    registHoseholdStation: (params) => {
        return post("/merchant/light/station/registHoseholdStation.do", params, 1);
    },
    // 户用项目租赁登记修改
    updateHoseholdStation: (params) => {
        return post("/merchant/light/station/updateHoseholdStation.do", params, 1);
    },
    // 公共建筑租赁-企业登记信息
    registCorpStation: (params) => {
        return post("/merchant/light/station/registCorpStation.do", params, 1);
    },
    // 公共建筑租赁-企业登记信息修改
    updateCorpStation: (params) => {
        return post("/merchant/light/station/updateCorpStation.do", params, 1);
    },
    // 公共建筑租赁-政府/事业单位登记信息
    registGovStation: (params) => {
        return post("/merchant/light/station/registGovStation.do", params, 1);
    },
    // 公共建筑租赁-政府/事业单位登记信息修改
    updateGovStation: (params) => {
        return post("/merchant/light/station/updateGovStation.do", params, 1);
    },
    // 整村登记信息
    registWholeVillageStation: (params) => {
        return post("/merchant/light/station/registWholeVillageStation.do", params, 1);
    },
    // 整村登记信息修改
    updateWholeVillageStation: (params) => {
        return post("/merchant/light/station/updateWholeVillageStation.do", params, 1);
    },
    /***
     * @description: 获单员施工队 2022.10
     */
    //  项目确认管理
    // 获取获单人员列表
    orderUserList: (params) => {
        return get("/merchant/light/orderUser/orderUserList.do", params);
    },
    // 查询获单人员
    getDesigneeList: (params) => {
        return get("/merchant/light/orderUser/findOrderUserByPage.do", params);
    },
    // 新增获单人员
    addOrderUser: (params) => {
        return post("/merchant/light/orderUser/addOrderUser.do", params, 1);
    },
    // 冻结获单人员
    freezeOrderUser: (params) => {
        return post("/merchant/light/orderUser/freezeOrderUser.do", params);
    },
    // 解冻获单人员
    unFreezeOrderUser: (params) => {
        return post("/merchant/light/orderUser/unFreezeOrderUser.do", params);
    },
    // 删除获单人员
    removeOrderUser: (params) => {
        return post("/merchant/light/orderUser/removeOrderUser.do", params);
    },
    // 获取施工队列表
    teamList: (params) => {
        return get("/merchant/light/constructionTeam/teamList.do", params);
    },
    // 查询施工队
    getTeamList: (params) => {
        return get("/merchant/light/constructionTeam/findTeamByPage.do", params);
    },
    // 新增施工队
    addConstructionTeam: (params) => {
        return post("/merchant/light/constructionTeam/addConstructionTeam.do", params, 1);
    },
    // 冻结施工队
    freezeConstructionTeam: (params) => {
        return post("/merchant/light/constructionTeam/freezeConstructionTeam.do", params);
    },
    // 解冻施工队
    unFreezeConstructionTeam: (params) => {
        return post("/merchant/light/constructionTeam/unFreezeConstructionTeam.do", params);
    },
    // 删除施工队
    removeConstructionTeam: (params) => {
        return post("/merchant/light/constructionTeam/removeConstructionTeam.do", params);
    },
    // 电站派工
    dispatchWorkOrder: (params) => {
        return post("/merchant/light/station/dispatchWorkOrder.do", params, 1);
    },
    // 查看派工详情
    getWorkOrderInfo: (params) => {
        return get("/merchant/light/workOrder/getWorkOrderInfo.do", params);
    },
    // 电站派工改派
    changeDispatchWorkOrder: (params) => {
        return post("/merchant/light/station/changeDispatchWorkOrder.do", params, 1);
    },
    // 派单列表
    workOrderList: (params) => {
        return get("/merchant/light/workOrder/workOrderList.do", params);
    },
    // 施工工单完工审核通过
    workOrderAuditOk: (params) => {
        return post("/merchant/light/workOrder/auditOk.do", params, 1);
    },
    // 施工工单完工审核驳回
    workOrderAuditReject: (params) => {
        return post("/merchant/light/workOrder/auditReject.do", params);
    },
    // 派工单导出
    exportWorkOrderList: (params) => {
        return get("/merchant/light/workOrder/exportWorkOrderList.do", params, 3);
    },
    /***
     * @description: 越秀模式 2022.11
     */
    // 用户登记-新增
    yxOwnerAdd: (params) => {
        return post("/merchant/light/owner/add.do", params, 1);
    },
    // 用户登记-修改
    yxOwnerEdit: (params) => {
        return post("/merchant/light/owner/edit.do", params, 1);
    },
    // 用户登记-列表
    yxOwnerFind: (params) => {
        return get("/merchant/light/owner/find.do", params);
    },
    // 用户登记-删除
    yxOwnerDelete: (params) => {
        return post("/merchant/light/owner/delete.do", params);
    },
    // 用户登记-导出
    yxOwnerExport: (params) => {
        return get("/merchant/light/owner/export.do", params, 3);
    },
    // 电站登记-业主详细信息
    yxStationOwnerDetail: (params) => {
        return get("/merchant/light/owner/stationOwnerDetail.do", params);
    },
    // 电站登记-越秀电站登记信息
    registYuexiuStation: (params) => {
        return post("/merchant/light/station/registYuexiuStation.do", params, 1);
    },
    // 电站登记-越秀电站登记信息修改
    updateYuexiuStation: (params) => {
        return post("/merchant/light/station/updateYuexiuStation.do", params, 1);
    },
    // /light/station/confirmAndSubmitStationInfo EPC-确认并提交审核操作 YX
    confirmAndSubmitStationInfo: (params) => {
        return post("/merchant/light/station/confirmAndSubmitStationInfo", params, 1);
    },
    // /light/station/confirmAndSubmitStationInfoZh EPC-确认并提交审核操作 ZH
    confirmAndSubmitStationInfoZh: (params) => {
        return post("/merchant/light/station/confirmAndSubmitStationInfoZh", params, 1);
    },
    // /light/station/confirmAndSubmitStationInfoCorp 中核公共建筑电站电站确认并提交审核操作
    confirmAndSubmitStationInfoCorp: (params) => {
        return post("/merchant/light/station/confirmAndSubmitStationInfoCorp", params, 1);
    },
    // /light/station/confirmAndSubmitStationInfoGov 中核政府/事业单位电站电站确认并提交审核操作
    confirmAndSubmitStationInfoGov: (params) => {
        return post("/merchant/light/station/confirmAndSubmitStationInfoGov", params, 1);
    },
    // * 华润华润电站确认并提交审核操作
    confirmAndSubmitStationInfoBT: (params) => {
        return post("/merchant/light/station/confirmAndSubmitStationInfoBT", params, 1);
    },
    // 电站登记-获取电站信息
    getYxStationById: (params) => {
        return get("/merchant/light/sp/getYxStationById.do", params);
    },
    // 分页查询能源币
    energyCoinFind: (params) => {
        return get("/merchant/light/coinStation/find.do", params);
    },
    // 能源币详情
    energyCoinDetail: (params) => {
        return get("/merchant/light/coinStation/detail.do", params);
    },
    // 能源币导出
    energyCoinExport: (params) => {
        return get("/merchant/light/coinStation/export.do", params, 3);
    },
    // 绿能小管家-列表
    butlerList: (params) => {
        return get("/merchant/light/station/nanny/list.do", params, 1);
    },
    // 绿能小管家-导出
    butlerExport: (params) => {
        return get("/merchant/light/station/nanny/export.do", params, 3);
    },
    // 绿能小管家推荐屋顶-列表
    butlerStationList: (params) => {
        return get("/merchant/light/station/nanny/user/list.do", params, 1);
    },
    // 绿能小管家推荐屋顶-导出
    butlerStationExport: (params) => {
        return get("/merchant/light/station/nanny/user/export.do", params, 3);
    },
    /***
     * @description: 分阶段完工 2023.01
     */
    // 支架部分完工申请-申请前电站信息查询
    holderPreApply: (params) => {
        return get("/merchant/light/part/complete/apply/preApply.do", params);
    },
    // 支架部分完工申请-列表
    holderList: (params) => {
        return get("/merchant/light/part/complete/apply/list.do", params);
    },
    // 支架部分完工申请-详情
    holderDetail: (params) => {
        return get("/merchant/light/part/complete/apply/detail.do", params);
    },
    // 支架部分完工申请-新增
    holderAdd: (params) => {
        return post("/merchant/light/part/complete/apply/add.do", params, 1);
    },
    // 支架部分完工申请-修改
    holderEdit: (params) => {
        return post("/merchant/light/part/complete/apply/edit.do", params, 1);
    },
    // 支架部分完工申请-删除
    holderDelete: (params) => {
        return post("/merchant/light/part/complete/apply/delete.do", params);
    },
    // 组件部分完工申请-申请前电站信息查询
    moudlePreApply: (params) => {
        return get("/merchant/light/module/complete/apply/preApply.do", params);
    },
    // 组件部分完工申请-列表
    moudleList: (params) => {
        return get("/merchant/light/module/complete/apply/list.do", params);
    },
    // 组件部分完工申请-详情
    moudleDetail: (params) => {
        return get("/merchant/light/module/complete/apply/detail.do", params);
    },
    // 组件部分完工申请-新增
    moudleAdd: (params) => {
        return post("/merchant/light/module/complete/apply/add.do", params, 1);
    },
    // 组件部分完工申请-修改
    moudleEdit: (params) => {
        return post("/merchant/light/module/complete/apply/edit.do", params, 1);
    },
    // 组件部分完工申请-删除
    moudleDelete: (params) => {
        return post("/merchant/light/module/complete/apply/delete.do", params);
    },
    /***
     * @description: 服务商合同查询 2023.02
     */
    // 公司合同-记录查询
    queryContractRecord: (params) => {
        return post("/merchant/light/sp/queryContractRecord.do", params);
    },
    // 公司合同-预览新合同
    previewNewContract: (params) => {
        // return get("/merchant/light/sp/previewNewContract.do", params);
        // 改为法大大签署 At 2023.4.19
        return get("/merchant/light/dcc/company/previewContract.do", params);
    },
    // 公司合同-签订新合同
    signNewContract: (params) => {
        // return post("/merchant/light/sp/signNewContract.do", params);
        // 改为法大大签署，并支持多种类型签署 At 2023.4.19
        return post("/merchant/light/dcc/company/signContract.do", params);
    },
    // 公司合同-查看已签订的新合同
    showNewContract: (params) => {
        return get("/merchant/light/sp/showNewContract.do", params);
    },
    // 公司合同-签订终止合同
    // 改为法大大签署，并支持多种类型签署，该接口弃用 At 2023.4.19
    // signStopContract: (params) => {
    //     return post("/merchant/light/sp/signStopContract.do", params);
    // },
    /***
     * @description: 工商业供应商订单 2023.03
     */
    // 光伏epc采购订单发货
    epcDeliver: (params) => {
        return post("/merchant/light/cmPreOrder/deliver.do", params, 1);
    },
    // 查询光伏epc采购订单列表
    getEpcExpresssList: (params) => {
        return get("/merchant/light/cmPreOrder/list.do", params);
    },
    // 导出光伏epc采购订单列表
    exportEpcExpresssList: (params) => {
        return get("/merchant/light/cmPreOrder/export.do", params, 3);
    },
    /***
     * @description: 光伏调拨单 2023.03
     */
    // 调入服务商调拨单列表
    transferInList: (params) => {
        return get("/merchant/light/transfer/in/list.do", params);
    },
    // 调出服务商调拨单列表
    transferOutList: (params) => {
        return get("/merchant/light/transfer/out/list.do", params);
    },
    // 调拨单出库
    outTransfer: (params) => {
        return post("/merchant/light/transfer/outTransfer.do", params, 1);
    },
    // 调拨单签收
    signTransfer: (params) => {
        return post("/merchant/light/transfer/signTransfer.do", params, 1);
    },
    // 修改签收影像
    updateSignImage: (params) => {
        return post("/merchant/light/transfer/updateSignImage.do", params, 1);
    },
    /***
     * @description: 多模式补充协议 2023.04
     */
    // 是否签署补充协议
    isSignedAgreement: (params) => {
        return post("/merchant/light/sp/isSignedAgreement", params, 1, true);
    },
    /***
     * @description: 资料库 2023.05.24
     */
    // 资料库查询
    databaseList: (params) => {
        return get("/merchant/light/databaseManagement/list.do", params);
    },
    /***
     * @description: 银联签约列表 2023.06.13
     */
    // 银联签约列表查询
    bankSigningList: (params) => {
        return get("/merchant/light/unionpay/user/list.do", params);
    },
    // 导出银联签约列表
    bankSigningExport: (params) => {
        return get("/merchant/light/unionpay/user/export.do", params, 3);
    },
    /***
     * @description: 项目确认管理（转法大大） 2023.06.15
     */
    // 预览项目合同
    previewProjectContract: (params) => {
        return get("/merchant/light/project/previewProjectContract.do", params);
    },
    // 签订项目合同
    signProjectContract: (params) => {
        return post("/merchant/light/project/signProjectContract.do", params);
    },
    // /light/policy/list.do 政策列表
    getPolicyList: (params) => {
        return get("/merchant/light/policy/list.do", params);
    },
    // /light/policy/spCount.do 待确认奖励数量
    getSpCount: (params) => {
        return get("/merchant/light/policy/spCount.do", params);
    },
    // /light/policy/confirm.do 确认政策
    confirmPolicy: (params) => {
        return post("/merchant/light/policy/confirm.do", params);
    },
    // /light/policy/detail.do 政策详情
    getPolicyDetail: (params) => {
        return get("/merchant/light/policy/detail.do", params);
    },
    //服务商额度配置
    getQuotaList: (params) => {
        return get("/merchant/light/Quota/list.do", params);
    },
    //员工账号管理
    getStaffList: (params) => {
        return get("/merchant/light/sp/staff/list.do", params);
    },
    addAtaff: (params) => {
        return post("/merchant/light/sp/staff/add.do", params, 1);
    },
    deleteList: (params) => {
        return post("/merchant/light/sp/staff/delete.do", params);
    },
    editRegion: (params) => {
        return post("/merchant/light/sp/staff/editRegion.do", params, 1);
    },
    getAddressList: (params) => {
        return get("/merchant/light/sp/staff/regionList.do", params);
    },
    //查询二级账号
    getSubSp: (params) => {
        return get("/merchant/light/spAccount/getSubSp.do", params);
    },
    //查询账号详细信息
    getStaffDetail: (params) => {
        return get("/merchant/light/sp/staff/detail.do", params);
    },
    //更新区域经理绑定子账号
    setStaffUpdate: (params) => {
        return post("/merchant/light/sp/staff/relation/update.do", params, 1);
    },
    // 修改方案变更
    //去申请
    getStationDetail: (params) => {
        return get("/merchant/stationInfo/update/getStationDetail.do", params);
    },
    //电站信息修改列表
    getBasicInfo: (params) => {
        return get("/merchant/stationInfo/update/getList.do", params);
    },
    //详情
    basicInfoDetail: (params) => {
        return get("/merchant/stationInfo/update/getDetail.do", params);
    },
    //删除
    basicInfoDelete: (params) => {
        return post("/merchant/stationInfo/update/delete.do", params, 1);
    },
    //修改
    basicInfoUpdate: (params) => {
        return post("/merchant/stationInfo/update/update.do", params, 1);
    },
    //新增信息修改
    basicInfoEdit: (params) => {
        return post("/merchant/stationInfo/update/createUpdate.do", params, 1);
    },
    //查询电站登记草稿列表
    getStationStagingList: (params) => {
        return get("/merchant/light/draft/getList.do", params);
    },
    //查询电站登记草稿详情
    getStationStagingDetail: (params) => {
        return get("/merchant/light/draft/getDetail.do", params);
    },
    //删除电站登记草稿
    deleteStationStaging: (params) => {
        return post("/merchant/light/draft/delete.do", params, 1);
    },
    // 电站登记-越秀电站登记信息暂存
    registerYuexiuStationCache: (params) => {
        return post("/merchant/light/station/registerYuexiuStationCache.do", params, 1);
    },
    // 电站登记-ZH电站登记信息暂存
    registerCnnChnStationCache: (params) => {
        return post("/merchant/light/station/registerCnnChnStationCache.do", params, 1);
    },
    // 户用项目租赁登记信息暂存
    registerHoseholdStationCache: (params) => {
        return post("/merchant/light/station/registerHoseholdStationCache.do", params, 1);
    },
    // 公共建筑租赁-企业登记信息暂存
    registerCorpStationCache: (params) => {
        return post("/merchant/light/station/registerCorpStationCache.do", params, 1);
    },
    // 公共建筑租赁-政府/事业单位登记信息暂存
    registerGovStationCache: (params) => {
        return post("/merchant/light/station/registerGovStationCache.do", params, 1);
    },
    // 整村登记信息暂存
    registerWholeVillageStationCache: (params) => {
        return post("/merchant/light/station/registerWholeVillageStationCache.do", params, 1);
    },
    // 服务商登记
    registerStationCache: (params) => {
        return post("/merchant/light/sp/registerStationCache.do", params, 1);
    },
    // 首页-结算信息-物料进度-电站超时预警情况
    getTimeoutBalanceMaterial: (params) => {
        return get("/merchant/sp/homepage/count1", params);
    },
    // 首页-电站进度监控-待办任务情况
    getProgressPending: (params) => {
        return get("/merchant/sp/homepage/count2", params);
    },
    // 首页接口3
    getStationCode: (params) => {
        return get("/merchant/sp/homepage/count3", params);
    },
    // 首页接口1
    getLogList: (params) => {
        return get("/merchant/sp/homepage/logList", params);
    },
    // 首页接口2
    getQueryLimit: (params) => {
        return get("/merchant/sp/homepage/queryLimit", params);
    },
    // 首页接口3
    getWarningList: (params) => {
        return get("/merchant/sp/homepage/warningList", params);
    },
    // 用户登记查询(中核)
    zhOwnerFind: (params) => {
        return get("/merchant/app/lightStation/owner/find.do", params);
    },
    // 删除用户登记(中核)
    zhOwnerDelete: (params) => {
        return post("/merchant/app/lightStation/owner/delete.do", params);
    },
    // 用户登记导出(中核)
    zhOwnerExport: (params) => {
        return get("/merchant/app/lightStation/owner/export.do", params, 3);
    },
    // 勘测派单(中核)
    addDispatch: (params) => {
        return post("/merchant/app/lightStation/dispatch.do", params);
    },
    // 勘测改派(中核)
    changeDispatch: (params) => {
        return post("/merchant/app/lightStation/dispatch/change.do", params);
    },
    // 电站登记暂存(中核)
    zhTemporaryStorage: (params) => {
        return post("/merchant/app/lightStation/temporaryStorage", params, 1);
    },
    // 查看电站登记暂存(中核)
    findTemporaryStorage: (params) => {
        return get("/merchant/light/station/findTemporaryStorage", params);
    },
    // 业主信息查询(中核)
    zhStationOwnerDetail: (params) => {
        return get("/merchant/app/lightStation/owner/detail.do", params);
    },
    // 删除电站登记暂存(中核)
    deleteZHTemporaryStorage: (params) => {
        return post("/merchant/app/lightStation/deleteTemporaryStorage", params);
    },
    // 查询勘测员(中核)
    staffFind: (params) => {
        return get("/merchant/light/sp/staff/findByRegion.do", params);
    },
    // 仓库管理-仓库设置（奥客隆）
    // 获取仓库数据列表
    getAuclonStore: (params) => {
        return get("/merchant/light/store/auclon/getStore.do", params);
    },
    // 启用
    enabled: (params) => {
        return post("/merchant/light/store/auclon/enabled.do", params);
    },
    // 禁用
    disabled: (params) => {
        return post("/merchant/light/store/auclon/disabled.do", params);
    },
    // 新增
    storeAdd: (params) => {
        return post("/merchant/light/store/auclon/add.do", params, 1);
    },
    // 仓库管理-库存管理（奥客隆）
    // 二级库存管理导出
    subStockExport: (params) => {
        return get("/merchant/light/sub/stock/export.do", params, 3);
    },
    // 二级仓库库存分页查询
    stockFindStock: (params) => {
        return get("/merchant/light/sub/stock/findStock.do", params);
    },
    // 仓库管理-调拨入库（奥客隆）
    // 二级仓库新增调拨单
    transferAdd: (params) => {
        return post("/merchant/light/sub/transfer/add.do", params, 1);
    },
    // 二级仓库库存调拨分页查询
    transferGet: (params) => {
        return get("/merchant/light/sub/transfer/get.do", params);
    },
    // 调入下拉数据
    findInStore: (params) => {
        return get("/merchant/light/store/auclon/findInStore.do", params);
    },
    // 调出下拉数据
    findOutStore: (params) => {
        return get("/merchant/light/store/auclon/findOutStore.do", params);
    },
    // 根据编码查物料
    transferGetSku: (params) => {
        return get("/merchant/light/sub/transfer/getSku.do", params);
    },
    // 待下单信息
    // 获取电站信息
    getStations: (params) => {
        return post("/merchant/light/makeOrder/vip/getStations.do", params, 1);
    },
    // 获取物料信息
    getSkus: (params) => {
        return get("/merchant/light/order/confirm/getSkus.do", params);
    },
    // 获取四件套待下单信息
    findStationAuxiliaryInfo: (params) => {
        return post("/merchant/light/station/findStationAuxiliaryInfo", params, 1);
    },
    // 奥客隆审核
    // 获取列表数据
    getConfirmOrder: (params) => {
        return get("/merchant/light/order/confirm/getConfirmOrder.do", params);
    },
    // 获取授权区域
    getConfirmOrderRegion: (params) => {
        return get("/merchant/light/order/confirm/getConfirmOrderRegion.do", params);
    },
    // 审核待下单审核
    orderConfirm: (params) => {
        return post("/merchant/light/order/confirm/confirm.do", params, 1);
    },
    // 审核待下单驳回
    orderQuitOrder: (params) => {
        return post("/merchant/light/order/confirm/quitOrder.do", params, 1);
    },
    // 查询待下单待审核详细信息
    getDetails: (params) => {
        return get("/merchant/light/order/confirm/getDetails.do", params);
    },
    //电站开发预警报表
    // 安装超期30天的电站列表
    exceedThrNotCompleteList: (params) => {
        return get("/merchant/report/developWarning/exceedThrNotCompleteList.do", params);
    },
    //  并网超期30天的电站列表
    exceedThrGenElectList: (params) => {
        return get("/merchant/report/developWarning/exceedThrGenElectList.do", params);
    },
    //  并网超期60天的电站列表
    exceedSixGenElectList: (params) => {
        return get("/merchant/report/developWarning/exceedSixGenElectList.do", params);
    },
    // 光伏服务商组件库龄查询
    findComponentLibrary: (params) => {
        return get("/merchant/light/componentLibrary/find.do", params);
    },
    // 光伏服务商库龄概况
    findOverviewComponentLibrary: (params) => {
        return get("/merchant/light/componentLibrary/findOverview.do", params);
    },
    // 导出组件库龄列表
    exportComponentLibrary: (params) => {
        return get("/merchant/light/componentLibrary/export.do", params, 3);
    },
    // 配货单管理-获取授权区域
    makeFindOrderRegion: (params) => {
        return get("/merchant/light/auxiliaryMakeOrder/findOrderRegion", params);
    },
    // 配货单管理-获取列表数据
    mekeListForMerchant: (params) => {
        return get("/merchant/light/auxiliaryMakeOrder/listForMerchant", params);
    },
    // 配货单管理-获取列表数据详情信息
    mekeOrderDetail: (params) => {
        return get("/merchant/light/auxiliaryMakeOrder/orderDetail", params);
    },
    // 配货单管理-导出物料明细
    mekeOrderExport: (params) => {
        return get("/merchant/light/auxiliaryMakeOrder/export.do", params, 3);
    },
    // 辅料类配货单-列表查询
    findAuxiliaryPurchaseOrderPage: (params) => {
        return get("/merchant/light/purchase/findAuxiliaryPurchaseOrderPage", params);
    },
    // 辅料类配货单-查看物料明细
    findAuxiliaryPurchaseOrderDetail: (params) => {
        return get("/merchant/light/purchase/findAuxiliaryPurchaseOrderDetail", params);
    },
    // 辅料类配货单-发货
    auxiliaryPurchaseOrderDeliver: (params) => {
        return post("/merchant/light/purchase/auxiliaryPurchaseOrderDeliver", params, 1);
    },
    // 计划订单管理-发货
    auxiliaryPurchaseOrderDirectDeliver: (params) => {
        return post("/merchant/light/purchase/auxiliaryPurchaseOrderDirectDeliver", params, 1);
    },
    // 导出物料电站详情信息
    exportSummaryPlanConfig: (params) => {
        return get("/merchant/light/planConfig/exportSummaryPlanConfig.do", params, 3);
    },
    /***
     * @description: 发票校验 2024.03.06
     */
    // 新增发票
    addInvoices: (params) => {
        return post("/merchant/invoiceCheck/addInvoices.do", params, 1);
    },
    // 发票删除
    deleteInvoice: (params) => {
        return post("/merchant/invoiceCheck/deleteInvoice.do", params);
    },
    // 识别发票信息
    getInvoiceInfo: (params) => {
        return post("/merchant/invoiceCheck/getInvoiceInfo.do", params, 1);
    },
    // 查询发票
    getinvoiceCheckList: (params) => {
        return get("/merchant/invoiceCheck/list.do", params);
    },
    // 手工录入发票
    manualAddInvoice: (params) => {
        return post("/merchant/invoiceCheck/manualAddInvoice.do", params, 1);
    },
    // 手工录入发票-查验
    manualAddInvoiceCheck: (params) => {
        return post("/merchant/invoiceCheck/manualAddInvoiceCheck.do", params, 1);
    },
    // 关联发票查询
    queryInvoiceForSettle: (params) => {
        return get("/merchant/invoiceCheck/queryInvoiceForSettle.do", params);
    },
    // 修改发票
    updateInvoices: (params) => {
        return post("/merchant/invoiceCheck/updateInvoices.do", params, 1);
    },
    /***
     * @description: 请款申请 2024.03.20
     */
    // 新增请款申请
    addPaymentRequest: (params) => {
        return post("/merchant/purchaseApplySettleNoPaper/add.do", params, 1);
    },
    // 新增请款申请-反显
    addPaymentRequestInfo: (params) => {
        return get("/merchant/purchaseApplySettleNoPaper/addPrepareInfo.do", params);
    },
    // 请款申请删除
    deletePaymentRequest: (params) => {
        return post("/merchant/purchaseApplySettleNoPaper/delete.do", params);
    },
    // 请款申请详情
    detailPaymentRequest: (params) => {
        return get("/merchant/purchaseApplySettleNoPaper/detail.do", params);
    },
    // 确认对账单-查看
    getConfirmBeforeBillUrl: (params) => {
        return get("/merchant/purchaseApplySettleNoPaper/getConfirmBeforeBillUrl.do", params);
    },
    // 确认对账单-盖章
    getConfirmUrl: (params) => {
        return get("/merchant/purchaseApplySettleNoPaper/getConfirmUrl.do", params);
    },
    // 分页查询请款申请
    getPaymentRequest: (params) => {
        return get("/merchant/purchaseApplySettleNoPaper/list.do", params);
    },
    // 新增请款申请-选择采购单
    queryPurchaseForSettle: (params) => {
        return get("/merchant/purchaseApplySettleNoPaper/queryPurchaseForSettle.do", params);
    },
    // 关联发票
    relateInvoice: (params) => {
        return post("/merchant/purchaseApplySettleNoPaper/relateInvoice.do", params, 1);
    },
    // 修改请款申请
    updatePaymentRequest: (params) => {
        return post("/merchant/purchaseApplySettleNoPaper/update.do", params, 1);
    },
    // 查询合同信息最新记录
    findNewOne: (params) => {
        return get("/merchant/lightStationContractInfo/findNewOne.do", params);
    },
    // 判断是否可以下载收单模板
    isExportPurchaseOrder: (params) => {
        return post("/merchant/light/purchase/exportPurchaseOrder.do", params, 1);
    },
    // 获取接收单模板
    exportPurchaseOrder: (params) => {
        return post("/merchant/light/purchase/exportPurchaseOrder.do", params, 3);
    },
    // 获取越秀银行支行
    getByCityIdAndHeadBankName: (params) => {
        return get("/merchant/bank/getByCityIdAndHeadBankName.do", params);
    },
    // 获取越秀主银行
    getAllHeadBank: (params) => {
    return get("/merchant/bank/getAllHeadBank.do", params);
    },
  /***
   * @description: 借件申请 2024.05.27
   */
  // 获取借件申请列表
  getBorrowItemsApplyList: (params) => {
    return get("/merchant/lightLend/findList.do", params);
  },
  // 获取借件申请出库单列表
  getBorrowItemsOutRecordList: (params) => {
    return get("/merchant/lightLend/findOutRecordList.do", params);
  },
  // 借件申请审核
  borrowItemsApplyAudit: (params) => {
    return post("/merchant/lightLend/lightLendAudit.do", params, 1);
  },
  // 借件申请发货
  borrowItemsDelivery: (params) => {
    return post("/merchant/lightLend/lightLendDelivery/snCode.do", params, 1);
  },
  // 下载组件编码导入模板
  downSnCodeListTemplate: (params) => {
    return get("/merchant/lightLend/sn/downTemplate.do", params, 3);
  },
    /***
     * @description: 负激励申诉 2024.05.30
     */
    // 负激励申诉-申请
    awardAppeal: (params) => {
        return post("/merchant/light/awardAppeal/appeal.do", params, 1);
    },
    // 负激励申诉-查询电站
    awardAppealCheck: (params) => {
        return get("/merchant/light/awardAppeal/check.do", params);
    },
    // 负激励申诉-删除
    deleteAwardAppeal: (params) => {
        return get("/merchant/light/awardAppeal/delete.do", params);
    },
    // 负激励申诉-详情
    awardAppealDetail: (params) => {
        return get("/merchant/light/awardAppeal/detail.do", params);
    },
    // 负激励申诉-修改
    editAwardAppeal: (params) => {
        return post("/merchant/light/awardAppeal/edit.do", params, 1);
    },
    // 负激励申诉-列表
    getAwardAppealList: (params) => {
        return get("/merchant/light/awardAppeal/list.do", params);
    },
    // 负激励申诉-请款申请校验
    checkItems: (params) => {
        return post("/merchant/light/awardAppeal/checkItems.do", params, 1);
    },
    //是否入驻提醒弹窗
    isRegister: (params) => {
        return post("/merchant/lightOperationProvider/isRegister", params, null,true);
    },
    // 共享额度-列表
    getShareQuotaList: (params) => {
        return get("/merchant/light/spShareQuota/list.do", params);
    },
    // 共享额度-申请
    shareQuotaApply: (params) => {
        return post("/merchant/light/spShareQuota/apply.do", params, 1);
    },
    // 共享额度-修改
    shareQuotaUpdate: (params) => {
        return get("/merchant/light/spShareQuota/update.do", params);
    },
    // 共享额度-详情
    shareQuotaDetail: (params) => {
        return get("/merchant/light/spShareQuota/detail.do", params);
    },
    // 共享额度-公司明细
    shareQuotaCompanyInfo: (params) => {
        return get("/merchant/light/spShareQuota/companyInfo.do", params);
    },
    // 共享额度-模糊查询子公司列表
    shareQuotaSidiaryList: (params) => {
        return get("/merchant/light/spShareQuota/subsidiarySpList.do", params);
    },
    // 共享额度-终止
    shareQuotaStop: (params) => {
        return get("/merchant/light/spShareQuota/stop.do", params);
    },
    // 共享额度-重新启用
    shareQuotaReApply: (params) => {
        return get("/merchant/light/spShareQuota/reApply.do", params);
    },
    // 共享额度-查看合同
    shareQuotaFindContract: (params) => {
        return get("/merchant/light/spShareQuota/findContract.do", params);
    },
    // 共享额度-预览共享额度合同
    shareQuotaPreviewQuotaContract: (params) => {
        return get("/merchant/light/spShareQuota/previewQuotaContract.do", params);
    },
    // 共享额度-签订合同
    shareQuotaSignQuotaContract: (params) => {
        return post(`/merchant/light/spShareQuota/signQuotaContract.do?orderNo=${params.orderNo}`);
    },
    // 质保金列表
    getDepositRecordList: (params) => {
        return get("/merchant/depositRecord/list.do", params);
    },
    // 导出质保金
    exportDepositRecord: (params) => {
        return get("/merchant/depositRecord/export.do", params, 3);
    },
    // * TOB越秀电站登记
    registerYueXiuStationForPub: (params) => {
        return post("/merchant/light/station/registerYueXiuStationForPub.do", params, 1);
    },
    // * TOB电站登记修改
    updateYueXiuStationInfoPublic: (params) => {
        return post("/merchant/light/station/updateYueXiuStationInfoPublic.do", params, 1);
    },
    // * TOB确认并提交
    confirmAndSubmitYueXiuStationInfoPublic: (params) => {
        return post("/merchant/light/station/confirmAndSubmitYueXiuStationInfoPublic.do", params, 1);
    },
    // 电站登记-CHD_EPC电站登记信息
    registChdEpcStation: (params) => {
        return post("/merchant/light/station/registChdEpcStation.do", params, 1);
    },
    // 电站登记-CHD_EPC电站登记信息修改
    updateChdStation: (params) => {
        return post("/merchant/light/station/updateChdStation.do", params, 1);
    },
    // 电站登记-CHD_EPC电站登记信息暂存
    registerChdStationCache: (params) => {
        return post("/merchant/light/station/registerChdStationCache.do", params, 1);
    },
    // 电站登记-CHD_EPC电站确认并提交审核操作
    confirmAndSubmitStationInfoChd: (params) => {
        return post("/merchant/light/station/confirmAndSubmitStationInfoChd", params, 1);
    },
};
