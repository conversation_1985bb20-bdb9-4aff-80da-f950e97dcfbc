const {
    get,
    post
} = require("@/axios/http.js");





// 保险报案(公众责任险)-删除
export const publicListDelete = (params) => {
    return post("/merchant/publicLiabilityInsurance/delete", params);
};
// 保险报案(公众责任险)-修改
export const publicListUpdate = (params) => {
    return post("/merchant/publicLiabilityInsurance/update", params, 1);
};
// 保险报案(公众责任险)-新增
export const publicListAdd = (params) => {
    return post("/merchant/publicLiabilityInsurance/add", params, 1);
};
// 保险报案(公众责任险)-导出
export const publicListExport = (params) => {
    return get("/merchant/publicLiabilityInsurance/doExport.do", params, 3);
};
// 保险报案(公众责任险)列表查询
export const publicList = (params) => {
    return get("/merchant/publicLiabilityInsurance/findByPage", params, 1);
};

// 保险报案(财产一切险)-删除
export const propertyListDelete = (params) => {
    return post("/merchant/allRisksInsurance/delete", params);
};
// 保险报案(财产一切险)-修改
export const propertyListUpdate = (params) => {
    return post("/merchant/allRisksInsurance/update", params, 1);
};
// 保险报案(财产一切险)组件,逆变器规格查询
export const propertyFindFindSkuOrSn = (params) => {
    return get("/merchant/allRisksInsurance/findSkuOrSn", params, 1);
};
// 保险报案(财产一切险)-电站编码查询电站信息
export const propertyFindByStationCode = (params) => {
    return get("/merchant/lightStationPolicyNo/findByStationCode", params, 1);
};
// 保险报案(财产一切险)-新增
export const propertyListAdd = (params) => {
    return post("/merchant/allRisksInsurance/add", params, 1);
};
// 保险报案(财产一切险)-导出
export const propertyListExport = (params) => {
    return get("/merchant/allRisksInsurance/doExport.do", params, 3);
};
// 保险报案(财产一切险)
export const propertyList = (params) => {
    return get("/merchant/allRisksInsurance/findByPage", params, 1);
};
// 保单号列表
export const policyNumberList = (params) => {
    return get("/merchant/lightStationPolicyNo/findByPage", params, 1);
};
// 运维电站租金详情
export const accountDetailOp = (params) => {
    return get("/merchant/light/lightRent/accountDetailOp.do", params);
};
// 运维电站租金查询
export const accountRecordByOp = (params) => {
    return get("/merchant/light/lightRent/accountRecordByOp.do", params);
};
// 特殊费用查询
export const getFindByPage = (params) => {
    return get("/merchant/positive/findByPage", params, 1);
};
// 下载导入模板
export const downTemplate = (params) => {
    return get("/merchant/positive/downTemplate", params, 3);
};
// 修改
export const findByChange = (params) => {
    return post("/merchant/positive/change", params, 1);
};
// 删除
export const specialDel = (params) => {
    return post("/merchant/positive/del", params);
};
// 导入
export const importData = (params) => {
    return post("/merchant/positive/importData.do", params, 1);
};
// 上传见证性资料
export const updateData = (params) => {
    return post("/merchant/positive/update", params, 1);
};
// 运维电站列表
export const lightOpStation = (params) => {
    return get("/merchant/lightOpStation/list.do", params, 1);
};
// 运维知识库
export const knowledgeList = (params) => {
    return get("/merchant/light/databaseManagementOp/list.do", params, 1);
};
// 签订合同
export const opSignMaster = (params) => {
    return post("/merchant/light/dcc/company/opSignMaster.do", params, 1);
};
//专业
export const previewOpContract = (params) => {
    return get("/merchant/light/dcc/company/previewOpContract.do", params);
};
//第三方服务商注册thirdParty
export const registTp = (params) => {
    return post("/merchant/lightOperationProvider/registSp.do", params, 1);
};
// 查询当前服务商签署状态
export const contractStatus = (params) => {
    return post("/merchant/lightOperationProvider/contractStatus", params, 1);
};
// 查询当前服务商签署状态
export const getLastSingedUrl = (params) => {
    return post("/merchant/light/dcc/company/getLastSingedUrl.do", params, 1);
};
// 运维服务商新合同弹窗标识
export const qualsNewContractSign = (params) => {
    return get("/merchant/light/op/contract/newContractSign", params);
};
// 新合同弹窗详情
export const qualsSignDetail = (params) => {
    return get("/merchant/light/op/contract/newContractSignDetail", params);
};
// CommonJS写法 /work/order/list.do
export default {
    // 逆变器列表
    lightInveterList: (params) => {
        return get("/merchant/lightOperationProvider/lightInveterList.do", params);
    },
    // 逆变器详情
    inveterDetail: (params) => {
        return get("/merchant/lightOperationProvider/inveterDetail.do", params);
    },
    // 历史天气查询
    historyWeather: (params) => {
        return get("/merchant/lightOperationProvider/historyWeather.do", params);
    },
    // 导出逆变器列表
    exportInveter: (params) => {
        return get("/merchant/lightOperationProvider/exportInveter.do", params, 3);
    },
    // 运维服务商签约
    getApply: (params) => {
        return post("/merchant/lightOperationProvider/apply.do", params);
    },
    checkOverdue: (params) => {
        return get("/merchant/light/staff/checkOverdue.do", params);
    },
    // 查询当前服务商类型
    getFindId: (params) => {
        return post("/merchant/lightOperationProvider/findId.do", params);
    },
    // 查看合同
    showContract: (params) => {
        // return get("/merchant/op/contract/show", params);
        // 改为法大大签署 At 2023.4.13
        return get("/merchant/light/dcc/company/previewOpContract.do", params);
    },
    // 签署合同
    signContract: (params) => {
        // return post("/merchant/op/contract/sign", params);
        // 改为法大大签署 At 2023.4.13
        return post("/merchant/light/dcc/company/opSignMaster.do", params, 1);
    },
    // 运维服务商签署区域授权协议合同
    opAuthority: (params) => {
        return post("/merchant/light/dcc/company/opAuthority.do", params);
    },
    checkContractStatus: (params) => {
        return get("/merchant/light/op/contract/checkContractStatus", params);
    },
    // 获取服务商信息
    getSpInfo: (params) => {
        return get("/merchant/light/sp/getSpInfo.do", params);
    },
    // 查询指定的运维服务商
    getMemberId: (params) => {
        return get("/merchant/lightOperationProvider/get.do", params);
    },
    // 获取运维工单列表
    getOprationList: (params) => {
        return get("/merchant/work/order/list.do", params);
    },
    // 改派/派工
    changeDispatcher: (params) => {
        return post("/merchant//work/order/dispatcher.do", params);
    },
    // 确认完工
    completeOrder: (params) => {
        return post("/merchant/work/order/complete.do", params);
    },
    // 获取运维工单详情
    getOrderDetails: (params) => {
        return get("/merchant/work/order/detail.do", params);
    },
    // 运维工单新增过程信息
    addProcess: (params) => {
        return post("/merchant/work/order/addProcess.do", params, 1);
    },
    // 运维工单故障现象
    findFirst: (params) => {
        return get("/merchant/lightFaultDictionary/findFirst.do", params);
    },
    // 运维工单故障原因
    findSecond: (params) => {
        return get("/merchant/lightFaultDictionary/findSecond.do", params);
    },
    // 获取所有三级故障字典列表
    findThird: (params) => {
        return get("/merchant/lightFaultDictionary/findThird.do", params);
    },
    // 运维工单导出
    getDoExport: (params) => {
        return get("/merchant/work/order/doExport.do", params, 3);
    },
    // 获取人员列表
    getPersonnelList: (params) => {
        return get("/merchant/light/staff/find.do", params, 0, true);
    },
    // 获取服务人员
    getStaffList: (params) => {
        return get("/merchant/light/staff/getList.do", params);
    },
    // 获取人员详情
    getPersonDetails: (params) => {
        return get("/merchant/light/staff/get.do", params);
    },
    // 新增员工
    newEmployee: (params) => {
        return post("/merchant/light/staff/add.do", params, 1);
    },
    // 修改员工状态
    editStatus: (params) => {
        return post("/merchant/light/staff/editEmployeeStatus.do", params, 1);
    },
    // 上传资料
    uploadData: (params) => {
        return post("/merchant/light/staff/uploadData.do", params, 1);
    },
    // 结算列表
    getBillsList: (params) => {
        // return get("/merchant/settle/bille/list.do", params)
        // At 2023.04.21 调整
        return get("/merchant/sp/ledger/findByPage", params);
    },
    // 导出结算列表
    billExport: (params) => {
        // return get("/merchant/settle/bille/doExport.do", params, 3)
        // At 2023.04.21 调整
        return get("/merchant/sp/ledger/doExport", params, 3);
    },
    // 结算列表-查看确认函
    getContractPdf: (params) => {
        return get("/merchant/sp/ledger/getContractPdf", params);
    },
    // 确认按钮校验
    checkConfirm: (params) => {
        return post("/merchant/sp/ledger/checkConfirm", params);
    },
    // 结算列表-确认签章
    signEleContract: (params) => {
        return get("/merchant/sp/ledger/signEleContract", params);
    },
    // 结算列表-账单内电站列表
    getBillDetails: (params) => {
        return get("/merchant/sp/ledger/showDetails", params);
    },
    // 结算列表-导出电站列表
    billDetailExport: (params) => {
        return get("/merchant/sp/ledger/doExportDetails", params, 3);
    },
    // 新增评价
    addEvaluate: (params) => {
        return post("/merchant/merchant/evaluate/add.do", params, 1);
    },
    // 获取备件信息列表
    getSpareList: (url, params) => {
        return post("/merchant/repairs/woPart/orderWoPartPage?page=" + url.page + "&rows=" + url.rows, params, 1);
    },
    // 获取备件信息 附件信息
    orderWoPartPageAndFile: (url, params) => {
        return post("/merchant/repairs/woPart/orderWoPartPageAndFile?page=" + url.page + "&rows=" + url.rows, params, 1);
    },
    // 运维服务商合同列表
    ospList: (params) => {
        return get("/merchant/light/op/contract/list.do", params);
    },
    // 查看已签订的新合同
    viewDetail: (params) => {
        return get("/merchant/light/op/contract/detail.do", params);
    },
    // 查询运维服务区域列表
    regionsList: (params) => {
        return get("/merchant/light/lightOpAuthorityZone/list.do", params);
    },
    // 新增服务区域审核
    addNewArea: (params) => {
        return post("/merchant/light/lightOpAuthorityZone/add.do", params, 1);
    },
    // 编辑服务区域审核
    editUpdate: (params) => {
        return post("/merchant/light/lightOpAuthorityZone/update.do", params, 1);
    },
    regionDelete: (params) => {
        return post("/merchant/light/lightOpAuthorityZone/delete.do", params);
    },
    //保证金总和
    marginSumPay: (params) => {
        return get("/merchant/light/lightOperationDeposit/sumPay", params);
    },
    // 运维保证金列表
    marginList: (params) => {
        return get("/merchant/light/lightOperationDeposit/list.do", params);
    },
    // 交运维保证金
    marginAddDeposit: (params) => {
        return post("/merchant/light/lightOperationDeposit/addDeposit.do", params, 1);
    },
    // 去支付校验接口
    marginCheckToPay: (params) => {
        return get("/merchant/light/lightOperationDeposit/checkToPay", params);
    },
    // 去支付保证金
    marginToPay: (params) => {
        return post("/merchant/light/lightOperationDeposit/toPay.do", params, 1);
    },
    //修改保证金
    modifyDeposit: (params) => {
        return post("/merchant/light/lightOperationDeposit/modifyDeposit.do", params, 1);
    },
    // 结算账单(工商业)
    getFindByPage: (params) => {
        return get("/merchant/sp/iac/findByPage", params);
    },
    // 结算账单-查看确认函(工商业)
    getOamContractPdf: (params) => {
        return get("/merchant/sp/iac/getContractPdf", params);
    },
    //确认按钮校验
    oamCheckConfirm: (params) => {
        return post("/merchant/sp/iac/checkConfirm", params);
    },
    // 结算账单-确认签章(工商业)
    getOamSignEleContract: (params) => {
        return get("/merchant/sp/iac/signEleContract", params);
    },
    // 导出结算账单(工商业)
    getOamDoExport: (params) => {
        return get("/merchant/sp/iac/doExport", params, 3);
    },
    // 电站数据变更列表
    powerPlantChangesList: (params) => {
        return get("/merchant/lightStationInverterChange/list.do", params);
    },
    //新增或者修改电站逆变器变更数据
    createData: (params) => {
        return post("/merchant/lightStationInverterChange/createData", params, 1);
    },
    //根据电站编码查逆变器
    findByStationCode: (params) => {
        return post("/merchant/lightStationInverterChange/findByStationCode", params);
    },
    //终止流程
    abortProcess: (params) => {
        return post("/merchant/lightStationInverterChange/stop", params);
    },
    //服务商正激励列表查询(户用)
    settPolicyPlusFind: (params) => {
        return get("/merchant/lightSpOpsPositive/findByPage", params);
    },
    //服务商正激励列表-导出(户用)
    settPolicyPlusExport: (params) => {
        return get("/merchant/lightSpOpsPositive/doExport", params, 3);
    },
    //服务商负激励列表查询(户用)
    settPolicyReductionFind: (params) => {
        return get("/merchant/lightSpOpsNegativeExcitation/findByPage", params);
    },
    //服务商负激励列表-导出(户用)
    settPolicyReductionExport: (params) => {
        return get("/merchant/lightSpOpsNegativeExcitation/doExport", params, 3);
    },
    //服务商正激励列表查询(工商业)
    oamPolicyPlusFind: (params) => {
        return get("/merchant/iac/positive/findByPage", params);
    },
    //服务商正激励列表-导出(工商业)
    oamPolicyPlusExport: (params) => {
        return get("/merchant/iac/positive/doExport", params, 3);
    },
    //服务商负激励列表查询(工商业)
    oamPolicyReductionFind: (params) => {
        return get("/merchant/iac/nagative/findByPage", params);
    },
    //服务商负激励列表-导出(工商业)
    oamPolicyReductionExport: (params) => {
        return get("/merchant/iac/nagative/doExport", params, 3);
    },
    // 企业资质
    // 质保期内的建站运维服务商弹窗提示
    qualsNewStationOp: (params) => {
        return get("/merchant/light/staff/newStationOp", params);
    },
    // 质保期内的建站运维服务商点击弹窗逻辑,增加质保期角色
    qualsAddNewWarrantyRole: (params) => {
        return get("/merchant/light/staff/addNewWarrantyRole", params);
    },
    // 已完成签约的老运维服务商的新政策服务兵资质认证弹窗
    qualsOperationStaff: (params) => {
        return get("/merchant/light/staff/completeOperationStaff", params);
    },
    // 已完成签约的老运维服务商的新政策企业资质认证弹窗
    qualsCertification: (params) => {
        return get("/merchant/light/staff/completeOperationCertification", params);
    },
    //运维商企业认证资质数据新增
    qualsAddData: (params) => {
        return post("/merchant/lightOperationCertification/addData", params);
    },
    //服务资质管理列表信息
    qualsFindData: (params) => {
        return post("/merchant/lightOperationCertification/findData", params, 0, true);
    },
    //上传或者驳回后修改资质认证
    qualsUploadData: (params) => {
        return post("/merchant/lightOperationCertification/uploadData", params, 1);
    },
    //运维工单申诉审核提交/修改
    appealCreateAppeal: (params) => {
        return post("/merchant/work/order/createAppeal.do", params);
    },
};
