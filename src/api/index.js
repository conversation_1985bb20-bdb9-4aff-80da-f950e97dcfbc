import {
    post,
    get,
    deletes
} from "@/axios/http.js";

function getIndex(params) {
    // return get('/mock/5cb48c7ed491cd741c54456f/base/index', params)
}
// 获取登录token
function login(params) {
    return post("/oauth2/oauth/token", params);
}
// 刷新token
function againGetToken(params) {
    return post("/oauth2/oauth/token", params);
}
// 发送手机验证码
function getVerificationCode(params) {
    return post("/merchant/member/reg/check_and_send_sms.do", params);
}
// 验证手机验证码
function cleakVerificationCode(params) {
    return post(`/merchant/member/reg/check_sms_code.do?code=${params.code}&reqid=${params.reqid}`);
}
// 注册用户
function doRegist(params) {
    return post("/merchant/member/reg/do_regist.do", params);
}
// 商品三级 选择
function getCategoryList(params) {
    return get("/merchant/item/category/children.do", params);
}
// 通过三级类目ID获取对应的SPU列表
function getCategoryFind(params) {
    return get("/merchant/item/spu/find_by_category.do", params);
}
// 获取用户信息
function getUserInfo(params) {
    return get("/oauth2/principal.do", params);
}
// 退出登录
function signOut(params) {
    return deletes("/oauth2/oauth/token", params);
}
// 根据商品spu  获取商品信息
function commodityInfo(params) {
    return get("/merchant/item/item/release_init.do", params);
}
// 查询保本价
function getBreakevenPrice(params) {
    return get("/merchant/item/item/getBreakevenPrice.do", params);
}
// 校验SKU
function releaseSkuValidate(params) {
    return post("/merchant/item/item/releaseSkuValidate.do", params, 1);
}
// 发布商品
function releaseItem(params) {
    return post("/merchant/item/item/releaseItem.do", params, 1);
}
// 产品列表
function getProductList(params) {
    return get("/merchant/item/sku/find.do", params);
}
// 商品列表
function getCommodityList(params) {
    return get("/merchant/item/item/find.do", params);
}
// 产品上架 下架
function skuOnOffShelf(params) {
    return post("/merchant/item/sku/skuOnOffShelf.do", params, 1);
}
// 修改产品
function getShopItem(params) {
    return get("/merchant/item/item/getShopItem.do", params, 1);
}
// 发布优惠券
function saveCoupon(params) {
    return post("/merchant/coupon/saveCoupon.do", params, 1);
}
// 获取发布优惠券 商品选选择
function couponListItem(params) {
    return get("/merchant/coupon/listItem.do", params);
}
// 获取优惠券列表
function getCouponList(params) {
    return get("/merchant/coupon/find.do", params);
}
// 发布优惠券
function releaseCoupon(params) {
    return post("/merchant/coupon/publish.do", params, 1);
}
// 删除优惠券
function deleteCoupon(params) {
    return post("/merchant/coupon/delete.do", params, 1);
}
// 根据优惠券ID查询适用商品
function showItem(params) {
    return get("/merchant/coupon/showItem.do", params, 1);
}
// /item/purchase/findCountyPurchaseItem.do  县域采购商品分页查询
function findCountyPurchaseItem(params) {
    return get("/merchant/item/purchase/findCountyPurchaseItem.do", params, 1);
}
// /item/purchase/findInternalPurchaseItem.do 内部采购商品分页查询
function findInternalPurchaseItem(params) {
    return get("/merchant/item/purchase/findInternalPurchaseItem.do", params, 1);
}
// /item/purchase/findEnterprisePurchaseItem.do 企业客户采购商品分页查询
function findEnterprisePurchaseItem(params) {
    return get("/merchant/item/purchase/findEnterprisePurchaseItem.do", params, 1);
}
// * 1TP0采购列表
function find1TP0Item(params) {
    return get("/merchant/item/purchase/find1TP0Item.do", params, 1);
}
// /item/purchase/findProfessionalPurchaseItem.do  专业客户采购商品分页查询
function findProfessionalPurchaseItem(params) {
    return get("/merchant/item/purchase/findProfessionalPurchaseItem.do", params, 1);
}
// /member/vatInvoiceQualification/get.do 获取当前用户增票资质
function getInvoice(params) {
    return get("/merchant//member/vatInvoiceQualification/get.do", params, 1);
}
// /member/vatInvoiceQualification/save.do保存增票资质
function saveInvoice(params) {
    return post("/merchant//member/vatInvoiceQualification/save.do", params, 1);
}
// 获取省市区
function getRegionList(params) {
    return get("/merchant/member/address/regionList.do", params);
}
// /member/vatInvoiceQualification/delete.do删除增票资质
function deleteInvoice(params) {
    return post("/merchant/member/vatInvoiceQualification/delete.do", params, 1);
}
// /member/address/list.do 获取用户收货地址列表
function getaddressList(params) {
    return get("/merchant/member/address/list.do", params);
}
// member/address/get.do 获取当前用户默认收货地址
function getaddress(params) {
    return get("/merchant/member/address/get.do", params);
}
// /member/address/saveAddress.do 保存/修改收货地址
function saveaddress(params) {
    return post("/merchant/member/address/saveAddress.do", params, 1);
}
// /member/address/mark_as_default.do 设置当前地址为默认收货地址
function markaddress(params) {
    return get("/merchant/member/address/mark_as_default.do", params);
}
// /item/purchase/listXiaoweiCategory.do 查询小微分类
function listXiaoweiCategory(params) {
    return get("/merchant/item/purchase/listXiaoweiCategory.do", params);
}
// /merchant/order/orderItem/orderItemList.do? 订单列表
function getOrder(params) {
    return get("/merchant/order/orderItem/orderItemList.do", params);
}
// merchant/order/orderItem/deliverOrderItem.do?    发货
function deliverOrderItem(params) {
    return post(
        `/merchant/order/orderItem/deliverOrderItem.do?orderItemId=${params.orderItemId}&deliveryVoucher=${params.deliveryVoucher}&expressNo=${params.expressNo}&expressCode=${params.expressCode}&expressName=${params.expressName}&visitType=${params.visitType}&expressType=${params.expressType}`
        );
}
// /order/orderItem/listAllExpress.do// 获取快递公司列表

function listAllExpress(params) {
    return get("/merchant/order/orderItem/listAllExpress.do", params);
}
// /order/orderItem/downloadTemplate.do下载批量订单发货模板
function downloadTemplate(params) {
    return get("/merchant/order/orderItem/downloadTemplate.do", params);
}
// merchant/order/orderItem/orderItemDetail.do?orderItemId=1  订单详情
function orderItemDetail(params) {
    return get("/merchant/order/orderItem/orderItemDetail.do", params);
}
// merchant/order/orderItem/findExpressRecord.do?orderItemId=  查看快递
function findExpressRecord(params) {
    return get("/merchant/order/orderItem/findExpressRecord.do", params);
}
// 订单确认 /merchant/order/cart/getOrderCartBySettle.do?skuNumberList=12%3A1%2C23%3A1
function getOrderCartBySettle(params) {
    return get("/merchant/order/cart/getOrderCartBySettle.do", params);
}
// 创建订单 /merchant/order/create.do
function createOrder(params) {
    return post("/merchant/order/create.do", params, 1);
}
// 10.1.132.22:8093/payapi/pay/createPayment.do" -  创建支付单
function createPayment(params) {
    return post("/payapi/pay/createPayment.do", params, 1);
}
/// payapi/pay/pay.do?payNo=JK0000001  支付
function pay(params) {
    return post(`/payapi/pay/pay.do?payNo=${params.payNo}`);
}
// /merchant/order/orderRepair/auditRefundOrderItem.do  审核退款
function auditRefundOrderItem(params) {
    return post(
        `/merchant/order/orderRepair/auditRefundOrderItem.do?auditStatus=${params.auditStatus}&description=${params.description}&orderItemId=${params.orderItemId}&visitType=${params.visitType}`
        );
}
// /merchant/order/orderItem/orderItemList.do? 获取我的订单
function getBuyerList(params) {
    return get("/merchant/order/buyer/orderItemList.do", params);
}
// /merchant/order/buyer/cancelOrderItem.do?orderItemId=1  取消订单
function cancelOrderItem(params) {
    return post(`/merchant/order/buyer/cancelOrderItem.do?orderItemId=${params.orderItemId}`);
}
// 客户入住 发送验证码
function getVerificationCodeTwo(params) {
    return post(`/merchant/member/reg/send_sms.do?mobile=${params.mobile}`);
}
// /merchant/corporate/regist.do //企业客户注册
function endRegist(params) {
    return post("/merchant/corporate/regist.do", params, 1);
}
// /merchant/corporate/regist.do //企业客户被驳回后再次注册
function endUpdate(params) {
    return post("/merchant/corporate/update.do", params, 1);
}
// /merchant/member/operator/operatorShopRelationList.do?pageNum=1&pageSize=10 查询购买人
function operatorShopRelationList(params) {
    return get(
        `/merchant/member/operator/operatorShopRelationList.do?pageNum=${params.pageNum}&pageSize=${params.pageSize}&operatorMobile=${params.operatorMobile}&operatorName=${params.operatorName}`
        );
}
// /order/buyer/uploadTransferVoucher.do 上传转账汇款凭证
function uploadTransferVoucher(params) {
    return post("/merchant/order/buyer/uploadTransferVoucher.do", params, 1);
}
// /order/buyer/applyRefundOrderItem.do 买家申请退款
function applyRefundOrderItem(params) {
    return post(
        `/merchant/order/buyer/applyRefundOrderItem.do?orderItemId=${params.orderItemId}&description=${params.description}&reason=${params.reason}`
        );
}
// /order/buyer/finishOrderItem.do 买家完成关闭订单
function finishOrderItem(params) {
    return post(`/merchant/order/buyer/finishOrderItem.do?orderItemId=${params.orderItemId}`);
}
// /order/buyer/cancelRefundOrderItem.do 买家取消退款
function cancelRefundOrderItem(params) {
    return post(`/merchant/order/buyer/cancelRefundOrderItem.do?orderItemId=${params.orderItemId}`);
}
// /shop/getAgreementMark.do 获取店铺阅读协议状态
function getAgreementMark(params) {
    return get("/merchant/mch/getAgreementMark.do");
}
// /shop/updateAgreementMark.do更新是否阅读协议状态
function updateAgreementMark(params) {
    return get("/merchant/mch/updateAgreementMark.do");
}
// /item/item/addShopRemark.do 添加商品商家备注
function addShopRemark(params) {
    return post("/merchant/item/item/addShopRemark.do", params, 1);
}
// /item/item/addShopRemark.do 添加商品商家备注  订单
function orderAddShopRemark(params) {
    return post("/merchant/order/orderItem/addShopRemark.do", params, 1);
}
// 找回密码-修改 /member/find_password/change.do
function passwordchange(params) {
    return post("/merchant/member/find_password/change.do", params);
}
// /member/changePassword.do 修改密码
function changePassword(params) {
    return post("/merchant/member/changePassword.do", params);
}
// /member/find_password/send_sms.do  获取验证码
function getSendSms(params) {
    return get("/merchant/member/find_password/send_sms.do", params);
}
// /merchant/shop/getShop.do
function getShopInfo(params) {
    return get("/merchant/shop/getShop.do", params);
}
// 获取当前企业客户信息
function getEntInfo(params) {
    return get("/merchant/corporate/get.do", params);
}
// 获取当前店铺或供应商的信息
function getKhInfo(params) {
    return get("/merchant/corporate/getInfo.do", params);
}
// /settle/sapPurchase/sapPurchaseList.do 对账单明细列表
function sapPurchaseList(params) {
    return get("/merchant/settle/sapPurchase/sapPurchaseList.do", params);
}
// /settle/purchaseApplySettle/purchaseApplySettle.do 结款列表
function purchaseApplySettle(params) {
    return get("/merchant/settle/purchaseApplySettle/purchaseApplySettle.do", params);
}
// /settle/purchaseApplySettle/purchaseApplySettleDetailView.do 结款列表明细
function purchaseApplySettleDetailView(params) {
    return get("/merchant/settle/purchaseApplySettle/purchaseApplySettleDetailView.do", params);
}
// /merchant/suppliers/addSuppliers.do 申请/修改供应商
function addSuppliers(params) {
    return post("/merchant/suppliers/addSuppliers.do", params, 1);
}
// /merchant/suppliers/getSuppliersInfo.do 获取供应商合同要续签/修改的单条记录
function getSuppliersInfo(params) {
    return get("/merchant/suppliers/getSuppliersInfo.do", params);
}
// /merchant/suppliers/suppliersList.do 获取供应商多条合同记录
function getSuppliersList(params) {
    return get("/merchant/suppliers/suppliersList.do", params);
}
// /merchant/oss/file/upload.do 上传文件
function uploadFile(params) {
    return post("/merchant/oss/file/upload.do", params);
}
// merchant/settle/sapPurchase/sapPurchaseListExcel.do 导出
function sapPurchaseListExcel(params) {
    return get("/merchant/settle/sapPurchase/sapPurchaseListExcel.do", params, 3);
}
// /order/orderItem/orderItemListExcel.do商家导出订单列表
function orderItemListExcel(params) {
    return get("/merchant/order/orderItem/orderItemListExcel.do", params, 3);
}
// /order/orderItem/orderItemCount.do 商家后台订单各个状态数量
function orderItemCount(params) {
    return get("/merchant/order/orderItem/orderItemCount.do", params);
}
// /item/saleArea/save.do 保存销售区域
function saleArea(params) {
    return post("/merchant/item/saleArea/save.do", params, 1);
}
// /item/saleArea/findByShop.do销售区块列表/
function findByShop(params) {
    return get("/merchant/item/saleArea/findByShop.do", params);
}
// /item/saleArea/defaultSaleArea.do 设置为默认销售区域
function defaultSaleArea(params) {
    return post(`/merchant/item/saleArea/defaultSaleArea.do?id=${params.id}`);
}
// /posthouse/posthouseList.do 商家后台自提点列表
function posthouseList(params) {
    return get("/merchant/posthouse/posthouseList.do", params);
}

function getGeocoder(params) {
    get("https://apis.map.qq.com/ws/geocoder/v1", params, "https://apis.map.qq.com/ws/geocoder/v1");
}
// /posthouse/addPosthouse.do 店铺创建自提点
function addPosthouse(params) {
    return post("/merchant/posthouse/addPosthouse.do", params, 1);
}
// /posthouse/downloadTemplate.do 下载模板
function downloadTemplateOne(params) {
    return get("/merchant/posthouse/downloadTemplate.do", params, 3);
}
// /posthouse/updatePosthouse.do 店铺xiugai自提点
function updatePosthouse(params) {
    return post("/merchant/posthouse/updatePosthouse.do", params, 1);
}
// /posthouse/posthouseErrExcel.do 错误自提点导出列表/
function posthouseErrExcel(params) {
    return get("/merchant/posthouse/posthouseErrExcel.do", params, 3);
}
// /order/group/findBySkuSubtotal.do 查询商品汇总
function findBySkuSubtotal(params) {
    return get("/merchant/order/group/findBySkuSubtotal.do", params);
}
// /order/group/findByDeliverSubtotal.do 查询配送清单
function findByDeliverSubtotal(params) {
    return get("/merchant/order/group/findByDeliverSubtotal.do", params);
}
// /order/group/findByPickSubtotal.do 查询提货清单
function findByPickSubtotal(params) {
    return get("/merchant/order/group/findByPickSubtotal.do", params);
}
// /order/group/exportBySkuSubtotal.do 导出商品汇总
function exportBySkuSubtotal(params) {
    return get("/merchant/order/group/exportBySkuSubtotal.do", params, 3);
}
// /order/group/exportByPickSubtotal.do 导出提货清单
function exportByPickSubtotal(params) {
    return get("/merchant/order/group/exportByPickSubtotal.do", params, 3);
}
// /order/group/exportByDeliverSubtotal.do 导出配送清单
function exportByDeliverSubtotal(params) {
    return get("/merchant/order/group/exportByDeliverSubtotal.do", params, 3);
}
// /order/group/exportByLineSubtotal.do 导出线路拣货清单
function exportByLineSubtotal(params) {
    return get("/merchant/order/group/exportByLineSubtotal.do", params, 3);
}
// /driverDelivery/driverDeliveryList.do 商家后台司机配送管理列表
function driverDeliveryList(params) {
    return get("/merchant/driverDelivery/driverDeliveryList.do", params);
}
// /driverDelivery/addDriverDelivery.do 创建司机配送
function addDriverDelivery(params) {
    return post("/merchant/driverDelivery/addDriverDelivery.do", params, 1);
}
// /region/getRegionList.do 获取区域信息
function getRegionListOne(params) {
    return get("/merchant/region/getRegionList.do", params);
}
// /driverDelivery/addDriverDeliveryRegion.do 添加镇/街道
function addDriverDeliveryRegion(params) {
    return post("/merchant/driverDelivery/addLinePosthouse.do", params, 1);
}
// /driverDelivery/updateDriverDelivery.do 修改司机配送
function updateDriverDelivery(params) {
    return post("/merchant/driverDelivery/updateDriverDelivery.do", params, 1);
}
// /driverDelivery/delLinePosthouse.do 删除自提点
function delLinePosthouse(params) {
    return post(
        `/merchant/driverDelivery/delLinePosthouse.do?posthouseId=${params.posthouseId}&lineId=${params.lineId}`);
}
// /shop/getRealShop.do 查询实时店铺信息
function getRealShop(params) {
    return get("/merchant/shop/getRealShop.do", params);
}
// /order/group/findByLineSubtotal.do 查询线路拣货清单
function findByLineSubtotal(params) {
    return get("/merchant/order/group/findByLineSubtotal.do", params);
}

function wareHttp(params) {
    return post("/mecv-protocol/niuren/wms", params, 1);
}

function cartHttp(params) {
    return post("/mecv-protocol/niuren/acccess_token/interface", params, 1);
}
// /expert/expertList.do 牛人客户列表
function expertList(params) {
    return get("/merchant/expert/expertList.do", params);
}
// /expert/disabledExpertCustomer.do 禁用客户
function disabledExpertCustomer(params) {
    return post(`/merchant/expert/disabledExpertCustomer.do?expertCustomerId=${params.expertCustomerId}`);
}
// /expert/enabledExpertCustomer.do 启用客户
function enabledExpertCustomer(params) {
    return post(`/merchant/expert/enabledExpertCustomer.do?expertCustomerId=${params.expertCustomerId}`);
}
// /expert/deletedExpertCustomer.do 删除客户
function deletedExpertCustomer(params) {
    return post(`/merchant/expert/deletedExpertCustomer.do?expertCustomerId=${params.expertCustomerId}`);
}
// /mch/fruzzFindByMerchantName.do 根据公司名称模糊查询公司
function fruzzFindByMerchantName(params) {
    return get("/merchant/mch/fruzzFindByMerchantName.do", params);
}
// /expert/getCustomerInfo.do 牛人客户信息
function getCustomerInfo(params) {
    return get("/merchant/expert/getCustomerInfo.do", params);
}
// order/orderItem/adjustPayAmount.do 调整订单支付金额
function adjustPayAmount(params) {
    return post(
        `/merchant/order/orderItem/adjustPayAmount.do?orderItemId=${params.orderItemId}&payAmount=${params.payAmount}`
        );
}
// /order/orderItem/offlineConfirmPaid.do  确收款 线下汇款订单确认收款
function offlineConfirmPaid(params) {
    return post(`/merchant/order/orderItem/offlineConfirmPaid.do?orderItemId=${params.orderItemId}`);
}
// /expert/addExpertCustomer.do 创建牛人客户
function addExpertCustomer(params) {
    return post("/merchant/expert/addExpertCustomer.do", params, 1);
}
// /order/orderItem/cctCarDeliverOrderItem.do  车队发货
function cctCarDeliverOrderItem(params) {
    return post(
        `/merchant/order/orderItem/cctCarDeliverOrderItem.do?storeCode=${params.storeCode}&storeName=${params.storeName}&orderItemId=${params.orderItemId}&cctCarCode=${params.cctCarCode}&cctCarName=${params.cctCarName}&deliveryFee=${params.deliveryFee}`
        );
}
// /mch/getByTaxNo.do 根据税号查询公司信息/
function getByTaxNo(params) {
    return get("/merchant/mch/getByTaxNo.do", params);
}
// /expert/saveCustomerAddress.do 保存/修改收货地址
function saveCustomerAddress(params) {
    return post("/merchant/expert/saveCustomerAddress.do", params, 1);
}
// expert/getCustomerDetail.do 牛人客户详情
function getCustomerDetail(params) {
    return get("/merchant/expert/getCustomerDetail.do", params);
}
// /order/orderItem/paidOrderSumCount.do 统计已付款的订单数量和金额汇总/
function paidOrderSumCount(params) {
    return get("/merchant/order/orderItem/paidOrderSumCount.do", params);
}
// /order/orderItem/paidOrderSumCountWeekGroup.do 指定的周数的已付款订单数量和金额汇总
function paidOrderSumCountWeekGroup(params) {
    return get("/merchant/order/orderItem/paidOrderSumCountWeekGroup.do", params);
}
// /workbenchFinancial/queryWorkbenchFinanceInfo.do 管理事项
function queryWorkbenchFinanceInfo(params) {
    return get("/merchant/workbenchFinancial/queryWorkbenchFinanceInfo.do", params);
}
// /expert/getCustomerSum.do 牛人客户数量
function getCustomerSum(params) {
    return get("/merchant/expert/getCustomerSum.do", params);
}
// /customerAnalyze/queryCustomerAnalyzeInfo.do 应收/逾期客户分析
function queryCustomerAnalyzeInfo(params) {
    return get("/merchant/customerAnalyze/queryCustomerAnalyzeInfo.do", params);
}
// /customerAnalyze/queryReceivableCustomerInfo.do 应收/逾期订单明细
function queryReceivableCustomerInfo(params) {
    return get("/merchant/customerAnalyze/queryReceivableCustomerInfo.do", params);
}
// /chargeSaleCustomer/queryChargeSaleCustomerInfo.do 赊销客户管理
function queryChargeSaleCustomerInfo(params) {
    return get("/merchant/chargeSaleCustomer/queryChargeSaleCustomerInfo.do", params);
}
// /financialSolutions/queryFinancialSolutions.do 金融结算列表
function jrRqueryChargeSaleCustomerInfo(params) {
    return get("/merchant/financialSolutions/queryFinancialSolutions.do", params);
}
// /financialSolutions/queryFinancialSolutionsDetails.do 金融结算明细
function queryFinancialSolutionsDetails(params) {
    return get("/merchant/financialSolutions/queryFinancialSolutionsDetails.do", params);
}
// /finance/incomeInventory/incomeInventoryList.do 商家后台收入盘点列表
function incomeInventoryList(params) {
    return get("/merchant/finance/incomeInventory/incomeInventoryList.do", params);
}
// /finance/incomeInventory/sumIncomeAmount.do 收入金额汇总
function sumIncomeAmount(params) {
    return get("/merchant/finance/incomeInventory/sumIncomeAmount.do", params);
}
// /member/vatInvoiceQualification/findByMember.do 用户增票资质列表
function findByMember(params) {
    return get("/merchant/member/vatInvoiceQualification/findByMember.do", params);
}
// /settle/shopSettlement/shopBindInfo.do 账号绑定信息
function shopBindInfo(params) {
    return get("/merchant/settle/shopSettlement/shopBindInfo.do", params);
}
// /settle/shopSettlement/shopCheckBillList.do 对账单
function shopCheckBillList(params) {
    return get("/merchant/settle/shopSettlement/shopCheckBillList.do", params);
}
// /settle/shopSettlement/shopCheckBillListExcel.do  对账单列表导出
function shopCheckBillListExcel(params) {
    return get("/merchant/settle/shopSettlement/shopCheckBillListExcel.do", params, 3);
}
// /settle/shopSettlement/shopSettlementBillList.do结算单
function shopSettlementBillList(params) {
    return get("/merchant/settle/shopSettlement/shopSettlementBillList.do", params);
}
// /settle/shopSettlement/shopSettlementBillListExcel.do 结算单列表导出
function shopSettlementBillListExcel(params) {
    return get("/merchant/settle/shopSettlement/shopSettlementBillListExcel.do", params, 3);
}
// /item/skuMeal/find.do 套餐商品关系列表
function skuMealFind(params) {
    return get("/merchant/item/skuMeal/find.do", params);
}
// /item/skuMeal/findSkuMealBySkuId.do 查询指定套餐关联的商品
function findSkuMealBySkuId(params) {
    return get("/merchant/item/skuMeal/findSkuMealBySkuId.do", params);
}
// /item/skuMeal/saveSkuMeal.do 保存套餐商品关系
function saveSkuMeal(params) {
    return post("/merchant/item/skuMeal/saveSkuMeal.do ", params, 1);
}
// /item/sku/getSkuById.do 查询指定的sku信息
function getSkuById(params) {
    return get("/merchant/item/sku/getSkuById.do", params);
}
// /expert/setCustomerCredit.do 修改客户额度和账期
function setCustomerCredit(params) {
    return post("/merchant/expert/setCustomerCredit.do ", params);
}
// /expert/findContractInfo.do 查询合同信息
function findContractInfo(params) {
    return get("/merchant/expert/findContractInfo.do", params);
}
// /expert/saveContract.do 保存合同
function saveContract(params) {
    return post("/merchant/expert/saveContract.do ", params, 1);
}
// /echannel/tradeOrderList.do 商户渠道交易订单列表
function tradeOrderList(params) {
    return get("/merchant/echannel/tradeOrderList.do", params);
}
// /echannel/getCctExpressPrice.do
function getCctExpressPrice(params) {
    return get("/merchant/echannel/getCctExpressPrice.do", params);
}
// /echannel/deliverTradeOrder.do 商户渠道交易发货
function deliverTradeOrder(params) {
    return post("/merchant/echannel/deliverTradeOrder.do ", params);
}
// /order/orderItem/orderItemJointVentureList.do 商家后台合资订单列表
function orderItemJointVentureList(params) {
    return get("/merchant/order/orderItem/orderItemJointVentureList.do", params);
}
// /order/orderItem/uploadAccountVoucher.do 上传对账单凭证
function uploadAccountVoucher(params) {
    return post("/merchant/order/orderItem/uploadAccountVoucher.do ", params);
}
// /order/orderItem/confirmSignQuantity.do 供应商确认签收数量
function confirmSignQuantity(params) {
    return post("/merchant/order/orderItem/confirmSignQuantity.do ", params);
}
// /order/orderItem/getOrderInvoice.do 主订单发票详情
function getOrderInvoice(params) {
    return get("/merchant/order/orderItem/getOrderInvoice.do", params);
}
// /order/orderItem/uploadInvoiceVoucher.do 上传发票凭证
function uploadInvoiceVoucher(params) {
    return post("/merchant/order/orderItem/uploadInvoiceVoucher.do ", params);
}
// /cart/addToCart.do 加入购物车
function addToCart(params) {
    return post("/merchant/cart/addToCart.do ", params, 1);
}
// /cart/getCartItemNumber.do 购物车商品数量
function getCartItemNumber(params) {
    return get("/merchant/cart/getCartItemNumber.do", params);
}
// /cart/get.do购物车
function getCartList(params) {
    return get("/merchant/cart/get.do", params);
}
// /cart/clearCart.do 删除购物车商品
function clearCart(params) {
    return post(`/merchant/cart/clearCart.do`, params, 1);
}
// /cart/batchAddToCart.do 加入购物车
function batchAddToCart(params) {
    return post("/merchant/cart/batchAddToCart.do ", params, 1);
}
// /cart/modifyNumber.do 修改购物车商品数量
function modifyNumber(params) {
    return post("/merchant/cart/modifyNumber.do ", params, 1);
}
// /yzzshop/getYzzShop.do 查询云智妆店铺信息
function getYzzShop(params) {
    return get("/merchant/yzzshop/getYzzShop.do", params);
}
// /yzzshop/setYzzShop.do 设置云智妆门店信息
function setYzzShop(params) {
    return post("/merchant/yzzshop/setYzzShop.do ", params, 1);
}
// /yzzcategory/listYzzCategory.do 商品分类列表
function listYzzCategory(params) {
    return get("/merchant/yzzcategory/listYzzCategory.do", params);
}
// /yzzcategory/addYzzCategory.do  创建商品分类
function addYzzCategory(params) {
    return post("/merchant/yzzcategory/addYzzCategory.do ", params, 1);
}
// /yzzcategory/disabled.do 禁用
function disabledType(params) {
    return post("/merchant/yzzcategory/disabled.do ", params);
}
// /yzzcategory/enabled.do 启用商品分类
function enabledType(params) {
    return post("/merchant/yzzcategory/enabled.do ", params);
}
// /yzzcategory/editYzzCategory.do 修改商品分类
function editYzzCategory(params) {
    return post("/merchant/yzzcategory/editYzzCategory.do ", params, 1);
}
// /yzzguide/listYzzGuide.do 导购列表
function listYzzGuide(params) {
    return get("/merchant/yzzguide/listYzzGuide.do", params);
}
// /yzzguide/disabled.do 禁用
function disabledguide(params) {
    return post("/merchant/yzzguide/disabled.do ", params);
}
// /yzzguide/enabled.do 启用商品分类
function enabledguide(params) {
    return post("/merchant/yzzguide/enabled.do ", params);
}
// /yzzguide/addYzzGuide.do 创建导购
function addYzzGuide(params) {
    return post("/merchant/yzzguide/addYzzGuide.do ", params, 1);
}
// /yzzcategoryitem/listYzzCategoryItem.do 商品列表
function getListYzzCategory(params) {
    return get("/merchant/yzzcategoryitem/listYzzCategoryItem.do", params);
}
// /yzzcategoryitem/addYzzCategoryItem.do 创建商品
function addYzzCategoryItem(params) {
    return post("/merchant/yzzcategoryitem/addYzzCategoryItem.do", params, 1);
}
// /yzzcategoryitem/disabled.do 禁用
function itemDisabled(params) {
    return post("/merchant/yzzcategoryitem/disabled.do", params);
}
// /yzzcategoryitem/enabled.do 启用商品
function itemEnabled(params) {
    return post("/merchant/yzzcategoryitem/enabled.do", params);
}
// /yzzcategoryitem/editYzzCategoryItem.do 修改分类商品
function editYzzCategoryItem(params) {
    return post("/merchant/yzzcategoryitem/editYzzCategoryItem.do", params, 1);
}
// /item/item/copyItem.do 复制商品
function copyItem(params) {
    return post("/merchant/item/item/copyItem.do", params, 1);
}
// /yzzoptitem/getYzzOptItem.do 查询云智妆优选商品
function getYzzOptItem(params) {
    return get("/merchant/yzzoptitem/getYzzOptItem.do", params);
}
// /yzzoptitem/getItemByItemId.do 查询云智妆优选商品
function getItemByItemId(params) {
    return get("/merchant/yzzoptitem/getItemByItemId.do", params);
}
// /yzzoptitem/addYzzOptItem.do 添加商品
function addYzzOptItem(params) {
    return post("/merchant/yzzoptitem/addYzzOptItem.do", params, 1);
}
// /yzzoptitem/delYzzOptItem.do 删除商品
function delYzzOptItem(params) {
    return post("/merchant/yzzoptitem/delYzzOptItem.do", params);
}
// /item/purchase/findYzzPurchaseItem.do 云智妆采购商品分页查询
function findYzzPurchaseItem(params) {
    return get("/merchant/item/purchase/findYzzPurchaseItem.do", params);
}
// /order/lnWallet/getOrderLnWallet.do 获取乐农钱包信息
function getOrderLnWallet(params) {
    return get("/merchant/order/lnWallet/getOrderLnWallet.do", params);
}
// /order/lnWallet/sendSmsCode.do 发送乐农钱包支付短信验证码
function sendSmsCode(params) {
    return post("/merchant/order/lnWallet/sendSmsCode.do", params);
}
// /yzzUserWallet/getCompanyName.do 查询公司名称
function getCompanyName(params) {
    return get("/merchant/yzzUserWallet/getCompanyName.do", params);
}
// /yzzUserWallet/rechargeUserWallet.do 钱包充值
function rechargeUserWallet(params) {
    return post("/merchant/yzzUserWallet/rechargeUserWallet.do", params, 1);
}
// /yzzUserWallet/getWalletAmount.do 查询钱包余额
function getWalletAmount(params) {
    return get("/merchant/yzzUserWallet/getWalletAmount.do", params);
}
// /yzzUserWallet/findWalletLogList.do 查询钱包操作记录
function findWalletLogList(params) {
    return get("/merchant/yzzUserWallet/findWalletLogList.do", params);
}
// /item/category/children.do 下级类目列表
function getChildren(params) {
    return get("/merchant/item/category/children.do", params);
}
// /item/item/shopItemSearch.do 农村店铺商品搜索
function shopItemSearch(params) {
    return get("/merchant/item/item/shopItemSearch.do", params);
}
// /shopCustomer/selectCustomer.do 选择/创建商店会员
function selectCustomer(params) {
    return post("/merchant/shopCustomer/selectCustomer.do", params);
}
// /item/sku/inputBarCode.do 录入条形码
function inputBarCode(params) {
    return post("/merchant/item/sku/inputBarCode.do", params);
}
// /shopCustomer/listShopCustomer.do 门店会员分页查询
function listShopCustomer(params) {
    return get("/merchant/shopCustomer/listShopCustomer.do", params);
}
export {
    listShopCustomer,
    inputBarCode,
    selectCustomer,
    shopItemSearch,
    getChildren,
    findWalletLogList,
    getWalletAmount,
    rechargeUserWallet,
    getCompanyName,
    sendSmsCode,
    getOrderLnWallet,
    findYzzPurchaseItem,
    delYzzOptItem,
    addYzzOptItem,
    getItemByItemId,
    getYzzOptItem,
    copyItem,
    editYzzCategoryItem,
    itemEnabled,
    itemDisabled,
    addYzzCategoryItem,
    getListYzzCategory,
    addYzzGuide,
    disabledguide,
    enabledguide,
    listYzzGuide,
    editYzzCategory,
    enabledType,
    disabledType,
    addYzzCategory,
    listYzzCategory,
    setYzzShop,
    getYzzShop,
    modifyNumber,
    batchAddToCart,
    clearCart,
    getCartList,
    getCartItemNumber,
    addToCart,
    uploadInvoiceVoucher,
    getOrderInvoice,
    confirmSignQuantity,
    uploadAccountVoucher,
    orderItemJointVentureList,
    deliverTradeOrder,
    getCctExpressPrice,
    tradeOrderList,
    saveContract,
    findContractInfo,
    setCustomerCredit,
    getSkuById,
    saveSkuMeal,
    findSkuMealBySkuId,
    skuMealFind,
    shopSettlementBillListExcel,
    shopSettlementBillList,
    shopCheckBillListExcel,
    shopCheckBillList,
    shopBindInfo,
    findByMember,
    sumIncomeAmount,
    incomeInventoryList,
    queryFinancialSolutionsDetails,
    jrRqueryChargeSaleCustomerInfo,
    queryChargeSaleCustomerInfo,
    queryReceivableCustomerInfo,
    queryCustomerAnalyzeInfo,
    getCustomerSum,
    queryWorkbenchFinanceInfo,
    paidOrderSumCountWeekGroup,
    paidOrderSumCount,
    getCustomerDetail,
    saveCustomerAddress,
    getByTaxNo,
    cctCarDeliverOrderItem,
    addExpertCustomer,
    offlineConfirmPaid,
    cartHttp,
    adjustPayAmount,
    getCustomerInfo,
    fruzzFindByMerchantName,
    deletedExpertCustomer,
    enabledExpertCustomer,
    disabledExpertCustomer,
    expertList,
    wareHttp,
    exportByLineSubtotal,
    findByLineSubtotal,
    getRealShop,
    delLinePosthouse,
    updateDriverDelivery,
    addDriverDeliveryRegion,
    getRegionListOne,
    addDriverDelivery,
    driverDeliveryList,
    exportByDeliverSubtotal,
    exportByPickSubtotal,
    exportBySkuSubtotal,
    findByPickSubtotal,
    findByDeliverSubtotal,
    findBySkuSubtotal,
    posthouseErrExcel,
    updatePosthouse,
    downloadTemplateOne,
    addPosthouse,
    getGeocoder,
    posthouseList,
    defaultSaleArea,
    findByShop,
    saleArea,
    orderItemCount,
    findInternalPurchaseItem,
    orderItemListExcel,
    sapPurchaseListExcel,
    purchaseApplySettleDetailView,
    purchaseApplySettle,
    addSuppliers,
    getSuppliersInfo,
    getSuppliersList,
    uploadFile,
    sapPurchaseList,
    getIndex,
    login,
    againGetToken,
    getVerificationCode,
    cleakVerificationCode,
    doRegist,
    getCategoryList,
    getCategoryFind,
    getUserInfo,
    signOut,
    commodityInfo,
    getBreakevenPrice,
    releaseSkuValidate,
    releaseItem,
    getProductList,
    getCommodityList,
    skuOnOffShelf,
    getShopItem,
    saveCoupon,
    couponListItem,
    getCouponList,
    releaseCoupon,
    deleteCoupon,
    showItem,
    findCountyPurchaseItem,
    findEnterprisePurchaseItem,
    findProfessionalPurchaseItem,
    getInvoice,
    saveInvoice,
    getRegionList,
    deleteInvoice,
    getaddressList,
    getaddress,
    saveaddress,
    markaddress,
    listXiaoweiCategory,
    getOrder,
    deliverOrderItem,
    listAllExpress,
    downloadTemplate,
    orderItemDetail,
    findExpressRecord,
    getOrderCartBySettle,
    createOrder,
    createPayment,
    pay,
    auditRefundOrderItem,
    getBuyerList,
    cancelOrderItem,
    getVerificationCodeTwo,
    endRegist,
    endUpdate,
    operatorShopRelationList,
    uploadTransferVoucher,
    applyRefundOrderItem,
    finishOrderItem,
    cancelRefundOrderItem,
    getAgreementMark,
    updateAgreementMark,
    addShopRemark,
    orderAddShopRemark,
    passwordchange,
    changePassword,
    getSendSms,
    getShopInfo,
    getEntInfo,
    getKhInfo,
    find1TP0Item
};
