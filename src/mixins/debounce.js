export default {
  data() {
    return {
      debounceTimer: null,
      msg: null,
    };
  },
  methods: {
    /**
     * @description: 防抖，立即触发：禁止连续点击
     * @param: {fn:回调函数；delay:number类型，setTimeout的时间参数;trailing:boolean是否后触发；}
     * @author: lufy.
     * @remark   At 2024-06-11 优化，1000改500，提高防抖准确性
     * @remark   2022-11-03 优化，新增常规防抖模式：后触发无通知
     */
    debounce(func, delay = 500, trailing = false) {
      let _that = this;
      let _args = arguments;
      let callNow = !_that.debounceTimer;
      const callback = () => {
        const res = func.apply(_that, _args);
        //释放promise
        if (res && res instanceof Promise) {
          res.then(() => {}).catch(() => {});
        }
      };
      _that.debounceTimer && clearTimeout(_that.debounceTimer);
      if (!trailing) {
        _that.debounceTimer = setTimeout(() => {
          _that.debounceTimer = null;
        }, delay); // 连续点击允许间隔

        if (callNow) {
          callback();
        } else {
          if (!this.msg) {
            this.msg = this.$Message.warning("点太快了！");
            setTimeout(() => {
              this.msg = null;
            }, 1500);
          }
        }
      } else {
        _that.debounceTimer = setTimeout(() => {
          callback();
        }, delay);
      }
    },
  },
};
