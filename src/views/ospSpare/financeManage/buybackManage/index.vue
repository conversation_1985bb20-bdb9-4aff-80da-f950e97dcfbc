<template>
  <div class="cus_list">
    <Breadcrumb class="bread">
      <Breadcrumb-item to="/index/">新能源管理</Breadcrumb-item>
      <Breadcrumb-item to="/apv/">项目管理</Breadcrumb-item>
      <Breadcrumb-item>备件回购</Breadcrumb-item>
    </Breadcrumb>
    <div class="list_son">
      <div class="list_but">
        <div class="condition">
          <p class="condition_p">回购单号</p>
          <Input v-model="searchObj.borrowNo" placeholder="请输入..." clearable style="width: 150px" />
        </div>
        <div class="condition">
          <p class="condition_p">电站编码</p>
          <Input v-model="searchObj.stationCode" placeholder="请输入..." clearable style="width: 150px" />
        </div>
        <div class="condition">
          <p class="condition_p">业主姓名</p>
          <Input v-model="searchObj.ownerName" placeholder="请输入..." clearable style="width: 150px" />
        </div>
        <div class="condition">
          <p class="condition_p">回购状态</p>
          <Select v-model="searchObj.status" clearable style="width:120px;">
            <Option v-for="(item, k) in status" :value="k" :key="k">{{ item }}</Option>
          </Select>
        </div>
        <div class="condition">
          <Button @click="debounce(queryList)" type="primary">查询</Button>
        </div>
      </div>
      <div class="list_but">
        <div class="condition">
          <Button type="success" @click="submitManage('add')">提交</Button>
<!--          <Button type="primary" @click="submitManage('edit')">修改</Button>-->
          <Button type="primary" @click="uploadFile">上传支付凭证</Button>
            <Button @click="exportFn" type="error">导出</Button>
        </div>
      </div>
      <div class="commListDiv">
        <table class="cus_table" cellspacing="0" cellpadding="0" width="100%">
          <tr>
          <th align="center">选择</th>
              <th align="center">单号</th>
              <th align="center">借件订单号</th>
              <th align="center">下单时间</th>
            <th align="center">备件编码</th>
            <th align="center">备件名称</th>
            <th align="center">数量</th>
            <th align="center">金额</th>
            <th align="center">下单人</th>
            <th align="center">转账状态</th>
            <th align="center">回购状态</th>
          </tr>
          <tr class="border_nt" v-for="(itm,inx) in commList" :key="inx">
              <td align="center" width="10">
                  <input type="checkbox" class="checkbox-input" ref="orderNoCheck" :value="itm.id"></input>
              </td>
              <td align="center" width="110">{{ itm.borrowNo }}</td>
              <td align="center" width="110">{{ itm.orderNo }}</td>
              <td align="center" width="160">{{ itm.submitDate }}</td>
            <td align="center" width="100">{{ itm.materialCode }}</td>
            <td align="center" width="120">{{ itm.materialDesc }}</td>
            <td align="center" width="100">{{ itm.orderAmount }}</td>
            <!--                            <td align="center" width="80">{{ itm.amount || '-' }}</td>-->
            <td align="center" width="100">{{ itm.materialPrice}}</td>
            <td align="center" width="100">{{ itm.submitBy }}</td>
            <td align="center" width="80">{{ payStatus[itm.payStatus]}}</td>
            <td align="center" width="120" >{{ status[itm.status] }}</td>
          </tr>
          <tbody v-if="!commList.length">
          <tr>
            <td colspan="7" align="center">暂无数据</td>
          </tr>
          </tbody>
        </table>
      </div>
      <Page style="margin:20px;" @on-change="changeCurrent" :page-size="page.pageSize" :current="page.pageNum" :total="totalElements" show-total show-elevator />
    </div>
      <submitOrEdit ref="submitOrEdit" @refresh="queryList" />
      <upload-file ref="uploadFileRef" @refresh="queryList"/>
  </div>

</template>
<script>
import { mapState } from 'vuex';
import { materialList, materialCancel } from '@/api/apv'
import API from '@/api/ospSpare/financeManage/buybackManage'
import submitOrEdit from "@/views/ospSpare/financeManage/buybackManage/submitOrEdit.vue";
import uploadFile from './uploadFile.vue'
export default {
  components: {submitOrEdit, uploadFile},
  data() {
    return {
      searchObj: {
          ownerName: '',
        borrowNo: '',
        stationCode: '',
        status: '',  // 状态(WAIT_AUDIT:待审核,AUDIT_REJECT:审核驳回,ENABLE:审核通过)
      },
      page: {
          pageNum: 1,
          pageSize: 10,
      },
      totalElements: 0,
      status: {
        "0": "初始",
        "1": "待分中心审核",
        "2": "待财务审核",
        "3": "待支付",
        "4": "审核驳回",
        "5": "打款提交",
        "6": "收款成功",
        "7": "收款失败",
      },
      moduleList: {
        "MODULE":"组件",
        "INVERTER": "逆变器"
      },
      payStatus: {
        "0":"未支付",
        "1": "已支付"
      },
      commList: [],
    };
  },

  computed: {
    ...mapState('index', ['spMode'])
  },

  mounted() {
    this.queryList();
  },

  methods: {
      // 导出
      exportFn() {
          let datas = this.$_.cloneDeep(this.searchObj);
          datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
          delete datas.pageNum
          delete datas.pageSize
          API.exportList(datas).then(res => {
              let binaryData = [];
              let link = document.createElement('a');
              binaryData.push(res.data);
              link.style.display = 'none';
              link.href = window.URL.createObjectURL(new Blob(binaryData));
              link.setAttribute('download', '备件回购列表.xlsx');
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
          })
      },
      uploadFile(){
          let list = this.$refs.orderNoCheck.filter(item => item.checked)
          let checkItemStatus = this.commList.find(e=> e.id==list[0].value).status
          console.log('================='+checkItemStatus)
          if(list.length!==1){
              this.$Message.warning('请选择一条数据')
              return
          }
          if(checkItemStatus!='3'){
              this.$Message.warning('请选择“待财务审核“的单据进行操作')
              return
          }
          this.$refs.uploadFileRef.orderId = list[0].value
          this.$refs.uploadFileRef.show = true
      },
      submitManage(type){
          let list = this.$refs.orderNoCheck.filter(item => item.checked)
          if(list.length!==1){
              this.$Message.warning('请选择一条数据')
              return
          }
          this.$refs.submitOrEdit.isEdit = type === 'edit';
          this.$refs.submitOrEdit.orderId = list[0].value
          this.$refs.submitOrEdit.modelEdit = true

      },
    // 切换分页
    changeCurrent(curr) {
      this.searchObj.pageNum = curr;
      this.getCommList();
    },

    // 查询按钮
    queryList() {
      this.commList = [];
      this.searchObj.pageNum = 1;
      this.getCommList();
    },

    // 设备列表
    getCommList() {
      let datas = this.searchObj
      API.getList({
          page: this.page.pageNum,
          rows: this.page.pageSize
      },datas).then(res => {
        let request = res.data.result
        if (request.content.length) {
          this.totalElements = request.totalElements;
          this.commList = request.content;
            console.log(this.commList, 111);
        } else {
          this.totalElements = 0
          this.commList = [];
        }
      });
    },

    // 删除
    deleteView(id) {
      let that = this
      let datas = {
        orderId: id
      }
      this.$Modal.confirm({
        title: '删除信息',
        content: '<p>请确认是否删除</p>',
        onOk: () => {
          materialCancel(datas).then(res => {
            if (res.data.success) {
              that.queryList()
            } else {
              that.$Message.error(res.data.error);
            }
          })
        },
        onCancel: () => {
          // this.$Message.info('Clicked cancel');
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
@import '@/style/_cus_list';
.checkbox-input{
    display: inline-flex;
    width: 30px;
    cursor: pointer;
    background-color: initial;
    appearance: checkbox;
    box-sizing: border-box;
    padding: initial;
    border: initial;
}
</style>
