<template>
    <Modal width="800" v-model="modelEdit" draggable sticky scrollable @on-cancel="closeAddModal" :title="modelTitle" :mask-closable="false">
        <Tabs :animated="false" v-model="currentTab">
            <TabPane label="增值税专用发票" name="1">
                <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="100">
                    <Row>
                        <Col span="8">
                            <FormItem label="公司名称" prop="companyName">
                                <Input v-model="formValidate.companyName" placeholder=" "></Input>
                            </FormItem>
                        </Col>
                        <Col span="8">
                            <FormItem label="纳税人识别号" prop="taxpayerNumber">
                                <Input v-model="formValidate.taxpayerNumber" placeholder=" "></Input>
                            </FormItem>
                        </Col>
                        <Col span="8">
                            <FormItem label="开户银行" prop="bankName">
                                <Input v-model="formValidate.bankName" placeholder=" "></Input>
                            </FormItem>
                        </Col>
                        <Col span="8">
                            <FormItem label="开户行账号" prop="bankCardNumber">
                                <Input v-model="formValidate.bankCardNumber" placeholder=" "></Input>
                            </FormItem>
                        </Col>
                        <Col span="8">
                            <FormItem label="注册地址" prop="registAddress">
                                <Input v-model="formValidate.registAddress" placeholder=" "></Input>
                            </FormItem>
                        </Col>
                        <Col span="8">
                            <FormItem label="注册电话" prop="registPhone">
                                <Input v-model="formValidate.registPhone" placeholder=" "></Input>
                            </FormItem>
                        </Col>
                        <Col span="8">
                            <FormItem label="备注" prop="remarks">
                                <Input v-model="formValidate.remarks" placeholder=" "></Input>
                            </FormItem>
                        </Col>
                    </Row>

                </Form>
            </TabPane>
            <TabPane label="电子普通发票" name="2">
                <Form ref="elseFormValidate" :model="elseFormValidate" :rules="elseRuleValidate" :label-width="85">
                    <Row>
                        <Col span="8">
                            <FormItem label="发票抬头" prop="companyName">
                                <Input v-model="elseFormValidate.companyName" placeholder=" "></Input>
                            </FormItem>
                        </Col>
                        <Col span="8">
                            <FormItem label="税务号" prop="taxpayerNumber">
                                <Input v-model="elseFormValidate.taxpayerNumber" placeholder=" "></Input>
                            </FormItem>
                        </Col>
                        <Col span="8">
                            <FormItem label="备注" prop="remarks">
                                <Input v-model="elseFormValidate.remarks" placeholder=" "></Input>
                            </FormItem>
                        </Col>
                    </Row>
                </Form>
            </TabPane>
        </Tabs>
        <div slot="footer">
            <Button type="primary" :loading="submitLoading" @click="submitResult">提交</Button>
            <Button type="default" style="margin-left: 10px"
                    @click="closeAddModal">取消</Button>
        </div>
    </Modal>
</template>
<script>
import API from "@/api/ospSpare/financeManage/buybackManage";

export default {
    name: 'submitOrEdit',
    data(){
        return{
            isEdit: false,
            orderId: '',
            fileId: '',
            submitLoading: false,
            currentTab: '1',
            modelEdit: false,
            modelTitle: '发票信息',
            formValidate: {
                companyName: '',
                taxpayerNumber: '',
                bankName: '',
                bankCardNumber: '',
                registAddress: '',
                registPhone: '',
                remarks: ''
            },
            ruleValidate: {
                companyName: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                taxpayerNumber: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                bankName: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                bankCardNumber: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                registAddress: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                registPhone: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                remarks: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],

            },
            elseFormValidate: {
                companyName: '',
                taxpayerNumber: '',
                remarks: '',
            },
            elseRuleValidate: {
                companyName: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                taxpayerNumber: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                remarks: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
            }
        }
    },
    watch: {
        modelEdit(val) {
            if (val) {
                this.getInfo();
            }
        }
    },
    methods: {
        getInfo(){
            API.getDetailApi({id: this.orderId}).then(res => {
                if(res.data.success){
                    const data = res.data.result?.invoice || {}
                    this.fileId = res.data.result?.invoice?.id || ''
                    for(let i in this.formValidate){
                        if(data[i]){
                            this.$set(this.formValidate, i, data[i])
                        }
                    }
                    for(let i in this.elseFormValidate){
                        if(data[i]){
                            this.$set(this.elseFormValidate, i, data[i])
                        }
                    }
                    this.currentTab = data.invoiceType
                }
            })
        },
        submitResult(){
            const name = this.currentTab=='2' ? 'elseFormValidate' : 'formValidate'
            this.$refs[name].validate((valid) => {
                if (valid) {
                    let data
                    if(this.currentTab=='1'){
                        data = {
                            id: this.orderId,
                            invoice: {
                                id: this.fileId,
                                invoiceType: this.currentTab,
                                ...this.formValidate
                            }

                        }
                    }else{
                       data = {
                           id: this.orderId,
                           invoice: {
                               id: this.fileId,
                               invoiceType: this.currentTab,
                               ...this.elseFormValidate
                           }
                       }
                    }
                    if(this.isEdit){
                        API.editApi(data).then(res => {
                            this.loadStatus = false
                            if(res.data.success){
                                this.$Message.success(res.data.result);
                                this.resetForm()
                                this.modelEdit = false
                                this.$emit('refresh')
                            }else{
                                this.$Message.warning(res.data.error);
                            }
                        })
                    }else{
                        API.submitApi(data).then(res => {
                            this.loadStatus = false
                            if(res.data.success){
                                this.$Message.success(res.data.result);
                                this.resetForm()
                                this.modelEdit = false
                                this.$emit('refresh')
                            }else{
                                this.$Message.warning(res.data.error);
                            }
                        })
                    }

                } else {
                }
            })

        },
        resetForm(){
            for(let i in this.formValidate){
                this.$set(this.formValidate, i, '')
            }
            for(let i in this.elseFormValidate){
                this.$set(this.elseFormValidate, i, '')
            }
        },
        closeAddModal(){
            this.resetForm()
            this.modelEdit = false
        },
    }
}
</script>
<style lang="scss" scoped>
::v-deep .ivu-form .ivu-form-item-label{
    font-size: 12px!important;
}
</style>
