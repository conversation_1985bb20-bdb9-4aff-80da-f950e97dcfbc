<template>
    <Modal width="600" v-model="show" :loading="true" title="上传凭证"
           :mask-closable="false">
        <div>
            <p>打款账户：（需用签约户头打款）</p>
            <p>银行:中国建设银行股份有限公司青岛海尔路支行</p>
            <p>户名:青岛海尔光伏新能源有限公司</p>
            <p>账号:37150198551000001771</p>
            <p>备注：运维组件回购</p>
        </div>
        <div v-if="list1" class="demo-upload-list" >
            <img :src="list1" loading="lazy" width="100%" style="height: 100%;object-fit: fill" alt=""/>
            <div class="demo-upload-list-cover" @click.stop>
                <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleView('1')"></Icon>
                <Icon type="ios-trash-outline" size="20" color="#fff" @click.stop="handleRemove('1')"></Icon>
            </div>
        </div>
        <Upload
            v-else
            ref="upload"
            :show-upload-list="false"
            :action="actionUrl"
            :headers="headers"
            :on-progress="progress"
            :on-success=" (response, file, fileList) => { return imgFileUploadSuccess(response, file, fileList); } "
            :format="['jpg','jpeg','png']"
            :on-format-error="handleFormatError"
            :on-exceeded-size="handleMaxSize"
            :max-size="18432"
            type="drag"
            style="display: inline-block;width:100px;margin-right: 20px">
            <div style="width: 100px;height:100px;line-height: 100px;">
                <Icon type="ios-cloudy-outline" size="20"></Icon>
            </div>
        </Upload>
        <Modal v-model="visible" width="99" reset-drag-position draggable sticky title="预览" footer-hide class-name="vertical-center-modal">
            <img class="preview" v-if="visibleImg" :src="visibleImg" @click="toTarget(visibleImg)" title="点击查看原图" alt="">
        </Modal>
        <div slot="footer">
            <Button type="primary" :loading="submitLoading" @click="submitResult">提交</Button>
            <Button type="default" style="margin-left: 10px"
                    @click="closeAddModal">取消</Button>
        </div>
    </Modal>
</template>
<script>
import API from "@/api/ospSpare/financeManage/buybackManage";

export default {
    data(){
        return {
            orderId: '',
            visible: false,
            visibleImg: '',
            submitLoading: false,
            show: false,
            actionUrl: `${process.env.VUE_APP_BASE_API}/merchant/oss/image/upload.do`,
            headers: { Authorization: `Bearer ${JSON.parse(window.localStorage.getItem('logins')).access_token}` },
            list1: ''
        }
    },
    methods: {
        toTarget(url) {
            window.open(url)
        },
        handleView (type) {
            this.visible = true;
            switch (type) {
                case '1':
                    this.visibleImg = this.list1;
                    break;
                case '2':
                    this.visibleImg = this.list2;
                    break;
                case '3':
                    this.visibleImg = this.list3;
                    break;
                case '4':
                    this.visibleImg = this.list4;
                    break;
            }
        },
        handleRemove (type) {
            switch (type) {
                case '1':
                    this.list1 = ''
                    break;
                case '2':
                    this.list2 = ''
                    break;
                case '3':
                    this.list3 = ''
                    break;
                case '4':
                    this.list4 = ''
                    break;
            }
        },
        progress() {
            this.$Message.loading({
                content: '上传中...',
                duration: 0
            });
        },
        handleFormatError(file) {
            this.$Notice.warning({
                title: '上传格式不正确',
                desc: '请上传正确的格式文件'
            });
        },
        imgFileUploadSuccess(response, file, fileList) {
            let that = this;
            this.list1 = response.result[0].imageUrl;
            setTimeout(() => {
                that.$Message.destroy();
            }, 500);
        },
        handleMaxSize (file) {
            this.$Notice.warning({
                title: '上传文件过大',
                desc: file.name + '文件太大，请压缩后上传.'
            });
        },
        closeAddModal(){
            this.show = false
            this.list1=''
            this.visible=false
            this.visibleImg=''
        },
        submitResult(){
            if(!this.list1){
                this.$Message.warning('请选择凭证');
                return
            }
            const data = {
                id: this.orderId,
                payImg: this.list1
            }

            this.submitLoading = true
            API.uploadFileApi(data).then(res => {
                this.submitLoading = false
                if(res.data.success){
                    this.$Message.success(res.data.result);
                    this.closeAddModal()
                    this.$emit('refresh')
                }else{
                    this.$Message.warning(res.data.error);
                }
            })
        }
    }
}
</script>
