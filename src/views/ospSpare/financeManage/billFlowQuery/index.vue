<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏备件管理</Breadcrumb-item>
            <Breadcrumb-item to="/ospSpare/">入库管理</Breadcrumb-item>
            <Breadcrumb-item>账单流水查询</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">业务单号：</p>
                    <Input v-model="searchObj.sourceNo" placeholder="请输入业务单号" clearable style="width: 200px" />
                </div>
<!--                <div class="condition">-->
<!--                    <p class="condition_p">服务商名称：</p>-->
<!--                    <Select v-model="searchObj.warehouseName" placeholder="请选择服务商名称" filterable clearable style="width: 200px">-->
<!--                        <Option v-for="item in warehouseList" :value="item.value" :key="item.key">{{ item.label }}</Option>-->
<!--                    </Select>-->
<!--                </div>-->
                <div class="condition">
                    <p class="condition_p">账单类型：</p>
                    <Select v-model="searchObj.operType" placeholder="请选择账单类型" clearable style="width: 200px">
                        <Option v-for="item in operTypeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">创建时间：</p>
                    <DatePicker ref="formDateSh" :value="submintDate" @on-change="
                        (data) => {
                          return selectDate(data, 'createDate');
                        }
                      " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择" style="width: 200px ">
                    </DatePicker>
                </div>
            </div>
            <div class="list_but" style="display: flex; justify-content: space-between; width: 100%;">
                <div class="condition"  style="display: flex;">
                    <Button @click="exportFn" type="success">导出</Button>
                    <span style="display: flex">
                        <div style="padding-top: 5px;"><Icon type="ios-information-circle-outline" size="25" color="orange"/></div>
                        <div style="padding: 8px 3px;">保证金总金额<span style="padding: 0 5px;">{{commList[0]?.imprestAmnt||0}}</span>元</div>
                    </span>
                </div>
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                </div>
            </div>
            <div class="commListDiv">
                <Table size="small" border ref="tableRef" :columns="tableColumn" :data="commList" @on-selection-change="selected => this.tableSelect = selected">
                    <template slot-scope="{ index }" slot="sn">
                        {{ index + 1 }}
                    </template>
<!--                    账单类型-->
                    <template slot-scope="{ index }" slot="operType">
                        {{ operTypeMap[commList[index].operType] }}
                    </template>
                </Table>
            </div>
            <Page style="margin: 20px" @on-change="changeCurrent" :current="searchObj.page"
                  :total="totalElements" show-total show-elevator />
        </div>
    </div>
</template>

<script>
import _data from "@/views/ospSpare/_edata"; // 数据字典
import API from "@/api/ospSpare/financeManage/billFlowQuery";
// import API from "@/api/ospSpare/stockOutManage/serviceStock";
export default {
    name: "serviceProviderOrder",
    data() {
        return {
            searchObj: {
                sourceON: "",
                warehouseName: "",
                operType: "",
                createDateStart: "",
                createDateEnd: "",
                pageNum: 1,
                pageSize: 10
            },
            formObj: {},
            staffList: [],
            totalElements: 0,
            submintDate: [],
            operTypeList: _data.operType,
            operTypeMap:[],
            warehouseList: [],
            commList: [],
            tableColumn: [
                { type: 'selection', minWidth: 60, title: '选择', align: 'center' },
                { title: '序号', minWidth: 80, align: 'center', slot: 'sn' },
                { title: '业务单号', minWidth: 180, key: 'sourceNo', align: 'center' },
                { title: '服务商名称', minWidth: 180, key: 'warehouseName', align: 'center' },
                { title: '账单类型', minWidth: 180, key: 'operType', align: 'center',slot: 'operType' },
                { title: '账单金额', minWidth: 180, key: 'operAmnt', align: 'center' },
                { title: '末次扣款前金额', minWidth: 180, key: 'endingAmnt', align: 'center' },
                { title: '扣款后金额', minWidth: 180, key: 'lastAmnt', align: 'center' },
                { title: '创建时间', minWidth: 180, key: 'createDate', align: 'center' },
                { title: '备注', minWidth: 180, key: 'remarks', align: 'center' }
            ],
            tableSelect: [],
        }
    },
    mounted() {
        this.getList()
        this.getDict()
    },
    methods: {
        // 导出
        exportFn() {
            let datas = this.$_.cloneDeep(this.searchObj);
            datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
            delete datas.pageNum
            delete datas.pageSize
            API.exportList(datas).then(res => {
                let binaryData = [];
                let link = document.createElement('a');
                binaryData.push(res.data);
                link.style.display = 'none';
                link.href = window.URL.createObjectURL(new Blob(binaryData));
                link.setAttribute('download', '账单流水查询.xlsx');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            })
        },
        // 取消
        cancel() {
            this.$message.confirm('是否确认取消?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$Message.success('取消成功!');
            })
        },
        // 删除
        del() {
            this.$message.confirm('此操作将永久删除该文件, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$Message.success('删除成功!');
            })
        },
        // 查询按钮
        queryList() {
            this.searchObj.pageNum = 1;
            this.getList();
        },

        // 重置按钮
        emptyList() {
            this.searchObj = {
                submintDateRange: "",
                orderCode: "",
                woId: "",
                orderType: "",
                orderStatus: "",
                branchName: "",
                pageNum: 1,
                pageSize: 10
            };
            this.submintDate = [];
        },
        // 选择日期
        selectDate(date, refObj) {
            this.submintDate = date;
            this.searchObj[refObj + "Start"] = date[0];
            this.searchObj[refObj + "End"] = date[1];
        },
        // 获取工单列表
        getList() {
            this.$Message.loading({
                content: "查询中...",
                duration: 0,
            });

            let datas = this.searchObj;
            datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
            delete datas.pageNum
            delete datas.pageSize
            const page = { page: this.searchObj.pageNum, rows: this.searchObj.pageSize }
            // 将所有选中项清空
            this.$refs.tableRef.selectAll(false);
            API.getList(page, datas).then(res => {
                if(res.data.success){
                    this.$Message.destroy();
                    this.commList = res.data.result.content
                    this.totalElements = res.data.result.totalElements
                }else{
                    this.commList=[]
                }
            }).catch(err=>{
                this.commList=[]
            })
        },
        // 获取下拉数据
        getDict() {
            // 账单类型
            _data.operType.forEach(item=>{
                this.operTypeMap[item.value]=item.label
            })

            // 获取服务商数据
            API.queryRoute({ 'warehouseLevel':'3'}).then(res => {
                let list=[]
                if(res.data.success){
                    res.data.result.forEach((item,index) => {
                       list.push({
                           'value':item.warehouseCode?item.warehouseCode:'',
                           'label':item.warehouseName?item.warehouseName:'',
                           'key':index
                       })
                    })
                    this.warehouseList=list
                }

            })
        },
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.pageNum = curr;
            this.getList();
        }
    }
}
</script>

<style lang="scss" scoped>
@import 'style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
