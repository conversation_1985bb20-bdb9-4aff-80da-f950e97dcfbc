<template>
    <Modal width="1200" v-model="modelEdit" draggable sticky scrollable :title="modelType === 'add' ? '新增' : '编辑'" :footer-hide="true"
           :mask-closable="false" :loading="loading">
        <div style="position: relative; width: 100%; height: 60vh;">
            <div style="padding-bottom: 40px; width: 100%; height: 60vh; overflow-y: auto">
                <Form :model="form" :rules="ruleValidate" :label-width="100" ref="form" :key="formKey">
                    <Row>
                        <Col span="12">
                            <FormItem label="服务商名称" prop="netName">
                                <Input type="text" maxlength="15" v-model="form.netName" clearable placeholder="服务商名称"
                                       style="width: 360px" disabled />
                            </FormItem>
                            <FormItem label="申请金额" prop="paymentAmount">
                              <RadioGroup v-if="this.modelType !== 'edit'" v-model="form.paymentAmount" style="width: 300px;">
                                <Radio label="5000">
                                  <span>5,000.00</span>
                                </Radio>
                                <Radio label="10000">
                                  <span>10,000.00</span>
                                </Radio>
                                <Radio label="15000">
                                  <span>15,000.00</span>
                                </Radio>
                                <Radio label="3">
                                <span>
                                  <Input type="text" maxlength="20" style="margin-right: 5px;" v-model="otherPaymentAmount" clearable placeholder="其他金额" />元</span>
                                </Radio>
                              </RadioGroup>
                              <Input v-else type="text" maxlength="20" style="margin-right: 5px;width: 360px" v-model="form.paymentAmount" clearable placeholder="" />
                            </FormItem>
                            <FormItem label="申请类型" prop="advanceType">
                                <RadioGroup v-model="form.advanceType">
                                    <Radio label="0">
                                        <span>交款</span>
                                    </Radio>
<!--                                    <Radio label="1">
                                        <span>退款</span>
                                    </Radio>-->
                                </RadioGroup>
                            </FormItem>
                            <FormItem label="付款方式" prop="payType">
                                <RadioGroup v-model="form.payType">
                                    <Radio label="0">
                                        <span>转账付款</span>
                                    </Radio>
<!--                                    <Radio label="1">
                                        <span>在线支付</span>
                                    </Radio>-->
                                </RadioGroup>
                            </FormItem>
                        </Col>
                        <Col span="12" class="remark">
                            <p><Icon type="md-alert" /> 请通过线下转账方式支付</p>
                            <p> 银行:中国建设银行股份有限公司青岛海尔路支行</p>
                            <p>户名:青岛海尔光伏新能源有限公司</p>
                            <p>账号:37150198551000001771</p>
                            <Upload ref="upload" class="inlinebox" :show-upload-list="false" :action="actionUrl" :headers="headers" :on-progress="progress" :on-success=" (response, file, fileList) => { return imgFileUploadSuccess(response, file, fileList); } " :format="['jpg', 'jpeg', 'png']" :max-size="40960" :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize">
                                <div class="file_pics">
                                    <Button v-if="form.fileUrls" type="info" style="margin-bottom: 15px">修改打款凭证</Button>
                                    <div v-if="form.fileUrls" class="demo-upload-list" >
                                        <img :src="form.fileUrls" loading="lazy" width="100%"  alt=""/>
                                        <div class="demo-upload-list-cover" @click.stop>
                                            <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleView()"></Icon>
                                            <Icon type="ios-trash-outline" size="20" color="#fff" @click.stop="handleRemove()"></Icon>
                                        </div>
                                    </div>
                                    <Button v-else type="info">上传打款凭证</Button>
                                </div>
                            </Upload>
                        </Col>
                    </Row>
                </Form>
            </div>
            <div style="position: absolute; z-index: 99; right: 20px; bottom: -30px; text-align: right; margin: 30px 0">
                <Button type="primary"
                        @click="debounce(handleSubmit)">保存</Button>
                <Button type="default" style="margin-left: 10px"
                        @click="modelEdit = false">取消</Button>
            </div>
            <Modal v-model="visible" width="99" reset-drag-position draggable sticky title="预览" footer-hide class-name="vertical-center-modal">
                <img class="preview" v-if="visibleImg" :src="visibleImg" @click="toTarget(visibleImg)" title="点击查看原图" alt="">
            </Modal>
        </div>
    </Modal>
</template>

<script>
import API from "@/api/ospSpare/financeManage/advanceManage"
export default {
    name: "addEdit",
    props: {
        modelType: {
            type: String,
            default: ""
        },
        rowData: {
            type: Object,
            default: () => {}
        },
        warehouseList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        const validateNum = (rule, value, callback) => {
            if (!value || value.trim().length === 0) {
                callback(new Error('申请金额不能为空'));
            } else {
                if (!/(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/.test(value)) {
                    callback(new Error('申请金额只能录入数字，且小数点最多2位，录入金额大于等于0'));
                } else {
                    callback();
                }
            }
        };
        return {
            loading: false,
            visible: false,
            modelEdit: false,
            formKey: 0,
            form: {
                netName: '',
                netCode: '',
                netId: '',
                paymentAmount: '5000',
                advanceType: '0',
                payType: '0',
                fileUrls: ''
            },
          otherPaymentAmount:'',
            ruleValidate: {
                netName: [
                    { required: true, message: '服务商名称不能为空', trigger: 'blur' }
                ],
                paymentAmount: [
                  { required: true, message: '申请金额不能为空', trigger: 'blur' },
                    { validator: validateNum, trigger: 'blur' }
                ],
                advanceType: [
                    { required: true, message: '请选择申请类型', trigger: 'change' }
                ],
                payType: [
                    { required: true, message: '请选择付款方式', trigger: 'change' }
                ]
            },
            visibleImg: '',
            actionUrl: `${process.env.VUE_APP_BASE_API}/merchant/oss/image/upload.do`,
            headers: { Authorization: `Bearer ${JSON.parse(window.localStorage.getItem('logins')).access_token}` },
            submitFun: {
                add: API.add,
                edit: API.update
            }
        }
    },
    methods: {
        // 初始化
        init() {
            this.form = {
                netName: '',
                netCode: '',
                netId: '',
                paymentAmount: '5000',
                advanceType: '0',
                payType: '0',
                fileUrls: ''
            }
            this.formKey ++
            if (this.modelType === 'edit') {
                this.form = this.$_.cloneDeep(this.rowData)
            }
            const userStr = window.localStorage.getItem('userInfo')
            if (userStr) {
                const userInfo = JSON.parse(userStr)
                const list = this.warehouseList.filter(item => item.memberId == userInfo.memberId)
                this.form.netName = list.length > 0 ? list[0].warehouseName : ""
                this.form.netId = list.length > 0 ? list[0].id : ""
                this.form.netCode = list.length > 0 ? list[0].warehouseCode : ""
            }
        },
        progress() {
            this.$Message.loading({
                content: '上传中...',
                duration: 0
            });
        },
        imgFileUploadSuccess(response, file, fileList) {
            let that = this;
            this.form.fileUrls = response.result[0].imageUrl;
            setTimeout(() => {
                that.$Message.destroy();
            }, 500);
        },
        handleFormatError(file) {
            this.$Notice.warning({
                title: '上传格式不正确',
                desc: '请上传正确的格式文件'
            });
        },

        handleMaxSize (file) {
            this.$Notice.warning({
                title: '上传文件过大',
                desc: file.name + '文件太大，请压缩后上传.'
            });
        },
        handleView (index, inx, tag) {
            this.visible = true;
            this.visibleImg = this.form.fileUrls;
            this.visibleName = '银行卡照片';
        },

        handleRemove (index, inx, tag) {
            this.form.fileUrls = ''
        },
        toTarget(url) {
            window.open(url)
        },
        handleSubmit () {
            if (!this.form.fileUrls) {
                this.$Message.error("请上传打款凭证");
                return
            }
          if(this.form.paymentAmount=='3'){
            this.form.paymentAmount=this.otherPaymentAmount
          }
            this.$refs.form.validate((valid) => {
                if (valid) {
                    this.loading = true
                  let params=JSON.parse(JSON.stringify(this.form))
                    this.submitFun[this.modelType](params).then(res => {
                        this.loading = false
                        if (res.data.success) {
                            this.modelEdit = false
                            this.$emit("handleSuccess")
                        } else {
                            this.$Message.error(res.data.error || res.data.message || '操作失败');
                        }
                    }).catch(e => {
                        this.loading = false
                        console.log(e)
                        this.$Message.error("获取服务失败，请重试");
                    })
                } else {
                }
            })
        },
    }
}
</script>

<style lang="scss" scoped>
@import '@/style/_cus_reg.scss';
@import '@/style/_cus_list.scss';
@import '@/style/_finish.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
.demo-upload-list {
    @extend .flex-row-center-center;
    position: relative;
    width: 50%;
    height: 100%;
    overflow: hidden;

    &:hover .demo-upload-list-cover {
        display: flex;
    }
}
.demo-upload-list-cover {
    @extend.flex-row-center-around;
    display: none;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,.6);
    z-index: 999;
    i{
        &:hover{
            cursor: pointer;
        }
    }
}
.remark{
    text-align: left;
    vertical-align: middle;
    float: left;
    font-size: 14px;
    color: #515a6e;
    line-height: 1;
    padding: 10px 12px 10px 0;
    box-sizing: border-box;
    p{
        margin-bottom: 24px;
        vertical-align: top;
        zoom: 1;
    }
}
</style>
