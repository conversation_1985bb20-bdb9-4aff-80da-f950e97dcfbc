<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏备件管理</Breadcrumb-item>
            <Breadcrumb-item to="/ospSpare/">财务管理</Breadcrumb-item>
            <Breadcrumb-item>备件保证金</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">申请单号：</p>
                    <Input v-model="searchObj.advanceCode" placeholder="请输入申请单号" clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">申请类型：</p>
                    <!-- TODO 对接下拉数据 -->
                    <Select v-model="searchObj.advanceType" placeholder="请选择申请类型" clearable style="width: 200px">
                        <Option v-for="item in advanceTypeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">申请状态：</p>
                    <!-- TODO 对接下拉数据 -->
                    <Select v-model="searchObj.advanceStatus" placeholder="请选择申请状态" clearable style="width: 200px">
                        <Option v-for="item in advanceStatusList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                </div>
<!--                <div class="condition">-->
<!--                    <p class="condition_p">服务商：</p>-->
<!--                    <Select v-model="searchObj.netName" clearable style="width: 200px" filterable>-->
<!--                        <Option v-for="(item, i) in warehouseList" :value="item.warehouseName" :key="i">{{ item.warehouseName }}</Option>-->
<!--                    </Select>-->
<!--                </div>-->
            </div>
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">创建时间：</p>
                    <DatePicker ref="formDateSh" :value="createDate" @on-change="
                        (data) => {
                          return selectDate(data, 'createDate');
                        }
                      " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择" style="width: 240px ">
                    </DatePicker>
                </div>
                <div class="condition">
                    <p class="condition_p">提交时间：</p>
                    <DatePicker ref="formDateSh" :value="submintDate" @on-change="
                        (data) => {
                          return selectDate(data, 'submintDate');
                        }
                      " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择" style="width: 240px ">
                    </DatePicker>
                </div>
            </div>
            <div class="list_but" style="display: flex; justify-content: space-between; width: 100%;">
                <div class="condition">
                    <Button @click="add" type="info">新增</Button>
                    <Button @click="edit" type="primary" :disabled="tableSelect.length !== 1  || tableSelect[0].advanceStatus !== '0' && tableSelect[0].advanceStatus !== '3'" >编辑</Button>
                    <Button @click="submit" type="success" :disabled="tableSelect.length !== 1  || tableSelect[0].advanceStatus !== '0' && tableSelect[0].advanceStatus !== '3' && tableSelect[0].advanceStatus !== '5'" >提交</Button>
                    <Button @click="del" type="error" :disabled="tableSelect.length !== 1  || tableSelect[0].advanceStatus !== '0'">删除</Button>
                    <Button @click="handleExport" type="warning">导出</Button>

                </div>
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                </div>
            </div>
            <div class="commListDiv">
                <Table size="small" border ref="tableRef" :columns="tableColumn" :data="commList" @on-selection-change="selected => this.tableSelect = selected">
                    <template slot-scope="{ index }" slot="sn">
                        {{ index + 1 }}
                    </template>
                    <template slot-scope="{ row }" slot="advanceType">
                        {{ enums.advanceType[row.advanceType + ''] }}
                    </template>
                    <template slot-scope="{ row }" slot="payType">
                        {{ enums.payType[row.payType + ''] }}
                    </template>
                    <template slot-scope="{ row }" slot="advanceStatus">
                        {{ enums.advanceStatus[row.advanceStatus + ''] }}
                    </template>
                    <template slot-scope="{ row }" slot="fileUrls">
                        <a :href="row.fileUrls" target="_blank">点击查看</a>
                    </template>
                </Table>
            </div>
            <Page style="margin: 20px" @on-change="changeCurrent" :current="searchObj.page"
                  :total="totalElements" show-total show-elevator />
        </div>
        <addEditPop ref="addEditPopRef" :row-data="rowData" :model-type="modelType" :warehouse-list="warehouseList" @handleSuccess="handleSuccess" />
    </div>
</template>

<script>
import _data from "@/views/ospSpare/_edata"; // 数据字典
import API from "@/api/ospSpare/financeManage/advanceManage";
import addEditPop from "./addEditPop";
import Common from "@/api/ospSpare/common";
import { removeClass } from "@/components/message-box/js/dom";
export default {
    name: "serviceProviderOrder",
    components: { addEditPop },
    data() {
        return {
            searchObj: {
                submintDateRange: "",
                advanceCode: "",
                advanceType: "",
                advanceStatus: "",
                branchName: "",
                pageNum: 1,
                pageSize: 10
            },
            formObj: {},
            staffList: [],
            totalElements: 0,
            submintDate: [],
            createDate: [],
            status: _data.status,
            advanceTypeList: _data.advanceType,
            advanceStatusList: _data.advanceStatus,
            sourceArr: _data.SourceArr,
            faultCodeTypeList: _data.faultCodeTypeList,
            tableColumn: [
                { type: 'selection', minWidth: 60, title: '选择', align: 'center' },
                { title: '序号', minWidth: 80, align: 'center', slot: 'sn' },
                { title: '申请单号', minWidth: 120, key: 'advanceCode', align: 'center' },
                { title: '申请类型', minWidth: 120, slot: 'advanceType', align: 'center' },
                { title: '付款方式', minWidth: 120, slot: 'payType', align: 'center' },
                { title: '申请状态', minWidth: 120, slot: 'advanceStatus', align: 'center' },
                { title: '服务商编码', minWidth: 120, key: 'netCode', align: 'center' },
                { title: '服务商名称', minWidth: 120, key: 'netName', align: 'center' },
                { title: '交款金额', minWidth: 120, key: 'paymentAmount', align: 'center' },
                { title: '提交时间', minWidth: 120, key: 'submitDate', align: 'center' },
                { title: '打开凭证', minWidth: 120, slot: 'fileUrls', align: 'center' },
                { title: '记账时间', minWidth: 120, key: 'voucherDate', align: 'center' },
                { title: '创建时间', minWidth: 120, key: 'createDate', align: 'center' },
                { title: '备注', minWidth: 120, key: 'remarks', align: 'center' },
            ],
            tableSelect: [],
            commList: [],
            modelEdit: false,
            modelType: 'add',
            modelEditAdd: false,
            warehouseList: [],
            rowData: {},
            detailData: {},
            enums: {
                advanceStatus: {
                    "0": "初始",
                    "1": "已提交",
                    "2": "交款成功",
                    "3": "交款驳回",
                    "4": "审批通过",
                    "5": "审批驳回",
                    "6": "退款成功",
                    "7": "退款失败",
                },
                branchName: {},
                advanceType: {
                    "0": "交款",
                    "1": "退款",
                },
                payType: {
                    "0": "转账付款",
                    "1": "在线支付",
                }
            }
        }
    },
    computed: {
        isOperate() {
            return this.tableSelect.length === 1 && this.tableSelect[0].purchaseStatus == '0'
        }
    },
    mounted() {
        this.getSelect()
    },
    methods: {
        // 获取下拉
        getSelect() {
            this.loading = true
            const pageStr = "?page=1&rows=9999"
            Common.getWarehouse({}, pageStr).then(res => {
                this.loading = false
                const resData = res.data || {}
                if (!this.$_.isEmpty(resData) && resData?.success) {
                    const list = resData?.result.content || [];
                    list.forEach(item => {
                        this.enums.branchName[item.branchId + ""] = item.warehouseName
                    })
                    this.warehouseList = list.filter(item => {
                        return item.warehouseLevel === '3'
                    })
                } else {
                    this.$Message.error('获取下拉失败!');
                }
                this.getList()
            }).catch(e => {
                console.log(e);
                this.loading = false
                this.getList()
                this.$Message.error('获取下拉失败!');
            })
        },
        // 新增
        add() {
            this.$refs.addEditPopRef.modelEdit = true
            this.modelType = 'add'
            this.$nextTick(() => {
                this.$refs.addEditPopRef.init()
            })
        },
        // 编辑
        edit() {
            this.$refs.addEditPopRef.modelEdit = true
            this.modelType = 'edit'
            this.rowData = this.tableSelect[0]
            this.$nextTick(() => {
                this.$refs.addEditPopRef.init()
            })
        },
        // 提交
        submit() {
            this.$message.confirm('是否确认提交?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$Message.loading({
                    content: "提交中...",
                    duration: 0,
                });
                API.submit(this.tableSelect[0]).then(res => {
                    this.$Message.destroy()
                    if (res.data.success) {
                        this.$Message.success("提交成功");
                        setTimeout(() => {
                            this.getList()
                        }, 2000);
                    } else {
                        this.$Message.error(res.data.error || res.data.message || '操作失败');
                    }
                }).catch(e => {
                    this.$Message.destroy()
                    this.$Message.error("获取服务失败，请重试");
                })
            })
        },
        // 删除
        del() {
            this.$message.confirm('是否确认删除?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$Message.loading({
                    content: "提交中...",
                    duration: 0,
                });
                API.delete({id: this.tableSelect[0].id}).then(res => {
                    this.$Message.destroy()
                    if (res.data.success) {
                        this.$Message.success("删除成功");
                        setTimeout(() => {
                            this.getList()
                        }, 2000);
                    } else {
                        this.$Message.error(res.data.error || res.data.message || '操作失败');
                    }
                }).catch(e => {
                    console.log(e);
                    this.$Message.destroy()
                    this.$Message.error("获取服务失败，请重试");
                })
            })
        },
        // 导出
        handleExport() {
            const params = this.searchObj
            this.$Message.loading({
                content: "导出中...",
                duration: 0,
            });
            API.export(params)
                .then(res => {
                    this.$Message.destroy()
                    const resData = res.data || {}
                    let binaryData = [];
                    let link = document.createElement('a');
                    binaryData.push(resData);
                    link.style.display = 'none';
                    link.href = window.URL.createObjectURL(new Blob(binaryData));
                    link.setAttribute('download', '备件保证金.xlsx');
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                })
                .catch((e) => {
                    this.$Message.destroy()
                    console.log(e)
                });
        },
        // 查询按钮
        queryList() {
            this.searchObj.pageNum = 1;
            this.getList();
        },

        // 重置按钮
        emptyList() {
            this.searchObj = {
                advanceCode: "",
                woId: "",
                advanceType: "",
                advanceStatus: "",
                branchName: "",
                pageNum: 1,
                pageSize: 10
            };
            this.submintDate = [];
            this.createDate = [];
        },
        // 选择日期
        selectDate(date, refObj) {
            this[refObj] = date;
            this.searchObj[refObj + "Start"] = date[0];
            this.searchObj[refObj + "End"] = date[1];
        },
        // 获取工单列表
        getList() {
            this.$Message.loading({
                content: "查询中...",
                duration: 0,
            });
            let params = this.$_.cloneDeep(this.searchObj);
            const pageText = "?page=" + this.searchObj.pageNum + "&rows=" + this.searchObj.pageSize
            API.getList(pageText, params).then(res => {
                this.$Message.destroy()
                this.tableSelect = []
                let request = res.data.result;
                if (res.data.success) {
                    this.totalElements = request.totalElements;
                    this.commList = request.content;
                } else {
                    this.totalElements = 0;
                    this.commList = [];
                }
            }).catch(e => {
                console.log(e);
                this.$Message.destroy()
                this.$Message.error("获取服务失败，请重试");
            })
        },
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.pageNum = curr;
            this.getList();
        },
        jumpto(path, id, count) {
            this.$router.push({
                path,
                query: { id, count },
            });
        },
        handleSuccess() {
            this.$nextTick(() => {
                this.getList()
                setTimeout(() => {
                    this.$Message.success('操作成功');
                }, 300)
            })
        },
    }
}
</script>

<style lang="scss" scoped>
@import 'style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
