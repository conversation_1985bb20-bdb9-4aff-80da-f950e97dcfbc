<template>
    <Modal width="80%" v-model="show" title="详情" :footer-hide="true" :styles="{ top: '5vh' }"
           :mask-closable="false">
        <div style="overflow-y: auto; width: 100%; height: 80vh; background: #eee; padding: 20px">
            <Card :bordered="false">
                <div class="cus_list" style="min-height: calc(80vh - 72px)">
                    <div class="list_son">
<!--                        <div class="step">-->
<!--                            <Steps class="stepdiv" :current="typeNode('orderStatusList', infoList.status)">-->
<!--                                <Step v-for="(item, index) in infoList.processLogs" :key="index" :content="timeChange(item.createdAt)"-->
<!--                                      :title="item.desc"></Step>-->
<!--                            </Steps>-->
<!--                        </div>-->
                    </div>
                    <div class="cus_reg">
                        <div class="cus_form" style="padding-top: 0">
                            <p class="cus_title">主单信息</p>
                            <el-descriptions class="margin-top" :column="3" border>
                                <el-descriptions-item>
                                    <template slot="label">
                                        出库单号
                                    </template>
                                    {{infoList?.tranCode}}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        工单号
                                    </template>
                                    {{infoList?.referId}}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        出库状态
                                    </template>
                                    <span v-if="infoList?.ifClose=='0'">未出库</span>
                                    <span v-if="infoList?.ifClose=='1'">已出库</span>
                                    <span v-else></span>
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        出库时间
                                    </template>
                                    {{infoList?.tranDate}}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        服务商编码
                                    </template>
                                    {{infoList?.warehouseCode}}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        服务商名称
                                    </template>
                                    {{infoList?.warehouseName}}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        创建时间
                                    </template>
                                    {{infoList?.createDate}}
                                </el-descriptions-item>
                            </el-descriptions>
                            <p class="cus_title">明细信息</p>
                            <Table size="small" border ref="tableRef" :columns="tableColumn" :data="commList">
                                <template slot-scope="{ index }" slot="sn">
                                    {{ index + 1 }}
                                </template>
                            </Table>
                        </div>
                    </div>
                </div>
            </Card>
        </div>
    </Modal>
</template>

<script>
import _data from "@/views/ospSpare/_edata"; // 数据字典
import API from "@/api/ospSpare/stockOutManage/serviceStockOut";

export default {
    data() {
        return {
            show: false,
            infoList: {},
            orderStatusList: _data.orderStatusList,
            sourceArr: _data.SourceArr,
            count: '',
            tableColumn: [
                { title: '序号',  minWidth: 80, align: 'center', slot: 'sn' },
                { title: '出库单号', minWidth: 100, key: 'tranCode', align: 'center' },
                { title: '备件编码', minWidth: 100, key: 'materialCode', align: 'center' },
                { title: '备件名称', minWidth: 100, key: 'materialDesc', align: 'center' },
                { title: '备件唯一码', minWidth: 100, key: 'batchNum', align: 'center' },
                { title: '应出库数量', minWidth: 100, key: 'orderAmnt', align: 'center' },
                { title: '实际出库数量', minWidth: 100, key: 'getAmnt', align: 'center' },
                { title: '工单备件登记ID', minWidth: 100, key: 'woPartId', align: 'center' },
            ],
            commList: [],
            orderId:'',
            referId:'',
        };
    },

    watch: {
        show(val) {
            if (val) {
                this.getInfo();
            }
        }
    },
    methods: {

        openImg(url) {
            window.open(url);
        },

        typeVal(list, value) {
            const obj = {};
            this[list].map((e) => {
                obj[e.value] = e.label || e.value;
            });
            // console.log(obj[value]);
            return obj[value];
        },

        typeNode(list, value) {
            const obj = {};
            this[list].map((e) => {
                obj[e.value] = e.node;
            });
            return obj[value];
        },

        timeChange(params) {
            // console.log(params);
            let dates = "";
            if (params) {
                dates = params.replace("T", " ");
            }
            return dates;
        },
        // 获取工单详情
        getInfo() {
            let params={'id':this.orderId}
            API.orderDetail(params).then(res => {
                this.infoList = res.data?.result
                this.infoList.referId=this.referId
                this.commList = res.data?.result?.spTranSnDetail||[]
            })
        },
    },
};
</script>

<style scoped lang="scss">
@import "@/style/_cus_list.scss";
@import "@/style/_cus_reg.scss";

.step {
    position: relative;
    margin: 0 20px;
    padding: 12px 6px 6px;
    border: 1px solid #e8eaec;

    .stepdiv {
        width: 80%;
        margin: 0 auto;
        @extend pt;
    }
}

.cus_title {
    margin-top: 20px;
    margin-bottom: 6px;
}
</style>
