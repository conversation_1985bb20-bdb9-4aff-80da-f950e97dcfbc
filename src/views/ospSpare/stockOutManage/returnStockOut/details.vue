<template>
    <Modal width="80%" v-model="show" title="详情" :footer-hide="true" :styles="{ top: '5vh' }"
           :mask-closable="false">
        <div style="overflow-y: auto; width: 100%; height: 80vh; background: #eee; padding: 20px">
            <Card :bordered="false">
                <div class="cus_list" style="min-height: calc(80vh - 72px)">
                    <div class="list_son">
<!--                        后期添加-->
<!--                        <div class="step">-->
<!--                            <Steps class="stepdiv" :current="typeNode('orderStatusList', infoList.status)">-->
<!--                                <Step v-for="(item, index) in infoList.processLogs" :key="index" :content="timeChange(item.createdAt)"-->
<!--                                      :title="item.desc"></Step>-->
<!--                            </Steps>-->
<!--                        </div>-->
                    </div>
                    <div class="cus_reg">
                        <div class="cus_form" style="padding-top: 0">
                            <p class="cus_title">主单信息</p>
                            <el-descriptions class="margin-top" :column="3" border>
                                <el-descriptions-item>
                                    <template slot="label">
                                        退返单号
                                    </template>
                                    {{infoList?.backCode}}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        退返仓库
                                    </template>
                                    {{infoList?.warehouseName}}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        签收仓库
                                    </template>
                                    {{infoList?.getWarehosueName}}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        退返状态
                                    </template>
                                    {{backStatusMap[infoList?.backStatus]}}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        退返类型
                                    </template>
                                    {{backTypeMap[infoList?.backType]}}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        退返时间
                                    </template>
                                    {{infoList?.backDate}}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        快递单号
                                    </template>
                                    {{infoList?.transportCode}}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        签收时间
                                    </template>
                                    {{infoList?.getDate}}
                                </el-descriptions-item>
                            </el-descriptions>
                            <p class="cus_title">明细信息</p>
                            <Table size="small" border ref="tableRef" :columns="tableColumn" :data="commList">
                                <template slot-scope="{ index }" slot="sn">
                                    {{ index + 1 }}
                                </template>
                                <!--                    附件查看-->
                                <template slot-scope="{ index }" slot="fileList">
                                    <Button  v-if="commList[index].fileList && commList[index].fileList.length>0"  type="info" ghost size="small" @click="handleView(commList[index].fileList)">附件查看</Button>
                                </template>
                            </Table>
                        </div>
                    </div>
                </div>
            </Card>
            <!--        附件查看-->
            <Modal v-model="visible" width="800px" reset-drag-position draggable sticky title="预览" footer-hide class-name="vertical-center-modal">
                <img class="preview" v-if="visibleImg" width="700" :src="visibleImg" @click="toTarget(visibleImg)" title="点击查看原图">
            </Modal>
        </div>
    </Modal>
</template>

<script>
import _data from "@/views/osp/_edata"; // 数据字典
import API from "@/api/ospSpare/stockOutManage/returnStockOut";

export default {
    data() {
        return {
            show: false,
            infoList: {},
            orderStatusList: _data.orderStatusList,
            sourceArr: _data.SourceArr,
            count: '',
            tableColumn: [
                { title: '序号',  minWidth: 80, align: 'center', slot: 'sn' },
                { title: '备件编码', minWidth: 100, key: 'materialCode', align: 'center' },
                { title: '备件描述', minWidth: 100, key: 'materialDesc', align: 'center' },
                { title: '单位', minWidth: 100, key: 'materialUnit', align: 'center' },
                { title: '单价', minWidth: 100, key: 'price', align: 'center' },
                { title: '退返数量', minWidth: 100, key: 'backAmnt', align: 'center' },
                { title: '签收数量', minWidth: 100, key: 'actBackAmnt', align: 'center' },
                { title: '工单备件申请ID', minWidth: 100, key: 'woPartId', align: 'center' },
                { title: '附件', minWidth: 100, key: 'fileList', align: 'center' ,slot: 'fileList'},
            ],
            orderId:'',
            commList: [],
            backStatusMap: {}, // 退件状态
            backTypeMap: {}, // 退件类型
            visible: false,
            visibleImg: '',
        };
    },
    watch: {
        show(val) {
            if (val) {
                this.getInfo();
            }
        }
    },
    methods: {
        // 打开查看附件弹框
        handleView(fileList){
            this.visible = true;
            this.visibleImg = fileList[0].fileUrl;
        },
        toTarget(url) {
            window.open(url)
        },
        openImg(url) {
            window.open(url);
        },

        typeVal(list, value) {
            const obj = {};
            this[list].map((e) => {
                obj[e.value] = e.label || e.value;
            });
            return obj[value];
        },

        typeNode(list, value) {
            const obj = {};
            this[list].map((e) => {
                obj[e.value] = e.node;
            });
            return obj[value];
        },

        timeChange(params) {
            let dates = "";
            if (params) {
                dates = params.replace("T", " ");
            }
            return dates;
        },
        // 获取工单详情
        getInfo() {
            API.orderDetail(this.orderId).then(res => {
                this.infoList = res.data?.result
                this.commList = res.data?.result?.oldbackListsDetailList||[]
            })
        },
    },
};
</script>

<style scoped lang="scss">
@import "@/style/_cus_list.scss";
@import "@/style/_cus_reg.scss";

.step {
    position: relative;
    margin: 0 20px;
    padding: 12px 6px 6px;
    border: 1px solid #e8eaec;

    .stepdiv {
        width: 80%;
        margin: 0 auto;
        @extend pt;
    }
}

.cus_title {
    margin-top: 20px;
    margin-bottom: 6px;
}
</style>
