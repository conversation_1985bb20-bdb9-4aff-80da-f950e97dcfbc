<template>
    <Modal width="1200" v-model="modelEdit" draggable sticky scrollable :title="modelTitle" :footer-hide="true"
           :mask-closable="false">
        <div style="position: relative; width: 100%; height: 60vh;">
            <div style="padding-bottom: 40px; width: 100%; height: 60vh; overflow-y: auto">
                <Form :model="formItem" :label-width="100" ref="formItem" inline>
                    <FormItem label="退返单号">
                        <Input type="text" maxlength="15" v-model="formItem.backCode" clearable
                               style="width: 360px" disabled />
                    </FormItem>
                    <FormItem label="申请服务商" prop="duty">
                        <Input type="text" maxlength="15" v-model="formItem.warehouseName" clearable
                               style="width: 360px" disabled/>
                    </FormItem>
                    <FormItem label="物流公司" prop="duty">
                        <Input type="text" maxlength="15" v-model="formItem.transportName  " clearable
                               style="width: 360px" disabled />
                    </FormItem>
                    <FormItem label="物流单号" prop="duty">
                        <Input type="text" maxlength="15" v-model="formItem.transportCode" clearable
                               style="width: 360px" disabled/>
                    </FormItem>
                </Form>
<!--                <div class="list_but" style="margin-bottom: 10px">-->
<!--                    <div class="condition">-->
<!--                        <Button @click="modelEditAdd = true" size="small" type="primary">添加</Button>-->
<!--                        <Button style="margin-left: 6px" @click="del" size="small" type="error" :disabled="!subTableSelect.length">删除</Button>-->
<!--                    </div>-->
<!--                </div>-->
                <div class="commListDiv">
                    <Table size="small" border ref="subTableRef" :columns="subTableColumn" :data="subTableData" @on-selection-change="selected => this.subTableSelect = selected">
                        <template slot-scope="{ index }" slot="actBackAmnt">
<!--                            {{ subTableData[index].submitNum }}-->
                            <InputNumber :min="0" type="number" v-model="subTableData[index].actBackAmnt"/>
<!--                            <Input v-model="subTableData[index].actBackAmnt" style="text-align: center" @on-change="tableInputChange" />-->
                        </template>
                    </Table>
                </div>
            </div>
            <div style="position: absolute; z-index: 99; right: 20px; bottom: -30px; text-align: right; margin: 30px 0">
                <Button type="primary"  style="margin-left: 10px"
                        @click="debounce(submitPop)">保存</Button>
                <Button type="default" style="margin-left: 10px"
                        @click="modelEdit = false">取消</Button>
            </div>
        </div>
    </Modal>
</template>

<script>
import API from "@/api/ospSpare/stockOutManage/returnStockOut";
import AreaChange from "@/utils/areaChange";

export default {
    name: "addEdit",
    data() {
        return {
            modelEdit: false,
            modelTitle: '编辑',
            formItem: {
                backCode: '',
                warehouseName: '',
                transportName  : '',
                transportCode: ''
            },
            subTableSelect: [],
            subTableData: [],
            subTableColumn: [
                {
                    title: '工单号',
                    key: 'csoId',
                    align: 'center'
                },
                {
                    title: '备件编码',
                    key: 'materialCode',
                    align: 'center'
                },
                {
                    title: '备件描述',
                    key: 'materialDesc',
                    align: 'center'
                },
                {
                    title: '单位',
                    key: 'materialUnit',
                    align: 'center'
                },
                {
                    title: '单价',
                    key: 'price',
                    align: 'center'
                },
                {
                    title: '退返数量',
                    key: 'backAmnt',
                    align: 'center'
                },
                {
                    title: '实际退返数量',
                    key: 'actBackAmnt',
                    slot: 'actBackAmnt',
                    align: 'center'
                }
            ],
            dataSource:{},
        }
    },
    watch: {
        modelEdit(val) {
            if (val) {
                this.getInfo();
            }
        }
    },
    methods: {
        tableInputChange(e) {
            this.$refs.subTableRef.selectAll(false);
        },
        // 保存方法
        submitPop() {
                //保存方法
                let arr=[]
                this.subTableData.forEach(t=>{
                    arr.push({
                        'id':t.id,
                        'actBackAmnt':t.actBackAmnt,
                        'backAmnt':t.backAmnt
                    })
                })
                let params={
                    'id':this.dataSource.id,
                    'transportCode':this.formItem.transportCode,
                    'transportName':this.formItem.transportName,
                    'oldbackListsDetailList':arr
                }
                API.updateDetail(params).then(res=>{
                    if(res.data.success){
                        this.$Message.success('编辑成功');
                      setTimeout(() => {
                        this.$emit('getList');
                      }, 2000);
                        this.modelEdit = false;
                    }else{
                        this.$Message.error(res.data.error);
                    }
                }).catch(err=>{
                    this.$Message.error(err.data.error);
                })
        },
        // 获取工单详情
        getInfo() {
            API.orderDetail(this.dataSource.id).then(res => {
                this.formItem = res.data?.result
                this.subTableData = res.data?.result?.oldbackListsDetailList||[]
            })
        }
    }
}
</script>

<style lang="scss" scoped>
@import '@/style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
