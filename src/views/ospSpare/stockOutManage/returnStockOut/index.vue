<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏备件管理</Breadcrumb-item>
            <Breadcrumb-item to="/ospSpare/">出库管理</Breadcrumb-item>
            <Breadcrumb-item>退返件出库</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">退返单号：</p>
                    <Input v-model="searchObj.backCode" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">备件编码：</p>
                    <Input v-model="searchObj.materialCode" placeholder="请输入备件编码" clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">退返状态：</p>
                    <Select v-model="searchObj.backStatus" placeholder="请选择订单类型" clearable style="width: 200px">
                        <Option v-for="item in backStatusList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">退返类型：</p>
                    <Select v-model="searchObj.backType" clearable style="width: 200px">
                        <Option v-for="item in backTypeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">服务商名称：</p>
                    <Input v-model="searchObj.warehosueName" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">供应商名称：</p>
                    <Input v-model="searchObj.getWarehouseName" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">运维单号：</p>
                    <Input v-model="searchObj.csoId" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">创建时间：</p>
                    <DatePicker ref="formDateSh" :value="submintDate" @on-change="
                        (data) => {
                          return selectDate(data, 'createDate');
                        }
                      " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择" style="width: 200px ">
                    </DatePicker>
                </div>
                <div class="condition">
                    <p class="condition_p">退返时间：</p>
                    <DatePicker ref="formDateSh" :value="submintDate2" @on-change="
                        (data) => {
                          return selectDate2(data, 'getDate');
                        }
                      " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择" style="width: 200px ">
                    </DatePicker>
                </div>
            </div>
            <div class="list_but" style="display: flex; justify-content: space-between; width: 100%;">
                <div class="condition">
                    <Button @click="edit" type="success" :disabled="this.tableSelect.length!='1'">编辑</Button>
                    <Button @click="confirmReturn" type="success" :disabled="confirmReturnAble">确认退返</Button>
                    <Button @click="logisticsUpdate" type="primary" :disabled="this.tableSelect.length===0">物流更新</Button>
                    <Button @click="routeInfo" type="primary"  :disabled="this.tableSelect.length!='1'">快递查询</Button>
                    <Button @click="exportFn" type="primary">导出</Button>
                    <a href="https://cdn01.rrsjk.com/images/旧件退返单（随旧件寄回）.docx" download="旧件退返单.docx" target="_blank">附件1：旧件退返单（随旧件寄回）</a>
                </div>
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                </div>
            </div>
            <div class="commListDiv">
                <Table size="small" border ref="tableRef" :columns="tableColumn" :data="commList" @on-selection-change="handleSelectChange">
                    <template slot-scope="{ index }" slot="sn">
                        {{ index + 1 }}
                    </template>
                    <!--                    退返单类型-->
                    <template slot-scope="{ index }" slot="backType">
                        {{ backTypeMap[commList[index].backType] }}
                    </template>
<!--                    退返单状态-->
                    <template slot-scope="{ index }" slot="backStatus">
                        {{ backStatusMap[commList[index].backStatus] }}
                    </template>
                    <!--                    附件查看-->
<!--                    <template slot-scope="{ index }" slot="fileList">-->
<!--                        <Button  v-if="commList[index].fileList && commList[index].fileList.length>0"  type="info" ghost size="small" @click="handleView(commList[index].fileList)">附件查看</Button>-->
<!--                    </template>-->
                    <template slot-scope="{ index }" slot="option">
                        <Button type="info" ghost size="small" @click="openDetail(commList[index])" style="margin: 3px">详情</Button>
                    </template>
                </Table>
            </div>
            <Page style="margin: 20px" @on-change="changeCurrent" :current="searchObj.page"
                  :total="totalElements" show-total show-elevator />
        </div>
<!--        编辑-->
        <addEditPop ref="addEditPopRef"  @getList="getList"/>
<!--        详情-->
        <detail ref="detailsRef" />
<!--        物流更新弹窗-->
        <expressChange ref="expressChangeRef" @getList="getList"/>
        <!--        路由信息-->
        <routeInfo ref="routeInfoRef"  :dataSource="tableSelect[0]" :backTypeMap="backTypeMap"/>
        <!--        附件查看-->
        <Modal v-model="visible" width="800px" reset-drag-position draggable sticky title="预览" footer-hide class-name="vertical-center-modal">
            <img class="preview" v-if="visibleImg" width="700" :src="visibleImg" @click="toTarget(visibleImg)" title="点击查看原图">
        </Modal>
    </div>
</template>

<script>
import _data from "@/views/ospSpare/_edata"; // 数据字典
import API from "@/api/ospSpare/stockOutManage/returnStockOut";
import addEditPop from "./addEditPop";
import detail from './details'
import expressChange from "@/views/ospSpare/stockOutManage/returnStockOut/expressChange.vue";
import routeInfo from "@/views/ospSpare/stockOutManage/returnStockOut/routeInfo.vue";
export default {
    name: "serviceProviderOrder",
    components: { routeInfo, addEditPop, detail,expressChange },
    data() {
        return {
            searchObj: {
                backCode: "",
                backStatus: "",
                backType: "",
                warehosueName: "",
                materialCode: "",
                getWarehouseName: "",
                createDate: "",
                getDate: "",
                pageNum: 1,
                pageSize: 10
            },
            formObj: {},
            staffList: [],
            totalElements: 0,
            submintDate: [],
            submintDate2: [],
            status: _data.status,
            backStatusList: _data.backStatus, // 退返状态
            backStatusMap: {}, // 退返状态
            backTypeList: _data.backType, // 退返类型
            backTypeMap: {}, // 退返类型
            sourceArr: _data.SourceArr,
            faultCodeTypeList: _data.faultCodeTypeList,
            tableColumn: [
                { type: 'selection', minWidth: 60, title: '选择', align: 'center' },
                { title: '序号',  minWidth: 80, align: 'center', slot: 'sn' },
                { title: '退返单号', minWidth: 180, key: 'backCode', align: 'center' },
                { title: '退返仓库名称', minWidth: 180, key: 'warehouseName', align: 'center' },
                { title: '退返单类型', minWidth: 100, key: 'backType', align: 'center',slot: 'backType'  },
                { title: '退返单状态', minWidth: 100, key: 'backStatus', align: 'center' ,slot: 'backStatus' },
                { title: '退返时间', minWidth: 160, key: 'backDate', align: 'center' },
                { title: '签收日期', minWidth: 160, key: 'getDate', align: 'center' },
                { title: '签收供应商', minWidth: 100, key: 'getWarehosueName', align: 'center' },
                { title: '签收人', minWidth: 160, key: 'updateBy', align: 'center' },
                { title: '创建时间', minWidth: 160, key: 'createDate', align: 'center' },
                { title: '备注', minWidth: 100, key: 'remarks', align: 'center' },
                // { title: '附件', minWidth: 100, key: 'fileList', align: 'center' ,slot: 'fileList'},
                { title: '操作', minWidth: 120, align: 'center', fixed: 'right', slot: 'option' }
            ],
            commList: [],
            tableSelect:[],
            modelEdit: false,
            modelEditAdd: false,
            visible: false,
            visibleImg: '',
            confirmReturnAble:true, // 退返签收按钮是否可操作
        }
    },
    mounted() {
        this.getList()
        this.toolsDict()
    },
    methods: {
        // 表格选择
        handleSelectChange(selected){
            this.tableSelect = selected
            if(this.tableSelect.length == 1 ){
                if(this.tableSelect[0].backStatus == '4'){
                    this.confirmReturnAble = false

                }else{
                    this.confirmReturnAble = true
                }
            }
        },
        // 打开查看附件弹框
        handleView(fileList){
            this.visible = true;
            this.visibleImg = fileList[0].fileUrl;
        },
        toTarget(url) {
            window.open(url)
        },
        toolsDict(){
            //退返状态
            _data.backStatus.forEach(item=>{
                this.backStatusMap[item.value]=item.label
            })
            //退返类型
            _data.backType.forEach(item=>{
                this.backTypeMap[item.value]=item.label
            })
        },
        // 物流更新
        logisticsUpdate() {
            this.$refs.expressChangeRef.dataSource = this.tableSelect
            this.$refs.expressChangeRef.modelEdit = true
        },
        // 路由信息
        routeInfo() {
            this.$refs.routeInfoRef.modelEdit = true
        },
        // 编辑
        edit() {
            this.$refs.addEditPopRef.dataSource = this.tableSelect[0]
            this.$refs.addEditPopRef.modelEdit = true
        },
        // 确认退返
        confirmReturn() {
            this.$message.confirm('是否确认退返?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                API.subStockOut(this.tableSelect[0].id).then(res=>{
                    if(res.data.success){
                        this.$Message.success('退返成功!');
                        this.getList()
                    }else{
                        this.$Message.error(res.data.error);
                    }
                }).catch(err=>{
                    this.$Message.error(err.data.error);
                })

            })
        },
        // 导出
        exportFn() {
            let datas = this.searchObj;
            let { pageNum, pageSize, ...datasEx } = { ...datas }
            API.exportList(datasEx).then(res => {
                let binaryData = [];
                let link = document.createElement('a');
                binaryData.push(res.data);
                link.style.display = 'none';
                link.href = window.URL.createObjectURL(new Blob(binaryData));
                link.setAttribute('download', '退返件出库.xlsx');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }).catch(() => {
                this.$Message.error('导出失败');
            });
        },
        // 打开详情
        openDetail(row) {
            this.$refs.detailsRef.orderId = row.id
            this.$refs.detailsRef.backStatusMap = this.backStatusMap
            this.$refs.detailsRef.backTypeMap = this.backTypeMap
            this.$refs.detailsRef.show = true
        },
        // 查询按钮
        queryList() {
            this.searchObj.pageNum = 1;
            this.getList();
        },
        // 重置按钮
        emptyList() {
            this.searchObj = {
                backCode: "",
                backStatus: "",
                backType: "",
                warehosueName: "",
                getWarehouseName: "",
                createDate: "",
                getDate: "",
                pageNum: 1,
                pageSize: 10
            };
            this.submintDate = [];
            this.submintDate2 = [];
        },
        // 选择日期
        selectDate(date, refObj) {
            this.submintDate = date;
            this.searchObj[refObj + "Start"] = date[0];
            this.searchObj[refObj + "End"] = date[1];
        },
        selectDate2(date, refObj) {
            this.submintDate2 = date;
            this.searchObj[refObj + "Start"] = date[0];
            this.searchObj[refObj + "End"] = date[1];
        },
        // 获取工单列表
        getList() {
            this.$Message.loading({
                content: "查询中...",
                duration: 0,
            });
            let datas = this.searchObj;
            //todo接口报错 张
            datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
            delete datas.pageNum
            delete datas.pageSize
            const page = { page: this.searchObj.pageNum, rows: this.searchObj.pageSize }
            // 将所有选中项清空
            this.$refs.tableRef.selectAll(false);
            API.getList(page, datas).then(res => {
                this.$Message.destroy();
                this.commList = res.data.result.content
                this.totalElements = res.data.result.totalElements
            })
        },
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.pageNum = curr;
            this.getList();
        },
        jumpto(path, id, count) {
            this.$router.push({
                path,
                query: { id, count },
            });
        }
    }
}
</script>

<style lang="scss" scoped>
@import '@/style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
