<template>
    <Modal width="1000" v-model="modelEdit" draggable sticky scrollable :title="title" :footer-hide="true"
           :mask-closable="false">
        <div style="position: relative; width: 100%; height: 30vh;">
            <div style="padding-bottom: 40px; width: 100%; height: 30vh; overflow-y: auto">
                <Form :model="formItem" :label-width="100" ref="formItem" inline>
                    <FormItem label="出库单号">
                        <Tooltip max-width="360" :content="formItem.backCode">
                            <Input type="text" v-model="formItem.backCode" clearable
                                   style="width: 360px" disabled />
                        </Tooltip>

                    </FormItem>
                    <FormItem label="方式">
                        <RadioGroup v-model="formItem.transportType"  @on-change="handelRadioChange">
                            <Radio label="0">快递</Radio>
                            <Radio label="1">自行发货</Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem v-if="transportShow" label="物流公司" prop="transport" :rules="{ required: transportShow, message: ' ' }">
                        <Select
                            v-show="transportShow"
                            v-model="formItem.transport"
                            filterable
                            placeholder="请选择..."
                            style="width: 360px">
                            <Option v-for="(option, index) in expressOptions" :value="option.value" :key="index">{{option.label}}</Option>
                        </Select>

                        <Input v-show="!transportShow" type="text" maxlength="15" v-model="formItem.transport" clearable
                               style="width: 360px" :disabled="formItem.transportType === '1'" />

                    </FormItem>
                    <FormItem label="物流单号" prop="transportCode" :rules="{ required: true, message: ' ' }">
                        <Input type="text" maxlength="15" v-model="formItem.transportCode" clearable placeholder="请输入物流单号"
                               style="width: 360px"/>
                    </FormItem>
                </Form>
                <!--                <div class="list_but" style="margin-bottom: 10px">-->
                <!--                    <div class="condition">-->
                <!--                        <Button @click="modelEditAdd = true" size="small" type="primary">添加</Button>-->
                <!--                        <Button style="margin-left: 6px" @click="del" size="small" type="error" :disabled="!subTableSelect.length">删除</Button>-->
                <!--                    </div>-->
                <!--                </div>-->
            </div>
            <div style="position: absolute; z-index: 99; right: 20px; bottom: -30px; text-align: right; margin: 30px 0">
                <Button type="primary"
                        @click="debounce(submitPop)">提交</Button>
                <Button type="default" style="margin-left: 10px"
                        @click="modelEdit = false">取消</Button>
            </div>
        </div>
    </Modal>
</template>

<script>
import API from "@/api/ospSpare/stockOutManage/returnStockOut";

export default {
    name: "addEdit",

    watch: {
        modelEdit(val) {
            if (val) {
                this.getInfo();
            }
        }
    },
    data() {
        return {
            modelEdit: false,
            loading: false,
            transportTypeList:'',
            formItem: {
                backCode: '',
                transportType: 0,
                transport: '',
                transportCode: '',
            },
            title:'更改物流',
            subTableSelect: [],
            transportShow:true, // 物流公司展示样式标记
            dataSource: [],
            expressOptions:[]
        }
    },
    mounted(){
        this.getSelect()
    },
    methods: {
        // 物流公司查询
        getSelect(){
            API.getExpressCompany({}).then(res=>{
                this.loading = false;
                if (res.data.result && res.data.result.length>0){
                    let list =[]
                    list= res.data.result.map(item => {
                        return {
                            value: item.companyCode,
                            label: item.companyName,
                            key:item.companyCode
                        };
                    });
                    this.$set(this, 'expressOptions', list)
                }else{
                    this.options = [];
                }
            })
        },
        // remoteMethod (query) {
        //     if (query !== '') {
        //         this.loading = true;
        //         let params={
        //             'companyName':query
        //         }
        //         this.expressOptions=[{value:'',label:''}]
        //         API.getExpressCompany(params).then(res=>{
        //             this.loading = false;
        //             if (res.data.result && res.data.result.length>0){
        //                 let list =[]
        //                 list= res.data.result.map(item => {
        //                     return {
        //                         value: item.companyCode,
        //                         label: item.companyName,
        //                         key:item.companyCode
        //                     };
        //                 });
        //                 // this.expressOptions=list
        //                 this.$set(this, 'expressOptions', list)
        //             }else{
        //                 this.options = [];
        //             }
        //         })
        //     } else {
        //         this.options = [];
        //     }
        // },
        handelRadioChange(e){
            if(e=='0'){
                this.transportShow=true;
            }else{
                this.transportShow=false;
            }
        },

        submitPop() {
                // 更新物流
            let ids = []
            this.dataSource.forEach(e=>{
                ids.push(e.id)
            })
            let params={
                ids,
                'transportCode':this.formItem.transportCode,//物流单号
                'transportName':this.formItem.transport,//物流公司
            }
                API.updateTransportDetail(params).then(res=>{
                    if(res.data.success){
                        this.$Message.destroy();
                        this.$Message.success(res.data.result);
                        setTimeout(() => {
                            this.$emit('getList')
                        }, 2000)
                        this.modelEdit = false
                        this.resetForm();
                    }else{
                        this.$Message.destroy();
                        this.$Message.error(res.data.error);
                    }
                }).catch(err=>{
                    this.$Message.destroy();
                    this.$Message.error(err.data.error);
                })
        },
        /*获取详情*/
        getInfo(){
            let codes = []
            this.dataSource.forEach(e=>{
                codes.push(e.backCode)
            })
            this.formItem.backCode = codes.join(',')
            //默认 快递类型选择 快递-0
            this.formItem.transportType='0';
            this.formItem.transport='' // 清空快递公司
        },
        // 重置
        resetForm(){
            this.formItem = {
                tranCode: '',
                transportType: 0,
                transport: '',
                transportCode: '',
            }
            this.subTableData=[];
            this.transportShow=true;
        },
    }
}
</script>
<style>
.Input /deep/ input {
    text-align: center;
}
</style>
<style lang="scss" scoped>
@import 'style/_cus_list.scss';
.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
