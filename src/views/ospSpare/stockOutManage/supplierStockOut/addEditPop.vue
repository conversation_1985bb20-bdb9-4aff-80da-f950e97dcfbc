<template>
    <Modal width="1200" v-model="modelEdit" draggable sticky scrollable :title="title" :footer-hide="true"
           :mask-closable="false">
        <div style="position: relative; width: 100%; height: 60vh;">
            <div style="padding-bottom: 40px; width: 100%; height: 60vh; overflow-y: auto">
                <Form :model="formItem" :label-width="100" ref="formItem" inline>
                    <FormItem label="出库单号">
                        <Input type="text" maxlength="15" v-model="formItem.tranCode" clearable placeholder="请输入出库单号"
                               style="width: 360px" disabled />
                    </FormItem>
                    <FormItem label="入库服务商">
                        <Input type="text" maxlength="15" v-model="formItem.receiveWarehouseName" clearable placeholder="请输入入库服务商"
                               style="width: 360px" disabled />
                    </FormItem>
                    <FormItem label="物流公司" prop="transport" :rules="{ required: popType !== 'closeOrderStock', message: ' ' }">
                        <Input type="text" maxlength="15" v-model="formItem.transport" clearable placeholder="请输入物流公司"
                               style="width: 360px" :disabled="popType === 'closeOrderStock'" />
                    </FormItem>
                    <FormItem label="物流单号" prop="transportCode" :rules="{ required: popType !== 'closeOrderStock', message: ' ' }">
                        <Input type="text" maxlength="15" v-model="formItem.transportCode" clearable placeholder="请输入物流单号"
                               style="width: 360px" :disabled="popType === 'closeOrderStock'" />
                    </FormItem>
                </Form>
                <!--                <div class="list_but" style="margin-bottom: 10px">-->
                <!--                    <div class="condition">-->
                <!--                        <Button @click="modelEditAdd = true" size="small" type="primary">添加</Button>-->
                <!--                        <Button style="margin-left: 6px" @click="del" size="small" type="error" :disabled="!subTableSelect.length">删除</Button>-->
                <!--                    </div>-->
                <!--                </div>-->
                <div class="commListDiv">
                    <Table size="small" border ref="subTableRef" :columns="subTableColumn" :data="subTableData" :filterMultiple="false"  @on-selection-change="selected => this.subTableSelect = selected">
                        <template slot-scope="{ index }" slot="amount">
                            <InputNumber v-if="popType === 'closeOrderStock'" :min="0" v-model="subTableData[index].amount" style="text-align: center" @on-change="tableInputChange" />
                            <span v-else>{{ subTableData[index].amount }}</span>
                        </template>
                    </Table>
                </div>
            </div>
            <div style="position: absolute; z-index: 99; right: 20px; bottom: -30px; text-align: right; margin: 30px 0">
                <Button type="primary"
                        @click="debounce(submitPop)">保存</Button>
                <Button type="default" style="margin-left: 10px"
                        @click="modelEdit = false">取消</Button>
            </div>
        </div>
    </Modal>
</template>

<script>
import API from "@/api/ospSpare/stockOutManage/supplierStockOut";

export default {
    name: "addEdit",
    props: {
        popType: {
            type: String,
            default: 'closeOrderStock'
        },
        dataSource:{
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    watch: {
        modelEdit(val) {
            if (val) {
                if(this.popType=='closeOrderStock'){
                    this.title='编辑'
                }
                if(this.popType=='logisticsUpdate'){
                    this.title='更改物流'
                }
                this.getInfo();
            }
        }
    },
    data() {
        return {
            modelEdit: false,
            modelTitle: '备件出库',
            formItem: {
                tranCode: '',
                receiveWarehouseName: '',
                transport: '',
                transportCode: '',
            },
            title:'编辑',
            subTableSelect: [],
            subTableData: [],
            subTableColumn: [
                {
                    type: 'selection',
                    width: 60,
                    title: '选择',
                    align: 'center'
                },
                {
                    title: '备件编码',
                    key: 'materialCode',
                    align: 'center'
                },
                {
                    title: '备件描述',
                    key: 'materialDesc',
                    align: 'center'
                },
                {
                    title: '备件唯一码',
                    key: 'batchNum',
                    align: 'center'
                },
                {
                    title: '单价',
                    key: 'materialPrice',
                    align: 'center'
                },
                {
                    title: '应出库数量',
                    key: 'amountOrg',
                    align: 'center'
                },
                {
                    title: '实际出库数量',
                    key: 'amount',
                    slot: 'amount',
                    align: 'center'
                }
            ]
        }
    },
    methods: {
        tableInputChange(e) {
            this.$refs.subTableRef.selectAll(false);
        },
        submitPop() {
            // 编辑
            if(this.popType=='closeOrderStock'){
                let params=[]
                this.subTableData.forEach(item=>{
                    params.push({
                        'id':item.id,
                        'amount':item.amount
                    })
                })
                API.updateDetail({'spTranDetail':params}).then(res=>{
                    if(res.data.success){
                        this.$Message.destroy();
                        this.$Message.success(res.data.result);
                        setTimeout(() => {
                            this.$emit('getList')
                        }, 2000);
                        this.modelEdit = false
                    }else{
                        this.$Message.destroy();
                        this.$Message.error(res.data.error);
                    }
                }).catch(err=>{
                    this.$Message.destroy();
                    this.$Message.error(err.data.error);
                })
            }else{
                if(this.subTableSelect.length<1){
                    this.$message.error('请选择数据！')
                    return
                }
                // 更新物流
                let params={
                    'id':this.subTableSelect[0].id,
                    'transportCode':this.formItem.transportCode,
                    'transport':this.formItem.transport
                }
                API.changeAddress(params).then(res=>{
                    if(res.data.success){
                        this.$Message.destroy();
                        this.$Message.success(res.data.result);
                        setTimeout(() => {
                            this.$emit('getList')
                        }, 2000);
                        this.modelEdit = false
                    }else{
                        this.$Message.destroy();
                        this.$Message.error(res.data.error);
                    }
                }).catch(err=>{
                    this.$Message.destroy();
                    this.$Message.error(err.data.error);
                })
            }


        },
    /*获取详情*/
        getInfo(){
            API.orderDetail(this.dataSource.id).then(res => {
                this.formItem = res.data?.result
                this.subTableData = res.data?.result?.spTranDetail||[]
            })
        }
    }
}
</script>
<style>
.Input /deep/ input {
    text-align: center;
}
</style>
<style lang="scss" scoped>
@import 'style/_cus_list.scss';
.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
