<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏备件管理</Breadcrumb-item>
            <Breadcrumb-item to="/ospSpare/">出库管理</Breadcrumb-item>
            <Breadcrumb-item>供应商出库</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">出库单号：</p>
                    <Input v-model="searchObj.tranCode" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">订单号：</p>
                    <Input v-model="searchObj.orderCode" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">发货仓库：</p>
                  <Select v-model="searchObj.warehouseName" filterable clearable style="width: 200px" placeholder="请选择...">
                    <Option v-for="(item,index) in warehosueList" :value="item.label" :key="index">{{ item.label }}</Option>
                  </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">入库仓库：</p>
                  <Select v-model="searchObj.receiveWarehouseName" filterable clearable style="width: 200px" placeholder="请选择...">
                    <Option v-for="(item,index) in receiveWarehouseList" :value="item.label" :key="index">{{ item.label }}</Option>
                  </Select>
                </div>
            </div>
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">出库状态：</p>
                    <Select v-model="searchObj.ifClose" clearable placeholder="请选择..." style="width: 200px">
                        <Option value="1" :key="1">已出库</Option>
                        <Option value="0" :key="0">未出库</Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">物流单号：</p>
                    <Input v-model="searchObj.tranSportCode" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">创建时间：</p>
                    <DatePicker ref="formDateSh" v-model="submintDate" @on-change="
                        (data) => {
                          return selectDate(data, 'createDate');
                        }
                      " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择" style="width: 200px ">
                    </DatePicker>
                </div>
                <div class="condition">
                    <p class="condition_p">出库时间：</p>
                    <DatePicker ref="formDateSh" v-model="submintDate2" @on-change="
                        (data) => {
                          return selectDate2(data, 'tranDate');
                        }
                      " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择" style="width: 200px ">
                    </DatePicker>
                </div>
            </div>
            <div class="list_but">

            </div>
            <div class="list_but" style="display: flex; justify-content: space-between; width: 100%;">
                <div class="condition">
<!--                    <Button @click="closeOrderStock" type="primary" :disabled="tableSelect.length !== 1 || tableSelect[0].ifClose !== '0'">编辑</Button>-->
                    <Button @click="outbound" type="primary" :disabled="tableSelect.length !== 1 || tableSelect[0].ifClose !== '0'">发货</Button>
                    <Button @click="cancel" type="error" :disabled="tableSelect.length !== 1  || tableSelect[0].ifClose !== '0'">取消出库</Button>
                    <Button @click="logisticsUpdate" type="primary" :disabled="tableSelect.length !== 1 || tableSelect[0].ifClose !== '1'">物流更新</Button>
                    <Button @click="routeInfo" type="primary" :disabled="tableSelect.length !== 1 || tableSelect[0].transportCode == ''|| !tableSelect[0].transportCode">快递查询</Button>
                    <Button @click="exportFn" type="success">导出</Button>
                </div>
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                </div>
            </div>
            <div class="commListDiv">
                <Table size="small" border ref="tableRef" :columns="tableColumn" :data="commList" @on-selection-change="selected => this.tableSelect = selected">
                    <template slot-scope="{ index }" slot="sn">
                        {{ index + 1 }}
                    </template>
<!--                    出库类型-->
                    <template slot-scope="{ index }" slot="tranType">
                        <span>{{tranTypeMap[commList[index].tranType]}}</span>
                    </template>
<!--                    出库状态-->
                    <template slot-scope="{ index }" slot="ifClose">
                        <span v-if="commList[index].ifClose=='0'">未出库</span>
                        <span v-if="commList[index].ifClose=='1'">已出库</span>
                        <span v-else></span>
                    </template>
                    <template slot-scope="{ index }" slot="option">
                        <Button type="info" ghost size="small" @click="openDetail(commList[index])" style="margin: 3px">详情</Button>
                    </template>
                </Table>
            </div>
            <Page style="margin: 20px" @on-change="changeCurrent" :current="searchObj.page"
                  :total="totalElements" show-total show-elevator />
        </div>
<!--        详情-->
        <detail ref="detailsRef"/>
<!--        关单出库 或 物流更新 弹窗-->
        <addEditPop ref="addEditPopRef" :popType="popType" :dataSource="tableSelect[0]"  @getList="getList"/>
<!--        物流更新弹窗-->
        <expressChange ref="expressChangeRef" :popType="popType" :dataSource="tableSelect[0]"  @getList="getList"/>
<!--        路由信息-->
        <routeInfo ref="routeInfoRef"  :dataSource="tableSelect[0]"/>
    </div>
</template>

<script>
import addEditPop from "./addEditPop";
import expressChange from "@/views/ospSpare/stockOutManage/supplierStockOut/expressChange.vue";
import detail from './details'
import routeInfo from './routeInfo'
import API from "@/api/ospSpare/stockOutManage/supplierStockOut"
import _data from "@/views/ospSpare/_edata";
import { removeClass } from "@/components/message-box/js/dom"; // 数据字典

export default {
    name: "serviceProviderOrder",
    components: { addEditPop, detail, routeInfo,expressChange },
    data() {
        return {
            searchObj: {
                tranCode: "",
                orderCode: "",
                warehouseName: "",
                receiveWarehouseName: "",
                ifClose: "0",
                tranSportCode: "",
                createDateStart: "",
                createDateEnd: "",
                tranDateStart: "",
                tranDateEnd: "",
                pageNum: 1,
                pageSize: 10
            },
            tableColumn: [
                { type: 'selection', minWidth: 60, title: '选择', align: 'center' },
                { title: '序号',  minWidth: 80, align: 'center', slot: 'sn' },
                { title: '出库单号', minWidth: 200, key: 'tranCode', align: 'center' },
                { title: '订单号', minWidth: 180, key: 'orderCode', align: 'center' },
                { title: '发货仓库', minWidth: 180, key: 'warehosueName', align: 'center' },
                { title: '中心名称', minWidth: 100, key: 'centerName', align: 'center' },
                { title: '入库仓库', minWidth: 100, key: 'receiveWarehouseName', align: 'center' },
                { title: '出库类型', minWidth: 100, key: 'tranType', align: 'center',slot: 'tranType' },
                { title: '出库状态', minWidth: 100, key: 'ifClose', align: 'center',slot: 'ifClose' },
                { title: '出库时间', minWidth: 160, key: 'tranDate', align: 'center' },
                { title: '物流公司', minWidth: 180, key: 'transportNamePinyin', align: 'center' },
                { title: '物流单号', minWidth: 180, key: 'transportCode', align: 'center' },
                { title: '创建时间', minWidth: 160, key: 'createDate', align: 'center' },
                { title: '操作', minWidth: 120, align: 'center', fixed: 'right', slot: 'option' }
            ],

            formObj: {},
            staffList: [],
            totalElements: 0,
            submintDate: [],
            submintDate2: [],
            tranDate: [],//出库时间
            createDate: [],//创建时间
            status: _data.status,
            tranTypeList: _data.tranType,
            tranTypeMap: {},
            sourceArr: _data.SourceArr,
            faultCodeTypeList: _data.faultCodeTypeList,
            commList: [],
            tableSelect: [],//表格选择的数据
            title: "一键派工",
            modelEdit: false,
            modelTitle: '新增',
            formItem: {
                aaa: '',
                bbb: ''
            },
            dictList:{
                tranSnType:[],
            },// 数据字典列表
            modelEditAdd: false,
            popType: 'closeOrderStock' ,// 关单出库 StockOutSub  编辑 closeOrderStock 物流更新 logisticsUpdate
          warehosueList:[],
          receiveWarehouseList:[],
        }
    },
    mounted() {
        this.getList()
        this.toolsDict()
    },
    methods: {
        // 字段map生成
        toolsDict(){
            // 出库类型
            _data.tranType.forEach(item=>{
                this.tranTypeMap[item.value]=item.label
            })

        //   获取发货仓库
          API.getWarehouse({'warehouseLevel':'1'}).then(res=>{
            if(res.data.success){
              let arr=[]
              res.data.result.forEach(item=>{
                arr.push({
                  'value':item.warehouseCode,
                  'label':item.warehouseName
                })
              })
              this.warehosueList=arr
            }
          })

        //   获取入库仓库
          API.getWarehouse({'warehouseLevel':'3'}).then(res=>{
            if(res.data.success){
              let arr=[]
              res.data.result.forEach(item=>{
                arr.push({
                  'value':item.warehouseCode?item.warehouseCode:'',
                  'label':item.warehouseName
                })
              })
              this.receiveWarehouseList=arr
            }
          })
        },
        // 关单出库
        StockOutSub(){
            API.subStockOut(this.tableSelect[0].id).then(res=>{
                if(res.data.success){
                    this.$Message.success(res.data.result);
                    setTimeout(() => {
                        this.getList()
                    }, 2000);
                }else{
                    this.$Message.error(res.data.error);
                }
            }).catch(err=>{
                this.$Message.error(err.data.error);
            })
        },
        // 编辑
        closeOrderStock() {
            this.popType = "closeOrderStock"
            this.$refs.addEditPopRef.modelEdit = true
        },
        // 关单出库
        outbound(){
            this.$refs.expressChangeRef.modelEdit = true
            this.popType = "outbound"
        },
        // 物流更新
        logisticsUpdate() {
            this.popType = "logisticsUpdate"
            this.$refs.expressChangeRef.modelEdit = true
        },
        // 路由信息
        routeInfo() {
            if(this.tableSelect.length!='1'){
                this.$Message.error('请选择一条数据！');
                return
            }
            this.$refs.routeInfoRef.modelEdit = true
        },
        // 取消出库
        cancel() {
            this.$message.confirm('是否确认取消出库?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                API.cancelStockOut(this.tableSelect[0].id).then(res=>{
                    if(res.data.success){
                        this.$Message.success(res.data.result);
                        setTimeout(() => {
                            this.getList()
                        }, 2000);
                    }else{
                        this.$Message.error(res.data.error);
                    }
                })
            }).catch(err=>{
                this.$Message.error(err.data.error);
            })
        },
        // 导出
        exportFn() {
            let datas = this.searchObj;
            let { pageNum, pageSize, ...datasEx } = { ...datas }
            this.removeEmptyValues(datasEx)
            API.exportList(datasEx).then(res => {
                let binaryData = [];
                let link = document.createElement('a');
                binaryData.push(res.data);
                link.style.display = 'none';
                link.href = window.URL.createObjectURL(new Blob(binaryData));
                link.setAttribute('download', '供应商出库.xlsx');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }).catch(() => {
                    this.$Message.error('导出失败');
            });
        },
        // 对象 去除 value为空的键值对
        removeEmptyValues(obj) {
            for (const key in obj) {
                if (typeof obj[key] === 'object') {
                    removeEmptyValues(obj[key]);
                }

                if (!obj[key] || (typeof obj[key] === 'object' && !Reflect.ownKeys(obj[key]).length)) {
                    delete obj[key];
                }
            }
        },
        // 打开详情
        openDetail(row) {
            this.$refs.detailsRef.orderId = row.id
            this.$refs.detailsRef.show = true
        },
        // 查询按钮
        queryList() {
            this.searchObj.pageNum = 1;
            this.getList();
        },

        // 重置按钮
        emptyList() {
            this.searchObj = {
                tranCode: "",
                orderCode: "",
                warehouseName: "",
                receiveWarehouseName: "",
                ifClose: "",
                tranSportCode: "",
                createDateStart: "",
                createDateEnd: "",
                tranDateStart: "",
                tranDateEnd: "",
                pageNum: 1,
                pageSize: 10
            };
            this.submintDate = [];
            this.submintDate2 = [];
            this.tranDate = []; // 出库时间
            this.createDate = [];//创建时间
        },
        // 选择日期
        selectDate(date, refObj) {
            this.submintDate = date;
            this.searchObj[refObj + "Start"] = date[0];
            this.searchObj[refObj + "End"] = date[1];
        },
      selectDate2(date, refObj) {
        this.submintDate2 = date;
        this.searchObj[refObj + "Start"] = date[0];
        this.searchObj[refObj + "End"] = date[1];
      },
        // 获取工单列表
        getList() {
            this.$Message.loading({
                content: "查询中...",
                duration: 0,
            });
            let datas = this.$_.cloneDeep(this.searchObj);
            datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
            delete datas.pageNum
            delete datas.pageSize
            const page = { page: this.searchObj.pageNum, rows: this.searchObj.pageSize }
            // 将所有选中项清空
            this.$refs.tableRef.selectAll(false);
            API.getList(page, datas).then(res => {
                this.$Message.destroy();
                this.commList = res.data.result.content
                this.totalElements = res.data.result.totalElements
            })
        },
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.pageNum = curr;
            this.getList();
        },
        jumpto(path, id, count) {
            this.$router.push({
                path,
                query: { id, count },
            });
        }
    }
}
</script>

<style lang="scss" scoped>
@import 'style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
