<template>
    <Modal width="1200" v-model="modelEdit" draggable sticky scrollable :title="title" :footer-hide="true"
           :mask-closable="false">
        <div style="position: relative; width: 100%; height: 60vh;">
            <div style="padding-bottom: 40px; width: 100%; height: 60vh; overflow-y: auto">
                <Form :model="formItem" :label-width="100" ref="formItem" inline>
                    <FormItem label="出库单号">
                        <Input type="text" maxlength="15" v-model="formItem.tranCode" clearable placeholder="请输入出库单号"
                               style="width: 360px" disabled />
                    </FormItem>
                    <FormItem label="方式">
                        <RadioGroup v-model="formItem.transportType"  @on-change="handelRadioChange">
                            <Radio label="0">快递</Radio>
                            <Radio label="1">自行发货</Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem v-if="transportShow" label="物流公司" prop="transport" :rules="{ required: popType !== 'closeOrderStock', message: ' ' }">
                        <Select
                            v-show="transportShow"
                            v-model="formItem.transport"
                            filterable
                            placeholder="请选择..."
                            style="width: 360px">
                            <Option v-for="(option, index) in expressOptions" :value="option.value" :key="index">{{option.label}}</Option>
                        </Select>

                        <Input v-show="!transportShow" type="text" maxlength="15" v-model="formItem.transport" clearable
                               style="width: 360px" :disabled="formItem.transportType === '1'" />

                    </FormItem>
                    <FormItem label="物流单号" prop="transportCode" :rules="{ required: popType !== 'closeOrderStock', message: ' ' }">
                        <Input type="text" maxlength="15" v-model="formItem.transportCode" clearable placeholder="请输入物流单号"
                               style="width: 360px" :disabled="popType === 'closeOrderStock'" />
                    </FormItem>
                </Form>
                <!--                <div class="list_but" style="margin-bottom: 10px">-->
                <!--                    <div class="condition">-->
                <!--                        <Button @click="modelEditAdd = true" size="small" type="primary">添加</Button>-->
                <!--                        <Button style="margin-left: 6px" @click="del" size="small" type="error" :disabled="!subTableSelect.length">删除</Button>-->
                <!--                    </div>-->
                <!--                </div>-->
                <div class="commListDiv">
                    <Table size="small" border ref="subTableRef" :columns="subTableColumn" :data="subTableData" :filterMultiple="false"  @on-selection-change="selected => this.subTableSelect = selected">
                        <template slot-scope="{ index }" slot="amount">
                            <Input v-if="popType === 'closeOrderStock'" class="Input" v-model="subTableData[index].amount" @on-change="tableInputChange" />
                            <span v-else>{{ subTableData[index].amount }}</span>
                        </template>
                    </Table>
                </div>
            </div>
            <div style="position: absolute; z-index: 99; right: 20px; bottom: -30px; text-align: right; margin: 30px 0">
                <Button type="primary"
                        @click="debounce(submitPop)">提交</Button>
                <Button type="default" style="margin-left: 10px"
                        @click="modelEdit = false">取消</Button>
            </div>
        </div>
    </Modal>
</template>

<script>
import API from "@/api/ospSpare/stockOutManage/supplierStockOut";

export default {
    name: "addEdit",
    props: {
        popType: {
            type: String,
            default: 'logisticsUpdate'
        },
        dataSource:{
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    watch: {
        modelEdit(val) {
            if (val) {
                if(this.popType=='closeOrderStock'){
                    this.title='编辑'
                }
                if(this.popType=='logisticsUpdate'){
                    this.title='物流更新'
                }
                if(this.popType=='outbound'){
                    this.title='关单出库'
                }

                this.getInfo();
            }else{
                this.resetForm()
            }
        }
    },
    mounted(){
        this.getSelect()
    },
    data() {
        return {
            modelEdit: false,
            transportTypeList:'',
            formItem: {
                tranCode: '',
                transportType: 0,
                transport: '',
                transportCode: '',
            },
            title:'更改物流',
            subTableSelect: [],
            subTableData: [],
            subTableColumn: [
                // {
                //     type: 'selection',
                //     width: 60,
                //     title: '选择',
                //     align: 'center'
                // },
                // {
                //     title: '出库单号',
                //     key: 'tranCode',
                //     align: 'center'
                // },
                {
                    title: '备件编码',
                    key: 'materialCode',
                    align: 'center'
                },
                {
                    title: '备件描述',
                    key: 'materialDesc',
                    align: 'center'
                },
                {
                    title: '备件唯一码',
                    key: 'batchNum',
                    align: 'center'
                },
                {
                    title: '单价',
                    key: 'materialPrice',
                    align: 'center'
                },
                {
                    title: '应出库数量',
                    key: 'amountOrg',
                    align: 'center'
                },
                {
                    title: '实际出库数量',
                    key: 'amount',
                    slot: 'amount',
                    align: 'center'
                }
            ],
            transportShow:true ,// 物流公司展示样式标记
            loading:false,
            expressOptions:[],
        }
    },
    methods: {
        // 获取所有物流公司
        getSelect(){
            API.getExpressCompany({}).then(res=>{
                this.loading = false;
                if (res.data.result && res.data.result.length>0){
                    let list =[]
                    list= res.data.result.map(item => {
                        return {
                            value: item.companyCode,
                            label: item.companyName,
                            key:item.companyCode
                        };
                    });
                    this.$set(this, 'expressOptions', list)
                }else{
                    this.options = [];
                }
            })
        },
        // remoteMethod (query) {
        //     if (query !== '') {
        //         this.loading = true;
        //         let params={
        //             'companyName':query
        //         }
        //         this.expressOptions=[{value:'',label:''}]
        //         API.getExpressCompany(params).then(res=>{
        //             this.loading = false;
        //             if (res.data.result && res.data.result.length>0){
        //                     let list =[]
        //                         list= res.data.result.map(item => {
        //                         return {
        //                             value: item.companyCode,
        //                             label: item.companyName,
        //                             key:item.companyCode
        //                         };
        //                     });
        //                 // this.expressOptions=list
        //                 this.$set(this, 'expressOptions', list)
        //             }else{
        //                 this.options = [];
        //             }
        //         })
        //     } else {
        //         this.options = [];
        //     }
        // },
        handelRadioChange(e){
            if(e=='0'){
                this.transportShow=true;
            }else{
                this.transportShow=false;
            }
        },
        tableInputChange(e) {
            this.$refs.subTableRef.selectAll(false);
        },
        submitPop() {
            // 编辑
            if(this.popType=='closeOrderStock'){
                let params=[]
                this.subTableData.forEach(item=>{
                    params.push({
                        'id':item.id,
                        'amount':item.amount
                    })
                })
                API.updateDetail({'spTranDetail':params}).then(res=>{
                    if(res.data.success){
                        this.$Message.destroy();
                        this.$Message.success(res.data.result);
                        setTimeout(() => {
                            this.$emit('getList')
                        }, 2000);
                        this.modelEdit = false
                    }else{
                        this.$Message.destroy();
                        this.$Message.error(res.data.error);
                    }
                }).catch(err=>{
                    this.$Message.destroy();
                    this.$Message.error(err.data.error);
                })
            }
            // 关单出库
            if(this.popType=='outbound'){
                // 首先进行关单出库
                var transportCode = this.formItem.transportCode;
                if(transportCode.length <= 6 || transportCode.length >= 32){
                    this.$Message.error('快递单号最小长度6个字符，最大长度32个字符!');
                    return
                }
                API.subStockOut(this.dataSource.id).then(res=>{
                    if(res.data.success){
                        // 成功后更新物流
                        let params={
                            'id':this.dataSource.id,
                            'transportCode':this.formItem.transportCode,
                            'transport':this.formItem.transport
                        }
                        API.changeAddress(params).then(res=>{
                            if(res.data.success){
                                this.$Message.destroy();
                                this.$Message.success(res.data.result)
                                setTimeout(() => {
                                    this.$emit('getList')
                                }, 2000);
                                this.modelEdit = false
                            }else{
                                this.$Message.error(res.data.error);
                            }
                        }).catch(err=>{
                            this.$Message.error('更改失败!');
                        })
                    }else{
                        this.$Message.error(res.data.error);
                    }
                }).catch(err=>{
                    this.$Message.error(err.data.error);
                })
            }
            // 物流更新
            if(this.popType=='logisticsUpdate'){
                // 更新物流
                let params={
                    'id':this.dataSource.id,
                    'transportCode':this.formItem.transportCode,
                    'transport':this.formItem.transport
                }
                API.changeAddress(params).then(res=>{
                    if(res.data.success){
                        this.$Message.destroy();
                        this.$Message.success(res.data.result)
                        setTimeout(() => {
                            this.$emit('getList')
                        }, 2000);
                        this.modelEdit = false
                        this.resetForm();
                    }else{
                        this.$Message.error(res.data.error);
                    }
                }).catch(err=>{
                    this.$Message.error('更改失败!');
                })
            }


        },
        // 重置表单
        resetForm(){
            this.formItem = {
                tranCode: '',
                transportType: 0,
                transport: '',
                transportCode: '',
            }
            this.subTableData=[];
            this.transportShow=true;
        },
        /*获取详情*/
        getInfo(){
            API.orderDetail(this.dataSource.id).then(res => {
                this.formItem.tranCode=res.data?.result.tranCode||''
                this.formItem.transportCode=res.data?.result.transportCode||''
                this.subTableData = res.data?.result?.spTranDetail||[]
                //默认 快递类型选择 快递-0
                this.formItem.transportType='0';
            })

        }
    }
}
</script>
<style>
.Input /deep/ input {
    text-align: center;
}
</style>
<style lang="scss" scoped>
@import '@/style/_cus_list.scss';
.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
