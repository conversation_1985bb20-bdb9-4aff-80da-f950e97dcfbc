<template>
    <Modal width="80%" v-model="show" title="详情" :footer-hide="true" :styles="{ top: '5vh' }"
           :mask-closable="false">
        <div style="overflow-y: auto; width: 100%; height: 80vh; background: #eee; padding: 20px">
            <Card :bordered="false">
                <div class="cus_list" style="min-height: calc(80vh - 72px)">
                    <div class="list_son">
<!--                        <div class="step">-->
<!--                            <Steps class="stepdiv" :current="typeNode('orderStatusList', infoList.status)">-->
<!--                                <Step v-for="(item, index) in infoList.processLogs" :key="index" :content="timeChange(item.createdAt)"-->
<!--                                      :title="item.desc"></Step>-->
<!--                            </Steps>-->
<!--                        </div>-->
                    </div>
                    <div class="cus_reg">
                        <div class="cus_form" style="padding-top: 0">
                            <p class="cus_title">主单信息</p>
                            <el-descriptions class="margin-top" :column="3" border>
                                <el-descriptions-item>
                                    <template slot="label">出库单号</template>
                                    {{infoList?.tranCode}}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">订单号</template>
                                    {{infoList?.orderCode}}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        发货仓库
                                    </template>
                                    {{infoList?.warehosueName}}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        入库仓库
                                    </template>
                                    {{infoList?.receiveWarehouseName}}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        出库类型
                                    </template>
                                    {{infoList?.tranTypeName}}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        出库状态
                                    </template>
                                    {{ifCloseList[infoList?.ifClose]}}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        出库时间
                                    </template>
                                    {{infoList?.tranDate}}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        物流公司
                                    </template>
                                    {{infoList?.transportNamePinyin}}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        物流单号
                                    </template>
                                    {{infoList?.transportCode}}
                                </el-descriptions-item>
                            </el-descriptions>
                            <p class="cus_title">订单明细</p>
                                <Table size="small" border ref="tableRef" :columns="tableColumn" :data="commList">
                                    <template slot-scope="{ index }" slot="sn">
                                        {{ index + 1 }}
                                    </template>
                                </Table>
                        </div>
                    </div>
                </div>
            </Card>
        </div>
    </Modal>
</template>

<script>
import _data from "@/views/ospSpare/_edata"; // 数据字典
import API from "@/api/ospSpare/stockOutManage/supplierStockOut"

export default {
  data() {
    return {
        show: false,
        orderId: '',
        infoList: {},
        orderStatusList: _data.orderStatusList,
        sourceArr: _data.SourceArr,
        tranSnType: _data.tranSnType,
        ifCloseList:{
            '0':'未关闭',
            '1':'已关闭',
            'a':'提交审批',
            'b':'转库存关单失败',
        },
        count: '',
        tableColumn: [
            { title: '序号',  minWidth: 80, align: 'center', slot: 'sn' },
            { title: '备件编码', minWidth: 100, key: 'materialCode', align: 'center' },
            { title: '备件描述', minWidth: 100, key: 'materialDesc', align: 'center' },
            { title: '备件唯一码', minWidth: 100, key: 'batchNum', align: 'center' },
            { title: '备件单价', minWidth: 100, key: 'materialPrice', align: 'center' },
            { title: '应出库数量', minWidth: 100, key: 'amountOrg', align: 'center' },
            { title: '实际出库数量', minWidth: 100, key: 'amount', align: 'center' },
        ],
        commList: [],
    };
  },
  watch: {
      show(val) {
            if (val) this.getInfo();
      }
  },
  methods: {
    openImg(url) {
      window.open(url);
    },

    typeVal(list, value) {
      const obj = {};
      this[list].map((e) => {
        obj[e.value] = e.label || e.value;
      });
      return obj[value];
    },

    typeNode(list, value) {
      const obj = {};
      this[list].map((e) => {
        obj[e.value] = e.node;
      });
      return obj[value];
    },

    timeChange(params) {
      let dates = "";
      if (params) {
        dates = params.replace("T", " ");
      }
      return dates;
    },
    // 获取工单详情
    getInfo() {
        API.orderDetail(this.orderId).then(res => {
                this.infoList = res.data?.result
                this.commList = res.data?.result?.spTranDetail||[]
                // 赋值 出库类型
                if(res.data.result.tranType){
                    this.tranSnType.forEach(item=>{
                        if(item.value==res.data.result.tranType){
                            this.infoList.tranTypeName=item.label
                        }
                    })
                }
        })
    },
  },
};
</script>

<style scoped lang="scss">
@import "@/style/_cus_list.scss";
@import "@/style/_cus_reg.scss";

.step {
  position: relative;
  margin: 0 20px;
  padding: 12px 6px 6px;
  border: 1px solid #e8eaec;

  .stepdiv {
    width: 80%;
    margin: 0 auto;
    @extend pt;
  }
}

.cus_title {
    margin-top: 20px;
    margin-bottom: 6px;
}
</style>
