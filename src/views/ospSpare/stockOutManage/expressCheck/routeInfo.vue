<template>
    <Modal width="1200" v-model="modelEdit" draggable sticky scrollable :title="modelTitle" :footer-hide="true"
           :mask-closable="false">
        <div style="position: relative; width: 100%; height: 62vh;">
            <div style="padding-bottom: 40px; width: 100%; height: 62vh; overflow-y: auto">
                <Form :model="formItem" :label-width="100" ref="formItem" inline>
                    <FormItem label="出库单号">
                        <Input type="text" maxlength="15" v-model="formItem.sendCode" clearable
                               style="width: 260px" disabled />
                    </FormItem>
                    <FormItem label="发货供应商">
                        <Input type="text" maxlength="15" v-model="formItem.sendWarehouseName" clearable
                               style="width: 260px" disabled />
                    </FormItem>
                    <FormItem label="入库服务商">
                        <Input type="text" maxlength="15" v-model="formItem.warehouseName" clearable
                               style="width: 260px" disabled />
                    </FormItem>
                    <FormItem label="发货时间">
                        <Input type="text" maxlength="15" v-model="formItem.tranDate" clearable
                               style="width: 260px" disabled />
                    </FormItem>
                    <FormItem label="物流公司">
                        <Input type="text" maxlength="15" v-model="formItem.transportName" clearable
                               style="width: 260px" disabled />
                    </FormItem>
                    <FormItem label="物流单号">
                        <Input type="text" maxlength="15" v-model="formItem.transportCode" clearable
                               style="width: 260px" disabled />
                    </FormItem>
                </Form>
                <div style="display: flex; justify-content: center; align-items: center; padding: 20px; border: 1px solid #eee !important; border-radius: 8px !important;">
                    <Steps  direction="vertical" style="width: 88%">
                        <Step v-for="(item,index) in subTableData" :title="item.time" :key="index" :content="item.context" />
                    </Steps>
                </div>
            </div>
            <div style="position: absolute; z-index: 99; right: 20px; bottom: -30px; text-align: right; margin: 30px 0">
                <Button type="default" style="margin-left: 10px"
                        @click="modelEdit = false">取消</Button>
            </div>
        </div>
    </Modal>
</template>

<script>
import API from "@/api/ospSpare/stockOutManage/supplierStockOut";

export default {
    name: "addEdit",
    data() {
        return {
            modelEdit: false,
            modelTitle: '路由详情',
            formItem: {},
            subTableData:[]// 路由日志数据
        }
    },
    props: {
        dataSource:{
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    watch: {
        modelEdit(val) {
            if (val) {
                this.getInfo();
            }
        }
    },
    methods: {
        /*获取详情*/
        getInfo(){
            this.formItem =this.dataSource;
            let params={
                'com': this.dataSource.transportNamePinyin,
                'nu': this.dataSource.transportCode
            }
            API.ExpressRouting(params).then(res => {
                this.subTableData = res.data?.result||[]
            })
        }
    }
}
</script>

<style lang="scss" scoped>
@import 'style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
