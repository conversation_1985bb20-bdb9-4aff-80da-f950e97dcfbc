<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏备件管理</Breadcrumb-item>
            <Breadcrumb-item to="/ospSpare/">入库管理</Breadcrumb-item>
            <Breadcrumb-item>快递路由查询</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">出库单号：</p>
                    <Input v-model="searchObj.sendCode" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">订单号：</p>
                    <Input v-model="searchObj.orderCode" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">备件编码：</p>
                    <Input v-model="searchObj.materialCode" placeholder="请输入备件编码" clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">发货仓库：</p>
                    <Input v-model="searchObj.sendWarehouseName" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">入库仓库：</p>
                    <Input v-model="searchObj.warehouseName" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">出库状态：</p>
                    <Select v-model="searchObj.ifClose" placeholder="请选择..." clearable style="width: 200px">
                        <Option value="0" key="0">未出库</Option>
                        <Option value="1" key="1">已出库</Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">物流公司：</p>
                    <Input v-model="searchObj.transportName" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">物流单号：</p>
                    <Input v-model="searchObj.transportCode" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">出库时间：</p>
                    <DatePicker ref="formDateSh" :value="submintDate" @on-change="
                        (data) => {
                          return selectDate(data, 'tranDate');
                        }
                      " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择" style="width: 200px ">
                    </DatePicker>
                </div>
                <div class="condition">
                    <p class="condition_p">出库时间：</p>
                    <DatePicker ref="formDateSh" :value="submintDate" @on-change="
                        (data) => {
                          return selectDate(data, 'creatreDate');
                        }
                      " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择" style="width: 200px ">
                    </DatePicker>
                </div>
            </div>
            <div class="list_but" style="display: flex; justify-content: space-between; width: 100%;">
                <div class="condition">
                    <Button @click="routeView" type="success" :disabled="this.tableSelect.length!='1'">路由查看</Button>
                </div>
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                </div>
            </div>
            <div class="commListDiv">
                <Table size="small" border ref="tableRef" :columns="tableColumn" :data="commList" @on-selection-change="selected => this.tableSelect = selected">
                    <template slot-scope="{ index }" slot="sn">
                        {{ index + 1 }}
                    </template>
                    <template slot-scope="{ index }" slot="ifClose">
                        <span v-if="commList[index].ifClose=='0'">未出库</span>
                        <span v-if="commList[index].ifClose=='1'">已出库</span>
                        <span v-else></span>
                    </template>
                </Table>
            </div>
            <Page style="margin: 20px" @on-change="changeCurrent" :current="searchObj.page"
                  :total="totalElements" show-total show-elevator />
        </div>
        <!--        路由信息-->
        <routeInfo ref="routeInfoRef" :dataSource="tableSelect[0]"/>
    </div>
</template>

<script>
import _data from "@/views/ospSpare/_edata"; // 数据字典
import API from "@/api/ospSpare/stockOutManage/expressCheck";
import routeInfo from './routeInfo'
export default {
    name: "serviceProviderOrder",
    components: { routeInfo },
    data() {
        return {
            searchObj: {
                sendCode: "",
                orderCode: "",
                sendWarehouseName: "",
                warehouseName: "",
                materialCode: "",
                ifClose: "",
                transportName: "",
                transportCode: "",
                tranDate: "",
                creatreDate: "",
                pageNum: 1,
                pageSize: 10
            },
            formObj: {},
            staffList: [],
            totalElements: 0,
            submintDate: [],
            status: _data.status,
            orderStatusList: _data.orderStatusList,
            sourceArr: _data.SourceArr,
            faultCodeTypeList: _data.faultCodeTypeList,
            tableColumn: [
                { type: 'selection', minWidth: 60, title: '选择', align: 'center' },
                { title: '序号',  minWidth: 80, align: 'center', slot: 'sn' },
                { title: '出库单号', minWidth: 180, key: 'sendCode', align: 'center' },
                { title: '订单号', minWidth: 180, key: 'orderCode', align: 'center' },
                { title: '供应商编码', minWidth: 100, key: 'sendWarehouseCode', align: 'center' },
                { title: '供应商名称', minWidth: 100, key: 'sendWarehouseName', align: 'center' },
                { title: '中心名称', minWidth: 100, key: 'centerName', align: 'center' },
                { title: '服务商编码', minWidth: 100, key: 'warehouseCode', align: 'center' },
                { title: '服务商名称', minWidth: 100, key: 'warehouseName', align: 'center' },
                { title: '出库状态', minWidth: 100, key: 'ifClose', align: 'center',slot: 'ifClose' },
                { title: '出库时间', minWidth: 100, key: 'tranDate', align: 'center' },
                { title: '备件编码', minWidth: 180, key: 'materialCode', align: 'center' },
                { title: '备件描述', minWidth: 160, key: 'materialDesc', align: 'center' },
                { title: '应发货数量', minWidth: 160, key: 'amountArg', align: 'center' },
                { title: '实际发货数量', minWidth: 160, key: 'amount', align: 'center' },
                { title: '物流公司', minWidth: 160, key: 'transportName', align: 'center' },
                { title: '物流单号', minWidth: 180, key: 'transportCode', align: 'center' },
                { title: '创建时间', minWidth: 180, key: 'creatreDate', align: 'center' },
            ],
            commList: [],
            tableSelect:[],
            title: "一键派工",
            modelEdit: false,
            modelTitle: '新增',
            formItem: {
                aaa: '',
                bbb: ''
            },
            modelEditAdd: false
        }
    },
    mounted() {
        this.getList()
    },
    methods: {
        // 路由查看
        routeView() {
            this.$refs.routeInfoRef.modelEdit = true
        },
        // 查询按钮
        queryList() {
            this.searchObj.pageNum = 1;
            this.getList();
        },

        // 重置按钮
        emptyList() {
            this.searchObj = {
                submintDateRange: "",
                orderCode: "",
                woId: "",
                orderType: "",
                orderStatus: "",
                branchName: "",
                pageNum: 1,
                pageSize: 10
            };
            this.submintDate = [];
        },
        // 选择日期
        selectDate(date, refObj) {
            this.submintDate = date;
            this.searchObj[refObj + "Start"] = date[0];
            this.searchObj[refObj + "End"] = date[1];
        },
        // 获取工单列表
        getList() {
            this.$Message.loading({
                content: "查询中...",
                duration: 0,
            });
            let datas = this.searchObj;
            datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
            delete datas.pageNum
            delete datas.pageSize
            const page = { page: this.searchObj.pageNum, rows: this.searchObj.pageSize }
            // 将所有选中项清空
            this.$refs.tableRef.selectAll(false);
            API.getList(page, datas).then(res => {
                if(res.data.success){
                    this.$Message.destroy();
                    this.commList = res.data.result.content
                    this.totalElements = res.data.result.totalElements
                }else{
                    this.commList=[]
                }
            }).catch(err=>{
                this.commList=[]
            })
            /* TODO 暂时没有接口 */
            // setTimeout(() => {
            //     this.$Message.destroy();
            //     this.commList = [
            //         {
            //             orderCode: '312123',
            //             id: '1',
            //             orderType: '类型',
            //             orderStatus: '状态',
            //             centerName: '中心',
            //             branchName: '服务商',
            //             suptBranchName: '仓库',
            //             submitDate: '申请',
            //             sendDate: '发货',
            //             sendCode: '出库',
            //             cancelDate: '取消',
            //             cancelBy: '人',
            //             cancelDesc: '原因',
            //             updateDate: '修改'
            //         }
            //     ]
            //     this.totalElements = 99
            // }, 1000)
        },
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.pageNum = curr;
            this.getList();
        }
    }
}
</script>

<style lang="scss" scoped>
@import 'style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
