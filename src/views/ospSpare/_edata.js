/*
 * @Author: lufy
 * @FilePath: /src/views/ospSpare/_edata.js
 * @Description: apv 数据字典
 * @remark: 历史优化原因，新增字典全部按对象 Object 类型处理。
 */

export default {
    /**
     * @description: 订单类型
     */
    orderType: [
        {value: "NCSO", label: "维修订单"},
        {value: "NA", label: "补库订单"},
        {value: "NAO", label: "调拨订单"},
        {value: "NORMAL", label: "差额补库订单"},
        {value: "borrow", label: "借件订单"}
    ],
    /**
     * @description: 订单状态
     */
    orderStatus: [
        {value: "init", label: "初始"},
        {value: "WAIT", label: "待货"},
        {value: "send", label: "待出库"},
        {value: "CLOSE", label: "已发货"},
        {value: "FCLOSE", label: "取消"},
        {value: "submit", label: "调拨提交"},
        {value: "back", label: "调拨撤回"},
        {value: "confirm", label: "调拨确认"}
    ],
    /**
     * @description: 出入库单类型-服务商
     */
    tranSnType: [
        {value: "1", label: "入库"},
        {value: "2", label: "出库"},
        {value: "3", label: "新件入库"},
        {value: "4", label: "旧件入库"},
        {value: "5", label: "调拨入库"},
        {value: "6", label: "借件入库"},
        {value: "7", label: "调整入库"},
        {value: "8", label: "调整出库"},
        {value: "9", label: "性能故障入库"}
    ],
    /**
     * @description: 出入库单类型-仓库
     */
    tranType: [
        {value: "1", label: "入库"},
        {value: "2", label: "出库"},
        {value: "3", label: "调正"},
        {value: "4", label: "调负"}
    ],
    /**
     * @description: 对账单类型
     */
    operType: [
        {value: "1", label: "财务加款"},
        {value: "2", label: "订单扣款"},
        {value: "3", label: "取消订单回款"},
        {value: "4", label: "旧件销账回款"},
        {value: "5", label: "退款扣款"}
    ],
    /**
     * @description: 旧件回退类型
     */
    backType: [
        {value: "1", label: "网点旧件"},
        {value: "5", label: "网点货损件"},
        {value: "7", label: "网点报废"},
        {value: "9", label: "网点无旧件返回"},
    ],
    /**
     * @description: 旧件回退状态
     */
    backStatus: [
        {value: "0", label: "初始"},
        {value: "2", label: "确认退返"},
        {value: "3", label: "厂家签收"},
        {value: "4", label: "厂家拒收"}
    ],
    /**
     * @description: 仓库等级
     */
    stockLevel: [
        {value: "1", label: "一级"},
        {value: "2", label: "二级"},
        {value: "3", label: "三级"}
    ],
    /**
     * @description: 货差分类
     */
    faultType: [
        {value: "0", label: "货损"},
        {value: "1", label: "错件"},
        {value: "2", label: "少件"},
    ],
    /**
     * @description: 责任分类
     */
    faultClassify: [
        {value: "0", label: "发货方"},
        {value: "1", label: "物流"},
    ],
    whetherToReissue: [
        {value: "0", label: "否"},
        {value: "1", label: "是"},
    ],
    /**
     * @description: 生产状态
     */
    productStatus: [
        {value: "0", label: "停产"},
        {value: "1", label: "生产"}
    ],
    /**
     * @description: 货损审批状态
     */
    shAuditStatus:[
        {value: "0", label: "已提交"},
        {value: "1", label: "通过"},
        {value: "2", label: "驳回"},
        {value: "3", label: "撤销"},
        {value: "4", label: "申诉中"},
    ],
    statusList: [
        { label: '初始', value: '0' },
        { label: '待中心经理审批', value: '1' },
        { label: '中心经理驳回', value: '2' },
        { label: '待总部审批', value: '3' },
        { label: '总部审批通过', value: '4' },
        { label: '总部驳回', value: '5' },
        { label: '待运维商认责', value: '6' },
        { label: '运维商认责', value: '7' },
        { label: '运维商不认责', value: '8' },
        { label: '待供应商认责', value: '9' },
        { label: '供应商认责', value: 'a' },
        { label: '供应商不认责', value: 'b' },
        { label: '建站商通过', value: 'c' },
        { label: '建站商驳回', value: 'd' },
        { label: '建站商发货', value: 'e' },
        { label: '运维商入库', value: 'f' },
        { label: '订单关闭', value: 'g' },
        { label: '取消借件', value: 'h' },
        { label: '提交建站借件成功', value: 'i' },
        { label: '提交建站借件失败', value: 'j' }
    ],
    dutyType:[
        {value: "1", label: "运维商责任"},
        {value: "2", label: "供应商责任"},
        {value: "3", label: "其他责任"},
    ],
    accountingStatus:[
        {value: "0", label: "成功"},
        {value: "1", label: "失败"},
    ],
    stationAuditStatus:[
        {value: "0", label: "同意"},
        {value: "1", label: "不同意"},
    ],
    orderSource:[
        {value: "0", label: "普通"},
        {value: "1", label: "特殊订单"},
    ]
};
