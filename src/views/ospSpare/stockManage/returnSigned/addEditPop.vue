<template>
    <Modal width="1200" v-model="modelEdit" draggable sticky scrollable :title="modelTitle" :footer-hide="true"
           :mask-closable="false">
        <div style="position: relative; width: 100%; height: 60vh;">
            <div style="padding-bottom: 40px; width: 100%; height: 60vh; overflow-y: auto">
                <Form :model="formItem" :label-width="100" ref="formItem" inline>
                    <FormItem label="退返单号">
                        <Input type="text" maxlength="15" v-model="formItem.backCode" clearable
                               style="width: 360px" disabled />
                    </FormItem>
                    <FormItem label="工单号">
                        <Input type="text" maxlength="15" v-model="formItem.csoId" clearable
                               style="width: 360px" />
                    </FormItem>
                    <FormItem label="服务商">
                        <Input type="text" maxlength="15" v-model="formItem.warehosueName" clearable
                               style="width: 360px" disabled/>
                    </FormItem>
                    <FormItem label="签收人">
                        <Input type="text" maxlength="15" v-model="formItem.updateBy" clearable
                               style="width: 360px" disabled/>
                    </FormItem>
                </Form>
<!--                <div class="list_but" style="margin-bottom: 10px">-->
<!--                    <div class="condition">-->
<!--                        <Button @click="modelEditAdd = true" size="small" type="primary">添加</Button>-->
<!--                        <Button style="margin-left: 6px" @click="del" size="small" type="error" :disabled="!subTableSelect.length">删除</Button>-->
<!--                    </div>-->
<!--                </div>-->
                <div class="commListDiv">
                    <Table size="small" border ref="subTableRef" :columns="subTableColumn" :data="subTableData" @on-selection-change="selected => this.subTableSelect = selected">
                        <template slot-scope="{ index }" slot="actBackAmnt">
                            <InputNumber :min="0" v-model="subTableData[index].actBackAmnt" style="text-align: center" @on-change="tableInputChange" />
                        </template>
                    </Table>
                </div>
            </div>
            <div style="position: absolute; z-index: 99; right: 20px; bottom: -30px; text-align: right; margin: 30px 0">
                <Button type="primary"
                        :disabled="!subTableSelect.length"
                        @click="debounce(submitPop)">保存</Button>
                <Button type="default" style="margin-left: 10px"
                        @click="modelEdit = false">取消</Button>
            </div>
        </div>
    </Modal>
</template>

<script>
import API from "@/api/ospSpare/stockOutManage/returnSigned";

export default {
    name: "addEdit",
    data() {
        return {
            modelEdit: false,
            modelTitle: '签收',
            formItem: {
                backCode: '',
                csoId: '',
                warehosueName: '',
                updateBy: ''
            },
            subTableSelect: [],
            subTableData: [],
            subTableColumn: [
                {
                    title: '退返单号',
                    key: 'backCode',
                    align: 'center'
                },
                {
                    title: '备件编码',
                    key: 'materialCode',
                    align: 'center'
                },
                {
                    title: '备件描述',
                    key: 'materialDesc',
                    align: 'center'
                },
                {
                    title: '单位',
                    key: 'materialUnit',
                    align: 'center'
                },
                {
                    title: '单价',
                    key: 'materialPrice',
                    align: 'center'
                },
                {
                    title: '退返数量',
                    key: 'backAmnt',
                    align: 'center'
                },
                {
                    title: '签收数量',
                    key: 'actBackAmnt',
                    slot: 'actBackAmnt',
                    align: 'center'
                }
            ],
            dataSource:{}, // 列表带入数据
        }
    },
    watch: {
        modelEdit(val) {
            if (val) {
                this.getInfo()
            }
        }
    },
    methods: {
        tableInputChange(e) {
            this.$refs.subTableRef.selectAll(false);
            console.log(e)
        },
        submitPop() {
            console.log(this.subTableSelect)
        },
        /*获取详情*/
        getInfo(){
            let params={'id':this.dataSource.id}
            API.orderDetail(params).then(res => {
                if(res.data.success){
                    this.formItem=res.data.result
                    this.subTableData = res.data?.result?.orderDetailList||[]
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped>
@import 'style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
