<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏备件管理</Breadcrumb-item>
            <Breadcrumb-item to="/ospSpare/">入库管理</Breadcrumb-item>
            <Breadcrumb-item>退返件接收</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">退返单号：</p>
                    <Input v-model="searchObj.backCode" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">退返状态：</p>
                    <Select v-model="searchObj.backStatus" placeholder="请选择..." clearable style="width: 200px">
                        <Option v-for="item in backStatusList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">退返类型：</p>
                    <Select v-model="searchObj.backType" placeholder="请选择..." clearable style="width: 200px">
                        <Option v-for="item in backTypeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">工单编号：</p>
                    <Input v-model="searchObj.csoId" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">服务商名称：</p>
                    <Input v-model="searchObj.warehosueName" placeholder="请输入..." clearable style="width: 200px" />
                </div>
<!--                <div class="condition">-->
<!--                    <p class="condition_p">接收仓库：</p>-->
<!--                    <Input v-model="searchObj.getWarehouseName" placeholder="请输入..." clearable style="width: 200px" />-->
<!--                </div>-->
                <div class="condition">
                    <p class="condition_p">创建时间：</p>
                    <DatePicker ref="formDateSh" :value="submintDate" @on-change="
                        (data) => { return selectDate(data, 'createDate'); }
                      " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择" style="width: 200px ">
                    </DatePicker>
                </div>
                <div class="condition">
                    <p class="condition_p">确认时间：</p>
                    <DatePicker ref="formDateSh" :value="submintDate" @on-change="
                        (data) => { return selectDate(data, 'getDate'); }
                      " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择" style="width: 200px ">
                    </DatePicker>
                </div>
            </div>

            <div class="list_but" style="display: flex; justify-content: space-between; width: 100%;">
                <div class="condition">
                    <Button @click="signIn" type="success" :disabled="isDisabledSign">维保</Button>
                    <Button @click="rejectOldback" type="success" :disabled="isDisabledSign">退回</Button>
                    <Button @click="exportFn" type="primary">导出</Button>
                </div>
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                </div>
            </div>
            <div class="commListDiv">
                <Table size="small" border ref="tableRef" :columns="tableColumn" :data="commList" @on-selection-change="selected => this.tableSelect = selected">
                    <template slot-scope="{ index }" slot="sn">
                        {{ index + 1 }}
                    </template>
                    <!--                    退返单类型-->
                    <template slot-scope="{ index }" slot="backType">
                        {{ backTypeMap[commList[index].backType] }}
                    </template>
                    <!--                    退返单状态-->
                    <template slot-scope="{ index }" slot="backStatus">
                        {{ backStatusMap[commList[index].backStatus] }}
                    </template>
                    <!--                    附件查看-->
<!--                    <template slot-scope="{ index }" slot="fileList">-->
<!--                        <Button v-if="commList[index].fileList && commList[index].fileList.length>0" @click="handleView(commList[index].fileList)" ghost type="info" size="small">附件查看</Button>-->
<!--                    </template>-->
                    <template slot-scope="{ index }" slot="option">
                        <Button type="info" ghost size="small" @click="openDetail(commList[index])" style="margin: 3px">详情</Button>
                    </template>
                </Table>
            </div>
            <Page style="margin: 20px" @on-change="changeCurrent" :current="searchObj.page"
                  :total="totalElements" show-total show-elevator />
        </div>
<!--        签收弹窗-->
        <addEditPop ref="addEditPopRef" />
<!--        详情弹窗-->
        <detail ref="detailsRef" />
        <!--        附件查看-->
<!--        <Modal v-model="visible" width="800px" reset-drag-position draggable sticky title="预览" footer-hide class-name="vertical-center-modal">-->
<!--            <img class="preview" v-if="visibleImg" width="700" :src="visibleImg" @click="toTarget(visibleImg)" title="点击查看原图">-->
<!--        </Modal>-->
    </div>
</template>

<script>
import _data from "@/views/ospSpare/_edata"; // 数据字典
import API from "@/api/ospSpare/stockOutManage/returnSigned";
import addEditPop from "./addEditPop";
import detail from './details'
export default {
    name: "serviceProviderOrder",
    components: { addEditPop, detail },
    data() {
        return {
            searchObj: {
                submintDateRange: "",
                orderCode: "",
                woId: "",
                orderType: "",
                orderStatus: "",
                branchName: "",
                pageNum: 1,
                pageSize: 10
            },
            formObj: {},
            staffList: [],
            totalElements: 0,
            submintDate: [],
            backStatusList: _data.backStatus,//退返单状态
            backStatusMap: [],
            backTypeList: _data.backType,//退返单类型
            backTypeMap: [],
            tableColumn: [
                { type: 'selection', minWidth: 60, title: '选择', align: 'center' },
                { title: '序号',  minWidth: 80, align: 'center', slot: 'sn' },
                { title: '退返单号', minWidth: 180, key: 'backCode', align: 'center' },
                { title: '中心名称', minWidth: 100, key: 'gmName', align: 'center' },
                { title: '退返仓库', minWidth: 180, key: 'warehouseName', align: 'center' },
                { title: '退返仓库编码', minWidth: 180, key: 'warehouseCode', align: 'center' },
                { title: '退返单类型', minWidth: 100, key: 'backType', align: 'center',slot:'backType' },
                { title: '退返单状态', minWidth: 100, key: 'backStatus', align: 'center' ,slot:'backStatus'},
                { title: '创建日期', minWidth: 160, key: 'createDate', align: 'center' },
                { title: '提交时间', minWidth: 160, key: 'backDate', align: 'center' },
                { title: '签收日期', minWidth: 180, key: 'getDate', align: 'center' },
                { title: '签收供应商', minWidth: 120, key: 'getWarehosueName', align: 'center' },
                { title: '签收人', minWidth: 120, key: 'updateBy', align: 'center' },
                // { title: '附件', minWidth: 100, key: 'fileList', align: 'center',slot:'fileList' },
                { title: '操作', minWidth: 120, align: 'center', fixed: 'right', slot: 'option' }
            ],
            commList: [],
            tableSelect:[],//表格选择数据
            modelEdit: false,
            modelEditAdd: false,
            visible: false,
            visibleImg: '',
        }
    },
    mounted() {
        this.getList()
        this.toolsDict()
    },
    computed: {
        isDisabledSign(){
            if(this.tableSelect.length===0) return true;
            let statusDis = this.tableSelect.find(item=> item.backStatus!=='2')
            if(statusDis) return true;
            return false;
        }
    },
    methods: {
        toolsDict(){
            //退返状态
            _data.backStatus.forEach(item=>{
                this.backStatusMap[item.value]=item.label
            })
            //退返类型
            _data.backType.forEach(item=>{
                this.backTypeMap[item.value]=item.label
            })
        },
        // 签收
        // signIn() {
        //     this.$refs.addEditPopRef.dataSource = this.tableSelect[0]
        //     this.$refs.addEditPopRef.modelEdit = true
        // },
        rejectOldback(){
            this.$message.confirm('是否确认签收回退单?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                let ids = this.tableSelect.map(item=>{return item.id})
                let params={ids}
                this.$Message.loading({
                    content: "拒收提交中...",
                    duration: 0,
                });
                API.rejectOldback(params).then(res => {
                    if (res.data.success) {
                        this.$Message.destroy();
                        this.$Message.success(res.data.result);
                        setTimeout(() => {
                            this.getList()
                        }, 2000);
                    } else {
                        this.$Message.destroy();
                        this.$Message.error(res.data.error);
                    }
                })
            })
        },
        signIn() {
            this.$message.confirm('是否确认签收回退单?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                let ids = this.tableSelect.map(item=>{return item.id})
                let params={ids}
                this.$Message.loading({
                    content: "签收确认中...",
                    duration: 0,
                });
                API.signIn(params).then(res => {
                    if (res.data.success) {
                        this.$Message.destroy();
                        this.$Message.success(res.data.result);
                        setTimeout(() => {
                            this.getList()
                        }, 2000);
                    } else {
                        this.$Message.destroy();
                        this.$Message.error(res.data.error);
                    }
                })
            })
        },
        // 导出
        exportFn() {
            let datas = this.searchObj;
            let { pageNum, pageSize, ...datasEx } = { ...datas }
            API.exportList(datasEx).then(res => {
                let binaryData = [];
                let link = document.createElement('a');
                binaryData.push(res.data);
                link.style.display = 'none';
                link.href = window.URL.createObjectURL(new Blob(binaryData));
                link.setAttribute('download', '退返件签收.xlsx');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }).catch(() => {
                this.$Message.error('导出失败');
            });
            this.$Message.success('导出');
        },
        // 打开查看附件弹框
        handleView(fileList){
            this.visible = true;
            this.visibleImg = fileList[0].fileUrl;
        },
        toTarget(url) {
            window.open(url)
        },
        // 打开详情
        openDetail(row) {
            this.$refs.detailsRef.orderId = row.id
            this.$refs.detailsRef.show = true
        },
        // 查询按钮
        queryList() {
            this.searchObj.pageNum = 1;
            this.getList();
        },

        // 重置按钮
        emptyList() {
            this.searchObj = {
                submintDateRange: "",
                orderCode: "",
                woId: "",
                orderType: "",
                orderStatus: "",
                branchName: "",
                pageNum: 1,
                pageSize: 10
            };
            this.submintDate = [];
        },
        // 选择日期
        selectDate(date, refObj) {
            this.submintDate = date;
            this.searchObj[refObj + "Start"] = date[0];
            this.searchObj[refObj + "End"] = date[1];
        },
        // 获取工单列表
        getList() {
            this.$Message.loading({
                content: "查询中...",
                duration: 0,
            });
            let datas = this.searchObj;
            datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
            delete datas.pageNum
            delete datas.pageSize
            const page = { page: this.searchObj.pageNum, rows: this.searchObj.pageSize }
            // 将所有选中项清空
            this.$refs.tableRef.selectAll(false);
            datas.isBack ='1'// 写死数据
            API.getList(page, datas).then(res => {
                if(res.data.success){
                    this.$Message.destroy();
                    this.commList = res.data.result.content
                    this.totalElements = res.data.result.totalElements
                }else{
                    this.commList=[]
                }
            }).catch(err=>{
                this.commList=[]
            })
        },
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.pageNum = curr;
            this.getList();
        },
        jumpto(path, id, count) {
            this.$router.push({
                path,
                query: { id, count },
            });
        }
    }
}
</script>

<style lang="scss" scoped>
@import 'style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
