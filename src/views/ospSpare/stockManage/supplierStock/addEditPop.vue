<template>
    <Modal width="1200" v-model="modelEdit" draggable sticky scrollable :title="modelTitle" :footer-hide="true"
           :mask-closable="false" :loading="loading">
        <div style="position: relative; width: 100%; height: 60vh;">
            <div style="padding-bottom: 40px; width: 100%; height: 60vh; overflow-y: auto">
                <Form ref="orderData" :model="orderData" :label-width="100" inline>
                    <FormItem label="入库单号" prop="tranCode">
                        <Input type="text" maxlength="15" v-model="orderData.tranCode" clearable placeholder="入库单号"
                               style="width: 360px" disabled />
                    </FormItem>
                    <FormItem label="采购订单号" prop="orderCode">
                        <Input type="text" maxlength="15" v-model="orderData.orderCode" clearable placeholder="采购订单号"
                               style="width: 360px" disabled />
                    </FormItem>
                    <FormItem label="入库仓库" prop="warehosueName">
                        <Input type="text" maxlength="15" v-model="orderData.warehosueName" clearable placeholder="入库仓库"
                               style="width: 360px" disabled />
                    </FormItem>
                    <FormItem label="操作人" prop="updateBy">
                        <Input type="text" maxlength="15" v-model="orderData.updateBy" clearable placeholder="操作人"
                               style="width: 360px" disabled />
                    </FormItem>
                </Form>
<!--                <div class="list_but" style="margin-bottom: 10px">-->
<!--                    <div class="condition">-->
<!--                        <Button @click="modelEditAdd = true" size="small" type="primary">添加</Button>-->
<!--                        <Button style="margin-left: 6px" @click="del" size="small" type="error" :disabled="!subTableSelect.length">删除</Button>-->
<!--                    </div>-->
<!--                </div>-->
                <div class="commListDiv">
                    <Table size="small" border ref="subTableRef" :columns="subTableColumn" :data="subTableData" @on-selection-change="selected => this.subTableSelect = selected">
                        <template slot-scope="{ index }" slot="amount">
<!--                            {{ subTableData[index].amount }}-->

                            <InputNumber :min="1" :max="subTableData[index].amountOrg * 1" v-model="subTableData[index].amount" style="text-align: center" />
                        </template>
                    </Table>
                </div>
            </div>
            <div style="position: absolute; z-index: 99; right: 20px; bottom: -30px; text-align: right; margin: 30px 0">
                <Button type="primary"
                        :loading="loading"
                        @click="debounce(submitPop)">保存</Button>
                <Button type="default" style="margin-left: 10px"
                        @click="modelEdit = false">取消</Button>
            </div>
        </div>
    </Modal>
</template>

<script>
import API from "@/api/ospSpare/stockManage/supplierStock";
export default {
    name: "addEdit",
    props: {
      rowData: {
          type: Object,
          default: () => {}
      }
    },
    data() {
        return {
            modelEdit: false,
            modelTitle: '编辑',
            loading: false,
            orderData: {
                tranCode: '',
                orderCode: '',
                warehosueName: '',
                updateBy: ''
            },
            subTableSelect: [],
            subTableData: [
            ],
            subTableColumn: [
                // {
                //     type: 'selection',
                //     width: 60,
                //     title: '选择',
                //     align: 'center'
                // },
                {
                    title: '入库单号',
                    key: 'tranCode',
                    align: 'center'
                },
                {
                    title: '备件编码',
                    key: 'materialCode',
                    align: 'center'
                },
                {
                    title: '备件描述',
                    key: 'materialDesc',
                    align: 'center'
                },
                {
                    title: '单位',
                    key: 'materialUnit',
                    align: 'center'
                },
                {
                    title: '单价',
                    key: 'settlePrice',
                    align: 'center'
                },
                {
                    title: '应入库数量',
                    key: 'amountOrg',
                    align: 'center'
                },
                {
                    title: '入库数量',
                    key: 'amount',
                    slot: 'amount',
                    align: 'center'
                }
            ]
        }
    },
    methods: {
        // 获取工单详情
        getInfo() {
            this.loading = true
            API.getInfo({ id: this.rowData.id }).then(res => {
                this.loading = false
                const resData = res.data
                if (resData.success) {
                    this.orderData = resData.result || {}
                    const list = this.orderData.spTranDetail || []
                    this.subTableData = list.map(item => {
                        item.tranCode = this.orderData.tranCode
                        return item
                    })
                } else {
                    this.orderData = {}
                    this.subTableData = []
                    this.$Message.error(resData.error || resData.message || '获取数据失败');
                }
            }).catch(e => {
                this.loading = false
                this.$Message.error("获取服务失败，请重试")
                this.orderData = {}
                this.subTableData = []
            })
        },
        tableInputChange(e) {
            this.$refs.subTableRef.selectAll(false);
        },
        submitPop() {
            if (this.subTableData.some(item => !item.amount)) {
                this.$Message.error("入库数量不能为空或者0")
                return
            }
            if (this.subTableData.some(item => item.amount < 1)) {
                this.$Message.error("入库数量不能小于1")
                return
            }
            if (this.subTableData.some(item => item.amount > item.amountOrg)) {
                this.$Message.error("入库数量不能大于应入库数量")
                return
            }
            const params = this.orderData
            params['spTranDetail'] = this.subTableData
            this.$Message.loading({
                content: "提交中...",
                duration: 0,
            });
            this.loading = true
            API.update(params).then(res => {
                this.$Message.destroy()
                this.loading = false
                if (res.data.success) {
                    this.modelEdit = false
                    this.$emit("handleSuccess")
                } else {
                    this.$Message.error(res.data.error || res.data.message || '操作失败');
                }
            }).catch(e => {
                this.loading = false
                console.log(e)
                this.$Message.destroy()
                this.$Message.error("获取服务失败，请重试");
            })
        },
    }
}
</script>

<style lang="scss" scoped>
@import 'style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
