<template>
    <Modal width="80%" v-model="show" title="详情" :footer-hide="true" :styles="{ top: '5vh' }"
           :mask-closable="false" :loading="loading">
        <div style="overflow-y: auto; width: 100%; height: 80vh;">
            <div class="cus_list" style="min-height: calc(80vh - 72px)">
                <!--                    <div class="list_son">-->
                <!--                        <div class="step">-->
                <!--                            <Steps class="stepdiv" :current="typeNode('orderStatusList', infoList.status)">-->
                <!--                                <Step v-for="(item, index) in infoList.processLogs" :key="index" :content="timeChange(item.createdAt)"-->
                <!--                                      :title="item.desc"></Step>-->
                <!--                            </Steps>-->
                <!--                        </div>-->
                <!--                    </div>-->
                <div class="cus_reg">
                    <div class="cus_form" style="padding-top: 0">
                        <p class="cus_title">主单信息</p>
                        <el-descriptions class="descriptions-list margin-top" :column="3" border>
                            <el-descriptions-item>
                                <template slot="label">
                                    入库单号
                                </template>
                                {{ orderData.tranCode }}
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template slot="label">
                                    采购订单号
                                </template>
                                {{ orderData.orderCode }}
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template slot="label">
                                    入库仓库
                                </template>
                                {{ orderData.warehosueName }}
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template slot="label">
                                    订单状态
                                </template>
                                {{ enums.ifClose[orderData.ifClose] }}
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template slot="label">
                                    入库类型
                                </template>
                                {{ enums.tranType[orderData.tranType] }}
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template slot="label">
                                    创建时间
                                </template>
                                {{ orderData.createDate }}
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template slot="label">
                                    创建人
                                </template>
                                {{ orderData.createBy }}
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template slot="label">
                                    操作人
                                </template>
                                {{ orderData.updateBy }}
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template slot="label">
                                    操作时间
                                </template>
                                {{ orderData.updateDate }}
                            </el-descriptions-item>
                        </el-descriptions>
                        <p class="cus_title">明细信息</p>
                        <div class="commListDiv">
                            <Table size="small" border ref="subTableRef" :columns="subTableColumn" :data="subTableData">
                                <template slot-scope="{ index }" slot="sn">
                                    {{ index + 1 }}
                                </template>
                            </Table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </Modal>
</template>

<script>
import _data from "@/views/osp/_edata"; // 数据字典
import API from "@/api/ospSpare/stockManage/supplierStock";

export default {
    props: {
        rowData: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            loading: false,
            show: false,
            infoList: {},
            orderStatusList: _data.orderStatusList,
            sourceArr: _data.SourceArr,
            count: '',
            orderData: {},
            enums: {
                ifClose: {
                    '0': '未关闭',
                    '1': '已关闭',
                    'a': '提交审批',
                    'b': '转库存关单失败',
                },
                tranType: {
                    '1': '入库',
                    '2': '出库',
                }
            },
            subTableData: [
            ],
            subTableColumn: [
                { title: '序号', minWidth: 80, align: 'center', slot: 'sn' },
                {
                    title: '入库单号',
                    key: 'tranCode',
                    align: 'center'
                },
                {
                    title: '备件编码',
                    key: 'materialCode',
                    align: 'center'
                },
                {
                    title: '备件描述',
                    key: 'materialDesc',
                    align: 'center'
                },
                {
                    title: '单位',
                    key: 'materialUnit',
                    align: 'center'
                },
                {
                    title: '单价',
                    key: 'settlePrice',
                    align: 'center'
                },
                {
                    title: '备件唯一码',
                    key: 'batchNum',
                    align: 'center'
                },
                {
                    title: '应入库数量',
                    key: 'amountOrg',
                    align: 'center'
                },
                {
                    title: '入库数量',
                    key: 'amount',
                    align: 'center'
                }
            ]
        };
    },
    mounted() {
    },
    methods: {
        openImg(url) {
            window.open(url);
        },

        typeVal(list, value) {
            const obj = {};
            this[list].map((e) => {
                obj[e.value] = e.label || e.value;
            });
            // console.log(obj[value]);
            return obj[value];
        },

        typeNode(list, value) {
            const obj = {};
            this[list].map((e) => {
                obj[e.value] = e.node;
            });
            return obj[value];
        },

        timeChange(params) {
            // console.log(params);
            let dates = "";
            if (params) {
                dates = params.replace("T", " ");
            }
            return dates;
        },
        // 获取工单详情
        getInfo() {
            this.loading = true
            API.getInfo({ id: this.rowData.id }).then(res => {
                this.loading = false
                const resData = res.data
                if (resData.success) {
                    this.orderData = resData.result || {}
                    const list = this.orderData.spTranDetail || []
                    this.subTableData = list.map(item => {
                        item.tranCode = this.orderData.tranCode
                        return item
                    })
                } else {
                    this.orderData = {}
                    this.subTableData = []
                    this.$Message.error(resData.error || resData.message || '获取数据失败');
                }
            }).catch(e => {
                this.loading = false
                this.$Message.error("获取服务失败，请重试")
                this.orderData = {}
                this.subTableData = []
            })
        },
    },
};
</script>

<style scoped lang="scss">
@import "@/style/_cus_list.scss";
@import "@/style/_cus_reg.scss";

.step {
    position: relative;
    margin: 0 20px;
    padding: 12px 6px 6px;
    border: 1px solid #e8eaec;

    .stepdiv {
        width: 80%;
        margin: 0 auto;
        @extend pt;
    }
}

.cus_title {
    margin-top: 20px;
    margin-bottom: 6px;
}
::v-deep .el-descriptions .is-bordered .el-descriptions-item__cell {
    width: 16.6%;
}
</style>
