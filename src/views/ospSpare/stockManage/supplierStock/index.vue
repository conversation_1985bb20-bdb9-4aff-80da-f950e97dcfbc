<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏备件管理</Breadcrumb-item>
            <Breadcrumb-item to="/ospSpare/">入库管理</Breadcrumb-item>
            <Breadcrumb-item>供应商入库</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">入库号：</p>
                    <Input v-model="searchObj.tranCode" placeholder="请输入入库号" clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">采购订单号：</p>
                    <Input v-model="searchObj.purchaseCode" placeholder="请输入采购订单号" clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">入库状态：</p>
                    <Select v-model="searchObj.ifClose" placeholder="请选择入库状态" clearable style="width: 200px">
                        <Option v-for="item in ifCloseOps" :key="item.value" :value="item.value">{{ item.label }}</Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">入库时间：</p>
                    <DatePicker ref="formDateSh" :value="createDate" @on-change="
                        (data) => {
                          return selectDate(data, 'createDate');
                        }
                      " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择" style="width: 240px ">
                    </DatePicker>
                </div>
            </div>
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">备件编码：</p>
                    <Input v-model="searchObj.materialCode" placeholder="请输入备件编码" clearable style="width: 200px" />
                </div>
            </div>
            <div class="list_but" style="display: flex; justify-content: space-between; width: 100%;">
                <div class="condition">
                    <Button @click="edit" type="primary" :disabled="!isEdit">编辑</Button>
                    <Button @click="closeOrderStock" type="primary"  :disabled="!isEdit">关单入库</Button>
                    <Button @click="exportFn" type="error">导出</Button>
                </div>
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                </div>
            </div>
            <div class="commListDiv">
                <Table size="small" border ref="tableRef" :columns="tableColumn" :data="commList" @on-selection-change="selected => this.tableSelect = selected">
                    <template slot-scope="{ index }" slot="sn">
                        {{ index + 1 }}
                    </template>
                    <template slot-scope="{ row }" slot="ifClose">
                        {{ enums.ifClose[row.ifClose + ''] }}
                    </template>
                    <template slot-scope="{ row }" slot="tranType">
                        {{ enums.tranType[row.tranType + ''] }}
                    </template>
                    <template slot-scope="{ index }" slot="option">
                        <Button type="info" ghost size="small" @click="openDetail(commList[index])" style="margin: 3px">详情</Button>
                    </template>
                </Table>
            </div>
            <Page style="margin: 20px" @on-change="changeCurrent" :current="searchObj.page"
                  :total="totalElements" show-total show-elevator />
        </div>

        <addEditPop ref="addEditPopRef" :row-data="selectData" @handleSuccess="handleSuccess()" />
        <detail ref="detailsRef" :row-data="detailData" />
    </div>
</template>

<script>
import _data from "@/views/osp/_edata"; // 数据字典
import API from "@/api/ospSpare/stockManage/supplierStock.js";
import addEditPop from "./addEditPop";
import detail from './details'
import _ from 'lodash'
export default {
    name: "serviceProviderOrder",
    components: { addEditPop, detail },
    data() {
        return {
            searchObj: {
                tranCode: '',
                purchaseCode: '',
                ifClose: '',
                createDateStart: '',
                createDateEnd: '',
                materialCode: '',
                pageNum: 1,
                pageSize: 10
            },
            formObj: {},
            staffList: [],
            totalElements: 0,
            createDate: [],
            tranDate: [],
            ifCloseOps: _data.ifClose,
            commList: [],
            tableColumn: [
                { type: 'selection', minWidth: 60, title: '选择', align: 'center' },
                { title: '序号', minWidth: 80, align: 'center', slot: 'sn' },
                { title: '仓库名称', minWidth: 180, key: 'warehosueName', align: 'center' },
                { title: '入库单号', minWidth: 180, key: 'tranCode', align: 'center' },
                { title: '采购订单号', minWidth: 180, key: 'purchaseCode', align: 'center' },
                { title: '入库类型', minWidth: 180, slot: 'tranType', align: 'center' },
                { title: '入库状态', minWidth: 180, slot: 'ifClose', align: 'center' },
                { title: '创建人', minWidth: 180, key: 'createBy', align: 'center' },
                { title: '创建日期', minWidth: 180, key: 'createDate', align: 'center' },
                { title: '入库操作人', minWidth: 180, key: 'updateBy', align: 'center' },
                { title: '入库日期', minWidth: 180, key: 'tranDate', align: 'center' },
                { title: '操作', minWidth: 120, align: 'center', fixed: 'right', slot: 'option' }
            ],
            tableSelect: [],
            enums: {
                ifClose: {
                    '0': '未关闭',
                    '1': '已关闭',
                    'a': '提交审批',
                    'b': '转库存关单失败',
                },
                tranType: {
                    '1': '入库',
                    '2': '出库',
                }
            },
            detailData: {}
        }
    },
    computed: {
      isEdit () {
          return this.tableSelect.length === 1 && this.tableSelect[0].ifClose == '0'
      },
      selectData () {
          return this.tableSelect.length > 0 ? this.tableSelect[0] : {}
      }
    },
    mounted() {
        this.getList()
    },
    methods: {
        // 导出
        exportFn() {
            let datas = this.$_.cloneDeep(this.searchObj);
            datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
            delete datas.pageNum
            delete datas.pageSize
            API.exportList(datas).then(res => {
                let binaryData = [];
                let link = document.createElement('a');
                binaryData.push(res.data);
                link.style.display = 'none';
                link.href = window.URL.createObjectURL(new Blob(binaryData));
                link.setAttribute('download', '供应商入库列表.xlsx');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            })
        },
        // 编辑
        edit() {
            this.$refs.addEditPopRef.modelEdit = true
            this.$nextTick(() => {
                this.$refs.addEditPopRef.getInfo()
            })
        },
        // 关单入库
        closeOrderStock() {
            this.$message.confirm('是否确定关单入库?', '提示', {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$Message.loading({
                    content: "提交中...",
                    duration: 0,
                });
                API.customsDocumentsWarehouse(this.tableSelect[0]).then(res => {
                    this.$Message.destroy()
                    if (res.data.success) {
                        this.$Message.success("关单入库成功");
                        setTimeout(() => {
                            this.getList()
                        }, 1000)
                    } else {
                        this.$Message.error(res.data.error || res.data.message || '操作失败');
                    }
                }).catch(e => {
                    console.log(e);
                    this.$Message.destroy()
                    this.$Message.error("获取服务失败，请重试");
                })
            })
        },
        // 取消
        cancel() {
            this.$message.confirm('是否确认取消?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$Message.success('取消成功!');
            })
        },
        // 删除
        del() {
            this.$message.confirm('此操作将永久删除该文件, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$Message.success('删除成功!');
            })
        },
        // 打开详情
        openDetail(row) {
            this.detailData = row
            this.$refs.detailsRef.show = true
            this.$nextTick(() => {
                this.$refs.detailsRef.getInfo()
            })
        },
        // 查询按钮
        queryList() {
            this.searchObj.pageNum = 1;
            this.getList();
        },

        // 重置按钮
        emptyList() {
            this.searchObj = {
                tranCode: '',
                purchaseCode: '',
                ifClose: '',
                createDateStart: '',
                createDateEnd: '',
                materialCode: '',
                pageNum: 1,
                pageSize: 10
            };
            this.createDate = [];
        },
        // 选择日期
        selectDate(date, refObj) {
            this.createDate = date;
            this.searchObj[refObj + "Start"] = date[0];
            this.searchObj[refObj + "End"] = date[1];
        },
        // 获取工单列表
        getList() {
            this.$Message.loading({
                content: "查询中...",
                duration: 0,
            });
            let params = this.$_.cloneDeep(this.searchObj);
            const pageText = "?page=" + this.searchObj.pageNum + "&rows=" + this.searchObj.pageSize
            API.getList(pageText, params).then(res => {
                this.$Message.destroy()
                this.tableSelect = []
                let request = res.data.result;
                if (res.data.success) {
                    this.totalElements = request.totalElements;
                    this.commList = request.content;
                } else {
                    this.totalElements = 0;
                    this.commList = [];
                }
            }).catch(e => {
                console.log(e);
                this.$Message.destroy()
                this.$Message.error("获取服务失败，请重试");
            })
        },
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.pageNum = curr;
            this.getList();
        },
        jumpto(path, id, count) {
            this.$router.push({
                path,
                query: { id, count },
            });
        },
        handleSuccess() {
            this.$nextTick(() => {
                this.getList()
                setTimeout(() => {
                    this.$Message.success('操作成功');
                }, 300)
            })
        }
    }
}
</script>

<style lang="scss" scoped>
@import 'style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
