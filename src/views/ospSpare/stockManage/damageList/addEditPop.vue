<template>
    <Modal width="1200" v-model="modelEdit" draggable sticky scrollable title="审核" :footer-hide="true"
           :mask-closable="false">
        <div style="position: relative; width: 100%; height: 60vh;">
            <div style="padding-bottom: 40px; width: 100%; height: 60vh; overflow-y: auto">
                <Form :model="formItem" :rules="ruleValidate" :label-width="100" ref="form" inline>
                    <FormItem label="发货单号">
                        <Input type="text" maxlength="15" v-model="formItem.sendCode" clearable
                               style="width: 360px" disabled />
                    </FormItem>
                    <FormItem label="入库单号">
                        <Input type="text" maxlength="15" v-model="formItem.tranCode" clearable
                               style="width: 360px" disabled />
                    </FormItem>
                    <FormItem label="入库仓库">
                        <Input type="text" maxlength="15" v-model="formItem.warehouseName" clearable
                               style="width: 360px" disabled />
                    </FormItem>
                    <FormItem label="备件编码">
                        <Input type="text" maxlength="15" v-model="formItem.materialCode" clearable
                               style="width: 360px" disabled />
                    </FormItem>
                    <FormItem label="备件描述">
                        <Input type="text" maxlength="15" v-model="formItem.materialDesc" clearable
                               style="width: 360px" disabled />
                    </FormItem>
                    <FormItem label="应入库数量">
                        <Input type="text" maxlength="15" v-model="formItem.amountOrg" clearable
                               style="width: 360px" disabled />
                    </FormItem>
                    <FormItem label="实际入库数量">
                        <Input type="text" maxlength="15" v-model="formItem.amount" clearable
                               style="width: 360px" disabled />
                    </FormItem>
                    <FormItem label="货差数量">
                        <Input type="text" maxlength="15" v-model="formItem.lostAmount" clearable
                               style="width: 360px" disabled />
                    </FormItem>
                    <FormItem label="货差分类">
                        <Input type="text" maxlength="15" v-model="formItem.faultType" clearable
                               style="width: 360px" disabled />
                    </FormItem>
                    <FormItem label="责任分类">
                        <Input type="text" maxlength="15" v-model="formItem.faultClassify" clearable
                               style="width: 360px" disabled />
                    </FormItem>
                    <FormItem label="审批状态" prop="auditStatus">
                        <Select v-model="formItem.auditStatus" filterable clearable style="width: 360px">
                            <Option value="1">同意</Option>
                            <Option value="2">驳回</Option>
                        </Select>
                    </FormItem>
                    <FormItem label="附件"  v-if="formItem.fileList && formItem.fileList.length>0">
                    <div style="display:flex;justify-content: left;">
                                <img v-for="(item,index) in formItem.fileList" :key="index" :src="item.fileUrl" loading="lazy" width="100" height="100%" style="margin-right: 5px;"/>
                    </div>

                    </FormItem>

                    <div>
                        <FormItem label="审批意见" prop="remarks">
                            <Input type="textarea" v-model="formItem.remarks" clearable
                                   style="width: 830px" />
                        </FormItem>
                    </div>
                </Form>
            </div>
            <div style="position: absolute; z-index: 99; right: 20px; bottom: -30px; text-align: right; margin: 30px 0">
                <Button type="primary"
                        @click="debounce(submitPop)">保存</Button>
                <Button type="default" style="margin-left: 10px"
                        @click="modelEdit = false">取消</Button>
            </div>
        </div>
    </Modal>
</template>

<script>
import API from "@/api/ospSpare/stockManage/damageList";
export default {
    name: "addEdit",
    props: {
        popType: {
            type: String,
            default: 'add'
        }
    },
    watch: {
        modelEdit(val) {
            if (val) {
                this.formItem=this.dataSource
                this.formItem.auditStatus=undefined;
                this.formItem.remarks=undefined;
            }
        }
    },
    data() {
        return {
            modelEdit: false,
            modelTitle: '新增',
            formItem: {
                sendCode: '',
                tranCode: '',
                warehouseName: '',
                materialCode: '',
                materialDesc: '',
                amountOrg: '',
                amount: '',
                lostAmount: '',
                faultType: '',
                faultClassify: '',
                remarks: undefined,
                auditStatus: undefined,
            },
            modelEditAdd: false,
            dataSource:{},
            ruleValidate: {
                auditStatus: [
                    { required: true, message: '请选择审批意见', trigger: 'change' }
                ],
                city: [
                    { required: true, message: '请选择审批意见', trigger: 'change' }
                ],
                remarks: [
                    { required: true, message: '请选择审批状态', trigger: 'blur' }
                ]
            },
        }
    },
    methods: {
        submitPop() {
            this.$refs.form.validate((valid) => {
                if (valid) {
                    let params={
                        'id':this.dataSource.id,
                        'auditStatus':this.formItem.auditStatus,
                        'remarks':this.formItem.remarks
                    }
                    API.approve(params).then(res=>{
                        if(res.data.success){
                            this.$Message.success('审批成功');
                            setTimeout(() => {
                                this.$Message.destroy();
                                this.$emit('getList');
                            }, 2000)
                            this.modelEdit = false;
                            this.resetForm();
                        }else{
                            this.$Message.error(res.data.error);
                        }
                    }).catch(err=>{
                        this.$Message.error(err.data.error);
                    })
                }
            })

        },
        // 重置表单
        resetForm(){
            this.subTableData=[]
            this.formItem= {
                sendCode: '',
                tranCode: '',
                warehouseName: '',
                materialCode: '',
                materialDesc: '',
                amountOrg: '',
                amount: '',
                lostAmount: '',
                faultType: '',
                faultClassify: '',
                remarks: undefined,
                auditStatus: undefined,
            }
        },
    }
}
</script>

<style lang="scss" scoped>
@import '@/style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
