<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏备件管理</Breadcrumb-item>
            <Breadcrumb-item to="/ospSpare/">入库管理</Breadcrumb-item>
            <Breadcrumb-item>货损货差列表-服务商</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">发货单号：</p>
                    <Input v-model="searchObj.sendCode" placeholder="请输入发货单号" clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">入库单号：</p>
                    <Input v-model="searchObj.tranCode" placeholder="请输入入库单号" clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">订单号：</p>
                    <Input v-model="searchObj.orderCode" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">审批状态：</p>
                    <Select v-model="searchObj.auditStatus" placeholder="请选择订单类型" clearable style="width: 200px">
                        <Option value="0">已提交</Option>
                        <Option value="1">通过</Option>
                        <Option value="2">驳回</Option>
                        <Option value="3">撤销申诉</Option>
                        <Option value="4">申诉中</Option>
                    </Select>
                </div>
            </div>
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">提交时间：</p>
                    <DatePicker ref="formDateSh" :value="createDate" @on-change="
                        (data) => {
                          return selectDate(data, 'createDate');
                        }
                      " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择提交时间" style="width: 240px ">
                    </DatePicker>
                </div>
                <div class="condition">
                    <p class="condition_p">审批时间：</p>
                    <DatePicker ref="formDateSh" :value="auditDate" @on-change="
                        (data) => {
                          return selectDate(data, 'auditDate');
                        }
                      " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择审批时间" style="width: 240px ">
                    </DatePicker>
                </div>
            </div>
            <div class="list_but" style="display: flex; justify-content: space-between; width: 100%;">
                <div class="condition">
                    <Button @click="handleExport" type="success">导出</Button>
                    <Button @click="edit" type="primary"  :disabled="this.tableSelect.length!='1' || this.tableSelect[0].auditStatus!='2'">编辑</Button>
<!--                    <Button @click="check" type="primary" :disabled="this.tableSelect.length!='1' || this.tableSelect[0].auditStatus!='2'">审批</Button>-->
                    <Button @click="reback" type="primary" :disabled="this.tableSelect.length!='1' || this.tableSelect[0].auditStatus!='2'">撤销申诉</Button>
                </div>
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                </div>
            </div>
            <div class="commListDiv">
                <Table size="small" border ref="tableRef" :columns="tableColumn" :data="commList" @on-selection-change="selected => this.tableSelect = selected">
                    <template slot-scope="{ index }" slot="sn">
                        {{ index + 1 }}
                    </template>
<!--                    入库单类型-->
                    <template slot-scope="{ index }" slot="tranType">
                       <span v-if="commList[index].tranType=='1'">入库</span>
                    </template>
                    <!--                    货差类型-->
                    <template slot-scope="{ index }" slot="faultType">
                        {{faultTypeMap[commList[index].faultType]}}
                    </template>
                    <!--                    责任分类-->
                    <template slot-scope="{ index }" slot="faultClassify">
                        {{faultClassifyMap[commList[index].faultClassify]}}
                    </template>
                    <!--                    是否补发-->
                    <template slot-scope="{ index }" slot="whetherToReissue">
                        <span v-if="commList[index].whetherToReissue=='1'">是</span>
                        <span v-if="commList[index].whetherToReissue=='0'">否</span>
                    </template>
<!--                    <template slot-scope="{ index }" slot="option">-->
<!--                        <Button type="info" ghost size="small" @click="openDetail(commList[index])" style="margin: 3px">详情</Button>-->
<!--                    </template>-->
<!--                    审批状态-->
                    <template slot-scope="{ index }" slot="auditStatus">
                        {{ shAuditStatusMap[commList[index].auditStatus] }}
                    </template>
                </Table>
            </div>
            <Page style="margin: 20px" @on-change="changeCurrent" :current="searchObj.page"
                  :total="totalElements" show-total show-elevator />
        </div>
<!--        编辑-->
        <edit ref="editRef"  @getList="getList"/>
<!--        审核-->
        <addEditPop ref="addEditPopRef"  @getList="getList"/>

    </div>
</template>

<script>
import _data from "@/views/ospSpare/_edata"; // 数据字典
import API from "@/api/ospSpare/stockManage/damageList";
import addEditPop from "./addEditPop";
import edit from "./edit";
import { removeClass } from "@/components/message-box/js/dom";

export default {
    name: "serviceProviderOrder",
    components: { addEditPop,edit },
    data() {
        return {
            searchObj: {
                sendCode: '',
                tranCode: '',
                orderCode: '',
                auditStatus: '',
                createDateStart: '',
                createDateEnd: '',
                auditDateStart: '',
                auditDateEnd: '',
                pageNum: 1,
                pageSize: 10
            },
            formObj: {},
            staffList: [],
            totalElements: 0,
            createDate: [], // 提交时间
            auditDate: [], // 审批时间
            status: _data.status,
            orderStatusList: _data.orderStatusList,
            sourceArr: _data.SourceArr,
            faultCodeTypeList: _data.faultCodeTypeList,
            faultType:_data.faultType,//货差类型
            faultTypeMap:[],//货差类型map
            faultClassify:_data.faultClassify,//货差分类
            faultClassifyMap:[],//货差分类map
            commList: [],
            tableColumn: [
                { type: 'selection', minWidth: 60, title: '选择', align: 'center' },
                { title: '序号', minWidth: 80, align: 'center', slot: 'sn' },
                { title: '中心名称', minWidth: 100, key: 'centerName', align: 'center' },
                { title: '服务商名称', minWidth: 180, key: 'warehouseName', align: 'center' },
                { title: '发货单号', minWidth: 200, key: 'sendCode', align: 'center' },
                { title: '入库单号', minWidth: 200, key: 'tranCode', align: 'center' },
                { title: '订单号', minWidth: 180, key: 'orderCode', align: 'center' },
                { title: '快递单号', minWidth: 180, key: 'transferCode', align: 'center' },
                { title: '入库单类型', minWidth: 180, key: 'tranType', align: 'center', slot: 'tranType' },
                { title: '审批状态', minWidth: 180, key: 'auditStatus', align: 'center', slot: 'auditStatus'  },
                { title: '审批备注', minWidth: 180, key: 'remarks', align: 'center' },
                { title: '备件编码', minWidth: 180, key: 'materialCode', align: 'center' },
                { title: '备件描述', minWidth: 180, key: 'materialDesc', align: 'center' },
                { title: '应入库数量', minWidth: 180, key: 'amountOrg', align: 'center' },
                { title: '备件唯一码', minWidth: 180, key: 'batchNum', align: 'center' },
                { title: '实际入库数量', minWidth: 180, key: 'amount', align: 'center' },
                { title: '货差数量', minWidth: 180, key: 'lostAmnt', align: 'center' },
                { title: '货差分类', minWidth: 180, key: 'faultType', align: 'center', slot: 'faultType' },
                { title: '责任分类', minWidth: 180, key: 'faultClassify', align: 'center', slot: 'faultClassify' },
                { title: '是否补发', minWidth: 180, key: 'whetherToReissue', align: 'center', slot: 'whetherToReissue' },
                { title: '提交时间', minWidth: 180, key: 'createDate', align: 'center' },
                { title: '收货人', minWidth: 180, key: 'phone', align: 'center' },
                { title: '电话', minWidth: 180, key: 'linkMan', align: 'center' },
                // { title: '附件名称', minWidth: 180, key: 'fileName', align: 'center' },
                // { title: '附件地址', minWidth: 180, key: 'fileUrl', align: 'center' }
            ],
            tableSelect: [],
            shAuditStatusMap:[]
        }
    },
    mounted() {
        this.getList()
        this.toolsDict()
    },
    methods: {
        toolsDict(){
            //审批状态
            _data.shAuditStatus.forEach(item=>{
                this.shAuditStatusMap[item.value]=item.label
            })
            //货差类型
            _data.faultType.forEach(item=>{
                this.faultTypeMap[item.value]=item.label
            })
            //货差分类
            _data.faultClassify.forEach(item=>{
                this.faultClassifyMap[item.value]=item.label
            })
        },
        // 撤销申诉
        reback(){
            let datas = { id: this.tableSelect[0].id };
            API.revokeAppeal(datas).then((res) => {
                if (res.data.success) {
                    this.$Message.success("撤销成功");
                    setTimeout(() => {
                        this.queryList();
                    }, 2000);
                } else {
                    this.$Message.error(res.data.error);
                }
            }).catch((res) => {
                this.$Message.error(res.data.message);
            });
        },
        // 编辑
        edit(){
            this.$refs.editRef.dataSource = this.tableSelect[0]
            this.$refs.editRef.modelEdit = true
        },
        //审批
        check(){
            this.$refs.addEditPopRef.dataSource = this.tableSelect[0]
            this.$refs.addEditPopRef.modelEdit = true
        },
        // 导出
        handleExport() {
            const params = this.searchObj
            params.type ='3'
            this.$Message.loading({
                content: "导出中...",
                duration: 0,
            });
            API.export(params)
                .then(res => {
                    this.$Message.destroy()
                    const resData = res.data || {}
                    let binaryData = [];
                    let link = document.createElement('a');
                    binaryData.push(resData);
                    link.style.display = 'none';
                    link.href = window.URL.createObjectURL(new Blob(binaryData));
                    link.setAttribute('download', '货损货差列表-服务商.xlsx');
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                })
                .catch((e) => {
                    this.$Message.destroy()
                    console.log(e)
                });
        },
        // 取消
        cancel() {
            this.$message.confirm('是否确认取消?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$Message.success('取消成功!');
            })
        },
        // 删除
        del() {
            this.$message.confirm('此操作将永久删除该文件, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$Message.success('删除成功!');
            })
        },
        // 查询按钮
        queryList() {
            this.searchObj.pageNum = 1;
            this.getList();
        },

        // 重置按钮
        emptyList() {
            this.searchObj = {
                sendCode: '',
                tranCode: '',
                orderCode: '',
                auditStatus: '',
                createDateStart: '',
                createDateEnd: '',
                auditDateStart: '',
                auditDateEnd: '',
                pageNum: 1,
                pageSize: 10
            };
            this.createDate = [];
            this.auditDate = [];
        },
        // 选择日期
        selectDate(date, refObj) {
            this[refObj] = date;
            this.searchObj[refObj + "Start"] = date[0];
            this.searchObj[refObj + "End"] = date[1];
        },
        // 获取工单列表
        getList() {
            this.$Message.loading({
                content: "查询中...",
                duration: 0,
            });
            let datas = this.$_.cloneDeep(this.searchObj);
            datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
            delete datas.pageNum
            delete datas.pageSize
            const page = { page: this.searchObj.pageNum, rows: this.searchObj.pageSize }
            datas.type ='3' // 1 服务商写死
            API.getList(page, datas).then(res => {
                this.$Message.destroy();
                this.commList = res.data.result.content
                this.totalElements = res.data.result.totalElements
            })
        },
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.pageNum = curr;
            this.getList();
        }
    }
}
</script>

<style lang="scss" scoped>
@import 'style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
