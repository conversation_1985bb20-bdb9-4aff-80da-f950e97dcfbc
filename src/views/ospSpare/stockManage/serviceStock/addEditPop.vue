<template>
    <Modal width="1200" v-model="modelEdit" draggable sticky scrollable :title="modelTitle" :footer-hide="true"
           :mask-closable="false">
        <div style="position: relative; width: 100%; height: 60vh;">
            <div style="padding-bottom: 40px; width: 100%; height: 60vh; overflow-y: auto">
                <Form :model="formItem" :label-width="100" ref="formItem" inline>
                    <FormItem label="入库单号">
                        <Input type="text" maxlength="15" v-model="formItem.tranCode" clearable
                               style="width: 360px" disabled />
                    </FormItem>
                    <FormItem label="来源订单号">
                        <Input type="text" maxlength="15" v-model="formItem.sourceOrderCode" clearable
                               style="width: 360px" disabled/>
                    </FormItem>
                    <FormItem label="入库仓库">
                        <Input type="text" maxlength="15" v-model="formItem.warehouseName" clearable
                               style="width: 360px" disabled/>
                    </FormItem>
                    <FormItem label="操作人">
                        <Input type="text" maxlength="15" v-model="formItem.updateBy" clearable
                               style="width: 360px" disabled/>
                    </FormItem>
                </Form>
<!--                <div class="list_but" style="margin-bottom: 10px">-->
<!--                    <div class="condition">-->
<!--                        <Button @click="modelEditAdd = true" size="small" type="primary">添加</Button>-->
<!--                        <Button style="margin-left: 6px" @click="del" size="small" type="error" :disabled="!subTableSelect.length">删除</Button>-->
<!--                    </div>-->
<!--                </div>-->
                <div class="commListDiv">
                    <Table size="small" border ref="subTableRef" :columns="subTableColumn" :data="subTableData">
                        <template slot-scope="{ index }" slot="getAmnt">
                            <Input v-model="subTableData[index].getAmnt" style="text-align: center" @on-change="tableInputChange" />
                        </template>
                        <template slot-scope="{ index }" slot="lostAmnt">
                            <Input v-model="subTableData[index].lostAmnt" style="text-align: center" @on-change="tableInputChange" />
                        </template>
<!--                        货差类型-->
                        <template slot-scope="{ index }" slot="faultType">
                            <Select v-model="subTableData[index].faultType" clearable style="text-align: center">
                                <Option v-for="item in faultType" :value="item.value" :key="item.value">{{ item.label }}</Option>
                            </Select>
                        </template>
                        <!--                        货差分类/责任分类-->
                        <template slot-scope="{ index }" slot="faultClassify">
                            <Select v-model="subTableData[index].faultClassify" clearable style="text-align: center">
                                <Option v-for="i in faultClassify" :value="i.value" :key="i.value">{{ i.label }}</Option>
                            </Select>
                        </template>
                        <!--                       是否补发-->
                        <template slot-scope="{ index }" slot="whetherToReissue">
                            <Select v-model="subTableData[index].whetherToReissue" clearable style="text-align: center">
                                <Option value="1" key="1">是</Option>
                                <Option value="0" key="0">否</Option>
                            </Select>
                        </template>
                    </Table>
                    <Upload ref="upload" class="inlinebox" multiple :show-upload-list="false" :action="actionUrl" :headers="headers" :on-progress="progress" :on-success=" (response, file, fileList) => { return imgFileUploadSuccess(response, file, fileList); } " :format="['jpg', 'jpeg', 'png']" :max-size="40960" :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize">
                        <div class="file_pics">
                            <Button type="info" style="margin: 15px 0">上传附件</Button>
                            <div v-if="fileUrls" class="demo-upload-list">
                                <div v-for="(item,index) in fileUrls" :key="index" style="position: relative;width: 400px;margin-right: 5px;">
                                        <img :src="item.imageUrl" loading="lazy" width="100%" height="100%"/>
                                        <div class="demo-upload-list-cover" @click.stop>
                                            <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleView(index)"></Icon>
                                            <Icon type="ios-trash-outline" size="20" color="#fff" @click.stop="handleRemove(index)"></Icon>
                                        </div>
                                </div>
                            </div>
                        </div>
                    </Upload>
                </div>
            </div>
            <div style="position: absolute; z-index: 99; right: 20px; bottom: -30px; text-align: right; margin: 30px 0">
                <Button type="primary"
                        @click="debounce(submitPop)">保存</Button>
                <Button type="default" style="margin-left: 10px"
                        @click="modelEdit = false">取消</Button>
            </div>
            <Modal v-model="visible" width="70%" reset-drag-position draggable sticky title="预览" footer-hide class-name="vertical-center-modal">
                <img class="preview" width="500px" v-if="visibleImg" :src="visibleImg" @click="toTarget(visibleImg)" title="点击查看原图">
            </Modal>
        </div>
    </Modal>
</template>

<script>
import _data from "@/views/ospSpare/_edata";
import API from "@/api/ospSpare/stockOutManage/serviceStock";
import store from "@/store";
export default {
    name: "addEdit",
    data() {
        return {
            modelEdit: false,
            visible: false,
            modelTitle: '编辑',
            formItem: {
                tranCode: '',
                sourceOrderCode: '',
                warehouseName: '',
                updateBy: ''
            },
            faultType:_data.faultType,
            faultClassify:_data.faultClassify,
            subTableSelect: [],
            subTableData: [],
            subTableColumn: [
                {
                    title: '备件编码',
                    key: 'materialCode',
                    width:160,
                    align: 'center'
                },
                {
                    title: '备件描述',
                    key: 'materialDesc',
                    width:180,
                    align: 'center'
                },
                {
                    title: '单位',
                    key: 'materialUnit',
                    align: 'center'
                },
                {
                    title: '单价',
                    key: 'materialPrice',
                    align: 'center'
                },
                {
                    title: '应入库数量',
                    key: 'orderAmnt',
                    align: 'center'
                },
                {
                    title: '实际入库数量',
                    key: 'getAmnt',
                    slot: 'getAmnt',
                    align: 'center',
                    width:130
                },
                {
                    title: '货差数量',
                    key: 'lostAmnt',
                    slot: 'lostAmnt',
                    align: 'center'
                },
                {
                    title: '货差类型',
                    key: 'faultType',
                    slot: 'faultType',
                    align: 'center'
                },
                {
                    title: '货差分类',
                    key: 'faultClassify',
                    slot: 'faultClassify',
                    align: 'center'
                },
                {
                    title: '是否补发',
                    key: 'whetherToReissue',
                    slot: 'whetherToReissue',
                    align: 'center'
                },
            ],
            dataSource:{},
            fileUrls:[],
            actionUrl: `${process.env.VUE_APP_BASE_API}/merchant/oss/image/upload.do`,
            headers: { Authorization: `Bearer ${JSON.parse(window.localStorage.getItem('logins')).access_token}` },
            visibleImg: '',
        }
    },
    watch: {
        modelEdit(val) {
            if (val) {
                this.getInfo();
            }
        }
    },
    methods: {
        progress() {
            this.$Message.loading({
                content: '上传中...',
                duration: 0
            });
        },
        imgFileUploadSuccess(response, file, fileList) {
            let that = this;
            // this.fileUrls = response.result;
            this.fileUrls.push(response.result[0])
            setTimeout(() => {
                that.$Message.destroy();
            }, 500);
        },
        handleFormatError(file) {
            this.$Notice.warning({
                title: '上传格式不正确',
                desc: '请上传正确的格式文件'
            });
        },

        handleMaxSize (file) {
            this.$Notice.warning({
                title: '上传文件过大',
                desc: file.name + '文件太大，请压缩后上传.'
            });
        },
        handleView (index, inx, tag) {
            this.visible = true;
            this.visibleImg = this.fileUrls[index].imageUrl;
            this.visibleName = '附件照片';
        },

        handleRemove (index, inx, tag) {
            this.fileUrls.splice(index,1)
        },
        toTarget(url) {
            window.open(url)
        },
        tableInputChange(e) {
            this.$refs.subTableRef.selectAll(false);
        },
        submitPop() {
            let arr=[]
            if(this.fileUrls.length>0){
                this.fileUrls.forEach(item=>{
                    arr.push({
                        'fileUrl':item.imageUrl
                    })
                })
            }
            let params={
                'id':this.dataSource.id,
                'ifClose':this.dataSource.ifClose,
                'referId':this.dataSource.referId  ,
                'tranCode':this.formItem.tranCode,
                'sourceOrderCode':this.formItem.sourceOrderCode,
                'warehouseName':this.formItem.warehouseName,
                'updateBy':this.formItem.updateBy,
                'spTranSnDetail':this.subTableData,
                'fileList':arr
            }
            API.updateDetail(params).then(res=>{
                if(res.data.success){
                    this.$Message.success('编辑成功');
                    this.$emit('getList');
                    this.modelEdit = false;
                }else{
                    this.$Message.error(res.data.error);
                }
            }).catch(err=>{
                this.$Message.error(err.data.error);
            })
        },
        /*获取详情*/
        getInfo(){
            let params={'id':this.dataSource.id}
            API.orderDetail(params).then(res => {
                if (res.data.success) {
                    this.formItem = res?.data?.result
                    this.formItem.updateBy=JSON.parse(window.localStorage.getItem('userInfo')).name
                    this.subTableData = res?.data?.result?.spTranSnDetail||[]
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped>
@import '@/style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
.demo-upload-list {
    @extend .flex-row-center-center;
    position: relative;
    width: 50%;
    height: 100%;
    overflow: hidden;

    &:hover .demo-upload-list-cover {
        display: flex;
    }
}
.demo-upload-list-cover {
    @extend.flex-row-center-around;
    display: none;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,.6);
    z-index: 999;
    i{
        &:hover{
            cursor: pointer;
        }
    }
}
:deep(.ivu-table-wrapper) {
overflow: visible;
}
</style>
