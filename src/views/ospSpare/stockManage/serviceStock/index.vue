<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏备件管理</Breadcrumb-item>
            <Breadcrumb-item to="/ospSpare/">入库管理</Breadcrumb-item>
            <Breadcrumb-item>服务商入库</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">入库单号：</p>
                    <Input v-model="searchObj.tranCode" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">订单号：</p>
                    <Input v-model="searchObj.purchaseCode" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">备件编码：</p>
                    <Input v-model="searchObj.materialCode" placeholder="请输入备件编码" clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">入库状态：</p>
                    <Select v-model="searchObj.ifClose" placeholder="请选择..." clearable style="width: 200px">
                        <Option value="0">未入库</Option>
                        <Option value="1">已入库</Option>
                        <Option value="2">货差审核中</Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">入库类型：</p>
                    <Select v-model="searchObj.tranType" clearable style="width: 200px"  placeholder="请选择..." >
                        <Option value="1">入库</Option>
                        <Option value="5">调拨入库</Option>
                        <Option value="6">借件入库</Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">入库仓库：</p>
                    <Input v-model="searchObj.warehouseName" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">入库创建时间：</p>
                    <DatePicker ref="formDateSh" :value="createDate" @on-change="
                        (data) => {
                          return selectDate(data, 'createDate');
                        }
                      " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择..." style="width: 200px ">
                    </DatePicker>
                </div>
                <div class="condition">
                    <p class="condition_p">入库时间：</p>
                    <DatePicker ref="formDateSh" :value="createDate2" @on-change="
                        (data) => {
                          return selectDate2(data, 'tranDate');
                        }
                      " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择" style="width: 200px ">
                    </DatePicker>
                </div>
            </div>
            <div  class="list_but" style="display: flex; justify-content: space-between; width: 100%;">
                <div  class="condition">
                    <Button @click="edit" type="primary" :disabled="this.tableSelect.length!=1 || this.tableSelect[0].ifClose!='0'">编辑</Button>
                    <Button @click="exportFn" type="error">导出</Button>
                    <Button @click="closeOrderStock" type="success" style="margin-right: 0px;" :disabled="this.tableSelect.length!=1 || this.tableSelect[0].ifClose!='0'">关单入库</Button>
                    <Button type="text" style="color: #2d8cf0">损货差请先点击”编辑按钮，录入货差信息</Button>
                </div>
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                </div>
            </div>
            <div class="commListDiv">
                <Table size="small" border ref="tableRef" :columns="tableColumn" :data="commList" @on-selection-change="selected => this.tableSelect = selected">
                    <template slot-scope="{ index }" slot="sn">
                        {{ index + 1 }}
                    </template>
                    <!--                    入库类型-->
                    <template slot-scope="{ index }" slot="tranType">
                        <span v-if="commList[index].tranType=='1'">入库</span>
                        <span v-if="commList[index].tranType=='5'">调拨入库</span>
                        <span v-if="commList[index].tranType=='6'">借件入库</span>
                        <span v-else></span>
                    </template>
<!--                    入库状态-->
                    <template slot-scope="{ index }" slot="ifClose">
                        <span v-if="commList[index].ifClose=='0'">未入库</span>
                        <span v-if="commList[index].ifClose=='1'">已入库</span>
                        <span v-if="commList[index].ifClose=='2'">货差审核中</span>
                        <span v-else></span>
                    </template>
                    <template slot-scope="{ index }" slot="option">
                        <Button type="info" ghost size="small" @click="openDetail(commList[index])" style="margin: 3px">详情</Button>
                    </template>
                    <template slot-scope="{ row }" slot="fileUrl">
                        <a v-if="row.fileUrl" :href="row.fileUrl" target="_blank">点击查看</a>
                    </template>
                </Table>
            </div>
            <Page style="margin: 20px" @on-change="changeCurrent" :current="searchObj.page"
                  :total="totalElements" show-total show-elevator />
        </div>
        <addEditPop ref="addEditPopRef" />
        <lendOrderPutIn ref="lendOrderPutInRef" @getPageList="getList"/>
<!--        详情-->
        <detail ref="detailsRef" />
    </div>
</template>

<script>
import _data from "@/views/ospSpare/_edata"; // 数据字典
import API from "@/api/ospSpare/stockOutManage/serviceStock";
import addEditPop from "./addEditPop";
import lendOrderPutIn from './lendOrderPutIn.vue'
import detail from './details'
export default {
    name: "serviceProviderOrder",
    components: { addEditPop, detail, lendOrderPutIn },
    data() {
        return {
            searchObj: {
                createDateRange: "",
                orderCode: "",
                materialCode: "",
                woId: "",
                orderType: "",
                orderStatus: "",
                branchName: "",
                pageNum: 1,
                pageSize: 10
            },
            formObj: {},
            staffList: [],
            totalElements: 0,
            createDate: [],
            createDate2: [],
            tableColumn: [
                { type: 'selection', minWidth: 60, title: '选择', align: 'center' },
                { title: '序号',  minWidth: 80, align: 'center', slot: 'sn' },
                { title: '发货单号', minWidth: 200, key: 'sendCode', align: 'center' },
                { title: '来源单号', minWidth: 180, key: 'sourceOrderCode', align: 'center' },
                { title: '入库单号', minWidth: 200, key: 'tranCode', align: 'center' },
                { title: '中心名称', minWidth: 100, key: 'centerName', align: 'center' },
                { title: '入库仓库', minWidth: 180, key: 'warehouseName', align: 'center' },
                { title: '入库类型', minWidth: 100, key: 'tranType', align: 'center',slot: 'tranType' },
                { title: '入库状态', minWidth: 100, key: 'ifClose', align: 'center',slot: 'ifClose' },
                { title: '物流公司', minWidth: 160, key: 'transportNamePinyin', align: 'center' },
                { title: '物流单号', minWidth: 160, key: 'transportCode', align: 'center' },
                { title: '发货时间', minWidth: 160, key: 'sendDate', align: 'center' },
                { title: '入库时间', minWidth: 160, key: 'tranDate', align: 'center' },
                { title: '创建时间', minWidth: 160, key: 'createDate', align: 'center' },
                { title: '查看附件', minWidth: 160, slot: 'fileUrl', align: 'center' },
                { title: '操作', minWidth: 120, align: 'center', fixed: 'right', slot: 'option' }
            ],
            commList: [],
            tableSelect:[],//表格选择数据
            modelEdit: false,
            modelEditAdd: false
        }
    },
    mounted() {
        this.getList()
    },
    methods: {
        // 导出
        exportFn() {
            let datas = this.$_.cloneDeep(this.searchObj);
            datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
            delete datas.pageNum
            delete datas.pageSize
            API.exportList(datas).then(res => {
                let binaryData = [];
                let link = document.createElement('a');
                binaryData.push(res.data);
                link.style.display = 'none';
                link.href = window.URL.createObjectURL(new Blob(binaryData));
                link.setAttribute('download', '服务商入库.xlsx');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            })
        },
        // 编辑
        edit() {
            if(this.tableSelect[0].tranType==='6'){
                this.$refs.lendOrderPutInRef.orderId = this.tableSelect[0].id
                this.$refs.lendOrderPutInRef.dataSource = this.tableSelect[0]
                this.$refs.lendOrderPutInRef.modelEdit = true
            }else {
                this.$refs.addEditPopRef.dataSource = this.tableSelect[0]
                this.$refs.addEditPopRef.modelEdit = true
            }

        },
        // 关单入库
        closeOrderStock() {
            this.$message.confirm('是否确定关单入库?', '提示', {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                let params={'id':this.tableSelect[0].id}
                API.closeTranSnIn(params).then(res=>{
                    if(res.data.success){
                        this.$Message.success('关单入库成功!');
                        setTimeout(() => {
                            this.$Message.destroy();
                            this.getList()
                        }, 1000)
                    }else{
                        this.$Message.error(res.data.error);
                    }
                }).catch(err=>{
                    this.$Message.error(err.data.error);
                })
            })
        },
        // 取消
        cancel() {
            this.$message.confirm('是否确认取消?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$Message.success('取消成功!');
            })
        },
        // 删除
        del() {
            this.$message.confirm('此操作将永久删除该文件, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$Message.success('删除成功!');
            })
        },
        // 打开详情
        openDetail(row) {
            this.$refs.detailsRef.orderId = row.id
            this.$refs.detailsRef.show = true
        },
        // 查询按钮
        queryList() {
            this.searchObj.pageNum = 1;
            this.getList();
        },

        // 重置按钮
        emptyList() {
            this.searchObj = {
                createDateRange: "",
                orderCode: "",
                woId: "",
                orderType: "",
                orderStatus: "",
                branchName: "",
                pageNum: 1,
                pageSize: 10
            };
            this.createDate = [];
            this.createDate2 = [];
        },
        // 选择日期
        selectDate(date, refObj) {
            this.createDate = date;
            this.searchObj[refObj + "Start"] = date[0];
            this.searchObj[refObj + "End"] = date[1];
        },
        // 选择日期
        selectDate2(date, refObj) {
            this.createDate2 = date;
            this.searchObj[refObj + "Start"] = date[0];
            this.searchObj[refObj + "End"] = date[1];
        },
        // 获取工单列表
        getList() {
            this.$Message.loading({
                content: "查询中...",
                duration: 0,
            });

            let datas = this.$_.cloneDeep(this.searchObj);
            datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
            delete datas.pageNum
            delete datas.pageSize
            const page = { page: this.searchObj.pageNum, rows: this.searchObj.pageSize }
            // 将所有选中项清空
            this.$refs.tableRef.selectAll(false);
            API.getList(page, datas).then(res => {
                if(res.data.success){
                    this.$Message.destroy();
                    this.commList = res.data.result.content
                    this.totalElements = res.data.result.totalElements
                }else{
                    this.commList=[]
                }
            }).catch(err=>{
                this.commList=[]
            })

        },
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.pageNum = curr;
            this.getList();
        },
        jumpto(path, id, count) {
            this.$router.push({
                path,
                query: { id, count },
            });
        }
    }
}
</script>

<style lang="scss" scoped>
@import '@/style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
