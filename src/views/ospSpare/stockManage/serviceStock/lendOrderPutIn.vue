<template>
    <Modal width="1200" height="500" v-model="modelEdit" draggable sticky scrollable :title="modelTitle" :footer-hide="true"
           :mask-closable="false">
<!--        <div style="position: relative; width: 100%; height: 60vh;">-->
            <div>
                <div style="padding-bottom: 40px;width: 100%">
                    <Form :model="formItem" :label-width="100" ref="formItem" inline>
                        <Row>
                            <Col span="12">
                                <FormItem label="入库单号" prop="tranCode">
                                    <Input v-model="tranCode" disabled placeholder=" " style="width: 360px"></Input>
                                </FormItem>
                            </Col>
                            <Col span="12">
                                <FormItem label="订单号" prop="sourceOrderId">
                                    <Input v-model="sourceOrderId" disabled placeholder=" " style="width: 360px"></Input>
                                </FormItem>
                            </Col>
                            <Col span="12">
                                <FormItem label="入库仓库" prop="warehouseName">
                                    <Input v-model="warehouseName" disabled placeholder=" " style="width: 360px"></Input>
                                </FormItem>
                            </Col>
                            <Col span="12">
                                <FormItem label="操作人" prop="name">
                                    <Input v-model="userInfo.name" disabled placeholder=" " style="width: 360px"></Input>
                                </FormItem>
                            </Col>
                        </Row>
                    </Form>
                    <div class="commListDiv">
                        <Table size="small" ref="subTableRef" :columns="subTableColumn" :data="subTableData" @on-selection-change="selected => this.subTableSelect = selected">
                            <template slot-scope="{ index }" slot="actualBatchNum">
                                <Input v-model="subTableData[index].actualBatchNum" placeholder=" " style="width: 160px"></Input>
                            </template>
                        </Table>
                        <Upload ref="upload" class="inlinebox" multiple :show-upload-list="false" :action="actionUrl" :headers="headers" :on-progress="progress" :on-success=" (response, file, fileList) => { return imgFileUploadSuccess(response, file, fileList); } " :format="['jpg', 'jpeg', 'png']" :max-size="18432" :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize">
                            <div class="file_pics">
                                <Button type="info" style="margin: 15px 0">上传附件</Button>
                                <div v-if="fileUrls" class="demo-upload-list">
                                    <div v-for="(item,index) in fileUrls" :key="index" style="position: relative;width: 400px;margin-right: 5px;">
                                        <img :src="item.imageUrl" loading="lazy" width="100%" height="100%"/>
                                        <div class="demo-upload-list-cover" @click.stop>
                                            <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleView(index)"></Icon>
                                            <Icon type="ios-trash-outline" size="20" color="#fff" @click.stop="handleRemove(index)"></Icon>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </Upload>
                    </div>
                </div>
            </div>

            <div style="position: absolute; z-index: 99; right: 20px; bottom: -30px; text-align: right; margin: 30px 0">
                <Button type="primary"
                        :loading="loadStatus"
                        @click="debounce(submitPop)">提交</Button>
                <Button type="default" style="margin-left: 10px"
                        @click="closeAddModal">取消</Button>
            </div>
<!--        </div>-->
        <Modal v-model="visible" width="99" reset-drag-position draggable sticky title="预览" footer-hide class-name="vertical-center-modal">
            <img class="preview" v-if="visibleImg" :src="visibleImg" @click="toTarget(visibleImg)" title="点击查看原图" alt="">
        </Modal>
    </Modal>
</template>

<script>
import API from "@/api/ospSpare/stockOutManage/serviceStock";

export default {
    name: "addEdit",
    data() {
        return {
            orderId: '',
            isAdd: true,
            loadStatus: false,
            allAmount: 0,
            codeList: [],
            visible: false, //预览图片
            visibleImg: '',
            modelEdit: false,
            modelTitle: '编辑',
            formItem: {
                tranCode: '',
                sourceOrderCode: '',
                warehouseName: '',
                updateBy: ''
            },
            subTableSelect: [],
            // orderCodeList: [
            //     {
            //         value: 'ceshi111',
            //         id: 1,
            //         label: '测试名称111',
            //         materialDesc: 'materialDesc',
            //         branchName: 'branchName',
            //         supplierName: 'supplierName',
            //         materialPrice: 100
            //     }
            // ],
            subTableData: [],
            subTableColumn: [
                {
                    title: '备件编码',
                    key: 'materialCode',
                    width: 120,
                    align: 'center'
                },
                {
                    title: '备件描述',
                    key: 'materialDesc',
                    width: 160,
                    align: 'center'
                },
                {
                    title: '单位',
                    key: 'materialUnit',
                    width: 180,
                    align: 'center'
                },
                {
                    title: '单价',
                    key: 'materialPrice',
                    width: 120,
                    align: 'center'
                },
                {
                    title: '应入库数量',
                    key: 'orderAmnt',
                    align: 'center'
                },
                {
                    title: '入库数量',
                    key: 'getAmnt',
                    align: 'center'
                },
                {
                    title: '组件sn编码',
                    key: 'actualBatchNum',
                    slot: 'actualBatchNum',
                    align: 'center',
                    width: 200
                }

            ],
            fileUrls:[],
            modelEditAdd: false,
            actionUrl: `${process.env.VUE_APP_BASE_API}/merchant/oss/image/upload.do`,
            headers: { Authorization: `Bearer ${JSON.parse(window.localStorage.getItem('logins')).access_token}` },
            userInfo: {},
            dataSource:{},
            tranCode: '',
            sourceOrderId: '',
            warehouseName: ''
        }
    },
    mounted() {
        this.userInfo = JSON.parse(window.localStorage.getItem('userInfo'))
    },
    watch: {
        modelEdit(val) {
            if (val) {
                this.getDetail();
            }
        }
    },
    methods: {
        getDetail(){
            API.orderDetail({id: this.orderId}).then(res=>{
                if(res.data.success){
                    this.subTableData = res.data.result?.spTranSnDetail || []
                    this.tranCode = res.data.result?.tranCode || ''
                    this.sourceOrderId = res.data.result?.sourceOrderId || ''
                    this.warehouseName = res.data.result?.warehouseName || ''
                }
            })
        },
        changeSupplier(val){
            if(!val) return false;
            let item = this.supplierList.find(e=> e.name===val)
            this.formItem.stationNetCode = item.mode
            this.formItem.stationNetMemberId = item.memberId
        },
        closeAddModal(){
            this.modelEdit = false
        },
        toTarget(url) {
            window.open(url)
        },
        handleView (item) {
            this.visible = true;
            this.visibleImg = item
        },
        handleRemove (index, inx, tag) {
            this.fileUrls.splice(index,1)
        },
        progress() {
            this.$Message.loading({
                content: '上传中...',
                duration: 0
            });
        },
        imgFileUploadSuccess(response, file, fileList) {
            let that = this;
            // this.list1.push(response.result[0].imageUrl);
            this.fileUrls.push(response.result[0])
            setTimeout(() => {
                that.$Message.destroy();
            }, 500);
        },
        handleFormatError(file) {
            this.$Notice.warning({
                title: '上传格式不正确',
                desc: '请上传正确的格式文件'
            });
        },

        handleMaxSize (file) {
            this.$Notice.warning({
                title: '上传文件过大',
                desc: file.name + '文件太大，请压缩后上传.'
            });
        },
        // 查询明细信息
        queryList() {

        },
        resetForm(){
            for(let i in this.formItem){
                this.$set(this.formItem, i, '')
            }
            // this.list1 = []
            this.subTableData = []
        },
        submitPop() {
            /*for(let i=0;i<this.subTableData.length;i++){
                const item = this.subTableData[i]
                if(!item.actualBatchNum){
                    this.$Message.warning('请输入组件sn编码');
                    return
                }
            }*/
            let arr=[]
            if(this.fileUrls.length>0){
                this.fileUrls.forEach(item=>{
                    arr.push({
                        'fileUrl':item.imageUrl
                    })
                })
            }
            let params={
                'id':this.orderId,
                'sourceOrderCode':this.dataSource.sourceOrderCode,
                'spTranSnDetail':this.subTableData,
                'fileList':arr
            }

            this.loadStatus = true
            API.lendOrderPutInApi(params).then(res => {
                this.loadStatus = false
                if(res.data.success){
                    this.$Message.success(res.data.result);
                    this.resetForm()
                    this.modelEdit = false
                    this.$emit('getPageList')
                }else{
                    this.$Message.warning(res.data.error);
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped>
@import '@/style/_cus_reg.scss';
@import '@/style/_cus_list.scss';
@import '@/style/_finish.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
::v-deep .ivu-table:before{
    display: none!important;
}
::v-deep .ivu-table-wrapper{
    overflow: initial!important;
}
.demo-upload-list {
    @extend .flex-row-center-center;
    position: relative;
    width: 100px;
    height: 100px;
    overflow: hidden;

    &:hover .demo-upload-list-cover {
        display: flex;
    }
}
.demo-upload-list-cover {
    @extend.flex-row-center-around;
    display: none;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,.6);
    z-index: 999;
    i{
        &:hover{
            cursor: pointer;
        }
    }
}
</style>
