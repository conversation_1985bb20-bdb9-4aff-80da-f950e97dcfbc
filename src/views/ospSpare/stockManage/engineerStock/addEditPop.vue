<template>
    <Modal width="1200" v-model="modelEdit" draggable sticky scrollable :title="modelTitle" :footer-hide="true"
           :mask-closable="false">
        <div style="position: relative; width: 100%; height: 60vh;">
            <div style="padding-bottom: 40px; width: 100%; height: 60vh; overflow-y: auto">
                <Form :model="formItem" :label-width="100" ref="formItem" inline>
                    <FormItem label="申请服务商">
                        <Input type="text" maxlength="15" v-model="formItem.warehouseName" clearable placeholder="申请仓库"
                               style="width: 360px" disabled />
                    </FormItem>
                    <FormItem label="收货地址" prop="duty">
                        <Input type="text" maxlength="15" v-model="formItem.address" clearable placeholder="调出仓库"
                               style="width: 360px" />
                    </FormItem>
                </Form>
                <div class="list_but" style="margin-bottom: 10px">
                    <div class="condition">
                        <Button @click="modelEditAdd = true" size="small" type="primary">添加</Button>
                        <Button style="margin-left: 6px" @click="del" size="small" type="error" :disabled="!subTableSelect.length">删除</Button>
                    </div>
                </div>
                <div class="commListDiv">
                    <Table size="small" border ref="subTableRef" :columns="subTableColumn" :data="subTableData" @on-selection-change="selected => this.subTableSelect = selected">
                        <template slot-scope="{ index }" slot="submitNum">
                            <!--                            {{ subTableData[index].submitNum }}-->
                            <Input v-model="subTableData[index].submitNum" style="text-align: center" @on-change="tableInputChange" />
                        </template>
                    </Table>
                </div>
                <!-- 添加 -->
                <Modal width="1000" v-model="modelEditAdd" draggable sticky scrollable :title="modelTitle" :footer-hide="true" :styles="{ top: 'calc(100px + 4vh)' }"
                       :mask-closable="false">
                    <div style="position: relative; width: 100%; height: 50vh;">
                        <div style="padding-bottom: 40px; width: 100%; height: 50vh; overflow-y: auto">
                            <Form :model="addDetailQuery" :label-width="100" ref="formItem" inline>
                                <FormItem label="备件编码">
                                    <Input type="text" maxlength="15" v-model="addDetailQuery.materialCode" clearable placeholder="备件编码"
                                           style="width: 160px" />
                                </FormItem>
                                <FormItem label="备件描述" prop="duty">
                                    <Input type="text" maxlength="15" v-model="addDetailQuery.materialDesc" clearable placeholder="备件描述"
                                           style="width: 160px" />
                                </FormItem>
                            </Form>
                            <div class="list_but" style="margin-bottom: 10px">
                                <div class="condition">
                                    <Button @click="debounce(queryList)" size="small" type="primary">查询</Button>
                                    <Button @click="debounce(emptyList)" style="margin-left: 6px" size="small">重置</Button>
                                </div>
                            </div>
                            <div class="commListDiv">
                                <Table size="small" border ref="selectTableRef" :columns="selectTableColumn" :data="selectTableData" @on-selection-change="selected => this.selectTableSelect = selected" />
                            </div>
                        </div>
                        <div style="position: absolute; z-index: 100; right: 20px; bottom: -30px; text-align: right; margin: 30px 0">
                            <Button type="primary"
                                    :disabled="!selectTableSelect.length"
                                    @click="debounce(submitSelect)">保存</Button>
                            <Button type="default" style="margin-left: 10px"
                                    @click="modelEditAdd = false">取消</Button>
                        </div>
                    </div>
                </Modal>
            </div>
            <div style="position: absolute; z-index: 99; right: 20px; bottom: -30px; text-align: right; margin: 30px 0">
                <Button type="primary"
                        :disabled="!subTableSelect.length"
                        @click="debounce(submitPop)">保存</Button>
                <Button type="default" style="margin-left: 10px"
                        @click="modelEdit = false">取消</Button>
            </div>
        </div>
    </Modal>
</template>

<script>
import API from "@/api/ospSpare/stockOutManage/supplierStockOut";

export default {
    name: "engineerCloseStock",
    data() {
        return {
            modelEdit: false,
            modelTitle: '关单入库',
            formItem: {
                warehouseName: '',
                address: ''
            },
            subTableSelect: [],
            subTableData: [
                {
                    orderCode: '备件编码',
                    orderDesc: '备件描述',
                    branchName: '供货商',
                    unit: '单位',
                    unitPrice: '单价',
                    submitNum: '12'
                },
                {orderCode: '444',
                    submitNum: '17'}
            ],
            subTableColumn: [
                {
                    type: 'selection',
                    width: 60,
                    title: '选择',
                    align: 'center'
                },
                {
                    title: '备件编码',
                    key: 'orderCode',
                    align: 'center'
                },
                {
                    title: '备件描述',
                    key: 'orderDesc',
                    align: 'center'
                },
                {
                    title: '供货商',
                    key: 'branchName',
                    align: 'center'
                },
                {
                    title: '单位',
                    key: 'unit',
                    align: 'center'
                },
                {
                    title: '单价',
                    key: 'unitPrice',
                    align: 'center'
                },
                {
                    title: '申请数量',
                    key: 'submitNum',
                    slot: 'submitNum',
                    align: 'center'
                }
            ],
            modelEditAdd: false,
            addDetailQuery: {
                materialCode: '',
                materialDesc: ''
            },
            selectTableSelect: [],
            selectTableColumn: [
                {
                    type: 'selection',
                    width: 60,
                    title: '选择',
                    align: 'center'
                },
                {
                    title: '备件编码',
                    key: 'materialCode',
                    align: 'center'
                },
                {
                    title: '备件描述',
                    key: 'materialDesc',
                    align: 'center'
                },
                {
                    title: '生产状态',
                    key: 'productStatus',
                    align: 'center'
                },
                {
                    title: '备注信息',
                    key: 'remarks',
                    align: 'center'
                }
            ],
            selectTableData: [
                {
                    materialCode: '123123',
                    materialDesc: 'qweqwe',
                    productStatus: '订单',
                    remarks: '史蒂夫',
                },
                { materialCode: '111' },
                { materialCode: '222' }
            ]
        }
    },
    methods: {
        del() {
            this.$Message.success('删除!');
            this.subTableData = this.subTableData.filter(item => !this.subTableSelect.some(v => v.orderCode === item.orderCode))
            // console.log(this.subTableSelect.filter(item => this.subTableData.some(v => v.orderCode === item.orderCode)))
        },
        // 查询明细信息
        queryList() {

        },
        // 清空明细信息查询条件
        emptyList() {
            this.addDetailQuery = {
                materialCode: '',
                materialDesc: ''
            }
        },
        tableInputChange(e) {
            this.$refs.subTableRef.selectAll(false);
        },
        submitSelect() {
            // 重复选中的订单
            const reqOrder = this.selectTableSelect.filter(item => this.subTableData.some(v => v.orderCode === item.orderCode))
            // 没有重复 可追加上的订单
            const addOrder = this.selectTableSelect.filter(item => !this.subTableData.some(v => v.orderCode === item.orderCode))
            // 重复订单提示标记
            let reqMsg = []
            reqOrder.forEach(item => {
                reqMsg.push(item.orderCode)
            })
            reqMsg = reqMsg.toString()
            if (addOrder.length && !reqOrder.length) {
                this.subTableData.push(...addOrder)
                this.modelEditAdd = false
                this.$refs.selectTableRef.selectAll(false);
                this.$Message.success('添加成功!');
            } else if (addOrder.length && reqOrder.length) {
                this.subTableData.push(...addOrder)
                this.modelEditAdd = false
                this.$refs.selectTableRef.selectAll(false);
                this.$Message.success('添加成功!去除重复订单：' + reqMsg);
            } else if (!addOrder.length && reqOrder.length) {
                this.$Message.error
                ('当前选中订单已添加：' + reqMsg);
            }
        },
        submitPop() {
            console.log(this.subTableSelect)
        },
        /*获取详情*/
        getInfo(){
            // API.orderDetail(this.dataSource.id).then(res => {
            //     this.formItem = res.data?.result
            //     this.subTableData = res.data?.result?.spTranDetail||[]
            // })
        }
    }
}
</script>

<style lang="scss" scoped>
@import 'style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
