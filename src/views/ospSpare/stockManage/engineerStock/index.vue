<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏备件管理</Breadcrumb-item>
            <Breadcrumb-item to="/ospSpare/">入库管理</Breadcrumb-item>
            <Breadcrumb-item>工程师备件入库</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">入库单号：</p>
                    <Input v-model="searchObj.tranCode" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">工单号：</p>
                    <Input v-model="searchObj.referId" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">服务商：</p>
                    <Input v-model="searchObj.warehouseName" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">入库状态：</p>
                    <Select v-model="searchObj.ifClose" placeholder="请选择..." clearable style="width: 200px">
                        <Option :value="0" :key="0">未入库</Option>
                        <Option :value="1" :key="1">已入库</Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">入库类型：</p>
                    <Select v-model="searchObj.tranType" clearable style="width: 200px">
                        <Option :value="3" :key="3">新件入库</Option>
                        <Option :value="4" :key="4">旧件入库</Option>
                        <Option :value="5" :key="4">组件入库</Option>
                        <Option :value="9" :key="9">性能件入库</Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">入库时间：</p>
                    <DatePicker ref="formDateSh" :value="submintDate" @on-change="
                        (data) => {
                          return selectDate(data, 'tranDate');
                        }
                      " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择" style="width: 200px ">
                    </DatePicker>
                </div>
                <div class="condition">
                    <p class="condition_p">创建时间：</p>
                    <DatePicker ref="formDateSh" :value="submintDate2" @on-change="
                        (data) => {
                          return selectDate2(data, 'createDate');
                        }
                      " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择" style="width: 200px ">
                    </DatePicker>
                </div>
            </div>
            <div class="list_but" style="display: flex; justify-content: space-between; width: 100%;">
                <div class="condition">
                    <Button @click="closeOrderStock" type="success" :disabled="this.tableSelect.length!='1'">关单入库</Button>
                </div>
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                </div>
            </div>
            <div class="commListDiv">
                <Table size="small" border ref="tableRef" :columns="tableColumn" :data="commList" @on-selection-change="selected => this.tableSelect = selected">
                    <template slot-scope="{ index }" slot="sn">
                        {{ index + 1 }}
                    </template>
<!--                    入库类型 -->
                    <template slot-scope="{ index }" slot="tranType">
                        <span v-if="commList[index].tranType=='3'">新件入库</span>
                        <span v-if="commList[index].tranType=='4'">旧件入库</span>
                        <span v-if="commList[index].tranType=='5'">组件入库</span>
                        <span v-if="commList[index].tranType=='9'">性能件入库</span>
                    </template>
<!--                    入库状态-->
                    <template slot-scope="{ index }" slot="ifClose">
                        <span v-if="commList[index].ifClose=='0'">未入库</span>
                        <span v-if="commList[index].ifClose=='1'">已入库</span>
                    </template>
                    <template slot-scope="{ index }" slot="option">
                        <Button type="info" ghost size="small" @click="openDetail(commList[index])" style="margin: 3px">详情</Button>
                    </template>
                </Table>
            </div>
            <Page style="margin: 20px" @on-change="changeCurrent" :current="searchObj.page"
                  :total="totalElements" show-total show-elevator />
        </div>

        <detail ref="detailsRef" />
    </div>
</template>

<script>
import _data from "@/views/ospSpare/_edata"; // 数据字典
import API from "@/api/ospSpare/stockOutManage/engineerStock";
import detail from './details'
export default {
    name: "serviceProviderOrder",
    components: { detail },
    data() {
        return {
            searchObj: {
                submintDateRange: "",
                orderCode: "",
                woId: "",
                orderType: "",
                orderStatus: "",
                branchName: "",
                pageNum: 1,
                pageSize: 10
            },
            formObj: {},
            tableColumn: [
                { type: 'selection', minWidth: 60, title: '选择', align: 'center' },
                { title: '序号',  minWidth: 80, align: 'center', slot: 'sn' },
                { title: '入库单号', minWidth: 200, key: 'tranCode', align: 'center' },
                { title: '工单号', minWidth: 180, key: 'referId', align: 'center' },
                { title: '中心名称', minWidth: 100, key: 'centerName', align: 'center' },
                { title: '服务商名称', minWidth: 180, key: 'warehouseName', align: 'center' },
                { title: '入库类型', minWidth: 100, key: 'tranType', align: 'center', slot: 'tranType'  },
                { title: '入库状态', minWidth: 100, key: 'ifClose', align: 'center',slot: 'ifClose' },
                { title: '入库时间', minWidth: 160, key: 'tranDate', align: 'center' },
                { title: '备注', minWidth: 160, key: 'remarks', align: 'center' },
                { title: '创建时间', minWidth: 160, key: 'createDate', align: 'center' },
                { title: '操作', minWidth: 120, align: 'center', fixed: 'right', slot: 'option' }
            ],
            tableSelect:[],
            staffList: [],
            totalElements: 0,
            submintDate: [],
            submintDate2: [],
            status: _data.status,
            orderStatusList: _data.orderStatusList,
            sourceArr: _data.SourceArr,
            faultCodeTypeList: _data.faultCodeTypeList,
            commList: [],
            title: "一键派工",
            modelEdit: false,
            modelTitle: '新增',
            formItem: {
                aaa: '',
                bbb: ''
            },
            modelEditAdd: false
        }
    },
    mounted() {
        this.getList()
    },
    methods: {
        // 关单入库
        closeOrderStock() {
            this.$message.confirm('是否确定关单入库?', '提示', {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                let params={'id':this.tableSelect[0].id}
                API.closeTranSnIn(params).then(res=>{
                    if(res.data.success){
                        this.$Message.success('关单入库成功!');
                        setTimeout(() => {
                            this.$Message.destroy();
                            this.getList()
                        }, 1000)
                    }else{
                        this.$Message.error(res.data.error);
                    }
                }).catch(err=>{
                    this.$Message.error(err.data.error);
                })
            })
        },
        // 打开详情
        openDetail(row) {
            this.$refs.detailsRef.orderId = row.id
            this.$refs.detailsRef.show = true
        },
        // 查询按钮
        queryList() {
            this.searchObj.pageNum = 1;
            this.getList();
        },

        // 重置按钮
        emptyList() {
            this.searchObj = {
                submintDateRange: "",
                orderCode: "",
                woId: "",
                orderType: "",
                orderStatus: "",
                branchName: "",
                pageNum: 1,
                pageSize: 10
            };
            this.submintDate = [];
            this.submintDate2 = [];
            this.getList()
        },
        // 选择日期
        selectDate(date, refObj) {
            this.submintDate = date;
            this.searchObj[refObj + "Start"] = date[0];
            this.searchObj[refObj + "End"] = date[1];
        },
        // 选择日期
        selectDate2(date, refObj) {
            this.submintDate2 = date;
            this.searchObj[refObj + "Start"] = date[0];
            this.searchObj[refObj + "End"] = date[1];
        },
        // 获取工单列表
        getList() {
            let datas = this.searchObj;
            datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
            if (datas.tranDateEnd) datas.tranDateEnd = datas.tranDateEnd + ' 23:59:59'
            if (datas.tranDateStart) datas.tranDateStart = datas.tranDateStart + ' 00:00:00'
            if (datas.createDateEnd) datas.createDateEnd = datas.createDateEnd + ' 23:59:59'
            if (datas.createDateStart) datas.createDateStart = datas.createDateStart + ' 00:00:00'
            delete datas.pageNum
            delete datas.pageSize
            const page = { page: this.searchObj.pageNum, rows: this.searchObj.pageSize }
            // 将所有选中项清空
            this.$refs.tableRef.selectAll(false);
            API.getList(page, datas).then(res => {
                if(res.data.success){
                    this.commList = res.data.result.content
                    this.totalElements = res.data.result.totalElements
                }else{
                    this.commList=[]
                }
            }).catch(err=>{
                this.commList=[]
            })

            /* TODO 暂时没有接口 */
            // setTimeout(() => {
            //     this.$Message.destroy();
            //     this.commList = [
            //         {
            //             orderCode: '312123',
            //             id: '1',
            //             orderType: '类型',
            //             orderStatus: '状态',
            //             centerName: '中心',
            //             branchName: '服务商',
            //             suptBranchName: '仓库',
            //             submitDate: '申请',
            //             sendDate: '发货',
            //             sendCode: '出库',
            //             cancelDate: '取消',
            //             cancelBy: '人',
            //             ifClose: '0',
            //             cancelDesc: '原因',
            //             updateDate: '修改'
            //         }
            //     ]
            //     this.totalElements = 99
            // }, 1000)

        },
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.pageNum = curr;
            this.getList();
        },
        jumpto(path, id, count) {
            this.$router.push({
                path,
                query: { id, count },
            });
        }
    }
}
</script>

<style lang="scss" scoped>
@import 'style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
