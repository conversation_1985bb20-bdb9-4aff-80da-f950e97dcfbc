<template>
    <Modal width="1000" v-model="modelEdit" draggable sticky scrollable title="编辑" :footer-hide="true"
           :mask-closable="false">
        <div style="position: relative; width: 100%; height: 60vh;">
            <div style="padding-bottom: 40px; width: 100%; height: 60vh; overflow-y: auto">
                <Form :model="formItem" :rules="ruleValidate" :label-width="100" ref="form" >
                    <FormItem label="审批意见">
                        <Input type="text" maxlength="15" v-model="formItem.remarks" clearable
                               style="width: 100%" disabled />
                    </FormItem>
                    <FormItem label="申诉原因" prop="appealReason">
                        <Input type="textarea" v-model="formItem.appealReason" clearable style="width: 100%" />
                    </FormItem>
                    <FormItem label="" prop="appealReason">
                        <Upload ref="upload" class="inlinebox" multiple :show-upload-list="false" :action="actionUrl" :headers="headers" :on-progress="progress" :on-success=" (response, file, fileList) => { return imgFileUploadSuccess(response, file, fileList); } " :format="['jpg', 'jpeg', 'png']" :max-size="40960" :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize">
                            <div class="file_pics">
                                <Button type="info" style="margin: 15px 0">上传附件</Button>
                                <div v-if="fileUrls" class="demo-upload-list">
                                    <div v-for="(item,index) in fileUrls" :key="index" style="position: relative;width: 400px;margin-right: 5px;">
                                        <img :src="item.imageUrl" loading="lazy" width="100%" height="100%"/>
                                        <div class="demo-upload-list-cover" @click.stop>
                                            <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleView(index)"></Icon>
                                            <Icon type="ios-trash-outline" size="20" color="#fff" @click.stop="handleRemove(index)"></Icon>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </Upload>
                    </FormItem>

                </Form>
            </div>
            <div style="position: absolute; z-index: 99; right: 20px; bottom: -30px; text-align: right; margin: 30px 0">
                <Button type="primary"
                        @click="debounce(submitPop)">提交</Button>
                <Button type="default" style="margin-left: 10px"
                        @click="modelEdit = false">取消</Button>
            </div>
            <Modal v-model="visible" width="70%" reset-drag-position draggable sticky title="预览" footer-hide class-name="vertical-center-modal">
                <img class="preview" width="500px" v-if="visibleImg" :src="visibleImg" @click="toTarget(visibleImg)" title="点击查看原图">
            </Modal>
        </div>
    </Modal>
</template>

<script>
import API from "@/api/ospSpare/stockManage/damageList";
export default {
    name: "addEdit",
    props: {
        popType: {
            type: String,
            default: 'add'
        }
    },
    watch: {
        modelEdit(val) {
            if (val) {
                this.formItem.remarks=this.dataSource.remarks
                this.fileUrls=[]
            }else{
                this.resetForm()
            }
        }
    },
    data() {
        return {
            modelEdit: false,
            visible: false,
            modelTitle: '新增',
            formItem: {
                remarks: undefined,
                appealReason: undefined,
            },
            modelEditAdd: false,
            dataSource:{},
            ruleValidate: {
                appealReason: [
                    { required: true, message: '请输入申诉原因', trigger: 'blur' }
                ]
            },
            fileUrls:[],
            actionUrl: `${process.env.VUE_APP_BASE_API}/merchant/oss/image/upload.do`,
            headers: { Authorization: `Bearer ${JSON.parse(window.localStorage.getItem('logins')).access_token}` },
            visibleImg: '',
        }
    },
    methods: {
        progress() {
            this.$Message.loading({
                content: '上传中...',
                duration: 0
            });
        },
        imgFileUploadSuccess(response, file, fileList) {
            let that = this;
            // this.fileUrls = response.result;
            this.fileUrls.push(response.result[0])
            setTimeout(() => {
                that.$Message.destroy();
            }, 500);
        },
        handleFormatError(file) {
            this.$Notice.warning({
                title: '上传格式不正确',
                desc: '请上传正确的格式文件'
            });
        },

        handleMaxSize (file) {
            this.$Notice.warning({
                title: '上传文件过大',
                desc: file.name + '文件太大，请压缩后上传.'
            });
        },
        handleView (index, inx, tag) {
            this.visible = true;
            this.visibleImg = this.fileUrls[index].imageUrl;
            this.visibleName = '附件照片';
        },
        handleRemove (index, inx, tag) {
            this.fileUrls.splice(index,1)
        },
        toTarget(url) {
            window.open(url)
        },
        submitPop() {
            this.$refs.form.validate((valid) => {
                if (valid) {
                    let arr=[]
                    if(this.fileUrls.length>0){
                        this.fileUrls.forEach(item=>{
                            arr.push({
                                'fileUrl':item.imageUrl
                            })
                        })
                    }
                    let params={
                        'id':this.dataSource.id,
                        'appealReason':this.formItem.appealReason,
                        'fileList':arr
                    }
                    API.update(params).then(res=>{
                        if(res.data.success){
                            this.$Message.success('编辑成功');
                            setTimeout(() => {
                                this.$Message.destroy();
                                this.$emit('getList');
                            }, 2000)
                            this.modelEdit = false;
                            this.resetForm();
                        }else{
                            this.$Message.error(res.data.error);
                        }
                    }).catch(err=>{
                        this.$Message.error(err.data.error);
                    })
                }
            })

        },
        // 重置表单
        resetForm(){
            this.subTableData=[]
            this.formItem= {
                remarks: undefined,
                appealReason: undefined,
            }
            this.fileUrls=[]
        },
    }
}
</script>

<style lang="scss" scoped>
@import '@/style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
.demo-upload-list {
    @extend .flex-row-center-center;
    position: relative;
    width: 50%;
    height: 100%;
    overflow: hidden;

    &:hover .demo-upload-list-cover {
        display: flex;
    }
}
.demo-upload-list-cover {
    @extend.flex-row-center-around;
    display: none;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,.6);
    z-index: 999;
    i{
        &:hover{
            cursor: pointer;
        }
    }
}
</style>
