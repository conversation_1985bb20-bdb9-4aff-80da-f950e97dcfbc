<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏备件管理</Breadcrumb-item>
            <Breadcrumb-item to="/ospSpare/">订单管理</Breadcrumb-item>
            <Breadcrumb-item>订单执行查看</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">提交时间：</p>
                    <DatePicker ref="formDateSh" :value="submitDate" @on-change="
                        (data) => {
                          return selectDate(data, 'submitDate');
                        }
                      " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择" style="width: 240px ">
                    </DatePicker>
                </div>
                <div class="condition">
                    <p class="condition_p">订单号：</p>
                    <Input v-model="searchObj.orderCode" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">工单号：</p>
                    <Input v-model="searchObj.woId" placeholder="请输入工单号" clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">申请仓库：</p>
                    <Select v-model="searchObj.warehouseId" filterable clearable style="width: 200px">
                        <Option v-for="item in warehouseList" :value="item.id" :key="item.id">{{ item.warehouseName }}</Option>
                    </Select>
                </div>
            </div>
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">订单类型：</p>
                    <Select v-model="searchObj.orderType" placeholder="请选择订单类型" clearable style="width: 200px">
                        <Option v-for="item in orderTypeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">订单状态：</p>
                    <Select v-model="searchObj.orderStatusList" multiple clearable style="width: 200px">
                        <Option v-for="item in orderStatusList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">备件编码：</p>
                    <Input v-model="searchObj.materialCode" placeholder="备件编码" clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">备件描述：</p>
                    <Input v-model="searchObj.materialDesc" placeholder="备件描述" clearable style="width: 200px" />
                </div>
            </div>
            <div class="list_but" style="display: flex; justify-content: space-between; width: 100%;">
                <div class="condition">
                    <Button @click="mateOrder" type="primary" :disabled="tableSelect.length !== 1 || (tableSelect[0].orderStatus !== 'WAIT' && tableSelect[0].orderStatus !== 'send')">匹配订单</Button>
                    <Button
                        @click="cancel"
                        type="primary"
                        :disabled="tableSelect.length !== 1 || tableSelect[0].suptWarehouseId !== this.suptWarehouseId || (tableSelect[0].orderStatus !== 'send' && tableSelect[0].orderStatus !== 'WAIT')"
                    >
                        取消订单
                    </Button>
                    <Button @click="exportFn" type="success">导出</Button>
                </div>
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                </div>
            </div>
            <div class="commListDiv">
                <Table size="small" border ref="tableRef" :columns="tableColumn" :data="commList" @on-selection-change="selected => this.tableSelect = selected">
                    <template slot-scope="{ index }" slot="sn">
                        {{ index + 1 }}
                    </template>
                    <template slot-scope="{ index }" slot="orderType">
                        {{ orderTypeMap[commList[index].orderType] }}
                        <!--                        <Input v-model="subTableData[index].submitNum" style="text-align: center" @on-change="tableInputChange" />-->
                    </template>
                    <template slot-scope="{ index }" slot="orderStatus">
                        {{ orderStatusMap[commList[index].orderStatus] }}
                    </template>
                    <template slot-scope="{ index }" slot="option">
                        <Button type="info" ghost size="small" @click="openFJDetail(commList[index])" style="margin: 3px">附件查看</Button>
                    </template>
                </Table>
            </div>
            <Page style="margin: 20px" @on-change="changeCurrent" :current="searchObj.page"
                  :total="totalElements" show-total show-elevator />
        </div>
        <!-- 取消订单 -->
        <Modal width="600" v-model="cancelShow" draggable sticky scrollable title="订单取消" :footer-hide="true"
               :mask-closable="false">
            <Form :label-width="80">
                <FormItem label="取消备注">
                    <Input v-model="cancelRemark" type="textarea" placeholder="请输入取消备注" clearable style="width: 400px" />
                </FormItem>
            </Form>
            <div style="text-align: right;">
                <Button type="primary"
                        @click="debounce(sub_cancel)">提交</Button>
                <Button type="default" style="margin-left: 10px"
                        @click="cancelShow = false">取消</Button>
            </div>
        </Modal>
        <detail ref="detailsRef" />
        <!--        附件查看  -->
        <Modal v-model="filUploadeVisible" width="800px" reset-drag-position footer-hide draggable sticky title="附件查看" class-name="vertical-center-modal">
            <Form class="form_deep" :model="orderWoForm"  :label-width="115" ref="orderWoForm" inline>
                <div class="list_but">
                    <FormItem label="损坏件" prop="damagedPart">
                        <div class="demo-upload-list" >
                            <img :src="orderWoForm?.damagedPart" loading="lazy" width="100%"  alt=""/>
                            <div class="demo-upload-list-cover" @click.stop>
                                <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleOrderWoView('1')"></Icon>
                            </div>
                        </div>

                    </FormItem>
                    <FormItem label="并网箱" prop="parallelMeshBox">
                        <div class="demo-upload-list" >
                            <img :src="orderWoForm?.parallelMeshBox" loading="lazy" width="100%"  alt=""/>
                            <div class="demo-upload-list-cover" @click.stop>
                                <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleOrderWoView('2')"></Icon>
                            </div>
                        </div>

                    </FormItem>
                    <FormItem label="并网箱铭牌" prop="Nameplate">
                        <div class="demo-upload-list" >
                            <img :src="orderWoForm?.Nameplate" loading="lazy" width="100%"  alt=""/>
                            <div class="demo-upload-list-cover" @click.stop>
                                <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleOrderWoView('3')"></Icon>
                            </div>
                        </div>

                    </FormItem>
                </div>
            </Form>
        </Modal>
        <!--        附件放大查看  -->
        <Modal v-model="visible" width="800px" reset-drag-position draggable sticky title="预览" footer-hide class-name="vertical-center-modal">
            <img class="preview" v-if="visibleImg" width="700" :src="visibleImg" @click="toTarget(visibleImg)" title="点击查看原图" alt="">
        </Modal>
    </div>
</template>

<script>
import _data from "@/views/ospSpare/_edata"; // 数据字典
import API from "@/api/ospSpare/orderManage/orderExecutionView";
import OSP from "@/api/osp";
import detail from './details'
import dict from "@/api/ospSpare";
import { removeClass } from "@/components/message-box/js/dom";
export default {
    name: "serviceProviderOrder",
    components: { detail },
    data() {
        return {
            searchObj: {
                submitDateStart: "",
                submitDateEnd: "",
                orderCode: "",
                woId: "",
                warehouseId: '',
                orderType: "",
                orderStatusList: ["WAIT","send",],
                materialCode: "",
                materialDesc: "",
                pageNum: 1,
                pageSize: 10
            },
            totalElements: 0,
            submitDate: [],
            orderTypeList: _data.orderType,
            orderStatusList: _data.orderStatus,
            warehouseList: [],
            orderTypeMap: {},
            orderStatusMap: {},
            commList: [],
            title: "一键派工",
            modelEdit: false,
            visible: false,
            filUploadeVisible: false,
            orderWoForm:{
                damagedPart:null,
                parallelMeshBox:null,
                Nameplate:null,
            },
            queryPage: { // 查询条件 No页数 Size每页大小
                page: 1,
                rows: 10
            },
            visibleImg: '',
            modelTitle: '新增',
            tableColumn: [
                { type: 'selection', minWidth: 60, title: '选择', align: 'center' },
                { title: '序号', minWidth: 80, align: 'center', slot: 'sn' },
                { title: '订单号', minWidth: 180, key: 'orderCode', align: 'center' },
                { title: '工单号', minWidth: 180, key: 'woId', align: 'center' },
                { title: '订单类型', minWidth: 180, key: 'orderType', align: 'center', slot: 'orderType' },
                { title: '订单状态', minWidth: 180, key: 'orderStatus', align: 'center', slot: 'orderStatus' },
                { title: '备件编号', minWidth: 180, key: 'materialCode', align: 'center' },
                { title: '备件描述', minWidth: 180, key: 'materialDesc', align: 'center' },
                { title: '申请数量', minWidth: 180, key: 'requireAmount', align: 'center' },
                { title: '发货数量', minWidth: 180, key: 'confirmSendAmount', align: 'center' },
                { title: '匹配数量', minWidth: 180, key: 'deelAmount', align: 'center' },
                { title: '取消数量', minWidth: 180, key: 'cancelAmount', align: 'center' },
                { title: '中心名称', minWidth: 180, key: 'centerName', align: 'center' },
                { title: '申请仓库', minWidth: 180, key: 'branchName', align: 'center' },
                { title: '收货地址', minWidth: 180, key: 'addressDetail', align: 'center' },
                { title: '发货仓库', minWidth: 180, key: 'suptBranchName', align: 'center' },
                { title: '提交时间', minWidth: 180, key: 'submitDate', align: 'center' },
                { title: '发货时间', minWidth: 180, key: 'sendDate', align: 'center' },
                { title: '逆变器序列号', minWidth: 180, key: 'inveterSn', align: 'center' },
                { title: '逆变器厂家', minWidth: 180, key: 'inveterFactory', align: 'center' },
                { title: '备件品牌', minWidth: 180, key: 'cancelDate', align: 'center' },
                { title: '备件分类', minWidth: 180, key: 'cancelDate', align: 'center' },
                { title: '是否返厂', minWidth: 180, key: 'cancelDate', align: 'center' },
                { title: '是否允许无旧件返还', minWidth: 180, key: 'cancelDate', align: 'center' },
                { title: '取消时间', minWidth: 180, key: 'cancelDate', align: 'center' },
                { title: '取消人', minWidth: 180, key: 'cancelBy', align: 'center' },
                { title: '取消原因', minWidth: 180, key: 'cancelDesc ', align: 'center' },
                { title: '修改时间', minWidth: 180, key: 'updateDate', align: 'center' },
                { title: '操作', minWidth: 120, align: 'center', fixed: 'right', slot: 'option' }

            ],
            tableSelect: [],
            modelEditAdd: false,
            cancelShow: false,
            suptWarehouseId: '', // 当前账号对应的服务商id
            cancelRemark: '', // 取消备注

        }
    },
    mounted() {
        this.getDict()
        this.getList()
    },
    methods: {
        toTarget(url) {
            window.open(url)
        },
        handleOrderWoView (type) {
            this.visibleName = '附件照片';
            if(type ==='1'){
                this.visibleImg = this.orderWoForm.damagedPart;
            }else if(type ==='2'){
                this.visibleImg = this.orderWoForm.parallelMeshBox;
            }else if(type ==='3'){
                this.visibleImg = this.orderWoForm.Nameplate;
            }
            this.visible = true;
        },
        /*查看附件*/
        openFJDetail(row){
            OSP.orderWoPartPageAndFile(this.queryPage, { serviceInfoId: row.woId }).then(resp=>{
                if(resp.data.success && resp.data.result.fileList.length>0) {
                    let fileList = resp.data.result.fileList
                    fileList.forEach(item => {
                        if (item.detailId === 'damagedPart') {
                            this.orderWoForm.damagedPart = item.fileUrl
                        } else if (item.detailId === 'parallelMeshBox') {
                            this.orderWoForm.parallelMeshBox = item.fileUrl
                        } else if (item.detailId === 'Nameplate') {
                            this.orderWoForm.Nameplate = item.fileUrl
                        }
                    })
                    this.filUploadeVisible = true
                }else{
                    this.orderWoForm={
                        damagedPart:null,
                        parallelMeshBox:null,
                        Nameplate:null
                    }
                    this.$Message.error('暂无附件信息');

                }
            }).catch(e=>{
                this.$Message.error('暂无附件信息');
                console.log(e);
            })
        },
        // 导出
        exportFn() {
            let datas = this.$_.cloneDeep(this.searchObj);
            datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
            delete datas.pageNum
            delete datas.pageSize
            for(let i in datas){
                if(Array.isArray(datas[i])){
                    datas[i] = datas[i].join(',')
                }
            }
            API.exportList(datas).then(res => {
                let binaryData = [];
                let link = document.createElement('a');
                binaryData.push(res.data);
                link.style.display = 'none';
                link.href = window.URL.createObjectURL(new Blob(binaryData));
                link.setAttribute('download', '订单执行查看.xlsx');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            })
        },
        // 提交
        submit() {
            this.$message.confirm('是否确认提交?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$Message.success('提交成功!');
            })
        },
        // 匹配订单
        mateOrder() {
            this.$message.confirm('是否确认匹配订单?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$Message.loading({
                    content: "订单匹配中...",
                    duration: 0,
                });
                API.orderMatching(this.tableSelect[0].id).then(res => {
                    if (res.data.success) {
                        this.$Message.destroy();
                        this.$Message.success(res.data.result);
                        setTimeout(() => {
                            this.getList()
                        }, 2000);
                    } else {
                        this.$Message.destroy();
                        this.$Message.error(res.data.error);
                    }
                })
            })
        },
        // 取消
        cancel() {
            this.cancelShow = true
            this.cancelRemark = ''
        },
        // 提交取消
        sub_cancel() {
            const data = {
                id: this.tableSelect[0].id,
                cancelRemark: this.cancelRemark,
                cancelAmount: this.tableSelect[0].requireAmount
            }
            this.$Message.loading({
                content: "取消中...",
                duration: 0,
            });
            API.cancelOrder(data).then(res => {
                if (res.data.success) {
                    this.$Message.destroy();
                    this.$Message.success(res.data.result);
                    this.cancelShow = false
                    setTimeout(() => {
                        this.getList()
                    }, 2000);
                } else {
                    this.$Message.destroy();
                    this.$Message.error(res.data.error);
                }
            })
        },
        // 删除
        del() {
            this.$message.confirm('此操作将永久删除该文件, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$Message.success('删除成功!');
            })
        },
        // 打开详情
        openDetail(row) {
            this.$refs.detailsRef.show = true
        },
        // 查询按钮
        queryList() {
            this.searchObj.pageNum = 1;
            this.getList();
        },

        // 重置按钮
        emptyList() {
            this.searchObj = {
                submitDateStart: "",
                submitDateEnd: "",
                orderCode: "",
                woId: "",
                warehouseId: '',
                orderType: "",
                orderStatusList: "",
                materialCode: "",
                materialDesc: "",
                pageNum: 1,
                pageSize: 10
            };
            this.submitDate = [];
        },
        // 选择日期
        selectDate(date, refObj) {
            this.submitDate = date;
            this.searchObj[refObj + "Start"] = date[0];
            this.searchObj[refObj + "End"] = date[1];
        },
        // 获取工单列表
        getList() {
            this.$Message.loading({
                content: "查询中...",
                duration: 0,
            });
            let datas = this.$_.cloneDeep(this.searchObj);
            datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
            delete datas.pageNum
            delete datas.pageSize
            const page = { page: this.searchObj.pageNum, rows: this.searchObj.pageSize }
            this.$refs.tableRef.selectAll(false);
            API.getList(page, datas).then((res) => {
                this.$Message.destroy();
                let request = res.data.result;
                if (res.data.success & (request.content.length > 0)) {
                    this.totalElements = request.totalElements;
                    this.commList = request.content;
                    request.content.map((e, inx) => {
                        e.area = (e.stationProvinceName || "") + (e.stationCityName || "") + (e.stationRegionName || "");
                    });
                } else {
                    this.totalElements = 0;
                    this.commList = [];
                }
            });
        },
        // 获取下拉数据
        getDict() {
            // 订单类型map
            this.orderTypeList.forEach(item => {
                this.orderTypeMap[item.value] = item.label
            })
            // 订单状态map
            this.orderStatusList.forEach(item => {
                this.orderStatusMap[item.value] = item.label
            })
            // 获取服务商数据
            dict.getWarehouse({ activeFlag: '1', warehouseLevel: '3' }).then(res => {
                const list = []
                res.data.result.forEach(item => {
                    if (list.indexOf(item.warehouseCode) === -1 && item.warehouseCode) {
                        list.push(item.warehouseCode)
                        this.warehouseList.push(item)
                    }
                })
            })
            //  获取当前登录账号对应的供应商id
            const member_id = JSON.parse(window.localStorage.getItem('logins')).member_id
            API.querySupp(member_id).then(res => {
                this.suptWarehouseId = res.data.result.id + ''
            })
        },
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.pageNum = curr;
            this.getList();
        },
        jumpto(path, id, count) {
            this.$router.push({
                path,
                query: { id, count },
            });
        }
    }
}
</script>

<style lang="scss" scoped>
@import '@/style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
.list_but {
    margin-bottom: 20px;
}
.demo-upload-list {
    @extend .flex-row-center-center;
    position: relative;
    width: 100px;
    height: 100px;
    //height: 100%;
    overflow: hidden;

    &:hover .demo-upload-list-cover {
        display: flex;
    }
}
.demo-upload-list-cover {
    @extend.flex-row-center-around;
    display: none;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,.6);
    z-index: 999;
    i{
        &:hover{
            cursor: pointer;
        }
    }
}
</style>
