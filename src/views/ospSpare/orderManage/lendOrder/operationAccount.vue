<template>
    <Modal width="80%" v-model="show" :loading="true" @on-cancel="closeAddModal" title="运维商认责" :styles="{ top: '5vh' }"
           :mask-closable="false">
        <div style="overflow-y: auto; width: 100%; height: 70vh; background: #eee; padding: 20px">
            <Card :bordered="false">
                <div class="cus_list" style="min-height: calc(80vh - 72px)">
                    <div class="cus_reg">
                        <div class="cus_form" style="padding-top: 0">
                            <p class="cus_title">主单信息</p>
                            <el-descriptions class="margin-top" :column="3" border>
                                <el-descriptions-item v-for="(item, index) in colList" :key="index">
                                    <template slot="label">
                                        {{item.label}}
                                    </template>
                                    <span v-if="item.value=='orderStatus'">
                                        {{ orderStatusMap[detailData[item.value]] }}
                                    </span>
                                    <span v-else>{{detailData[item.value]}}</span>
                                </el-descriptions-item>
                            </el-descriptions>
                            <p class="cus_title">明细信息</p>
                            <table class="cus_table" border="0" cellspacing="0" cellpadding="0" width="100%">
                                <tr>
                                    <th align="center">序号</th>
                                    <th align="center">备件编码</th>
                                    <th align="center">备件描述</th>
                                    <th align="center">供应商</th>
                                    <th align="center">单价</th>
                                    <th align="center">需求数量</th>
                                    <th align="center">发货数量</th>
                                </tr>
                                <tr v-for="(item, index) in subTableData" :key="index">
                                    <td align="center" width="80">{{index+1}}</td>
                                    <td align="center" width="80">{{item.materialCode}}</td>
                                    <td align="center" width="120">{{item.materialName}}</td>
                                    <td align="center" width="70">{{item.supplierName}}</td>
                                    <td align="center" width="100">{{item.materialPrice}}</td>
                                    <td align="center" width="100">{{item.orderAmount}}</td>
                                    <td align="center" width="100">{{item.sendAmount}}</td>
                                </tr>
                            </table>
                            <p class="cus_title">附件信息</p>
                            <div class="fileList" style="display: flex;padding: 0 10px;box-sizing: border-box">
                                <div class="fileArea" style="width: 300px">
                                    <span style="display: inline-block;margin-bottom: 10px">整机</span>
                                    <div style="display: flex;">
                                        <div v-if="list1" class="demo-upload-list" style="margin-right: 20px">
                                            <img :src="list1" loading="lazy" width="100%" style="height: 100%;object-fit: fill;border-radius: 5px" alt=""/>
                                            <div class="demo-upload-list-cover" @click.stop>
                                                <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleView('1')"></Icon>
                                            </div>
                                        </div>
                                        <div v-if="list2" class="demo-upload-list" >
                                            <img :src="list2" loading="lazy" width="100%" style="height: 100%;object-fit: fill;border-radius: 5px" alt=""/>
                                            <div class="demo-upload-list-cover" @click.stop>
                                                <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleView('2')"></Icon>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="fileArea">
                                    <span style="display: inline-block;margin-bottom: 10px">组件</span>
                                    <div style="display: flex;">
                                        <div class="demo-upload-list" style="margin-right: 20px" v-for="(item, index) in list3" :key="index">
                                            <img :src="item" loading="lazy" width="100%" style="height: 100%;object-fit: fill;border-radius: 5px" alt=""/>
                                            <div class="demo-upload-list-cover" @click.stop>
                                                <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleView(item, '2')"></Icon>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="cus_title">审批信息</p>
                            <div style="padding: 10px 10px 0;display: flex;">
                                <div class="remark" style="display: flex;align-items: flex-start;margin-right: 20px">
                                    <span style="white-space: nowrap;display: inline-block;height: 32px;line-height: 32px;margin-right: 12px">判定责任</span>
                                    <span style="display: inline-block;width: 180px;height: 32px;line-height: 32px">
                                        {{detailData.dutyType=='1'?'运维商责任':detailData.dutyType=='2'?'供应商责任':detailData.dutyType=='3'?'其他责任':detailData.dutyType}}
                                    </span>
                                </div>
                                <div class="remark" style="display: flex;align-items: flex-start">
                                    <span style="white-space: nowrap;display: inline-block;height: 32px;line-height: 32px;margin-right: 12px">审批意见</span>
                                    <Input v-model="detailData.zbAuditDesc" type="textarea" placeholder="" style="width: 290px" readonly></Input>
                                </div>
                            </div>
                            <p class="cus_title">组件编码</p>
                            <div style="display: flex;flex-wrap: wrap;padding: 10px 10px 0;box-sizing: border-box">
                                <Input v-for="(item,index) in codeList" :key="index" readonly v-model="codeList[index]" style="width: 120px;margin-right: 10px;margin-bottom: 10px">
                                    <span slot="prepend">{{index+1}}</span>
                                </Input>
                            </div>
                            <p class="cus_title">认责结果</p>
                            <div style="padding: 10px 10px 0;box-sizing: border-box">
                                <div style="display: flex;align-items: center;margin-bottom: 20px">
                                    <span style="margin-right: 20px">认责结果</span>
                                    <RadioGroup v-model="result">
                                        <Radio label="同意"></Radio>
                                        <Radio label="不同意"></Radio>
                                    </RadioGroup>
                                </div>
                                <div class="remark" style="display: flex;align-items: flex-start">
                                    <span style="white-space: nowrap;margin-right: 20px">认责意见</span>
                                    <Input v-model="count" type="textarea" placeholder="" style="width: 290px"></Input>
                                </div>
                            </div>


                        </div>
                    </div>
                </div>
            </Card>
        </div>
        <Modal v-model="visible" width="99" reset-drag-position draggable sticky title="预览" footer-hide class-name="vertical-center-modal">
            <img class="preview" v-if="visibleImg" :src="visibleImg" @click="toTarget(visibleImg)" title="点击查看原图" alt="">
        </Modal>
        <div slot="footer">
            <Button type="primary" :loading="submitLoading" @click="submitResult">提交</Button>
            <Button type="default" style="margin-left: 10px"
                    @click="closeAddModal">取消</Button>
        </div>
    </Modal>
</template>

<script>
import _data from "./dict"; // 数据字典
import API from "@/api/ospSpare/orderManage/lendOrder";
import { mapGetters, mapState } from "vuex";

export default {
    props: {
        orderId: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            result: '',
            show: false,
            infoList: {},
            orderStatusList: _data.statusList,
            orderStatusMap: {},
            sourceArr: _data.SourceArr,
            count: '',
            colList: [
                {label: '订单号', value: 'orderNo' },
                {label: '审批单号', value: 'auditNo' },
                {label: '订单状态', value: 'orderStatus' },
                {label: '申请服务商', value: 'warehouseName' },
                {label: '建站服务商', value: 'stationNetName' },
                {label: '运维单号', value: 'woId' },
                {label: '借件申请时间', value: '' },
                {label: '申请通过时间', value: '' },
            ],
            detailData: {},
            codeList: [],
            subTableData: [],
            list1: '',
            list2: '',
            list3: [],
            visibleImg: '',
            visible: false,
            submitLoading: false
        };
    },
    mounted() {
        this.getDict()
    },
    methods: {
        ...mapGetters("index", ["getListArr"]),
        ...mapState("index", ['spInfo']),
        submitResult(){
            if(!this.result){
                this.$Message.warning('请选择认责结果');
                return
            }
            if(!this.count){
                this.$Message.warning('请输入认责意见');
                return
            }
            this.submitLoading = true
            const data= {
                id: this.detailData.id,
                memberId: this.spInfo().memberId,
                auditStatus: this.result === '同意' ? '0' : '1',
                auditDesc: this.count
            }
            API.operationAccountApi(data).then(res => {
                this.submitLoading = false
                if(res.data.success){
                    this.$Message.success(res.data.result);
                    this.resetForm()
                    this.show = false
                    this.$emit('getPageList')
                }else{
                    this.$Message.warning(res.data.error);
                }
            })
        },
        resetForm(){
            this.result = ''
            this.count = ''
        },
        closeAddModal(){
            this.resetForm()
            this.show = false
        },
        getDict(){
            this.orderStatusList.forEach(item => {
                this.orderStatusMap[item.value] = item.label
            })
        },
        toTarget(url) {
            window.open(url)
        },
        handleView (type, elseType) {
            this.visible = true;
            if(elseType){
                this.visibleImg = type;
                return false;
            }
            switch (type) {
                case '1':
                    this.visibleImg = this.list1;
                    break;
                case '2':
                    this.visibleImg = this.list2;
                    break;
                case '3':
                    this.visibleImg = this.list3;
                    break;
                case '4':
                    this.visibleImg = this.list4;
                    break;
            }
        },
        openImg(url) {
            window.open(url);
        },

        typeVal(list, value) {
            const obj = {};
            this[list].map((e) => {
                obj[e.value] = e.label || e.value;
            });
            // console.log(obj[value]);
            return obj[value];
        },

        typeNode(list, value) {
            const obj = {};
            this[list].map((e) => {
                obj[e.value] = e.node;
            });
            return obj[value];
        },

        timeChange(params) {
            // console.log(params);
            let dates = "";
            if (params) {
                dates = params.replace("T", " ");
            }
            return dates;
        },
        asyncOK () {
            setTimeout(() => {
                this.show = false;
            }, 2000);
        },
        // 获取工单详情
        getDetail(){
            API.getOrderDetail(this.orderId).then(res=>{
                if(res.data.success){
                    const data = res.data.result
                    this.detailData = data
                    this.subTableData = data?.detailList || []
                    this.codeList = data.modelSn.split(',')
                    let fileList = data.fileList.filter(item=> item.fileFlag=='1')
                    let fileList2 = data.fileList.filter(item=> item.fileFlag=='2')
                    if(fileList.length>1){
                        this.list1 = fileList[0].fileAddress
                        this.list2 = fileList[1].fileAddress
                    }else{
                        this.list1 = fileList[0].fileAddress
                    }
                    fileList2.forEach(item=>{
                        this.list3.push(item.fileAddress)
                    })
                }
            })
        },
    },
};
</script>

<style scoped lang="scss">
@import '@/style/_cus_reg.scss';
@import '@/style/_cus_list.scss';
@import '@/style/_finish.scss';

.step {
    position: relative;
    margin: 0 20px;
    padding: 12px 6px 6px;
    border: 1px solid #e8eaec;

    .stepdiv {
        width: 80%;
        margin: 0 auto;
        @extend pt;
    }
}
::v-deep .ivu-input-wrapper{
    height: 32px!important;
}
.cus_title {
    margin-top: 20px;
    margin-bottom: 6px;
}
.demo-upload-list {
    @extend .flex-row-center-center;
    position: relative;
    width: 100px;
    height: 100px;
    overflow: hidden;

    &:hover .demo-upload-list-cover {
        display: flex;
    }
}
.demo-upload-list-cover {
    @extend.flex-row-center-around;
    display: none;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,.6);
    z-index: 999;
    i{
        &:hover{
            cursor: pointer;
        }
    }
}
</style>
