<template>
  <Modal width="80%" v-model="show" :loading="true" @on-ok="asyncOK" title="详情" :styles="{ top: '5vh' }"
         :mask-closable="false" :footer-hide="true">
    <div style="overflow-y: auto; width: 100%; height: 70vh; background: #eee; padding: 20px">
      <Card :bordered="false">
        <div class="cus_list" style="min-height: calc(80vh - 72px)">
          <div class="cus_reg">
            <div class="cus_form" style="padding-top: 0">
              <p class="cus_title">主单信息</p>
              <el-descriptions class="margin-top" :column="3" border>
                <el-descriptions-item v-for="(item, index) in colList" :key="index">
                  <template slot="label">
                    {{item.label}}
                  </template>
                  <span v-if="item.value=='orderStatus'">
                    {{ orderStatusMap[detailData[item.value]] }}
                  </span>
                    <span v-else-if="item.value=='accountingStatus'">
                    {{ accountingStatusMap[detailData[item.value]] }}
                  </span>
                    <span v-else-if="item.value=='dutyType'">
                    {{ dutyTypeMap[detailData[item.value]] }}
                  </span>
                    <span v-else-if="item.value=='stationAuditStatus'">
                    {{ stationAuditStatusMap[detailData[item.value]] }}
                  </span>
                  <span v-else>{{detailData[item.value]}}</span>
                </el-descriptions-item>
              </el-descriptions>
              <p class="cus_title">明细信息</p>
              <table class="cus_table" border="0" cellspacing="0" cellpadding="0" width="100%">
                <tr>
                  <th align="center">序号</th>
                  <th align="center">备件编码</th>
                  <th align="center">备件描述</th>
                  <th align="center">供应商</th>
                  <th align="center">单价</th>
                  <th align="center">需求数量</th>
                  <th align="center">发货数量</th>
                </tr>
                <tr v-for="(item, index) in detailList" :key="index">
                  <td align="center" width="80">{{index+1}}</td>
                  <td align="center" width="80">{{item.materialCode}}</td>
                  <td align="center" width="120">{{item.materialDesc}}</td>
                  <td align="center" width="70">{{item.supplierName}}</td>
                  <td align="center" width="100">{{item.materialPrice}}</td>
                  <td align="center" width="100">{{item.orderAmount}}</td>
                  <td align="center" width="100">{{item.sendAmount}}</td>
                </tr>
              </table>
              <p class="cus_title">附件信息</p>
              <div class="fileList" style="display: flex;padding: 0 10px;box-sizing: border-box">
                <div class="fileArea" style="width: 300px">
                  <span style="display: inline-block;margin-bottom: 10px">整机</span>
                  <div style="display: flex;">
                    <div v-if="list1" class="demo-upload-list" style="margin-right: 20px">
                      <img :src="list1" loading="lazy" width="100%" style="height: 100%;object-fit: fill;border-radius: 5px" alt=""/>
                      <div class="demo-upload-list-cover" @click.stop>
                        <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleView('1')"></Icon>
                      </div>
                    </div>
                    <div v-if="list2" class="demo-upload-list" >
                      <img :src="list2" loading="lazy" width="100%" style="height: 100%;object-fit: fill;border-radius: 5px" alt=""/>
                      <div class="demo-upload-list-cover" @click.stop>
                        <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleView('2')"></Icon>
                      </div>
                    </div>
                  </div>

                </div>
                <div class="fileArea">
                  <span style="display: inline-block;margin-bottom: 10px">组件</span>
                  <div style="display: flex;">
                    <div v-for="(item, index) in list3" :key="index" class="demo-upload-list" style="margin-right: 20px">
                      <img :src="item" loading="lazy" width="100%" style="height: 100%;object-fit: fill;border-radius: 5px" alt=""/>
                      <div class="demo-upload-list-cover" @click.stop>
                        <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleView(item, '2')"></Icon>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <p class="cus_title">损坏组件SN码</p>
              <div style="display: flex;flex-wrap: wrap;padding: 10px 10px 0;box-sizing: border-box">
                  <div v-for="(item,index) in codeList" :key="index"    >
                      <Input  v-model="codeList[index]" readonly style="width: 220px;margin-right: 10px;margin-bottom: 10px">
                          <span slot="prepend">{{index+1}}</span>
                      </Input>
                      <span style="color: red">
<!--                                    0重复使用，1校验通过，2校验不通过-->
                      <span v-if="snDetailList[index] && snDetailList[index].checkFlag=='0'">重复使用</span>
                      <span v-else-if="snDetailList[index] && snDetailList[index]?.checkFlag=='2'">SN码不存在</span>
                      <span v-else-if="snDetailList[index] && snDetailList[index]?.checkFlag=='1'" style="color: #0da334">校验通过</span>
                      <span v-else-if="snDetailList[index] && snDetailList[index]?.checkFlag=='4'" >请输入组件SN码</span>
                      <span v-else-if="snDetailList[index] && snDetailList[index]?.checkFlag=='5'" >非当前电站组件SN</span>
                      <span v-else-if="snDetailList[index] && snDetailList[index]?.checkFlag=='6'" >校验通过，列表为空</span>
                  </span>
                  </div>

              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
    <Modal v-model="visible" width="99" reset-drag-position draggable sticky title="预览" footer-hide class-name="vertical-center-modal">
      <img class="preview" v-if="visibleImg" :src="visibleImg" @click="openImg(visibleImg)" title="点击查看原图" alt="">
    </Modal>
  </Modal>
</template>

<script>
import _data from "./dict"; // 数据字典
import API from "@/api/ospSpare/orderManage/lendOrder"; // 数据字典
export default {
  props: {
    orderId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      visible: false,
      list1: '',
      list2: '',
      list3: [],
      list4: '',
      visibleImg: '',
      codeList: [],
      snDetailList: [],
      detailList: [],
      detailData: {},
      show: false,
      infoList: {},
      orderStatusList: _data.statusList,
      accountingStatusList: _data.accountingStatus,
      dutyTypeList: _data.dutyType,
      stationAuditStatusList: _data.stationAuditStatus,
      orderStatusMap: {},
      accountingStatusMap: {},
      dutyTypeMap: {},
      stationAuditStatusMap: {},
      count: '',
      colList: [
        {label: '订单号', value: 'orderNo' },
        {label: '审批单号', value: 'auditNo' },
        {label: '订单状态', value: 'orderStatus' },
        {label: '申请服务商', value: 'warehouseName' },
        {label: '建站服务商', value: 'stationNetName' },
        {label: '运维单号', value: 'woId' },
        {label: '回购单号', value: 'purchaseNo' },
        {label: '创建时间', value: 'createDate' },
        {label: '提交时间', value: 'submitDate' },
        {label: '责任类型', value: 'dutyType' },
        {label: '中心经理审批人', value: 'centerAuditBy' },
        {label: '中心经理审批时间', value: 'centerAuditDate' },
        {label: '中心经理意见', value: 'centerAuditDesc' },
        {label: '总部审批人', value: 'zbAuditBy' },
        {label: '总部审批时间', value: 'zbAuditDate' },
        {label: '总部审批意见', value: 'zbAuditDesc' },
        {label: '建站商确认人', value: 'stationAuditBy' },
        {label: '建站商确认状态', value: 'stationAuditStatus' },
        {label: '建站商确认时间', value: 'stationAuditDate' },
        {label: '建站商意见', value: 'stationAuditDesc' },
        {label: '供应商确认人', value: 'supplierAuditBy' },
        {label: '供应商确认状态', value: 'supplierAuditStatus' },
        {label: '供应商确认时间', value: 'supplierAuditDate' },
        {label: '供应商意见', value: 'supplierAuditDesc' },
        {label: '记账凭证号', value: 'accountingNo' },
        {label: '记账状态', value: 'accountingStatus' },
        {label: '记账时间', value: 'accountingDate' },
        {label: '记账描述', value: 'accountingDesc' },
      ]
    };
  },

  mounted() {
    this.getDict()
    this.getInfo();
  },

  methods: {
    getDict(){
      this.orderStatusList.forEach(item => {
        this.orderStatusMap[item.value] = item.label
      })
      this.accountingStatusList.forEach(item => {
          this.accountingStatusMap[item.value] = item.label
      })
      this.dutyTypeList.forEach(item => {
          this.dutyTypeMap[item.value] = item.label
      })
      this.stationAuditStatusList.forEach(item => {
          this.stationAuditStatusMap[item.value] = item.label
      })
    },
    getDetail(){
      API.getOrderDetail(this.orderId).then(res=>{
        if(res.data.success){
          const data = res.data.result
          this.detailData = data
          this.codeList = data.modelSn.split(',')
          this.snDetailList = data.snDetailList
          this.detailList = data?.detailList || []
          let fileList = data.fileList.filter(item=> item.fileFlag=='1')
          let fileList2 = data.fileList.filter(item=> item.fileFlag=='2')
          if(fileList.length>1){
            this.list1 = fileList[0].fileAddress
            this.list2 = fileList[1].fileAddress
          }else{
            this.list1 = fileList[0].fileAddress
          }
            this.list3 = []
            fileList2.forEach(item=>{
                this.list3.push(item.fileAddress)
            })
        }
      })
    },
    handleView (type, elseType) {
        this.visible = true;
        if(elseType){
            this.visibleImg = type;
            return false;
        }
      switch (type) {
        case '1':
          this.visibleImg = this.list1;
          break;
        case '2':
          this.visibleImg = this.list2;
          break;
        case '3':
          this.visibleImg = this.list3;
          break;
        case '4':
          this.visibleImg = this.list4;
          break;
      }
    },
    openImg(url) {
      window.open(url);
    },

    typeVal(list, value) {
      const obj = {};
      this[list].map((e) => {
        obj[e.value] = e.label || e.value;
      });
      // console.log(obj[value]);
      return obj[value];
    },

    typeNode(list, value) {
      const obj = {};
      this[list].map((e) => {
        obj[e.value] = e.node;
      });
      return obj[value];
    },

    timeChange(params) {
      // console.log(params);
      let dates = "";
      if (params) {
        dates = params.replace("T", " ");
      }
      return dates;
    },
    asyncOK () {
      setTimeout(() => {
        this.show = false;
      }, 2000);
    },
    // 获取工单详情
    getInfo() {
      let datas = {
        workOrderSn: this.$route.query.id,
      };
      this.count = this.$route.query.count
    },
  },
};
</script>

<style scoped lang="scss">
@import '@/style/_cus_reg.scss';
@import '@/style/_cus_list.scss';
@import '@/style/_finish.scss';

.step {
  position: relative;
  margin: 0 20px;
  padding: 12px 6px 6px;
  border: 1px solid #e8eaec;

  .stepdiv {
    width: 80%;
    margin: 0 auto;
    @extend pt;
  }
}
::v-deep .ivu-input-wrapper{
  height: 32px!important;
}
.cus_title {
  margin-top: 20px;
  margin-bottom: 6px;
}
.demo-upload-list {
  @extend .flex-row-center-center;
  position: relative;
  width: 100px;
  height: 100px;
  overflow: hidden;

  &:hover .demo-upload-list-cover {
    display: flex;
  }
}
.demo-upload-list-cover {
  @extend.flex-row-center-around;
  display: none;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0,0,0,.6);
  z-index: 999;
  i{
    &:hover{
      cursor: pointer;
    }
  }
}
</style>
