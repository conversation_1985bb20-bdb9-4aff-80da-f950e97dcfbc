<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏备件管理</Breadcrumb-item>
            <Breadcrumb-item to="/ospSpare/">订单管理</Breadcrumb-item>
            <Breadcrumb-item>服务商调拨-确认</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">订单号：</p>
                    <Input v-model="searchObj.orderCode" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">审批单号：</p>
                    <Input v-model="searchObj.businessKey" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">备件编码：</p>
                    <Input v-model="searchObj.materialCode" placeholder="请输入备件编码" clearable style="width: 200px" />
                </div>
<!--                <div class="condition">-->
<!--                    <p class="condition_p">调入服务商：</p>-->
<!--                    <Select v-model="searchObj.suptWarehouseId" filterable clearable style="width: 200px">-->
<!--                        <Option v-for="item in warehouseList" :value="item.id" :key="item.id">{{ item.warehouseName }}</Option>-->
<!--                    </Select>-->
<!--                </div>-->
<!--                <div class="condition">-->
<!--                    <p class="condition_p">调出服务商：</p>-->
<!--                    <Select v-model="searchObj.warehouseId" filterable clearable style="width: 200px">-->
<!--                        <Option v-for="item in warehouseList" :value="item.id" :key="item.id">{{ item.warehouseName }}</Option>-->
<!--                    </Select>-->
<!--                </div>-->
                <div class="condition">
                    <p class="condition_p">订单状态：</p>
                    <Select v-model="searchObj.orderStatus" clearable style="width: 200px">
                        <Option v-for="item in orderStatusList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">创建时间：</p>
                    <DatePicker ref="formDateSh" :value="submintDate" @on-change="
                        (data) => {
                          return selectDate(data, 'createDate');
                        }
                      " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择" style="width: 200px ">
                    </DatePicker>
                </div>
            </div>

            <div class="list_but" style="display: flex; justify-content: space-between; width: 100%;">
                <div class="condition">
<!--                    <Button @click="add" type="primary">新增</Button>-->
<!--                    <Button @click="edit" type="primary" :disabled="this.tableSelect.length!=1">编辑</Button>-->
<!--                    <Button @click="submit" type="primary" :disabled="this.tableSelect.length!=1 || this.tableSelect[0].orderStatus !='init'">提交</Button>-->
                    <Button @click="check" type="info"  :disabled="tableSelect.length !== 1 || tableSelect[0].orderStatus !== 'submit'" >调拨确认</Button>
<!--                    <Button @click="del" type="error" :disabled="this.tableSelect.length!=1">删除</Button>-->
                    <Button @click="exportFn" type="error">导出</Button>

                </div>
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                </div>
            </div>
            <div class="commListDiv">
                <Table size="small" border ref="tableRef" :columns="tableColumn" :data="commList" @on-selection-change="selected => this.tableSelect = selected">
                    <template slot-scope="{ index }" slot="sn">
                        {{ index + 1 }}
                    </template>
<!--                    订单状态-->
                    <template slot-scope="{ index }" slot="orderStatus">
                        <span v-if="commList[index].orderStatus=='init'">初始</span>
                        <span v-if="commList[index].orderStatus=='WAIT'">待货</span>
                        <span v-if="commList[index].orderStatus=='send'">待出库</span>
                        <span v-if="commList[index].orderStatus=='CLOSE'">已发货</span>
                        <span v-if="commList[index].orderStatus=='FCLOSE'">取消</span>
                        <span v-if="commList[index].orderStatus=='submit'">调拨提交</span>
                        <span v-if="commList[index].orderStatus=='back'">调拨撤回</span>
                        <span v-if="commList[index].orderStatus=='end'">关闭</span>
                        <span v-if="commList[index].orderStatus=='confirm'">调拨确认</span>
                    </template>
                    <!--                    审批状态-->
                    <template slot-scope="{ index }" slot="auditStatus">
                        <span v-if="commList[index].auditStatus=='0'">未提交</span>
                        <span v-if="commList[index].auditStatus=='1'">已提交</span>
                        <span v-if="commList[index].auditStatus=='2'">审批通过</span>
                        <span v-if="commList[index].auditStatus=='3'">审批驳回</span>
                    </template>
<!--                    订单类型-->
                    <template slot-scope="{ index }" slot="orderType">
                        {{ orderTypeMap[commList[index].orderType] }}
                    </template>
                    <template slot-scope="{ index }" slot="option">
                        <Button type="info" ghost size="small" @click="openDetail(commList[index])" style="margin: 3px">详情</Button>
                    </template>
                </Table>
            </div>
            <Page style="margin: 20px" @on-change="changeCurrent" :current="searchObj.page"
                  :total="totalElements" show-total show-elevator />
        </div>
        <addEditPop ref="addEditPopRef" :popType="popType" @getList="getList"/>
        <detail ref="detailsRef" />
        <!--        调拨确认-->
        <check ref="checkRef" @getList="getList" />
    </div>
</template>

<script>
import _data from "@/views/ospSpare/_edata"; // 数据字典
import API from "@/api/ospSpare/orderManage/serviceAllot";
import addEditPop from "./addEditPop";
import detail from './details'
import check from ".//check.vue";
import dict from '@/api/ospSpare'
export default {
    name: "serviceProviderOrder",
    components: { addEditPop, detail,check },
    data() {
        return {
            searchObj: {
                orderCode: "",
                businessKey: "",
                warehouseId: "",
                materialCode: "",
                suptWarehouseId: "",
                orderStatus: "",
                createDateStart: "",
                createDateEnd: "",
                pageNum: 1,
                pageSize: 10
            },
            formObj: {},
            orderTypeMap: {},
            staffList: [],
            warehouseList: [],
            totalElements: 0,
            submintDate: [],
            orderStatusList: [
                {value: "init", label: "初始"},
                {value: "WAIT", label: "待货"},
                {value: "send", label: "待出库"},
                {value: "CLOSE", label: "已发货"},
                {value: "FCLOSE", label: "取消"},
                {value: "submit", label: "调拨提交"},
                {value: "confirm", label: "调拨确认"},
                {value: "back", label: "调拨撤回"},
                {value: "end", label: "关闭"},
            ],
            popType:'',
            sourceArr: _data.SourceArr,
            faultCodeTypeList: _data.faultCodeTypeList,
            orderTypeList: _data.orderType,
            tableColumn: [
                { type: 'selection', minWidth: 60, title: '选择', align: 'center' },
                { title: '序号',  minWidth: 80, align: 'center', slot: 'sn' },
                { title: '订单号', minWidth: 180, key: 'orderCode', align: 'center' },
                { title: '订单类型', minWidth: 180, key: 'orderType', align: 'center' , slot: 'orderType'},
                { title: '订单状态', minWidth: 100, key: 'orderStatus', align: 'center', slot: 'orderStatus'  },
                { title: '审批单号', minWidth: 180, key: 'businessKey', align: 'center' },
                { title: '中心名称', minWidth: 100, key: 'centerName', align: 'center' },
                { title: '调入服务商', minWidth: 160, key: 'warehouseName', align: 'center' },
                { title: '调出服务商', minWidth: 160, key: 'suptWarehouseName', align: 'center' },
                { title: '申请时间', minWidth: 160, key: 'submitDate', align: 'center' },
                { title: '审批时间', minWidth: 160, key: 'auditDate', align: 'center' },
                { title: '审批状态', minWidth: 100, key: 'auditStatus', align: 'center',slot: 'auditStatus' },
                { title: '撤回时间', minWidth: 160, key: 'cancelDate', align: 'center' },
                { title: '操作', minWidth: 120, align: 'center', fixed: 'right', slot: 'option' }
            ],
            commList: [],
            tableSelect:[],//表格选择数据
            title: "一键派工",
            modelEdit: false,
            modelTitle: '新增',
            formItem: {
                aaa: '',
                bbb: ''
            },
            modelEditAdd: false
        }
    },
    mounted() {
        this.getDict()
        this.getList()
    },
    methods: {
        // 导出
        exportFn() {
            let datas = this.$_.cloneDeep(this.searchObj);
            datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
            delete datas.pageNum
            delete datas.pageSize
            API.exportListConfirm(datas).then(res => {
                let binaryData = [];
                let link = document.createElement('a');
                binaryData.push(res.data);
                link.style.display = 'none';
                link.href = window.URL.createObjectURL(new Blob(binaryData));
                link.setAttribute('download', '服务商调拨-确认列表.xlsx');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            })
        },
        // 获取下拉数据
        getDict() {
            // 订单类型map
            this.orderTypeList.forEach(item => {
                this.orderTypeMap[item.value] = item.label
            })
            // 获取服务商数据
            dict.getWarehouse({ activeFlag: '1', warehouseLevel: '3' }).then(res => {
                const list = []
                res.data.result.forEach(item => {
                    if (list.indexOf(item.warehouseCode) === -1 && item.warehouseCode) {
                        list.push(item.warehouseCode)
                        this.warehouseList.push(item)
                    }
                })
            })
        },
        // 新增
        add() {
            this.popType='add';
            this.$refs.addEditPopRef.modelEdit = true
        },
        // 编辑
        edit() {
            this.popType='edit';
            this.$refs.addEditPopRef.dataSource = this.tableSelect[0]
            this.$refs.addEditPopRef.modelEdit = true
        },
        // 提交
        submit() {
            this.$message.confirm('是否确认提交?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
            //todo接口待确定
                let params={'id':this.tableSelect[0].id}
                API.submitServicePIT(params).then(res=>{
                    if(res.data.success){
                        this.$Message.success('提交成功!');
                        setTimeout(() => {
                           this.getList()
                        }, 2000)
                    }else{
                        this.$Message.error(res.data.error);
                    }
                }).catch(err=>{
                    this.$Message.error(err.data.error);
                })
            })
        },
        // 删除
        del() {
            this.$message.confirm('此操作将永久删除该文件, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                let params={
                    'id':this.tableSelect[0].id
                }
                API.delServicePIT(params).then(res=>{
                    if(res.data.success){
                        this.$Message.success('删除成功!');
                        setTimeout(() => {
                            this.$Message.destroy();
                            this.getList()
                        }, 1000)
                    }else{
                        this.$Message.error(res.data.error);
                    }
                }).catch(err=>{
                    this.$Message.error(err.data.error);
                })
            })
        },
        //调拨确认
        check(){
            this.$refs.checkRef.orderId = this.tableSelect[0].id
            this.$refs.checkRef.modelEdit = true
        },
        // 打开详情
        openDetail(row) {
            this.$refs.detailsRef.orderId = row.id
            this.$refs.detailsRef.show = true
        },
        // 查询按钮
        queryList() {
            this.searchObj.pageNum = 1;
            this.getList();
        },

        // 重置按钮
        emptyList() {
            this.searchObj = {
                orderCode: "",
                businessKey: "",
                warehouseId: "",
                suptWarehouseId: "",
                orderStatus: "",
                createDateStart: "",
                createDateEnd: "",
                pageNum: 1,
                pageSize: 10
            };
            this.submintDate = [];
        },
        // 选择日期
        selectDate(date, refObj) {
            this.submintDate = date;
            this.searchObj[refObj + "Start"] = date[0];
            this.searchObj[refObj + "End"] = date[1];
        },
        // 获取工单列表
        getList() {
            this.$Message.loading({
                content: "查询中...",
                duration: 0,
            });
            let datas = this.searchObj;
            datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
            delete datas.pageNum
            delete datas.pageSize
            datas.type='3'
          const page = { page: this.searchObj.pageNum, rows: this.searchObj.pageSize }
            // 将所有选中项清空
            this.$refs.tableRef.selectAll(false);
            API.getList(page, datas).then(res => {
                if(res.data.success){
                    this.$Message.destroy();
                    this.commList = res.data.result.content
                    this.totalElements = res.data.result.totalElements
                }else{
                    this.commList=[]
                }
            }).catch(err=>{
                this.commList=[]
            })

            // let datas = this.searchObj;
            // /* TODO 暂时没有接口 */
            // setTimeout(() => {
            //     this.$Message.destroy();
            //     this.commList = [
            //         {
            //             orderCode: '312123',
            //             id: '1',
            //             orderType: '类型',
            //             orderStatus: '状态',
            //             centerName: '中心',
            //             branchName: '服务商',
            //             suptBranchName: '仓库',
            //             submitDate: '申请',
            //             sendDate: '发货',
            //             sendCode: '出库',
            //             cancelDate: '取消',
            //             cancelBy: '人',
            //             cancelDesc: '原因',
            //             updateDate: '修改'
            //         }
            //     ]
            //     this.totalElements = 99
            // }, 1000)
        },
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.pageNum = curr;
            this.getList();
        },
        jumpto(path, id, count) {
            this.$router.push({
                path,
                query: { id, count },
            });
        }
    }
}
</script>

<style lang="scss" scoped>
@import 'style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
