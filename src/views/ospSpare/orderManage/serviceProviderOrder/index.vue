<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏备件管理</Breadcrumb-item>
            <Breadcrumb-item to="/ospSpare/">订单管理</Breadcrumb-item>
            <Breadcrumb-item>服务商订单</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">订单号：</p>
                    <Input v-model="searchObj.orderCode" placeholder="请输入订单号" clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">工单号：</p>
                    <Input v-model="searchObj.woId" placeholder="请输入工单号" clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">备件编码：</p>
                    <Input v-model="searchObj.materialCode" placeholder="请输入备件编码" clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">订单类型：</p>
                    <Select v-model="searchObj.orderType" placeholder="请选择订单类型" filterable clearable style="width: 200px">
                        <Option v-for="item in orderTypeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                </div>

            </div>
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">订单状态：</p>
                    <Select v-model="searchObj.orderStatus" filterable clearable style="width: 200px">
                        <Option v-for="item in orderStatusList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">申请仓库：</p>
                    <Select v-model="searchObj.warehouseId" filterable clearable style="width: 200px">
                        <Option v-for="item in warehouseList" :value="item.id" :key="item.id">{{ item.warehouseName }}</Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">提交时间：</p>
                    <DatePicker ref="formDateSh" :value="submitDate" @on-change="
                        (data) => {
                          return selectDate(data, 'submitDate');
                        }
                      " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择" style="width: 240px ">
                    </DatePicker>
                </div>
            </div>
            <div class="list_but" style="display: flex; justify-content: space-between; width: 100%;">
                <div class="condition">
                    <Button @click="add" type="primary">新增</Button>
                    <Button @click="edit" type="primary" :disabled="tableSelect.length !== 1 || tableSelect[0].orderStatus !== 'init'">编辑</Button>
                    <Button @click="submit" type="success" :disabled="tableSelect.length !== 1 || tableSelect[0].orderStatus !== 'init'">提交</Button>
                    <Button @click="cancel" type="error" :disabled="tableSelect.length !== 1 || tableSelect[0].orderStatus !== 'WAIT'">取消订单</Button>
                    <Button @click="del" type="error" :disabled="tableSelect.length !== 1 || tableSelect[0].orderStatus !== 'init'">删除</Button>
                    <Button @click="importFn" type="success">导入</Button>
                    <Button @click="exportFn" type="error">导出</Button>
                </div>
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                </div>
            </div>
            <div class="commListDiv">
                <Table size="small" border ref="tableRef" :columns="tableColumn" :data="commList" @on-selection-change="selected => this.tableSelect = selected">
                    <template slot-scope="{ index }" slot="sn">
                        {{ index + 1 }}
                    </template>
                    <template slot-scope="{ index }" slot="orderType">
                        {{ orderTypeMap[commList[index].orderType] }}
                        <!--                        <Input v-model="subTableData[index].submitNum" style="text-align: center" @on-change="tableInputChange" />-->
                    </template>
                    <template slot-scope="{ index }" slot="orderStatus">
                        {{ orderStatusMap[commList[index].orderStatus] }}
                    </template>
                    <template slot-scope="{ index }" slot="option">
                        <Button type="info" ghost size="small" @click="openDetail(commList[index])" style="margin: 3px">详情</Button>
                    </template>
                </Table>
            </div>
            <Page style="margin: 20px; text-align: right" @on-change="changeCurrent" :current="searchObj.page"
                  :total="totalElements" show-total show-elevator />
        </div>
        <!-- 取消订单 -->
        <Modal width="600" v-model="cancelShow" draggable sticky scrollable title="订单取消" :footer-hide="true"
               :mask-closable="false">
            <Form :label-width="80">
                <FormItem label="取消备注">
                    <Input v-model="cancelRemark" type="textarea" placeholder="请输入取消备注" clearable style="width: 400px" />
                </FormItem>
            </Form>
            <div style="text-align: right;">
                <Button type="primary"
                        @click="debounce(sub_cancel)">提交</Button>
                <Button type="default" style="margin-left: 10px"
                        @click="cancelShow = false">取消</Button>
            </div>
        </Modal>
        <detail ref="detailsRef" :orderTypeMap="orderTypeMap" :orderStatusMap="orderStatusMap" />
        <addEditPop ref="addEditPopRef" @getList="getList" />
        <!-- 添加 -->
        <Modal width="600" v-model="modelEditAdd" draggable sticky scrollable title="导入" :footer-hide="true" :styles="{ top: 'calc(100px + 4vh)' }"
               :mask-closable="false">
            <div style="position: relative; width: 100%; height: auto;">
                <Upload
                    multiple
                    type="drag"
                    :headers=headers
                    :action=actionUrl
                    :on-success="uploadThen"
                    :on-error="uploadThen"
                    :show-upload-list="false"
                >
                    <div style="padding: 20px 0">
                        <Icon type="ios-cloud-upload" size="150" style="color: #3399ff"></Icon>
                        <p>点击或拖动文件到这里上传</p>
                    </div>
                </Upload>
                <Button type="success" long @click="downloadTemplate" style="margin-top: 20px">下载服务商订单导入模板</Button>
            </div>
        </Modal>
    </div>
</template>

<script>
import _data from "@/views/ospSpare/_edata"; // 数据字典
import addEditPop from "./addEditPop";
import detail from './details'
import API from "@/api/ospSpare/orderManage/serviceProviderOrder";
import dict from '@/api/ospSpare'

export default {
    name: "serviceProviderOrder",
    components: { addEditPop, detail },
    data() {
        return {
            searchObj: {
                orderCode: '',
                woId: '',
                orderType: '',
                materialCode: '',
                orderStatus: '',
                branchName: '',
                submitDateStart: '',
                submitDateEnd: '',
                pageNum: 1,
                pageSize: 10
            },
            totalElements: 0,
            submitDate: [],
            orderTypeList: _data.orderType,
            orderStatusList: _data.orderStatus,
            warehouseList: [],
            orderTypeMap: {},
            orderStatusMap: {},
            commList: [],
            tableColumn: [
                { type: 'selection', minWidth: 60, title: '选择', align: 'center' },
                { title: '序号', minWidth: 80, align: 'center', slot: 'sn' },
                { title: '订单号', minWidth: 200, key: 'orderCode', align: 'center' },
                { title: '工单号', minWidth: 200, key: 'woId', align: 'center' },
                { title: '订单类型', minWidth: 100, key: 'orderType', align: 'center', slot: 'orderType' },
                { title: '订单状态', minWidth: 100, key: 'orderStatus', align: 'center', slot: 'orderStatus' },
                { title: '中心名称', minWidth: 100, key: 'centerName', align: 'center' },
                { title: '申请服务商', minWidth: 180, key: 'warehouseName', align: 'center' },
                { title: '发货仓库', minWidth: 180, key: 'suptBranchName', align: 'center' },
                { title: '提交时间', minWidth: 180, key: 'submitDate', align: 'center' },
                { title: '发货时间', minWidth: 180, key: 'sendDate', align: 'center' },
                { title: '发货单号', minWidth: 100, key: 'transferCode', align: 'center' },
                { title: '取消时间', minWidth: 180, key: 'cancelDate', align: 'center' },
                { title: '取消人', minWidth: 120, key: 'cancelBy', align: 'center' },
                { title: '取消原因', minWidth: 180, key: 'cancelDesc', align: 'center' },
                { title: '修改时间', minWidth: 180, key: 'updateDate', align: 'center' },
                { title: '订单创建时间', minWidth: 180, key: 'createDate', align: 'center' },
                { title: '操作', minWidth: 120, align: 'center', fixed: 'right', slot: 'option' }
            ],
            tableSelect: [],
            cancelShow: false,
            cancelRemark: '',
            modelEditAdd: false,
            token: '',
            actionUrl: `${process.env.VUE_APP_BASE_API}/merchant/repairs/spOrder/importData`,
            headers: { Authorization: `Bearer ${JSON.parse(window.localStorage.getItem("logins")).access_token}` },
        }
    },
    watch: {
        tableSelect(e) {
            console.log(e)
        }
    },
    mounted() {
        this.getList()
        this.getDict()
        this.token = JSON.parse(window.localStorage.getItem("logins"))?.access_token || 'unAuthor'
    },
    methods: {
        // 新增
        add() {
            this.$refs.addEditPopRef.modelTitle = '新增'
            this.$refs.addEditPopRef.modelEdit = true
        },
        // 编辑
        edit() {
            this.$refs.addEditPopRef.modelTitle = '编辑'
            this.$refs.addEditPopRef.orderId = this.tableSelect[0].id
            this.$refs.addEditPopRef.modelEdit = true
        },
        // 提交
        submit() {
            this.$message.confirm('是否确认提交?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$Message.loading({
                    content: "提交中...",
                    duration: 0,
                });
                API.subOrder({id: this.tableSelect[0].id}).then(res => {
                    if(res.data.success) {
                        this.$Message.destroy();
                        this.$Message.success(res.data.result);
                        this.getList()
                    } else {
                        this.$Message.destroy();
                        this.$Message.error(res.data.error);
                    }
                })
            })
        },
        // 取消
        cancel() {
            this.cancelShow = true
            this.cancelRemark = ''
        },
        // 提交取消
        sub_cancel() {
            const data = {
                orderId: this.tableSelect[0].id,
                cancelRemark: this.cancelRemark
            }
            this.$Message.loading({
                content: "取消中...",
                duration: 0,
            });
            API.cancelOrder(data).then(res => {
                if (res.data.success) {
                    this.$Message.destroy();
                    this.$Message.success(res.data.result);
                    this.cancelShow = false
                    this.getList()
                } else {
                    this.$Message.destroy();
                    this.$Message.error(res.data.error);
                }
            })
        },
        // 删除
        del() {
            this.$message.confirm('此操作将永久删除该文件, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$Message.loading({
                    content: "删除中...",
                    duration: 0,
                });
                API.delOrder({id: this.tableSelect[0].id}).then(res => {
                    if(res.data.success) {
                        this.$Message.destroy();
                        this.$Message.success(res.data.result);
                        this.getList()
                    } else {
                        this.$Message.destroy();
                        this.$Message.error(res.data.error);
                    }
                })
            })
        },
        // 导入
        importFn() {
            this.modelEditAdd = true
        },
        uploadThen(e) {
            console.log(e)
            if (e.success) {
                this.$Message.success(e.result);
                this.modelEditAdd = false
                this.queryList()
            } else {
                this.$Message.error(e.error);
            }
        },
        // 下载导入模板
        downloadTemplate() {
            API.exportTemplateList().then(res => {
                let binaryData = [];
                let link = document.createElement('a');
                binaryData.push(res.data);
                link.style.display = 'none';
                link.href = window.URL.createObjectURL(new Blob(binaryData));
                link.setAttribute('download', '服务商订单导入模板.xlsx');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            })
        },
        // 导出
        exportFn() {
            let datas = this.$_.cloneDeep(this.searchObj);
            datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
            delete datas.pageNum
            delete datas.pageSize
            API.exportList(datas).then(res => {
                let binaryData = [];
                let link = document.createElement('a');
                binaryData.push(res.data);
                link.style.display = 'none';
                link.href = window.URL.createObjectURL(new Blob(binaryData));
                link.setAttribute('download', '服务商订单列表.xlsx');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            })
        },
        // 打开详情
        openDetail(row) {
            this.$refs.detailsRef.orderId = row.id
            this.$refs.detailsRef.show = true
        },
        // 查询按钮
        queryList() {
            this.searchObj.pageNum = 1;
            this.getList();
        },

        // 重置按钮
        emptyList() {
            this.searchObj = {
                orderCode: '',
                woId: '',
                orderType: '',
                orderStatus: '',
                branchName: '',
                submitDateStart: '',
                submitDateEnd: '',
                pageNum: 1,
                pageSize: 10
            };
            this.submitDate = [];
        },
        // 选择日期
        selectDate(date, refObj) {
            this.submitDate = date;
            this.searchObj[refObj + "Start"] = date[0];
            this.searchObj[refObj + "End"] = date[1];
        },
        // 获取工单列表
        getList() {
            let datas = this.$_.cloneDeep(this.searchObj);
            datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
            delete datas.pageNum
            delete datas.pageSize
            const page = { page: this.searchObj.pageNum, rows: this.searchObj.pageSize }
            // 将所有选中项清空
            this.$refs.tableRef.selectAll(false);
            API.getList(page, datas).then(res => {
                this.commList = res.data.result.content
                this.totalElements = res.data.result.totalElements
            })
        },
        // 获取下拉数据
        getDict() {
            // 订单类型map
            this.orderTypeList.forEach(item => {
                this.orderTypeMap[item.value] = item.label
            })
            // 订单状态map
            this.orderStatusList.forEach(item => {
                this.orderStatusMap[item.value] = item.label
            })
            // 获取服务商数据
            dict.getWarehouse({ activeFlag: '1', warehouseLevel: '3' }).then(res => {
                const list = []
                res.data.result.forEach(item => {
                    if (list.indexOf(item.warehouseCode) === -1 && item.warehouseCode) {
                        list.push(item.warehouseCode)
                        this.warehouseList.push(item)
                    }
                })
            })
        },
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.pageNum = curr;
            this.getList();
        },
        jumpto(path, id, count) {
            this.$router.push({
                path,
                query: { id, count },
            });
        }
    }
}
</script>

<style lang="scss" scoped>
@import 'style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
