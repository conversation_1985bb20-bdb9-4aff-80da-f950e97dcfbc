<template>
    <div>
    <Modal width="1200" v-model="modelEdit" draggable sticky scrollable :title="modelTitle" :footer-hide="true"
           :mask-closable="false">
        <div style="position: relative; width: 100%; height: 60vh;">
            <div style="padding-bottom: 40px; width: 100%; height: 60vh; overflow-y: auto">
                <Form :model="formItem" :label-width="100" ref="formItem" inline>
                    <FormItem label="申请服务商">
                        <Input type="text" maxlength="15" v-model="formItem.warehouseName" clearable placeholder="申请服务商"
                               style="width: 360px" disabled />
                    </FormItem>
                    <FormItem label="收货地址" prop="duty">
                        <div class="infoArea">
                            <div style="height: 20px;">{{this.formItem.province}}{{this.formItem.city}}{{this.formItem.region}} </div>
                            <div style="height: 20px;">{{this.formItem.receiveAddress}} </div>
                            <div style="height: 20px;">{{this.formItem.linkMan}} {{this.formItem.phone}} </div>
                        </div>
                    </FormItem>
                    <Button @click="handleChangeAddress" class="AddrssButtom" size="small" type="text" >修改地址</Button>
                </Form>
                <div class="list_but" style="margin-bottom: 10px">
                    <div class="condition">
                        <Button @click="modelEditAdd = true" size="small" type="primary">添加</Button>
                        <Button style="margin-left: 6px" @click="del" size="small" type="error" :disabled="!subTableSelect.length">删除</Button>
                    </div>
                </div>
                <div class="commListDiv">
                    <Table size="small" border ref="subTableRef" :columns="subTableColumn" :data="subTableData" @on-selection-change="selected => this.subTableSelect = selected">
                        <template slot-scope="{ index }" slot="requireAmount">
                        <!--    <Input type="number" v-model="subTableData[index].requireAmount" />-->
                            <InputNumber :min="0" v-model="subTableData[index].requireAmount" style="text-align: center" @on-change="tableInputChange" />
                        </template>
                        <!--   备件品牌-->
                        <template slot-scope="{ index }" slot="materialBrand">
                            {{materialBrandMap[subTableData[index].materialBrand]}}
                        </template>
                        <!--     备件分类-->
                        <template slot-scope="{ index }" slot="materialType">
                            {{materialTypeMap[subTableData[index].materialType]}}
                        </template>
                    </Table>
                </div>
                <!-- 添加 -->
                <Modal width="1000" v-model="modelEditAdd" draggable sticky scrollable title="添加明细" :footer-hide="true" :styles="{ top: 'calc(100px + 4vh)' }"
                       :mask-closable="false">
                    <div style="position: relative; width: 100%; height: 50vh;">
                        <div style="padding-bottom: 40px; width: 100%; height: 50vh; overflow-y: auto">
                            <Form :model="addDetailQuery" :label-width="100" ref="formItem" inline>
                                <FormItem label="备件编码">
                                    <Input type="text" maxlength="15" v-model="addDetailQuery.materialCode" clearable placeholder="备件编码"
                                           style="width: 260px" />
                                </FormItem>
                                <FormItem label="备件描述" >
                                    <Input type="text" maxlength="15" v-model="addDetailQuery.materialDesc" clearable placeholder="备件描述"
                                           style="width: 260px" />
                                </FormItem>
                                <FormItem label="备件品牌" >
                                    <Select v-model="addDetailQuery.materialBrand" filterable clearable style="width: 260px">
                                        <Option v-for="item in materialBrandOptions" :value="item.dictValue" :label="item.dictLabel" :key="item.id"></Option>
                                    </Select>
                                </FormItem>
                                <FormItem label="备件分类" >
                                    <Select v-model="addDetailQuery.materialType" filterable clearable style="width: 260px">
                                        <Option v-for="item in materialTypeOptions" :value="item.dictValue" :label="item.dictLabel" :key="item.id"></Option>
                                    </Select>
                                </FormItem>
                            </Form>
                            <div class="list_but" style="margin-bottom: 10px">
                                <div class="condition">
                                    <Button @click="debounce(getDetailList)" size="small" type="primary">查询</Button>
                                    <Button @click="debounce(emptyList)" style="margin-left: 6px" size="small">重置</Button>
                                </div>
                            </div>
                            <div class="commListDiv">
                                <Table size="small" border ref="selectTableRef" :columns="selectTableColumn" :data="selectTableData" @on-selection-change="selected => this.selectTableSelect = selected">
                                    <template slot-scope="{ index }" slot="productStatus">
                                        {{ productMap[selectTableData[index].productStatus] }}
                                    </template>
                                    <!--   备件品牌-->
                                    <template slot-scope="{ index }" slot="materialBrand">
                                        {{materialBrandMap[selectTableData[index].materialBrand]}}
                                    </template>
                                    <!--     备件分类-->
                                    <template slot-scope="{ index }" slot="materialType">
                                        {{materialTypeMap[selectTableData[index].materialType]}}
                                    </template>
                                </Table>
                            </div>
                            <Page style="margin: 20px; text-align: right" @on-change="changeCurrent" :current="queryPage.page"
                                  :total="selectTableTotal" show-total show-elevator />
                        </div>
                        <div style="position: absolute; z-index: 100; right: 20px; bottom: -30px; text-align: right; margin: 30px 0">
                            <Button type="primary"
                                    :disabled="!selectTableSelect.length"
                                    @click="debounce(submitSelect)">保存</Button>
                            <Button type="default" style="margin-left: 10px"
                                    @click="modelEditAdd = false">取消</Button>
                        </div>
                    </div>
                </Modal>
            </div>
            <div style="position: absolute; z-index: 99; right: 20px; bottom: -30px; text-align: right; margin: 30px 0">
                <Button type="primary"
                        :disabled="!subTableData.length"
                        @click="debounce(submitPop)">保存</Button>
                <Button type="default" style="margin-left: 10px"
                        @click="modelEdit = false">取消</Button>
            </div>
        </div>
    </Modal>
    <Address ref="addressRef" @choseAddress="choseAddress"/>
    </div>
</template>

<script>
import API from "@/api/ospSpare/orderManage/serviceProviderOrder";
import Common from "@/api/ospSpare/common";
import _data from "@/views/ospSpare/_edata"; // 数据字典
import Address from "./address.vue";
import detail from "@/views/ospSpare/orderManage/serviceProviderOrder/details.vue";
export default {
    name: "addEdit",
    components:{Address},
    data() {
        return {
            modelEdit: false,
            modelTitle: '新增',
            orderId: '',
            data: {},
            queryPage: { // 查询条件 No页数 Size每页大小
                page: 1,
                rows: 10
            },
            formItem: {
                warehouseName: '',
                address: '',
                linkMan: '',
                phone: '',
                province: '',
                city: '',
                region: '',
                receiveAddress: '',
            },
            subTableSelect: [],
            subTableData: [],
            subTableColumn: [
                {type: 'selection', width: 60, title: '选择', align: 'center'},
                {title: '备件编码',width: 160, key: 'materialCode', align: 'center'},
                {title: '备件描述',width: 200, key: 'materialDesc', align: 'center'},
                {title: '备件品牌', key: 'materialBrand', align: 'center',slot: 'materialBrand'},
                {title: '备件分类', key: 'materialType', align: 'center',slot: 'materialType'},
                {title: '供货商', key: 'providerName', align: 'center'},
                {title: '单位', key: 'materialUnit', align: 'center'},
                {title: '单价', key: 'materialPrice', align: 'center'},
                {title: '申请数量', key: 'requireAmount', slot: 'requireAmount', align: 'center'}
            ],
            modelEditAdd: false,
            addDetailQuery: {
                materialCode: '',
                materialDesc: '',
                materialBrand: '',
                materialType: '',
                productStatus: '1',
                ifModule: '0'
            },
            selectTableSelect: [],
            selectTableColumn: [
                {type: 'selection', width: 60, title: '选择', align: 'center'},
                {title: '备件编码', width: 160, key: 'materialCode', align: 'center'},
                {title: '备件描述', width: 200, key: 'materialDesc', align: 'center'},
                {title: '备件品牌', key: 'materialBrand', align: 'center',slot: 'materialBrand'},
                {title: '备件分类', key: 'materialType', align: 'center',slot: 'materialType'},
                {title: '备件规格', key: 'materialUnit', align: 'center'},
                {title: '供货商名称', key: 'providerName', align: 'center'},
                {title: '生产状态', key: 'productStatus', align: 'center', slot: 'productStatus'},
            ],
            selectTableData: [], // 添加明细列表
            selectTableTotal: 0, // 添加明细列表总条数
            productMap: {},
            materialBrandOptions:[],
            materialBrandMap:{},
            materialTypeOptions:[],
            materialTypeMap:{},
        }
    },
    watch: {
        // 打开新增弹窗
        modelEdit(val) {
            if (val) {
                _data.productStatus.forEach(item => {
                    this.productMap[item.value] = item.label
                })
                this.subTableData = []
                this.$refs.subTableRef.selectAll(false);
                this.selectTableData = []
                this.$refs.selectTableRef.selectAll(false);
                if (this.modelTitle === '新增') this.add_init()
                else this.edit_init()
            }
        },
        // 打开添加明细弹窗
        modelEditAdd(val) {
            if (val) {
                this.getDetailList()
            }
        }
    },
    mounted() {
        this.getSelect()
    },
    methods: {
        // 选择地址回显
        choseAddress(addressObj){
                this.formItem.linkMan=addressObj.linkMan
                this.formItem.phone=addressObj.phone
                this.formItem.province=addressObj.province
                this.formItem.city=addressObj.city
                this.formItem.region=addressObj.region
                this.formItem.receiveAddress=addressObj.receiveAddress
        },
        //打开 修改地址 弹框
        handleChangeAddress(){
            this.$refs.addressRef.modelEdit = true
        },
        //获取下拉数据
        getSelect(){
            Promise.all([
                Common.getDict({dictType: 'materialBrand'}),
                Common.getDict({dictType: 'materialType'}),
            ]).then(res => {
                this.materialBrandOptions = res[0].data?.result || []
                this.materialTypeOptions = res[1].data?.result || []
                if(this.materialBrandOptions.length>0){
                    this.materialBrandOptions.forEach(item=>{
                        this.materialBrandMap[item.dictValue]=item.dictLabel
                    })
                }
                if(this.materialTypeOptions.length>0){
                    this.materialTypeOptions.forEach(item=>{
                        this.materialTypeMap[item.dictValue]=item.dictLabel
                    })
                }
            }).catch(e => {
                console.log(e);
                this.$Message.error('获取下拉失败')
            })

        },
        // 新增订单初始化
        add_init() {
            const member_id = JSON.parse(window.localStorage.getItem('logins')).member_id
            API.addAddressLoading(member_id).then(res => {
                this.formItem.warehouseName = res.data.result.warehouse.warehouseName
                this.formItem.phone = res.data.result.warehouseAddress.phone
                this.formItem.linkMan = res.data.result.warehouseAddress.linkMan
                this.formItem.province = res.data.result.warehouseAddress.province
                this.formItem.city = res.data.result.warehouseAddress.city
                this.formItem.region = res.data.result.warehouseAddress.region
                this.formItem.receiveAddress = res.data.result.warehouseAddress.receiveAddress
                this.data = res.data.result.warehouse
            })
        },
        // 编辑订单初始化
        edit_init() {
            API.editLoading(this.orderId).then(res => {
                const data = res.data.result || {}
                this.formItem.warehouseName = data.warehouseName || ''
                this.formItem.address = data.receiveAddress || ''
                this.subTableData = data.orderDetailList || []
                this.data = data
                this.formItem.phone = data.phone
                this.formItem.linkMan = data.linkMan
                this.formItem.province = data.province
                this.formItem.city = data.city
                this.formItem.region = data.region
                this.formItem.receiveAddress = data.receiveAddress
            })
        },
        del() {
            this.$Message.success('删除成功!');
            this.subTableData = this.subTableData.filter(item => !this.subTableSelect.some(v => v.materialCode === item.materialCode))
            this.$refs.subTableRef.selectAll(false);
        },
        changeCurrent(e) {
            this.queryPage.page = e
            this.getDetailList()
        },
        // 获取添加明细接口
        getDetailList() {
            let data = this.addDetailQuery
            if (this.modelTitle === '编辑') data.deptmentId = this.data.suptWarehouseId
            data.ifModule = '0'
            API.getDetailList(this.queryPage, data).then(res => {
                this.$refs.selectTableRef.selectAll(false);
                this.selectTableTotal = res.data.result.totalElements
                this.selectTableData = res.data.result.content
            })
        },
        // 清空明细信息查询条件
        emptyList() {
            this.addDetailQuery = {
                materialCode: '',
                materialDesc: ''
            }
        },
        tableInputChange(e) {
            this.$refs.subTableRef.selectAll(false);
        },
        submitSelect() {
            // settlePrice
            const selectList = []
            this.selectTableSelect.forEach(item => {
                const _item = item
                _item.materialPrice = _item.settlePrice
                _item.requireAmount = 1
                selectList.push(_item)
            })
            // 重复选中的订单
            const reqOrder = selectList.filter(item => this.subTableData.some(v => v.materialCode === item.materialCode))
            // 没有重复 可追加上的订单
            const addOrder = selectList.filter(item => !this.subTableData.some(v => v.materialCode === item.materialCode))
            // 重复订单提示标记
            let reqMsg = []
            reqOrder.forEach(item => {
                reqMsg.push(item.materialCode)
            })
            reqMsg = reqMsg.toString()
            if (addOrder.length && !reqOrder.length) {
                this.subTableData.push(...addOrder)
                this.modelEditAdd = false
                this.$refs.selectTableRef.selectAll(false);
                this.$Message.success('添加成功!');
            } else if (addOrder.length && reqOrder.length) {
                this.subTableData.push(...addOrder)
                this.modelEditAdd = false
                this.$refs.selectTableRef.selectAll(false);
                this.$Message.success('添加成功!去除重复订单：' + reqMsg);
            } else if (!addOrder.length && reqOrder.length) {
                this.$Message.error
                ('当前选中订单已添加：' + reqMsg);
            }
        },
        submitPop() {
            if (this.modelTitle === '新增') {
                const orderDetailList = []
                this.subTableData.forEach(item => {
                    orderDetailList.push({
                        materialId: item.id,
                        materialCode: item.materialCode,
                        materialDesc: item.materialDesc,
                        materialUnit: item.materialUnit,
                        standard: item.sendStandard,
                        materialPrice: item.settlePrice,
                        requireAmount: item.requireAmount,
                        suptWarehouseId: item.deptmentId
                    })
                })
                const data = {
                    warehouseId: this.data.id,
                    branchId: this.data.branchId,
                    orderDetailList: orderDetailList,
                    linkMan: this.formItem.linkMan,
                    phone: this.formItem.phone,
                    province: this.formItem.province,
                    city: this.formItem.city,
                    region: this.formItem.region,
                    receiveAddress: this.formItem.receiveAddress,
                }
                this.$Message.loading({
                    content: "提交中...",
                    duration: 0,
                });
                API.addOrderSave(data).then(res => {
                    if (res.data.success) {
                        this.$Message.destroy();
                        this.$Message.success(res.data.result);
                        this.$emit('getList')
                        this.modelEdit = false
                    } else {
                        this.$Message.destroy();
                        this.$Message.error(res.data.error);
                    }
                })
            } else {
                const orderDetailList = []
                ///
                this.subTableData.forEach(item => {
                    orderDetailList.push({
                        id: item.materialId ? item.id : null,
                        materialId: item.materialId ? item.materialId : item.id,
                        materialCode: item.materialCode,
                        materialDesc: item.materialDesc,
                        materialUnit: item.materialUnit,
                        standard: item.sendStandard,
                        materialPrice: item.settlePrice,
                        requireAmount: item.requireAmount,
                        suptWarehouseId: item.deptmentId
                    })
                })
                const data = {
                    id: this.data.id,
                    branchId: this.data.branchId,
                    orderDetailList: orderDetailList,
                    linkMan: this.formItem.linkMan,
                    phone: this.formItem.phone,
                    province: this.formItem.province,
                    city: this.formItem.city,
                    region: this.formItem.region,
                    receiveAddress: this.formItem.receiveAddress,
                }
                this.$Message.loading({
                    content: "提交中...",
                    duration: 0,
                });
                API.editOrderSave(data).then(res => {
                    if (res.data.success) {
                        this.$Message.destroy();
                        this.$Message.success(res.data.result);
                        this.$emit('getList')
                        this.modelEdit = false
                    } else {
                        this.$Message.destroy();
                        this.$Message.error(res.data.error);
                    }
                })
            }
        }
    }
}
</script>

<style lang="scss" scoped>
@import '@/style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
.AddrssButtom{
    position: absolute;
   left: 496px;
    top: 40px;
    color: #2d8cf0;
}
.infoArea{
    width: 360px;
    height: 80px;
    background-color: #f3f3f3;
    opacity: 1;
    cursor: not-allowed;
    color: #ccc;
    padding: 4px 7px;
    border: 1px solid #dcdee2;
    border-radius: 4px;
}
</style>
