<template>
    <Modal width="80%" v-model="show" title="详情" :footer-hide="true" :styles="{ top: '5vh' }"
           :mask-closable="false">
        <div style="overflow-y: auto; width: 100%; height: 80vh; background: #eee; padding: 20px">
            <Card :bordered="false">
                <div class="cus_list" style="min-height: calc(80vh - 72px)">
<!--                    <div class="list_son">
                        <div class="step">
                            <Steps class="stepdiv" :current="4">
                                <Step v-for="(item, index) in infoList.processLogs" :key="index" :content="timeChange(item.createdAt)"
                                      :title="item.desc"></Step>
                            </Steps>
                        </div>
                    </div>-->
                    <div class="cus_reg">
                        <div class="cus_form" style="padding-top: 0">
                            <p class="cus_title">主单信息</p>
                            <el-descriptions class="margin-top" :column="3" border>
                                <el-descriptions-item>
                                    <template slot="label">
                                        订单号
                                    </template>
                                    {{ data.orderCode }}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        申请仓库
                                    </template>
                                    {{ data.warehouseName }}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        发货仓库
                                    </template>
                                    {{ data.suptWarehouseName }}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        订单状态
                                    </template>
                                    {{ orderStatusMap[data.orderStatus] }}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        订单类型
                                    </template>
                                    {{ orderTypeMap[data.orderType] }}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        提交时间
                                    </template>
                                    {{ data.submitDate }}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        物流单号
                                    </template>
                                    {{ data.transferCode }}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        发货时间
                                    </template>
                                    {{ data.sendDate }}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template slot="label">
                                        收货地址
                                    </template>
                                    <div> {{ data.province }} {{ data.city }} {{ data.region }}</div>
                                    <div> {{ data.linkMan }} {{ data.phone?data.phone:'-' }}</div>
                                </el-descriptions-item>
                            </el-descriptions>
                            <p class="cus_title">明细信息</p>
                            <table class="cus_table" border="0" cellspacing="0" cellpadding="0" width="100%">
                                <tr>
                                    <th align="center">序号</th>
                                    <th align="center">备件编码</th>
                                    <th align="center">备件描述</th>
                                    <th align="center">单位</th>
                                    <th align="center">单价</th>
                                    <th align="center">需求数量</th>
                                    <th align="center">发货数量</th>
                                    <th align="center">匹配数量</th>
                                    <th align="center">取消数量</th>
                                    <th align="center">工单备件申请ID</th>
                                </tr>
                                <tr v-for="(item, index) in data.orderDetailList" :key="index">
                                    <td align="center" width="80">{{ index + 1 }}</td>
                                    <td align="center" width="120">{{ item.materialCode }}</td>
                                    <td align="center" width="120">{{ item.materialDesc }}</td>
<!--                                    <td align="center" width="120">{{ item.warehouseName }}</td>-->
                                    <td align="center" width="120">{{ item.materialUnit }}</td>
                                    <td align="center" width="120">{{ item.materialPrice }}</td>
                                    <td align="center" width="120">{{ item.requireAmount }}</td>
                                    <td align="center" width="120">{{ item.confirmSendAmount }}</td>
                                    <td align="center" width="120">{{ item.deelAmount }}</td>
                                    <td align="center" width="120">{{ item.cancelAmount }}</td>
                                    <td align="center" width="120">{{ item.woPartId }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </Card>
        </div>
    </Modal>
</template>

<script>
import _data from "@/views/ospSpare/_edata"; // 数据字典
import API from "@/api/ospSpare/orderManage/serviceProviderOrder";
// import { Desensitized } from "@/utils/Desensitized";

export default {
    props: {
        orderTypeMap: {
            type: Object,
            default: () => {
                return {}
            }
        },
        orderStatusMap: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
  data() {
    return {
        show: false,
        orderId: '',
      infoList: {},
        data: {},
        // orderTypeList: _data.orderType,
        // orderStatusList: _data.orderStatus,
      count: ''
    };
  },
    watch: {
        show(val) {
            if (val) this.getInfo();
        }
    },
    computed: {
        // desensitized() {
        //     return Desensitized;
        // }
    },
  methods: {

    // typeNode(list, value) {
    //   const obj = {};
    //   this[list].map((e) => {
    //     obj[e.value] = e.node;
    //   });
    //   return obj[value];
    // },

    timeChange(params) {
      // console.log(params);
      let dates = "";
      if (params) {
        dates = params.replace("T", " ");
      }
      return dates;
    },
    // 获取工单详情
    getInfo() {
        API.orderDetail(this.orderId).then(res => {
            this.data = res.data.result || {}
        })


      let datas = {
        workOrderSn: this.$route.query.id,
      };
      this.count = this.$route.query.count
      setTimeout(() => {
          const request = {
              "workOrderSn": "FX20230912102844372",
              "status": "CLOSED",
              "opName": "新能源测试公司",
              "opStaff": null,
              "opStaffMobile": null,
              "stationCode": "230522094432966",
              "stationName": "葛海超103",
              "stationPhone": "18561903791",
              "stationProvinceName": "山东省",
              "stationCityName": "青岛市",
              "stationRegionName": "平度市",
              "stationAddress": "测试地址050012",
              "subCenterCode": "GFTS",
              "subCenterName": "唐山",
              "faultCode": null,
              "faultLevel": null,
              "faultCodeType": null,
              "faultCodeDesc": null,
              "productType": "逆变器",
              "brandName": "孚日",
              "sku": "LIGHTNBQ001",
              "inveterFactory": null,
              "falutReason": null,
              "falutAppearance": null,
              "resolvent": null,
              "arrangeUserTime": null,
              "doorTime": null,
              "completeTime": null,
              "imgBefore": null,
              "imgAfter": null,
              "source": "TRIPPING_OPERATION_STATION",
              "workOrderType1": null,
              "workOrderType2": null,
              "workOrderType3": null,
              "linkName": null,
              "linkMobile": null,
              "linkFaultAppearance": null,
              "linFaultDesc": null,
              "repairOrderSn": "TZ20230911151704547",
              "remark": null,
              "expireDays": null,
              "expireStatus": "NOT_EXPIRED",
              "logs": [
                  {
                      "id": 1288,
                      "workOrderSn": "FX20230912102844372",
                      "desc": "已关闭",
                      "nodeType": "LOG",
                      "createdAt": "2023-09-12T10:29:09"
                  }
              ],
              "processLogs": [
                  {
                      "id": null,
                      "workOrderSn": "FX20230912102844372",
                      "desc": "待派工",
                      "nodeType": "PROCESS_IMG",
                      "createdAt": "2023-09-12T10:28:44"
                  },
                  {
                      "id": null,
                      "workOrderSn": "FX20230912102844372",
                      "desc": "已派工",
                      "nodeType": "PROCESS_IMG",
                      "createdAt": null
                  },
                  {
                      "id": null,
                      "workOrderSn": "FX20230912102844372",
                      "desc": "服务中",
                      "nodeType": "PROCESS_IMG",
                      "createdAt": null
                  },
                  {
                      "id": null,
                      "workOrderSn": "FX20230912102844372",
                      "desc": "已完工",
                      "nodeType": "PROCESS_IMG",
                      "createdAt": null
                  },
                  {
                      "id": null,
                      "workOrderSn": "FX20230912102844372",
                      "desc": "已关闭",
                      "nodeType": "PROCESS_IMG",
                      "createdAt": "2023-09-12T10:29:09"
                  }
              ],
              "receiveModules": null,
              "usedModules": null,
              "notUsedModules": null,
              "leproblem": null,
              "lcproblem": null,
              "prodesc": null
          }
          this.infoList = request;
          this.infoList.area = (request.stationProvinceName || "") + (request.stationCityName || "") + (request.stationRegionName || "");
      }, 1000)
    },
  },
};
</script>

<style scoped lang="scss">
@import "@/style/_cus_list.scss";
@import "@/style/_cus_reg.scss";

.step {
  position: relative;
  margin: 0 20px;
  padding: 12px 6px 6px;
  border: 1px solid #e8eaec;

  .stepdiv {
    width: 80%;
    margin: 0 auto;
    @extend pt;
  }
}

.cus_title {
    margin-top: 20px;
    margin-bottom: 6px;
}
</style>
