<template>
    <Modal width="1200" v-model="modelEdit" draggable sticky scrollable :title="modelType === 'add' ? '新增' : '编辑'" :footer-hide="true"
           :mask-closable="false" :loading="loading">
        <div style="position: relative; width: 100%; height: 60vh;">
            <div style="padding-bottom: 40px; width: 100%; height: 60vh; overflow-y: auto">
                <Form :model="form" :label-width="100" ref="form" inline>
                    <FormItem label="申请供应商">
                        <Input type="text" maxlength="15" v-model="form.branchName" clearable placeholder="申请供应商"
                               style="width: 360px" disabled />
                    </FormItem>
                </Form>
                <div class="list_but" style="margin-bottom: 10px">
                    <div class="condition">
                        <Button @click="add()" size="small" type="primary">添加</Button>
                        <Button style="margin-left: 6px" @click="del" size="small" type="error" :disabled="!subTableSelect.length">删除</Button>
                    </div>
                </div>
                <div class="commListDiv">
                    <Table size="small" border ref="subTableRef" :columns="subTableColumn" :data="subTableData" @on-selection-change="selected => this.subTableSelect = selected">
                        <template slot-scope="{ index }" slot="requestAmount">
                            <InputNumber :min="0" v-model="subTableData[index].requestAmount" style="text-align: center" />
                        </template>
                    </Table>
                </div>
                <!-- 添加 -->
                <Modal width="1000" v-model="modelEditAdd" draggable sticky scrollable :title="modelTitle" :footer-hide="true" :styles="{ top: 'calc(100px + 4vh)' }"
                       :mask-closable="false" :loading="modalLoading">
                    <div style="position: relative; width: 100%; height: 50vh;">
                        <div style="padding-bottom: 40px; width: 100%; height: 50vh; overflow-y: auto">
                            <Form :model="addDetailQuery" :label-width="100" ref="form" inline>
                                <FormItem label="备件编码">
                                    <Input type="text" maxlength="15" v-model="addDetailQuery.materialCode" clearable placeholder="备件编码"
                                           style="width: 160px" />
                                </FormItem>
                                <FormItem label="备件描述" prop="duty">
                                    <Input type="text" maxlength="15" v-model="addDetailQuery.materialDesc" clearable placeholder="备件描述"
                                           style="width: 160px" />
                                </FormItem>
                                <FormItem label="备件品牌" >
                                    <Select v-model="addDetailQuery.materialBrand" filterable clearable style="width: 260px">
                                        <Option v-for="item in materialBrandOptions" :value="item.dictValue" :label="item.dictLabel" :key="item.id"></Option>
                                    </Select>
                                </FormItem>
                                <FormItem label="备件分类" >
                                    <Select v-model="addDetailQuery.materialType" filterable clearable style="width: 260px">
                                        <Option v-for="item in materialTypeOptions" :value="item.dictValue" :label="item.dictLabel" :key="item.id"></Option>
                                    </Select>
                                </FormItem>
                            </Form>
                            <div class="list_but" style="margin-bottom: 10px">
                                <div class="condition">
                                    <Button @click="debounce(queryList)" size="small" type="primary">查询</Button>
                                    <Button @click="debounce(emptyList)" style="margin-left: 6px" size="small">重置</Button>
                                </div>
                            </div>
                            <div class="commListDiv">
                                <Table size="small" border ref="selectTableRef" :columns="selectTableColumn" :data="selectTableData" @on-selection-change="selected => this.selectTableSelect = selected" >
                                    <template slot-scope="{ row }" slot="productStatus">
                                        {{ enums.productStatusEnum[row.productStatus + ''] }}
                                    </template>
                                </Table>
                            </div>
                            <Page style="margin: 20px" @on-change="changeCurrent" :current="addDetailQuery.page"
                                  :total="totalElements" show-total show-elevator />
                        </div>
                        <div style="position: absolute; z-index: 100; right: 20px; bottom: -30px; text-align: right; margin: 30px 0">
                            <Button type="primary"
                                    :disabled="!selectTableSelect.length"
                                    @click="debounce(submitSelect)">添加</Button>
                            <Button type="default" style="margin-left: 10px"
                                    @click="modelEditAdd = false">取消</Button>
                        </div>
                    </div>
                </Modal>
            </div>
            <div style="position: absolute; z-index: 99; right: 20px; bottom: -30px; text-align: right; margin: 30px 0">
                <Button type="primary"
                        :disabled="!subTableData.length"
                        @click="debounce(submitPop)">保存</Button>
                <Button type="default" style="margin-left: 10px"
                        @click="modelEdit = false">取消</Button>
            </div>
        </div>
    </Modal>
</template>

<script>
import API from "@/api/ospSpare/orderManage/PO";
import Common from "@/api/ospSpare/common";

export default {
    name: "addEdit",
    props: {
        modelType: {
            type: String,
            default: ""
        },
        rowData: {
            type: Object,
            default: () => {}
        },
        warehouseList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            loading: false,
            modalLoading: false,
            modelEdit: false,
            modelTitle: '新增',
            form: {
                branchName: '',
                branchId: ''
            },
            userBranchInfo: {
            },
            subTableSelect: [],
            subTableData: [
            ],
            subTableColumn: [
                {
                    type: 'selection',
                    width: 60,
                    title: '选择',
                    align: 'center'
                },
                {
                    title: '备件编码',
                    key: 'materialCode',
                    align: 'center'
                },
                {
                    title: '备件描述',
                    key: 'materialDesc',
                    align: 'center'
                },
                {
                    title: '单位',
                    key: 'materialUnit',
                    align: 'center'
                },
                {
                    title: '单价',
                    key: 'settlePrice',
                    align: 'center'
                },
                {
                    title: '申请数量',
                    key: 'requestAmount',
                    slot: 'requestAmount',
                    align: 'center'
                }
            ],
            modelEditAdd: false,
            addDetailQuery: {
                materialCode: '',
                materialDesc: '',
                materialBrand: '',
                materialType: '',
                pageNum: 1,
                pageSize: 10
            },
            totalElements: 0,
            selectTableSelect: [],
            selectTableColumn: [
                {
                    type: 'selection',
                    width: 60,
                    title: '选择',
                    align: 'center'
                },
                {
                    title: '备件编码',
                    key: 'materialCode',
                    align: 'center'
                },
                {
                    title: '备件描述',
                    key: 'materialDesc',
                    align: 'center'
                },
                {
                    title: '备件规格',
                    key: 'materialUnit',
                    align: 'center'
                },
                {
                    title: '生产状态',
                    slot: 'productStatus',
                    align: 'center'
                },

            ],
            selectTableData: [
            ],
            enums: {
                productStatusEnum: {
                    "0": "停产",
                    "1": "生产"
                }
            },
            submitFun: {
                "add": API.add,
                "edit": API.update
            },
            materialBrandOptions:[],
            materialTypeOptions:[],
        }
    },
    mounted() {
        this.getSelect()
    },
    methods: {
        //获取下拉数据
        getSelect(){
            Promise.all([
                Common.getDict({dictType: 'materialBrand'}),
                Common.getDict({dictType: 'materialType'}),
            ]).then(res => {
                this.materialBrandOptions = res[0].data?.result || []
                this.materialTypeOptions = res[1].data?.result || []
            }).catch(e => {
                console.log(e);
                this.$Message.error('获取下拉失败')
            })

        },
        // 初始化
        init() {
            this.subTableData = []
            this.subTableSelect = []
            const userStr = window.localStorage.getItem('userInfo')
            if (userStr) {
                const userInfo = JSON.parse(userStr)
                const list = this.warehouseList.filter(item => item.memberId == userInfo.memberId)
                this.userBranchInfo = list.length > 0 ? list[0] : {}
                this.form.branchName = list.length > 0 ? list[0].warehouseName : ""
                this.form.branchId =  list.length > 0 ? list[0].branchId : ""
                this.form.storeId = list.length > 0 ? list[0].id : ""
            }
            if (this.modelType === 'edit') {
                this.getInfo()
            }
        },
        getInfo() {
            this.loading = true
            API.getInfo({ id: this.rowData.id }).then(res => {
                this.loading = false
                const resData = res.data
                if (resData.success) {
                    this.form = resData.result || {}
                    this.subTableData = this.form.spPurchaseDetail || []
                    const list = this.warehouseList.filter(item => item.branchId == this.form.branchId)
                    this.userBranchInfo = list.length > 0 ? list[0] : {}
                    this.form.branchName = list.length > 0 ? list[0].warehouseName : ""
                } else {
                    this.orderData = {}
                    this.subTableData = []
                    this.$Message.error(resData.error || resData.message || '获取数据失败');
                }
            }).catch(e => {
                console.log(e);
                this.loading = false
                this.$Message.error("获取服务失败，请重试")
                this.form = {}
                this.subTableData = []
            })
        },
        del() {
            this.$Message.success('删除!');
            this.subTableData = this.subTableData.filter(item => !this.subTableSelect.some(v => v.materialCode === item.materialCode))
        },
        tableInputChange(e) {
            this.$refs.subTableRef.selectAll(false);
            console.log(e)
        },
        submitSelect() {
            // 重复选中的订单
            const reqOrder = this.selectTableSelect.filter(item => this.subTableData.some(v => v.materialCode === item.materialCode))
            // 没有重复 可追加上的订单
            const addOrder = this.selectTableSelect.filter(item => !this.subTableData.some(v => v.materialCode === item.materialCode))
            // 重复订单提示标记
            let reqMsg = []
            reqOrder.forEach(item => {
                reqMsg.push(item.materialCode)
            })
            // 申请数量默认赋值1
            addOrder.forEach(item=>{
                item.requestAmount=1
            })
            reqMsg = reqMsg.toString()
            if (addOrder.length && !reqOrder.length) {
                this.subTableData.push(...addOrder)
                this.modelEditAdd = false
                this.$refs.selectTableRef.selectAll(false);
                this.$Message.success('添加成功!');
            } else if (addOrder.length && reqOrder.length) {
                this.subTableData.push(...addOrder)
                this.modelEditAdd = false
                this.$refs.selectTableRef.selectAll(false);
                this.$Message.success('添加成功!去除重复订单：' + reqMsg);
            } else if (!addOrder.length && reqOrder.length) {
                this.$Message.error
                ('当前选中订单已添加：' + reqMsg);
            }
        },
        add() {
            this.modelEditAdd = true
            this.emptyList()
            this.getMaterialList()
        },
        submitPop() {
            const params = this.$_.cloneDeep(this.form)
            params['spPurchaseDetail'] = this.subTableData.map(i => {
                i.materialId = i.id
                return i
            })
            this.loading = true
            this.submitFun[this.modelType](params).then(res => {
                this.loading = false
                if (res.data.success) {
                    this.modelEdit = false
                    this.$emit("handleSuccess")
                } else {
                    this.$Message.error(res.data.error || res.data.message || '操作失败');
                }
            }).catch(e => {
                this.loading = false
                console.log(e)
                this.$Message.error("获取服务失败，请重试");
            })
        },
        // 切换分页
        changeCurrent(curr) {
            this.addDetailQuery.pageNum = curr;
            this.getMaterialList();
        },
        // 查询按钮
        queryList() {
            this.addDetailQuery.pageNum = 1;
            this.getMaterialList();
        },

        // 重置按钮
        emptyList() {
            this.addDetailQuery = {
                materialCode: "",
                materialDesc: "",
                materialBrand: "",
                materialType: "",
                pageNum: 1,
                pageSize: 10
            };
        },
        // 获取备件列表
        getMaterialList() {
            const params = {
                materialCode: this.addDetailQuery.materialCode,
                materialDesc: this.addDetailQuery.materialDesc,
                materialBrand: this.addDetailQuery.materialBrand,
                materialType: this.addDetailQuery.materialType,
                providerCode: this.userBranchInfo?.warehouseCode
            }
            const pageText = "?page=" + this.addDetailQuery.pageNum + "&rows=" + this.addDetailQuery.pageSize
            this.modalLoading = true
            Common.getMaterial(params, pageText).then(res => {
                this.modalLoading = false
                this.selectTableSelect = []
                let request = res.data.result;
                if (res.data.success) {
                    this.totalElements = request.totalElements;
                    this.selectTableData = request.content;
                } else {
                    this.totalElements = 0;
                    this.selectTableData = [];
                }
            }).catch(e => {
                console.log(e);
                this.modalLoading = false
                this.$Message.error("获取服务失败，请重试");
            })
        }
    }
}
</script>

<style lang="scss" scoped>
@import 'style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
