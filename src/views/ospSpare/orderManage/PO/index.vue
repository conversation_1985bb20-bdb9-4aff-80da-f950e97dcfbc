<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏备件管理</Breadcrumb-item>
            <Breadcrumb-item to="/ospSpare/">订单管理</Breadcrumb-item>
            <Breadcrumb-item>入库订单</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">提交时间：</p>
                    <DatePicker ref="formDateSh" :value="submitDate" @on-change="
                        (data) => {
                          return selectDate(data, 'submitDate');
                        }
                      " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择" style="width: 210px ">
                    </DatePicker>
                </div>
                <div class="condition">
                    <p class="condition_p">创建时间：</p>
                    <DatePicker ref="formDate" :value="createDate" @on-change="
                        (data) => {
                          return selectDate(data, 'createDate');
                        }
                      " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择" style="width: 210px ">
                    </DatePicker>
                </div>
              <div class="condition">
                <p class="condition_p">来源单号：</p>
                <Input v-model="searchObj.sourceNo" placeholder="请输入来源单号" clearable style="width: 200px" />
              </div>
                <div class="condition">
                    <p class="condition_p">备件编码：</p>
                    <Input v-model="searchObj.materialCode" placeholder="请输入备件编码" clearable style="width: 200px" />
                </div>
              <div class="condition">
                <p class="condition_p">采购单号：</p>
                <Input v-model="searchObj.purchaseCode" placeholder="请输入采购单号" clearable style="width: 200px" />
              </div>
                <div class="condition">
                    <p class="condition_p">订单状态：</p>
                    <!-- TODO 对接下拉数据 -->
                    <Select v-model="searchObj.purchaseStatus" clearable style="width: 200px">
                        <Option v-for="item in purchaseStatusList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">供应商：</p>
                    <!-- TODO 对接下拉数据 -->
                    <Select v-model="searchObj.branchName" clearable style="width: 200px" filterable>
                        <Option v-for="(item, i) in warehouseList" :value="item.warehouseName" :key="i">{{ item.warehouseName }}</Option>
                    </Select>
                </div>
            </div>
            <div class="list_but" style="display: flex; justify-content: space-between; width: 100%;">
                <div class="condition">
                    <Button @click="add" type="primary">新增</Button>
                    <Button @click="edit" :disabled="!isOperate" type="primary">编辑</Button>
                    <Button @click="submit" :disabled="!isOperate" type="primary">提交</Button>
                    <Button @click="del" :disabled="!isOperate" type="error">删除</Button>
                    <Button @click="handleExport" type="success">导出</Button>
                </div>
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                </div>
            </div>
            <div class="commListDiv">
                <Table size="small" border ref="tableRef" :columns="tableColumn" :data="commList" @on-selection-change="selected => this.tableSelect = selected">
                    <template slot-scope="{ index }" slot="sn">
                        {{ index + 1 }}
                    </template>
                    <template slot-scope="{ row }" slot="purchaseStatus">
                        {{ enums.purchaseStatus[row.purchaseStatus + ''] }}
                    </template>
                    <template slot-scope="{ row }" slot="branchName">
                        {{ enums.branchName[row.branchId + ''] }}
                    </template>
                    <template slot-scope="{ index }" slot="option">
                        <Button type="info" ghost size="small" @click="openDetail(commList[index])" style="margin: 3px">详情</Button>
                    </template>
                </Table>
            </div>
            <Page style="margin: 20px" @on-change="changeCurrent" :current="searchObj.page"
                  :total="totalElements" show-total show-elevator />
        </div>
        <addEditPop ref="addEditPopRef" :row-data="rowData" :model-type="modelType" :warehouse-list="warehouseList" @handleSuccess="handleSuccess" />
        <detail ref="detailsRef" :row-data="detailData" :warehouse-list="warehouseList" />
    </div>
</template>

<script>
import _data from "@/views/osp/_edata"; // 数据字典
import API from "@/api/ospSpare/orderManage/PO";
import Common from "@/api/ospSpare/common";
import addEditPop from "./addEditPop";
import detail from './details'
export default {
    name: "serviceProviderOrder",
    components: { addEditPop, detail },
    data() {
        return {
            searchObj: {
              submitDateRange: "",
              orderCode: "",
              woId: "",
              orderType: "",
              materialCode: "",
              purchaseStatus: "",
              branchName: "",
              pageNum: 1,
              pageSize: 10
            },
            tableColumn: [
                { type: 'selection', minWidth: 60, title: '选择', align: 'center' },
                { title: '序号', minWidth: 80, align: 'center', slot: 'sn' },
                { title: '采购订单', minWidth: 180, key: 'purchaseCode', align: 'center' },
                { title: '采购单状态', minWidth: 180, slot: 'purchaseStatus', align: 'center' },
                { title: '供应商名称', minWidth: 180, slot: 'branchName', align: 'center' },
                { title: '创建时间', minWidth: 180, key: 'createDate', align: 'center' },
                { title: '申请人', minWidth: 180, key: 'createBy', align: 'center' },
                { title: '提交时间', minWidth: 180, key: 'submitDate', align: 'center' },
                { title: '提交人', minWidth: 180, key: 'submitBy', align: 'center' },
                { title: '修改时间', minWidth: 180, key: 'updateDate', align: 'center' },
                { title: '操作', minWidth: 80, slot: 'option', fixed: 'right', align: 'center' },
            ],
            tableSelect: [],
            formObj: {},
            staffList: [],
            totalElements: 0,
            submitDate: [],
            create: [],
            createDate: [],
            status: _data.status,
            purchaseStatusList: _data.procurementOrderType,
            sourceArr: _data.SourceArr,
            faultCodeTypeList: _data.faultCodeTypeList,
            commList: [],
            title: "一键派工",
            modelEdit: false,
            modelType: 'add',
            modelEditAdd: false,
            warehouseList: [],
            rowData: {},
            detailData: {},
            enums: {
                purchaseStatus: {
                    "0": "初始",
                    "1": "已提交",
                    "2": "关闭",
                },
                branchName: {}
            }
        }
    },
    computed: {
        isOperate() {
            return this.tableSelect.length === 1 && this.tableSelect[0].purchaseStatus == '0'
        }
    },
    mounted() {
        this.getSelect()
    },
    methods: {
        // 获取下拉
        getSelect() {
            this.loading = true
            const pageStr = "?page=1&rows=9999"
            Common.getWarehouse({}, pageStr).then(res => {
                this.loading = false
                const resData = res.data || {}
                if (!this.$_.isEmpty(resData) && resData?.success) {
                    const list = resData?.result.content || [];
                    list.forEach(item => {
                        this.enums.branchName[item.branchId + ''] = item.warehouseName
                    })
                    this.warehouseList = list.filter(item => {
                        return item.warehouseLevel === '1'
                    })
                } else {
                    this.$Message.error('获取下拉失败!');
                }
                this.getList()
            }).catch(e => {
                this.loading = false
                this.getList()
                this.$Message.error('获取下拉失败!');
            })
        },
        // 新增
        add() {
            this.modelType = 'add'
            this.$refs.addEditPopRef.modelEdit = true
            this.rowData = {}
            this.$nextTick(() => {
                this.$refs.addEditPopRef.init()
            })
        },
        // 编辑
        edit() {
            this.modelType = 'edit'
            this.rowData = this.tableSelect[0]
            this.$refs.addEditPopRef.modelEdit = true
            this.$nextTick(() => {
                this.$refs.addEditPopRef.init()
            })
        },
        // 提交
        submit() {
            this.$message.confirm('是否确认提交?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$Message.loading({
                    content: "提交中...",
                    duration: 0,
                });
                API.submit(this.tableSelect[0]).then(res => {
                    this.$Message.destroy()
                    if (res.data.success) {
                        this.$Message.success("提交成功");
                        this.getList()
                    } else {
                        this.$Message.error(res.data.error || res.data.message || '操作失败');
                    }
                }).catch(e => {
                    console.log(e);
                    this.$Message.destroy()
                    this.$Message.error("获取服务失败，请重试");
                })
            })
        },
        // 删除
        del() {
            this.$message.confirm('是否确认删除?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$Message.loading({
                    content: "提交中...",
                    duration: 0,
                });
                API.delete({id: this.tableSelect[0].id}).then(res => {
                    this.$Message.destroy()
                    if (res.data.success) {
                        this.$Message.success("删除成功");
                        this.getList()
                    } else {
                        this.$Message.error(res.data.error || res.data.message || '操作失败');
                    }
                }).catch(e => {
                    console.log(e);
                    this.$Message.destroy()
                    this.$Message.error("获取服务失败，请重试");
                })
            })
        },
        // 打开详情
        openDetail(row) {
            this.detailData = row
            this.$refs.detailsRef.show = true
            this.$nextTick(() => {
                this.$refs.detailsRef.getInfo()
            })
        },
        // 查询按钮
        queryList() {
            this.searchObj.pageNum = 1;
            this.getList();
        },

        // 重置按钮
        emptyList() {
            this.searchObj = {
              submitDateRange: "",
              sourceNo: "",
              purchaseCode: "",
              purchaseStatus: "",
              branchName: "",
              pageNum: 1,
              pageSize: 10
            };
            this.submitDate = [];
        },
        // 选择日期
        selectDate(date, refObj) {
            this[refObj] = date;
            this.searchObj[refObj + "Start"] = date[0];
            this.searchObj[refObj + "End"] = date[1];
        },
        // 获取工单列表
        getList() {
          let params = this.$_.cloneDeep(this.searchObj);
          const pageText = "?page=" + this.searchObj.pageNum + "&rows=" + this.searchObj.pageSize
          API.getList(pageText, params).then(res => {
            // this.$Message.destroy()
            this.tableSelect = []
            let request = res.data.result;
            if (res.data.success) {
              this.totalElements = request.totalElements;
              this.commList = request.content;
            } else {
              this.totalElements = 0;
              this.commList = [];
            }
          }).catch(e => {
            console.log(e);
            this.$Message.destroy()
            this.$Message.error("获取服务失败，请重试");
          })
        },
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.pageNum = curr;
            this.getList();
        },
        jumpto(path, id, count) {
            this.$router.push({
                path,
                query: { id, count },
            });
        },
        handleSuccess() {
            this.$nextTick(() => {
                this.getList()
                setTimeout(() => {
                    this.$Message.success('操作成功');
                }, 300)
            })
        },
        // 导出
        handleExport() {
            this.$Message.loading({
                content: "导出中...",
                duration: 0,
            });
            let datas = this.$_.cloneDeep(this.searchObj);
            datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
            delete datas.pageNum
            delete datas.pageSize
            API.export(datas)
                .then(res => {
                    this.$Message.destroy()
                    const resData = res.data || {}
                    let binaryData = [];
                    let link = document.createElement('a');
                    binaryData.push(resData);
                    link.style.display = 'none';
                    link.href = window.URL.createObjectURL(new Blob(binaryData));
                    link.setAttribute('download', '入库订单.xlsx');
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                })
                .catch((e) => {
                    this.$Message.destroy()
                    console.log(e)
                });
        },
    }
}
</script>

<style lang="scss" scoped>
@import '@/style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
