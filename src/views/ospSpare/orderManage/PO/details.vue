<template>
    <Modal width="80%" v-model="show" title="详情" :footer-hide="true" :styles="{ top: '5vh' }"
           :mask-closable="false" :loading="loading">
        <div style="overflow-y: auto; width: 100%; height: 80vh;">
            <div class="cus_list" style="min-height: calc(80vh - 72px)">
                <!--                    <div class="list_son">-->
                <!--                        <div class="step">-->
                <!--                            <Steps class="stepdiv" :current="typeNode('orderStatusList', infoList.status)">-->
                <!--                                <Step v-for="(item, index) in infoList.processLogs" :key="index" :content="timeChange(item.createdAt)"-->
                <!--                                      :title="item.desc"></Step>-->
                <!--                            </Steps>-->
                <!--                        </div>-->
                <!--                    </div>-->
                <div class="cus_reg">
                    <div class="cus_form" style="padding-top: 0">
                        <p class="cus_title">主单信息</p>
                        <el-descriptions class="descriptions-list margin-top" :column="3" border>
                            <el-descriptions-item>
                                <template slot="label">
                                    订单号
                                </template>
                                {{ orderData.purchaseCode }}
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template slot="label">
                                    供应商名称
                                </template>
                                {{ orderData.branchName }}
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template slot="label">
                                    订单状态
                                </template>
                                {{ enums.purchaseStatus[orderData.purchaseStatus] }}
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template slot="label">
                                    申请时间
                                </template>
                                {{ orderData.createDate }}
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template slot="label">
                                    提交时间
                                </template>
                                {{ orderData.submitDate }}
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template slot="label">
                                    提交人
                                </template>
                                {{ orderData.submitBy }}
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template slot="label">
                                    创建人
                                </template>
                                {{ orderData.createBy }}
                            </el-descriptions-item>
                        </el-descriptions>
                        <p class="cus_title">明细信息</p>
                        <div class="commListDiv">
                            <Table size="small" border ref="subTableRef" :columns="subTableColumn" :data="subTableData">
                                <template slot-scope="{ index }" slot="sn">
                                    {{ index + 1 }}
                                </template>
                            </Table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </Modal>
</template>

<script>
import _data from "@/views/osp/_edata"; // 数据字典
import API from "@/api/ospSpare/orderManage/PO";

export default {
    props: {
        rowData: {
            type: Object,
            default: () => {}
        },
        warehouseList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            loading: false,
            show: false,
            infoList: {},
            orderStatusList: _data.orderStatusList,
            sourceArr: _data.SourceArr,
            count: '',
            orderData: {},
            enums: {
                purchaseStatus: {
                    "0": "初始",
                    "1": "已提交",
                    "2": "关闭",
                },
            },
            subTableData: [
            ],
            subTableColumn: [
                { title: '序号', minWidth: 80, align: 'center', slot: 'sn' },
                {
                    title: '入库单号',
                    key: 'tranCode',
                    align: 'center'
                },
                {
                    title: '备件编码',
                    key: 'materialCode',
                    align: 'center'
                },
                {
                    title: '备件描述',
                    key: 'materialDesc',
                    align: 'center'
                },
                {
                    title: '单位',
                    key: 'materialUnit',
                    align: 'center'
                },
                {
                    title: '单价',
                    key: 'settlePrice',
                    align: 'center'
                },
                {
                    title: '需求数量',
                    key: 'requestAmount',
                    align: 'center'
                }
            ]
        };
    },
    mounted() {
    },
    methods: {
        openImg(url) {
            window.open(url);
        },

        typeVal(list, value) {
            const obj = {};
            this[list].map((e) => {
                obj[e.value] = e.label || e.value;
            });
            // console.log(obj[value]);
            return obj[value];
        },

        typeNode(list, value) {
            const obj = {};
            this[list].map((e) => {
                obj[e.value] = e.node;
            });
            return obj[value];
        },

        timeChange(params) {
            // console.log(params);
            let dates = "";
            if (params) {
                dates = params.replace("T", " ");
            }
            return dates;
        },
        // 获取工单详情
        getInfo() {
            this.loading = true
            API.getInfo({ id: this.rowData.id }).then(res => {
                this.loading = false
                const resData = res.data
                if (resData.success) {
                    this.orderData = resData.result || {}
                    this.subTableData = this.orderData.spPurchaseDetail || []
                    const list = this.warehouseList.filter(item => item.branchId == this.orderData.branchId)
                    this.userBranchInfo = list.length > 0 ? list[0] : {}
                    this.orderData.branchName = list.length > 0 ? list[0].warehouseName : ""
                } else {
                    this.orderData = {}
                    this.subTableData = []
                    this.$Message.error(resData.error || resData.message || '获取数据失败');
                }
            }).catch(e => {
                console.log(e);
                this.loading = false
                this.$Message.error("获取服务失败，请重试")
                this.orderData = {}
                this.subTableData = []
            })
        },
    },
};
</script>

<style scoped lang="scss">
@import "@/style/_cus_list.scss";
@import "@/style/_cus_reg.scss";

.step {
    position: relative;
    margin: 0 20px;
    padding: 12px 6px 6px;
    border: 1px solid #e8eaec;

    .stepdiv {
        width: 80%;
        margin: 0 auto;
        @extend pt;
    }
}

.cus_title {
    margin-top: 20px;
    margin-bottom: 6px;
}
::v-deep .el-descriptions .is-bordered .el-descriptions-item__cell {
    width: 16.6%;
}
</style>
