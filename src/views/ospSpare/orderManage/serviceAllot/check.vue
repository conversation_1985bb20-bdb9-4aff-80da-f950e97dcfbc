<template>
    <Modal width="800" v-model="modelEdit" draggable sticky scrollable title="调拨确认" :footer-hide="true"
           :mask-closable="false">
        <div style="position: relative; width: 100%; height: 30vh;">
            <div style="padding-bottom: 40px; width: 100%; height: 60vh; overflow-y: auto">
                <Form :model="formItem" :label-width="100" ref="formItem" :rules="ruleValidate" >
                    <FormItem label="确认状态" prop="auditStatus">
                        <RadioGroup v-model="formItem.auditStatus">
                            <Radio label="1">通过</Radio>
                            <Radio label="0">驳回</Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem label="确认备注">
                        <Input type="textarea" maxlength="100" :rows="4"  v-model="formItem.auditResult" clearable placeholder="请输入"
                               style="width: 100%" />
                    </FormItem>
                </Form>
            </div>
            <div style="position: absolute; z-index: 99; right: 20px; bottom: -30px; text-align: right; margin: 30px 0">
                <Button type="primary"
                        @click="debounce(submitPop)">保存</Button>
                <Button type="default" style="margin-left: 10px"
                        @click="modelEdit = false">取消</Button>
            </div>
        </div>
    </Modal>
</template>

<script>
import API from "@/api/ospSpare/orderManage/serviceAllot";
export default {
    name: "addEdit",
    data() {
        return {
            modelEdit: false,
            data: {},
            queryPage: { // 查询条件 No页数 Size每页大小
                page: 1,
                rows: 10
            },
            formItem: {
                auditStatus: '',
                auditResult: ''
            },
            selectTableData: [], // 添加明细列表
            selectTableTotal: 0, // 添加明细列表总条数
            productMap: {},
            materialBrandOptions:[],
            materialBrandMap:{},
            materialTypeOptions:[],
            materialTypeMap:{},
            ruleValidate: {
                auditStatus: [
                    { required: true, message: '请选择', trigger: 'blur' }
                ]
            },
            orderId:''
        }
    },
    watch: {
        // 打开新增弹窗
        modelEdit(val) {
            if (val) {

            }
        },
    },
    mounted() {
    },
    methods: {
        tableInputChange(e) {
            this.$refs.subTableRef.selectAll(false);
        },
        reset(){
            this.orderId=''
            this.formItem = {
                auditStatus: '',
                auditResult: ''
            }
        },
        submitPop() {
            this.$refs['formItem'].validate((valid) => {
                if (valid) {
                    const data = {
                        id: this.orderId,
                        auditStatus: this.formItem.auditStatus,
                        auditResult: this.formItem.auditResult,
                    }
                    // this.$Message.loading({
                    //     content: "提交中...",
                    //     duration: 0,
                    // });
                    API.approvalServicePIT(data).then(res => {
                        if (res.data.success) {
                            this.$Message.destroy();
                            this.$Message.success(res.data.result);
                            this.$emit('getList')
                            this.modelEdit = false
                            this.reset();
                        } else {
                            this.$Message.destroy();
                            this.$Message.error(res.data.error);
                        }
                    })
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped>

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
