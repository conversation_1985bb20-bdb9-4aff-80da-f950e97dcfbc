<template>
    <Modal width="1200" v-model="modelEdit" draggable sticky scrollable :title="modelTitle" :footer-hide="true"  @on-cancel="handleCancel"
           :mask-closable="false">
        <div style="position: relative; width: 100%; height: 60vh;">
            <div style="padding-bottom: 40px; width: 100%; height: 60vh; overflow-y: auto">
                <Form :model="formItem" :label-width="100" ref="formItem" inline>
                    <FormItem label="申请仓库">
                        <Input type="text" maxlength="15" v-model="formItem.warehouseName" clearable placeholder="申请仓库"
                               style="width: 360px" disabled />
                    </FormItem>
                    <FormItem label="调出仓库">
                        <Select v-model="formItem.suptWarehouseId" filterable clearable style="width: 360px">
                            <Option v-for="(item,index) in suptBranchList" :value="item.value" :key="index">{{ item.label }}</Option>
                        </Select>
                    </FormItem>
                </Form>
                <div class="list_but" style="margin-bottom: 10px">
                    <div class="condition">
                        <Button @click="openSelect" size="small" type="primary">添加</Button>
                        <Button style="margin-left: 6px" @click="del" size="small" type="error" :disabled="!subTableSelect.length">删除</Button>
                    </div>
                </div>
                <div class="commListDiv">
                    <Table size="small" border ref="subTableRef" :columns="subTableColumn" :data="subTableData" @on-selection-change="selected => this.subTableSelect = selected">
                        <template slot-scope="{ index }" slot="requireAmount">
                            <!--                            {{ subTableData[index].submitNum }}-->
                            <InputNumber :min="1" :max="subTableData[index].storgeAmnt" v-model="subTableData[index].requireAmount" style="text-align: center" @on-change="tableInputChange" />
                        </template>
                    </Table>
                </div>
                <!-- 添加 -->
                <Modal width="1000" v-model="modelEditAdd" draggable sticky scrollable :title="modelTitle" :footer-hide="true" :styles="{ top: 'calc(100px + 4vh)' }"
                       :mask-closable="false">
                    <div style="position: relative; width: 100%; height: 50vh;">
                        <div style="padding-bottom: 40px; width: 100%; height: 50vh; overflow-y: auto">
                            <Form :model="addDetailQuery" :label-width="100" ref="formItem" inline>
                                <FormItem label="备件编码">
                                    <Input type="text" maxlength="15" v-model="addDetailQuery.materialCode" clearable placeholder="备件编码"
                                           style="width: 160px" />
                                </FormItem>
                                <FormItem label="备件描述" prop="duty">
                                    <Input type="text" maxlength="15" v-model="addDetailQuery.materialDesc" clearable placeholder="备件描述"
                                           style="width: 160px" />
                                </FormItem>
                            </Form>
                            <div class="list_but" style="margin-bottom: 10px">
                                <div class="condition">
                                    <Button @click="debounce(queryList)" size="small" type="primary">查询</Button>
                                    <Button @click="debounce(emptyList)" style="margin-left: 6px" size="small">重置</Button>
                                </div>
                            </div>
                            <div class="commListDiv">
                                <Table size="small" border ref="selectTableRef" :columns="selectTableColumn" :data="selectTableData" @on-selection-change="selected => this.selectTableSelect = selected">
                                    <template slot-scope="{ index }" slot="productStatus">
                                        <span v-if="selectTableData[index].productStatus=='0'">停产</span>
                                        <span v-if="selectTableData[index].productStatus=='1'">生产</span>
                                    </template>
                                </Table>
                            </div>
                        </div>
                        <div style="position: absolute; z-index: 100; right: 20px; bottom: -30px; text-align: right; margin: 30px 0">
                            <Button type="primary"
                                    :disabled="!selectTableSelect.length"
                                    @click="debounce(submitSelect)">保存</Button>
                            <Button type="default" style="margin-left: 10px"
                                    @click="modelEditAdd = false">取消</Button>
                        </div>
                    </div>
                </Modal>
            </div>
            <div style="position: absolute; z-index: 99; right: 20px; bottom: -30px; text-align: right; margin: 30px 0">
                <Button type="primary"
                        @click="debounce(submitPop)">保存</Button>
                <Button type="default" style="margin-left: 10px"
                        @click="handleCancel">取消</Button>
            </div>
        </div>
    </Modal>
</template>

<script>
import API from "@/api/ospSpare/orderManage/serviceAllot";

export default {
    name: "addEdit",
    props: {
        popType: {
            type: String,
            default: 'add'
        }
    },
    watch: {
        modelEdit(val) {
            if (val) {
                if(this.popType=='add'){
                    this.modelTitle='新增'
                  //获取申请服务商
                  this.getBranchName()
                    this.getsuptBranch('') // 获取调出仓库下拉数据
                }
                if(this.popType=='edit'){
                    this.modelTitle='编辑'
                    this.getInfo();
                    this.getsuptBranch('') // 获取调出仓库下拉数据
                }
            }
        }
    },
    data() {
        return {
            modelEdit: false,
            modelTitle: '新增',
            formItem: {
                warehouseName: '',
                warehouseId: '',
                suptWarehouseId: '',
                supportBranchId: '',
                branchId:''
            },
            subTableSelect: [],
            subTableData: [],
            subTableColumn: [
                {
                    type: 'selection',
                    width: 60,
                    title: '选择',
                    align: 'center'
                },
                {
                    title: '备件编码',
                    key: 'materialCode',
                    align: 'center'
                },
                {
                    title: '备件描述',
                    key: 'materialDesc',
                    align: 'center'
                },
                {
                    title: '备件品牌',
                    key: 'materialBrand',
                    align: 'center'
                },
                {
                    title: '备件分类',
                    key: 'materialType',
                    align: 'center'
                },
                {
                    title: '可用库存数量',
                    key: 'storgeAmnt',
                    align: 'center'
                },
                {
                    title: '申请数量',
                    key: 'requireAmount',
                    align: 'center',
                    slot:'requireAmount'
                }
            ],
            modelEditAdd: false,
            addDetailQuery: {
                materialCode: '',
                materialDesc: ''
            },
            selectTableSelect: [],
            selectTableColumn: [
                {
                    type: 'selection',
                    width: 60,
                    title: '选择',
                    align: 'center'
                },
                {
                    title: '备件编码',
                    key: 'materialCode',
                    align: 'center'
                },
                {
                    title: '备件描述',
                    key: 'materialDesc',
                    align: 'center'
                },
                {
                    title: '备件品牌',
                    key: 'materialBrand',
                    align: 'center'
                },
                {
                    title: '备件分类',
                    key: 'materialType',
                    align: 'center'
                },
                {
                    title: '备件规格',
                    key: 'materialUnit',
                    align: 'center'
                },
                {
                    title: '可用库存量',
                    key: 'storgeAmnt',
                    align: 'center'
                }
            ],
            selectTableData: [],
            suptBranchList:[], // 调出仓库下拉数据
            dataSource:{}, // 列表带入数据
        }
    },
    methods: {
      //获取申请服务商
      getBranchName(){
        //  获取当前登录账号对应的供应商id
        const member_id = JSON.parse(window.localStorage.getItem('logins')).member_id
        API.queryStoreId(member_id).then(res=>{
          if(res.data.success){
            this.formItem.warehouseName = res.data.result.warehouseName
            this.formItem.warehouseId = res.data.result.id
            this.formItem.branchId = res.data.result.branchId
          }
        })
      },
        // 获取调出仓库下拉数据
        getsuptBranch(id){
            let params={
                'warehouseId':id
            }
            API.queryRoute(params).then(res=>{
                if(res.data.success){
                    if(res.data.result.length && res.data.result.length>0){
                        this.suptBranchList=res.data.result.map((e)=>{
                            return{
                                'value':e.id*1,
                                'label':e.warehouseName,
                                'branchId':e.branchId,
                            }
                        })
                    }

                }
            })
        },
        del() {
            this.$Message.success('删除!');
            this.subTableData = this.subTableData.filter(item => !this.subTableSelect.some(v => v.batchNum === item.batchNum))
            // console.log(this.subTableSelect.filter(item => this.subTableData.some(v => v.batchNum === item.batchNum)))
        },
        // 打开 添加选择框
        openSelect(){
            if(!this.formItem.suptWarehouseId || this.formItem.suptWarehouseId==''){
                this.$Message.error('请先选择调出仓库！')
                return
            }
            this.queryList()
            this.modelEditAdd = true
        },
        // 查询明细信息
        queryList() {
            let params={
                'warehouseId':this.formItem.suptWarehouseId,
                'materialCode':this.addDetailQuery.materialCode,
                'materialDesc':this.addDetailQuery.materialDesc,
            }
            API.cdMaterial(params).then(res=>{
                if(res.data.success){
                    this.selectTableData=res.data.result
                }
            })
        },
        // 清空明细信息查询条件
        emptyList() {
            this.addDetailQuery = {
                materialCode: '',
                materialDesc: ''
            }
        },
        tableInputChange(e) {
            this.$refs.subTableRef.selectAll(false);
        },
        submitSelect() {
            // 重复选中的订单
            const reqOrder = this.selectTableSelect.filter(item => this.subTableData.some(v => v.materialCode === item.materialCode))
            // 没有重复 可追加上的订单
            const addOrder = this.selectTableSelect.filter(item => !this.subTableData.some(v => v.materialCode === item.materialCode))
            // 重复订单提示标记
            let reqMsg = []
            reqOrder.forEach(item => {
                reqMsg.push(item.batchNum)
            })
            reqMsg = reqMsg.toString()
            if (addOrder.length && !reqOrder.length) {
                addOrder.forEach(order=>{
                    order.requireAmount=order.storgeAmnt
                    order.standard=order.sendStandard
                })
                this.subTableData.push(...addOrder)
                this.modelEditAdd = false
                this.$refs.selectTableRef.selectAll(false);
                this.$Message.success('添加成功!');
            } else if (addOrder.length && reqOrder.length) {
                addOrder.forEach(order=>{
                    order.requireAmount=order.storgeAmnt
                    order.standard=order.sendStandard
                    order.materialPrice=order.price
                })
                this.subTableData.push(...addOrder)
                this.modelEditAdd = false
                this.$refs.selectTableRef.selectAll(false);
                this.$Message.success('添加成功!去除重复订单：' + reqMsg);
            } else if (!addOrder.length && reqOrder.length) {
                this.$Message.error
                ('当前选中订单已添加：' + reqMsg);
            }
        },
        submitPop() {
          let arr=JSON.parse(JSON.stringify(this.subTableData))
            arr.forEach(item=>{
                delete item.id
            })
            this.suptBranchList.forEach(item=>{
                if(item.value==this.formItem.suptWarehouseId){
                    this.formItem.supportBranchId=item.branchId
                }
            })
            let params={
                'warehouseId':this.formItem.warehouseId,
                'suptWarehouseId':this.formItem.suptWarehouseId,
                'supportBranchId':this.formItem.supportBranchId,
                'branchId':this.formItem.branchId,
                'orderDetailList':arr
            }
            //新增
            if(this.popType=='add'){
                   API.addServicePIT(params).then(res=>{
                       if(res.data.success){
                           this.$Message.success('新增成功');
                           setTimeout(() => {
                               this.$Message.destroy();
                               this.$emit('getList');
                           }, 2000)
                           this.modelEdit = false;
                           this.resetForm();
                       }else{
                           this.$Message.error(res.data.error);
                       }
                   }).catch(err=>{
                       this.$Message.error(err.data.error);
                   })
            }
            //编辑
            if(this.popType=='edit'){
                API.updateServicePIT(params).then(res=>{
                    if(res.data.success){
                        this.$Message.success('编辑成功');
                        setTimeout(() => {
                            this.$Message.destroy();
                            this.$emit('getList');
                        }, 2000)
                        this.modelEdit = false;
                       this.resetForm();
                    }else{
                        this.$Message.error(res.data.error);
                    }
                }).catch(err=>{
                    this.$Message.error(err.data.error);
                })
            }
        },
        // 重置表单
        resetForm(){
            this.subTableData=[]
            this.formItem= {
                warehouseId: '',
                warehouseName: '',
                suptWarehouseId: '',
                supportBranchId: '',
            }
        },
        /*获取详情*/
        getInfo(){
            let params={'id':this.dataSource.id}
            API.orderDetail(params).then(res => {
                if(res.data.success){
                    this.formItem.suptWarehouseId=res.data.result.suptWarehouseId*1
                    this.formItem.warehouseId=res.data.result.warehouseId
                    this.formItem.warehouseName=res.data.result.suptBranchName
                    this.formItem.supportBranchId=res.data.result.supportBranchId
                    this.formItem.branchId=res.data.result.branchId
                    this.subTableData = res.data?.result?.orderDetailList||[]
                }

            })
        },
        // 关闭弹框
        handleCancel(){
            this.handleReset();
            this.modelEdit = false
        },
    //     重置所有数据
        handleReset(){
            this.formItem= {
                warehouseId: '',
                warehouseName: '',
                suptWarehouseId: '',
                supportBranchId:'',
                branchId:''
            };
            this.subTableData=[]
            this.subTableSelect=[]
        }
    }
}
</script>

<style lang="scss" scoped>
@import '@/style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
