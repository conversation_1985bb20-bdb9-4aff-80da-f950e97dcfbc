<template>
    <Modal width="1200" v-model="modelEdit" draggable sticky scrollable :title="modelTitle" :footer-hide="true"
           :mask-closable="false" @on-cancel="resetForm">
        <div style="position: relative; width: 100%; height: 60vh;">
            <div style="display: flex;width: 100%">
                <div style="padding-bottom: 40px; width: 75%; height: 60vh; overflow-y: auto">
                    <Form :model="formItem" :label-width="100" ref="formItem" inline>
                        <FormItem label="运维单号">
                            <Input type="text" v-model="formItem.woId" clearable placeholder="请输入"
                                   style="width: 260px" @on-blur="getWoDetail"/>
                        </FormItem>
                        <FormItem label="建站服务商" prop="duty">
                            <Select
                                v-model="formItem.stationNetName"
                                filterable
                                clearable
                                style="width: 260px"
                                @on-change="changeSupplier"
                            >
                                <Option v-for="(item, index) in supplierList" :value="item.name" :key="index">{{item.name}}</Option>
                            </Select>
                        </FormItem>
                        <div style="position: relative">
                            <FormItem label="收货地址" prop="duty">
                                <div class="infoArea">
                                    <div style="height: 20px;">{{this.formItem.consigneeAddress}} </div>
                                    <div style="height: 20px;">{{this.formItem.consignee}} {{this.formItem.consigneePhone}} </div>
                                </div>
                            </FormItem>
                            <Button @click="handleChangeAddress" class="AddrssButtom" size="small" type="text" >修改地址</Button>
                        </div>

                    </Form>
                    <div class="list_but" style="margin-bottom: 10px">
<!--                        <div class="condition">-->
<!--                            <Button @click="addComponent" size="small" type="primary">添加</Button>-->
<!--                            <Button style="margin-left: 6px" @click="del" size="small" type="error" :disabled="!subTableSelect.length">删除</Button>-->
<!--                        </div>-->
                    </div>
                    <div class="commListDiv">
                        <Table size="small" ref="subTableRef" :columns="subTableColumn" :data="subTableData" @on-selection-change="selected => this.subTableSelect = selected">
                            <template slot-scope="{ index }" slot="materialDesc">
                                <Select
                                    v-model="subTableData[index].materialDesc"
                                    filterable
                                    clearable
                                    style="width: 180px"
                                    @on-change="(e)=>{tableMateriaCodeChange(e, index)}"
                                >
                                    <Option v-for="(item, index) in orderCodeList" :value="item.materialDesc" :key="index">{{item.materialDesc}}</Option>
                                </Select>
                            </template>
                            <template slot-scope="{ index }" slot="orderAmount">
                                <!--                            {{ subTableData[index].submitNum }}-->
                                <span v-if="!subTableData[index].materialDesc"></span>
                                <Input v-else v-model="subTableData[index].orderAmount" style="text-align: center" @on-keyup="onlyNumber(index)" @on-blur="tableInputChange" />
                            </template>
                        </Table>
                    </div>
                    <div class="componentCode" style="margin-top: 50px">
                        <span>组件编码</span>
                        <div class="componentCodeArea" style="margin-top: 20px;display: flex;flex-wrap: wrap">
                            <Input v-for="(item,index) in allAmount" :key="index" v-model="codeList[index]" style="width: 120px;margin-right: 10px;margin-bottom: 10px">
                                <span slot="prepend">{{index+1}}</span>
                            </Input>
                        </div>
                    </div>
                </div>
                <div class="uploadFileArea" style="width: 25%;height: 60vh; padding-left: 10px;overflow-y: auto;padding-bottom: 100px">
                    <span style="font-size: 16px">见证性资料</span>
                    <div style="margin-top: 20px;width: 100%">
                        <span style="display: block;margin-bottom: 12px">整体</span>
                        <div class="fileArea" style="width: 100%;display: flex;justify-content: space-around">
                            <div v-if="list1" class="demo-upload-list" >
                                <img :src="list1" loading="lazy" width="100%" style="height: 100%;object-fit: fill" alt=""/>
                                <div class="demo-upload-list-cover" @click.stop>
                                    <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleView('1')"></Icon>
                                    <Icon type="ios-trash-outline" size="20" color="#fff" @click.stop="handleRemove('1')"></Icon>
                                </div>
                            </div>
                            <Upload
                                v-else
                                ref="upload"
                                :show-upload-list="false"
                                :action="actionUrl"
                                :headers="headers"
                                :on-progress="progress"
                                :on-success=" (response, file, fileList) => { return imgFileUploadSuccess(response, file, fileList); } "
                                :format="['jpg','jpeg','png']"
                                :on-format-error="handleFormatError"
                                :on-exceeded-size="handleMaxSize"
                                :max-size="18432"
                                type="drag"
                                style="display: inline-block;width:100px;">
                                <div style="width: 100px;height:100px;line-height: 100px;">
                                    <Icon type="ios-cloudy-outline" size="20"></Icon>
                                </div>
                            </Upload>
                            <div v-if="list2" class="demo-upload-list" >
                                <img :src="list2" loading="lazy" width="100%" style="height: 100%;object-fit: fill" alt=""/>
                                <div class="demo-upload-list-cover" @click.stop>
                                    <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleView('2')"></Icon>
                                    <Icon type="ios-trash-outline" size="20" color="#fff" @click.stop="handleRemove('2')"></Icon>
                                </div>
                            </div>
                            <Upload
                                v-else
                                ref="upload"
                                :show-upload-list="false"
                                :action="actionUrl"
                                :headers="headers"
                                :on-progress="progress"
                                :on-success=" (response, file, fileList) => { return imgFileUploadSuccess2(response, file, fileList); } "
                                :format="['jpg','jpeg','png']"
                                :on-format-error="handleFormatError"
                                :on-exceeded-size="handleMaxSize"
                                :max-size="18432"
                                type="drag"
                                style="display: inline-block;width:100px;">
                                <div style="width: 100px;height:100px;line-height: 100px;">
                                    <Icon type="ios-cloudy-outline" size="20"></Icon>
                                </div>
                            </Upload>
                        </div>
                    </div>
                    <div style="margin-top: 20px;width: 100%;">
                        <span style="display: block;margin-bottom: 12px">组件</span>
                        <div class="fileArea" style="width: 100%;display: flex;justify-content: space-around;flex-wrap: wrap;">
                            <div v-if="list3" class="demo-upload-list" style="margin-bottom: 20px">
                                <img :src="list3" loading="lazy" width="100%" style="height: 100%;object-fit: fill" alt=""/>
                                <div class="demo-upload-list-cover" @click.stop>
                                    <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleView('3')"></Icon>
                                    <Icon type="ios-trash-outline" size="20" color="#fff" @click.stop="handleRemove('3')"></Icon>
                                </div>
                            </div>
                            <Upload
                                v-else
                                ref="upload"
                                :show-upload-list="false"
                                :action="actionUrl"
                                :headers="headers"
                                :on-progress="progress"
                                :on-success=" (response, file, fileList) => { return imgFileUploadSuccess3(response, file, fileList); } "
                                :format="['jpg','jpeg','png']"
                                :on-format-error="handleFormatError"
                                :on-exceeded-size="handleMaxSize"
                                :max-size="18432"
                                type="drag"
                                style="display: inline-block;width:100px;margin-bottom: 20px">
                                <div style="width: 100px;height:100px;line-height: 100px;">
                                    <Icon type="ios-cloudy-outline" size="20"></Icon>
                                </div>
                            </Upload>
                            <div v-if="list4" class="demo-upload-list" style="margin-bottom: 20px">
                                <img :src="list4" loading="lazy" width="100%" style="height: 100%;object-fit: fill" alt=""/>
                                <div class="demo-upload-list-cover" @click.stop>
                                    <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleView('4')"></Icon>
                                    <Icon type="ios-trash-outline" size="20" color="#fff" @click.stop="handleRemove('4')"></Icon>
                                </div>
                            </div>
                            <Upload
                                v-else
                                ref="upload"
                                :show-upload-list="false"
                                :action="actionUrl"
                                :headers="headers"
                                :on-progress="progress"
                                :on-success=" (response, file, fileList) => { return imgFileUploadSuccess4(response, file, fileList); } "
                                :format="['jpg','jpeg','png']"
                                :on-format-error="handleFormatError"
                                :on-exceeded-size="handleMaxSize"
                                :max-size="18432"
                                type="drag"
                                style="display: inline-block;width:100px;margin-bottom: 20px">
                                <div style="width: 100px;height:100px;line-height: 100px;">
                                    <Icon type="ios-cloudy-outline" size="20"></Icon>
                                </div>
                            </Upload>
                            <div v-if="list5" class="demo-upload-list" style="margin-bottom: 20px">
                                <img :src="list5" loading="lazy" width="100%" style="height: 100%;object-fit: fill" alt=""/>
                                <div class="demo-upload-list-cover" @click.stop>
                                    <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleView('5')"></Icon>
                                    <Icon type="ios-trash-outline" size="20" color="#fff" @click.stop="handleRemove('5')"></Icon>
                                </div>
                            </div>
                            <Upload
                                v-else
                                ref="upload"
                                :show-upload-list="false"
                                :action="actionUrl"
                                :headers="headers"
                                :on-progress="progress"
                                :on-success=" (response, file, fileList) => { return imgFileUploadSuccess5(response, file, fileList); } "
                                :format="['jpg','jpeg','png']"
                                :on-format-error="handleFormatError"
                                :on-exceeded-size="handleMaxSize"
                                :max-size="18432"
                                type="drag"
                                style="display: inline-block;width:100px;margin-bottom: 20px">
                                <div style="width: 100px;height:100px;line-height: 100px;">
                                    <Icon type="ios-cloudy-outline" size="20"></Icon>
                                </div>
                            </Upload>
                            <div v-if="list6" class="demo-upload-list" style="margin-bottom: 20px">
                                <img :src="list6" loading="lazy" width="100%" style="height: 100%;object-fit: fill" alt=""/>
                                <div class="demo-upload-list-cover" @click.stop>
                                    <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleView('6')"></Icon>
                                    <Icon type="ios-trash-outline" size="20" color="#fff" @click.stop="handleRemove('6')"></Icon>
                                </div>
                            </div>
                            <Upload
                                v-else
                                ref="upload"
                                :show-upload-list="false"
                                :action="actionUrl"
                                :headers="headers"
                                :on-progress="progress"
                                :on-success=" (response, file, fileList) => { return imgFileUploadSuccess6(response, file, fileList); } "
                                :format="['jpg','jpeg','png']"
                                :on-format-error="handleFormatError"
                                :on-exceeded-size="handleMaxSize"
                                :max-size="18432"
                                type="drag"
                                style="display: inline-block;width:100px;margin-bottom: 20px">
                                <div style="width: 100px;height:100px;line-height: 100px;">
                                    <Icon type="ios-cloudy-outline" size="20"></Icon>
                                </div>
                            </Upload>
                            <div v-if="list7" class="demo-upload-list" style="margin-bottom: 20px">
                                <img :src="list7" loading="lazy" width="100%" style="height: 100%;object-fit: fill" alt=""/>
                                <div class="demo-upload-list-cover" @click.stop>
                                    <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleView('7')"></Icon>
                                    <Icon type="ios-trash-outline" size="20" color="#fff" @click.stop="handleRemove('7')"></Icon>
                                </div>
                            </div>
                            <Upload
                                v-else
                                ref="upload"
                                :show-upload-list="false"
                                :action="actionUrl"
                                :headers="headers"
                                :on-progress="progress"
                                :on-success=" (response, file, fileList) => { return imgFileUploadSuccess7(response, file, fileList); } "
                                :format="['jpg','jpeg','png']"
                                :on-format-error="handleFormatError"
                                :on-exceeded-size="handleMaxSize"
                                :max-size="18432"
                                type="drag"
                                style="display: inline-block;width:100px;margin-bottom: 20px">
                                <div style="width: 100px;height:100px;line-height: 100px;">
                                    <Icon type="ios-cloudy-outline" size="20"></Icon>
                                </div>
                            </Upload>
                            <div v-if="list8" class="demo-upload-list" style="margin-bottom: 20px">
                                <img :src="list8" loading="lazy" width="100%" style="height: 100%;object-fit: fill" alt=""/>
                                <div class="demo-upload-list-cover" @click.stop>
                                    <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleView('8')"></Icon>
                                    <Icon type="ios-trash-outline" size="20" color="#fff" @click.stop="handleRemove('8')"></Icon>
                                </div>
                            </div>
                            <Upload
                                v-else
                                ref="upload"
                                :show-upload-list="false"
                                :action="actionUrl"
                                :headers="headers"
                                :on-progress="progress"
                                :on-success=" (response, file, fileList) => { return imgFileUploadSuccess8(response, file, fileList); } "
                                :format="['jpg','jpeg','png']"
                                :on-format-error="handleFormatError"
                                :on-exceeded-size="handleMaxSize"
                                :max-size="18432"
                                type="drag"
                                style="display: inline-block;width:100px;">
                                <div style="width: 100px;height:100px;line-height: 100px;">
                                    <Icon type="ios-cloudy-outline" size="20"></Icon>
                                </div>
                            </Upload>
                        </div>
                    </div>
                </div>
            </div>

            <div style="position: absolute; z-index: 99; right: 20px; bottom: -30px; text-align: right; margin: 30px 0">
                <Button type="primary"
                        :loading="loadStatus"
                        @click="debounce(submitPop)">保存</Button>
                <Button type="default" style="margin-left: 10px"
                        @click="closeAddModal">取消</Button>
            </div>
        </div>
        <Modal v-model="visible" width="99" reset-drag-position draggable sticky title="预览" footer-hide class-name="vertical-center-modal">
            <img class="preview" v-if="visibleImg" :src="visibleImg" @click="toTarget(visibleImg)" title="点击查看原图" alt="">
        </Modal>
        <Address ref="addressRef" @choseAddress="choseAddress"/>
    </Modal>
</template>

<script>
import API from "@/api/ospSpare/orderManage/lendOrder";
import Address from "@/views/ospSpare/orderManage/serviceProviderOrder/address.vue";

export default {
    name: "addEdit",
    components: { Address },
    props: {
        orderId: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            isAdd: true,
            loadStatus: false,
            allAmount: 0,
            codeList: [],
            visible: false, //预览图片
            visibleImg: '',
            modelEdit: false,
            modelTitle: '新增',
            formItem: {
                woId: '',
                stationNetCode: '',
                stationNetMemberId: '',
                stationNetName: '',
                consignee:'',
                consigneePhone:'',
                consigneeAddress:'',
                stationCode: '',
                ownerName: ''
            },
            subTableSelect: [],
            orderCodeList: [],
            subTableData: [
                {
                    materialId: '',
                    materialCode: '',
                    materialDesc: '',
                    materialPrice: '',
                    supplierName: '',
                    orderAmount: '',
                    key: 1
                }
            ],
            subTableColumn: [
                {
                    type: 'selection',
                    width: 60,
                    title: '选择',
                    align: 'center'
                },
                {
                    title: '备件名称',
                    key: 'materialDesc',
                    slot: 'materialDesc',
                    width: 200,
                    align: 'center'
                },
                {
                    title: '备件编码',
                    key: 'materialCode',
                    width: 200,
                    align: 'center'
                },
                {
                    title: '单价',
                    key: 'materialPrice',
                    align: 'center'
                },
                {
                    title: '申请数量',
                    key: 'orderAmount',
                    slot: 'orderAmount',
                    align: 'center'
                }
            ],
            modelEditAdd: false,
            actionUrl: `${process.env.VUE_APP_BASE_API}/merchant/oss/image/upload.do`,
            headers: { Authorization: `Bearer ${JSON.parse(window.localStorage.getItem('logins')).access_token}` },
            list1: '',
            list2: '',
            list3: '',
            list4: '',
            list5: '',
            list6: '',
            list7: '',
            list8: '',
            supplierList: [],
            loading1: false
        }
    },
    mounted() {
        this.getMaterialList()
        this.getSupplier()
    },
    methods: {
        //打开 修改地址 弹框
        handleChangeAddress(){
            this.$refs.addressRef.modelEdit = true
        },
        // 选择地址回显
        choseAddress(addressObj){
            this.formItem.consignee=addressObj.linkMan
            this.formItem.consigneePhone=addressObj.phone
            this.formItem.consigneeAddress=addressObj.province+addressObj.city+addressObj.region + addressObj.receiveAddress
        },
        getWoDetail(){
            API.getWoIdDetail({
                workOrderSn: this.formItem.woId
            }).then(res=>{
                if(res.data.success){
                    if(res.data?.result?.success){
                        this.formItem.stationCode = res.data.result.result.stationCode
                        this.formItem.ownerName = res.data.result.result.stationName
                    }else{
                        this.$Message.error(res.data.result.error)
                        this.$set(this.formItem, 'woId', '')
                    }
                }else{
                    this.$Message.error(res.data.error)
                }

            })
        },
        getMaterialList(){
            API.getmaterialListApi({
                productStatus:'1',
                ifModule: '0'
            }).then(res=>{
                this.loading1 = false;
                let request = res.data.result;
                if (res.data.success && (request.content.length > 0)) {
                    this.orderCodeList = request.content;
                } else {
                    this.orderCodeList = [];
                }
            })
        },
        getDetail(){
            API.getOrderDetail(this.orderId).then(res=>{
                if(res.data.success){
                    const data = res.data.result
                    this.formItem.woId = data?.woId || ''
                    this.formItem.stationNetCode = data?.stationNetCode || ''
                    this.formItem.stationNetMemberId = data?.stationNetMemberId || ''
                    this.formItem.stationNetName = data?.stationNetName || ''
                    this.formItem.stationCode = data?.stationCode || ''
                    this.formItem.ownerName = data?.ownerName || ''
                    this.formItem.consignee = data?.consignee || ''
                    this.formItem.consigneePhone = data?.consigneePhone || ''
                    this.formItem.consigneeAddress = data?.consigneeAddress || ''
                    this.subTableData = data.detailList
                    let list = data.modelSn.split(',')
                    this.allAmount = list.length
                    this.codeList = list
                    let fileList = data.fileList.filter(item=> item.fileFlag=='1')
                    let fileList2 = data.fileList.filter(item=> item.fileFlag=='2')
                    if(fileList.length>1){
                        this.list1 = fileList[0].fileAddress
                        this.list2 = fileList[1].fileAddress
                    }else{
                        this.list1 = fileList[0].fileAddress
                    }
                    for(let i=0;i<fileList.length;i++){
                        this['list'+i] = fileList[i].fileAddress
                    }
                    for(let i=0;i<fileList2.length;i++){
                        let num = i+3
                        this['list'+num] = fileList2[i].fileAddress
                    }
                }
            })
        },
        changeSupplier(val){
            if(!val) return false;
            let item = this.supplierList.find(e=> e.name===val)
            this.formItem.stationNetCode = item.spCode
            this.formItem.stationNetMemberId = item.memberId
        },
        tableMateriaCodeChange(val, index){
            if(!val){
                this.subTableData[index].materialId = ''
                this.subTableData[index].materialCode = ''
                this.subTableData[index].supplierName = ''
                this.subTableData[index].materialPrice = ''
                return
            }
            let item = this.orderCodeList.find(item=> item.materialDesc == val)
            this.subTableData[index].materialId = item.id
            this.subTableData[index].materialCode = item.materialCode
            this.subTableData[index].supplierName = item.providerName
            this.subTableData[index].materialPrice = item.settlePrice
        },
        getSupplier(){
            API.getSupplierList({}).then(res=>{
                this.supplierList = res.data?.result || []
            })
        },
        closeAddModal(){
            this.modelEdit = false
            this.resetForm()
        },
        onlyNumber(index){
            if(this.subTableData[index].orderAmount == 0){
                this.subTableData[index].orderAmount=""
            }else{
                this.subTableData[index].orderAmount=this.subTableData[index].orderAmount.replace(/\D/g,'')
            }
        },
        toTarget(url) {
            window.open(url)
        },
        handleView (type) {
            this.visible = true;
            switch (type) {
                case '1':
                    this.visibleImg = this.list1;
                    break;
                case '2':
                    this.visibleImg = this.list2;
                    break;
                case '3':
                    this.visibleImg = this.list3;
                    break;
                case '4':
                    this.visibleImg = this.list4;
                    break;
                case '5':
                    this.visibleImg = this.list1;
                    break;
                case '6':
                    this.visibleImg = this.list2;
                    break;
                case '7':
                    this.visibleImg = this.list3;
                    break;
                case '8':
                    this.visibleImg = this.list4;
                    break;
            }
        },
        handleRemove (type) {
            switch (type) {
                case '1':
                    this.list1 = ''
                    break;
                case '2':
                    this.list2 = ''
                    break;
                case '3':
                    this.list3 = ''
                    break;
                case '4':
                    this.list4 = ''
                    break;
                case '5':
                    this.list5 = ''
                    break;
                case '6':
                    this.list6 = ''
                    break;
                case '7':
                    this.list7 = ''
                    break;
                case '8':
                    this.list8 = ''
                    break;
            }
        },
        progress() {
            this.$Message.loading({
                content: '上传中...',
                duration: 0
            });
        },
        imgFileUploadSuccess(response, file, fileList) {
            let that = this;
            this.list1 = response.result[0].imageUrl;
            setTimeout(() => {
                that.$Message.destroy();
            }, 500);
        },
        imgFileUploadSuccess2(response, file, fileList) {
            let that = this;
            this.list2 = response.result[0].imageUrl;
            setTimeout(() => {
                that.$Message.destroy();
            }, 500);
        },
        imgFileUploadSuccess3(response, file, fileList) {
            let that = this;
            this.list3 = response.result[0].imageUrl;
            setTimeout(() => {
                that.$Message.destroy();
            }, 500);
        },
        imgFileUploadSuccess4(response, file, fileList) {
            let that = this;
            this.list4 = response.result[0].imageUrl;
            setTimeout(() => {
                that.$Message.destroy();
            }, 500);
        },
        imgFileUploadSuccess5(response, file, fileList) {
            let that = this;
            this.list5 = response.result[0].imageUrl;
            setTimeout(() => {
                that.$Message.destroy();
            }, 500);
        },
        imgFileUploadSuccess6(response, file, fileList) {
            let that = this;
            this.list6 = response.result[0].imageUrl;
            setTimeout(() => {
                that.$Message.destroy();
            }, 500);
        },
        imgFileUploadSuccess7(response, file, fileList) {
            let that = this;
            this.list7 = response.result[0].imageUrl;
            setTimeout(() => {
                that.$Message.destroy();
            }, 500);
        },
        imgFileUploadSuccess8(response, file, fileList) {
            let that = this;
            this.list8 = response.result[0].imageUrl;
            setTimeout(() => {
                that.$Message.destroy();
            }, 500);
        },
        handleFormatError(file) {
            this.$Notice.warning({
                title: '上传格式不正确',
                desc: '请上传正确的格式文件'
            });
        },

        handleMaxSize (file) {
            this.$Notice.warning({
                title: '上传文件过大',
                desc: file.name + '文件太大，请压缩后上传.'
            });
        },
        del() {
            this.subTableData = this.subTableData.filter(item => !this.subTableSelect.some(v => v.key === item.key))
            this.tableInputChange()
            // console.log(this.subTableSelect.filter(item => this.subTableData.some(v => v.orderCode === item.orderCode)))
        },
        // 查询明细信息
        queryList() {

        },
        tableInputChange() {
            let list = this.subTableData
            let amount = 0
            list.forEach(item => {
               if(item.orderAmount){
                   amount += Number(item.orderAmount)
               }
            })
            this.allAmount = amount
            if(this.codeList.length>0 && this.codeList.length>amount){
                this.codeList = this.codeList.slice(0, amount)
            }
        },
        addComponent(){
            let item = {
                materialId: '',
                materialCode: '',
                materialName: '',
                materialDesc: '',
                branchName: '',
                supplierName: '',
                materialPrice: '',
                orderAmount: '',
                key: this.subTableData.length+1
            }
            this.subTableData.push(item)
        },
        resetForm(){
            for(let i in this.formItem){
                this.$set(this.formItem, i, '')
            }
            this.list1 = this.list2 = this.list3 = this.list4 = ''
            this.allAmount = 0
            this.codeList = []
            this.subTableData = [
                {
                    materialId: '',
                    materialCode: '',
                    materialDesc: '',
                    materialPrice: '',
                    orderAmount: '',
                    key: 1
                }
            ]
        },
        submitPop() {
            if(!this.formItem.woId){
                this.$Message.warning('请输入运维单号');
                return
            }
            if(!this.formItem.stationNetName){
                this.$Message.warning('请选择建站服务商');
                return
            }
            for(let i=0;i<this.subTableData.length;i++){
                const item = this.subTableData[i]
                if(!item.materialCode){
                    this.$Message.warning('请选择备件');
                    return
                }
                if(!item.orderAmount){
                    this.$Message.warning('请输入申请数量');
                    return
                }
            }
            if(this.codeList.length!== this.allAmount){
                this.$Message.warning('请输入组件编码');
                return
            }
            if(this.codeList.length===this.allAmount){
                for(let i=0;i<this.codeList.length;i++){
                    if(!this.codeList[i]){
                        this.$Message.warning('请输入组件编码');
                        return
                    }
                }
            }
            if(!this.list1 && !this.list2){
                this.$Message.warning('最少选择一张整体图片');
                return
            }
            if(!this.list3 && !this.list4 && !this.list5 && !this.list6 && !this.list7 && !this.list8){
                this.$Message.warning('最少选择一张组件图片');
                return
            }
            let fileLists = []
            if(this.list1) fileLists.push({fileFlag: '1', fileAddress: this.list1});
            if(this.list2) fileLists.push({fileFlag: '1', fileAddress: this.list2});
            if(this.list3) fileLists.push({fileFlag: '2', fileAddress: this.list3});
            if(this.list4) fileLists.push({fileFlag: '2', fileAddress: this.list4});
            const member_id = JSON.parse(window.localStorage.getItem('logins')).member_id
            const data = {
                warehouseMemberId: member_id,
                consigneeAddress: this.formItem.consigneeAddress,
                consigneePhone: this.formItem.consigneePhone,
                consignee: this.formItem.consignee,
                stationNetMemberId: this.formItem.stationNetMemberId,
                stationNetCode: this.formItem.stationNetCode,
                stationNetName: this.formItem.stationNetName,
                woId: this.formItem.woId,
                stationCode: this.formItem.stationCode,
                ownerName: this.formItem.ownerName,
                detailList: this.subTableData,
                modelSn: this.codeList.join(','),
                fileList: fileLists
            }
            this.loadStatus = true
            if(this.isAdd){
                API.addOrder(data).then(res => {
                    this.loadStatus = false
                    if(res.data.success){
                        this.$Message.success(res.data.result);
                        this.resetForm()
                        this.modelEdit = false
                        this.$emit('getPageList')
                    }else{
                        this.$Message.warning(res.data.error);
                    }
                })
            }else{
                data.id = this.orderId
                API.updateOrder(data).then(res => {
                    this.loadStatus = false
                    if(res.data.success){
                        this.$Message.success(res.data.result);
                        this.resetForm()
                        this.modelEdit = false
                        this.$emit('getPageList')
                    }else{
                        this.$Message.warning(res.data.error);
                    }
                })
            }

        }
    }
}
</script>

<style lang="scss" scoped>
@import '@/style/_cus_reg.scss';
@import '@/style/_cus_list.scss';
@import '@/style/_finish.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
::v-deep .ivu-table:before{
    display: none!important;
}
::v-deep .ivu-table-wrapper{
    overflow: initial!important;
}
.demo-upload-list {
    @extend .flex-row-center-center;
    position: relative;
    width: 100px;
    height: 100px;
    overflow: hidden;

    &:hover .demo-upload-list-cover {
        display: flex;
    }
}
.demo-upload-list-cover {
    @extend.flex-row-center-around;
    display: none;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,.6);
    z-index: 999;
    i{
        &:hover{
            cursor: pointer;
        }
    }
}
.infoArea{
    width: 360px;
    height: 80px;
    background-color: #f3f3f3;
    opacity: 1;
    cursor: not-allowed;
    color: #ccc;
    padding: 4px 7px;
    border: 1px solid #dcdee2;
    border-radius: 4px;
}
.AddrssButtom{
    position: absolute;
    left: 460px;
    top: 0px;
    color: #2d8cf0;
}
</style>
