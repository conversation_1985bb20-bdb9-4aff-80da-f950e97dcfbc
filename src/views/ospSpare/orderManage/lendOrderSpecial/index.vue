<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏备件管理</Breadcrumb-item>
            <Breadcrumb-item to="/ospSpare/">订单管理</Breadcrumb-item>
            <Breadcrumb-item>借件订单-特殊来源</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">订单编号：</p>
                    <Input v-model="searchObj.orderNo" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">订单状态：</p>
                    <!-- TODO 对接下拉数据 -->
                    <Select v-model="searchObj.orderStatus" clearable style="width: 200px">
                        <Option v-for="item in orderStatusList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">运维编号：</p>
                    <Input v-model="searchObj.woId" placeholder="请输入运维编号" clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">电站编码：</p>
                    <Input v-model="searchObj.stationCode" placeholder="请输入电站编码" clearable style="width: 200px" />
                </div>
            </div>
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">业主名称：</p>
                    <Input v-model="searchObj.ownerName" placeholder="请输入业主名称" clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">申请时间：</p>
                    <DatePicker ref="formDateSh" :value="submintDate" @on-change="
                        (data) => {
                          return selectDate(data, 'submintDate');
                        }
                      " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择" style="width: 240px ">
                    </DatePicker>
                </div>
            </div>
            <div class="list_but" style="display: flex; justify-content: space-between; width: 100%;">
                <div class="condition">
                    <Button @click="add" type="primary">新增</Button>
                    <Button @click="edit" type="primary" :disabled="tableSelect.length !== 1 || tableSelect[0].orderStatus !== '0'">编辑</Button>
                    <Button @click="submit" type="primary" :disabled="tableSelect.length !== 1">提交</Button>
                    <Button @click="cancel" type="primary" :disabled="tableSelect.length !== 1 || tableSelect[0].orderStatus !== '2'">取消</Button>
                    <Button @click="del" type="error" :disabled="tableSelect.length !== 1 || tableSelect[0].orderStatus !== '0'">删除</Button>
                    <Button @click="exportFn" type="default">导出</Button>
                    <a href="https://cdn01.rrsjk.com/images/组件调拨验收单.docx" download="组件调拨验收单.docx" target="_blank">附件1：组件调拨验收单</a>
                </div>
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                </div>
            </div>
            <div class="commListDiv">
                <Table size="small" border ref="tableRef" :columns="tableColumn" :data="commList" @on-selection-change="selected => this.tableSelect = selected">
                    <template slot-scope="{ index }" slot="sn">
                        {{ index + 1 }}
                    </template>
                    <template slot-scope="{ index }" slot="orderStatus">
                        {{ orderStatusMap[commList[index].orderStatus] }}
                    </template>
                    <template slot-scope="{ index }" slot="orderSource">
                        {{ orderSourceMap[commList[index].orderSource] }}
                    </template>
                    <template slot-scope="{ row }" slot="accountingStatus">
                        {{ enums.accountingStatus[row.accountingStatus + ''] }}
                    </template>
                    <template slot-scope="{ index }" slot="option">
                        <Button type="info" ghost size="small" @click="openDetail(commList[index])" style="margin: 3px">详情</Button>
                    </template>
                </Table>
            </div>
            <Page style="margin: 20px" @on-change="changeCurrent" :current="searchObj.page"
                  :total="totalElements" show-total show-elevator />
        </div>
        <addEditPop ref="addEditPopRef" :orderId="chooseItemId" @getPageList="getList"/>
        <detail ref="detailsRef" :orderId="chooseItemId"/>
    </div>
</template>

<script>
import _data from "@/views/ospSpare/_edata"; // 数据字典
import API from "@/api/ospSpare/orderManage/lendOrder";
import detail from './details'
import addEditPop from "./addEditPop";
export default {
    name: "serviceProviderOrder",
    components: { addEditPop, detail },
    data() {
        return {
            searchObj: {
                submintDateRange: "",
                orderNo: "",
                woId: "",
                orderType: "",
                orderStatus: "",
                branchName: "",
                stationCode:"",
                ownerName:"",
                orderSource:1,
                pageNum: 1,
                pageSize: 10
            },
            formObj: {},
            staffList: [],
            totalElements: 0,
            submintDate: [],
            orderStatusList: _data.statusList,
            orderSourceList: _data.orderSource,
            orderStatusMap: {},
            orderSourceMap: {},
            commList: [],
            title: "一键派工",
            modelEdit: false,
            modelTitle: '新增',
            formItem: {
                aaa: '',
                bbb: ''
            },
            modelEditAdd: false,
            supplierList: [],
            tableSelect: [],
            chooseItemId: 0,
            accountingStatusList: _data.accountingStatus,
            tableColumn: [
                { type: 'selection', minWidth: 60, title: '选择', align: 'center' },
                { title: '序号', minWidth: 80, align: 'center', slot: 'sn' },
                { title: '订单编号', minWidth: 200, key: 'orderNo', align: 'center' },
                { title: '订单来源', minWidth: 200, key: 'orderSource',slot: 'orderSource',  align: 'center' },
                // { title: '审批单号', minWidth: 200, key: 'auditNo', align: 'center' },
                { title: '订单状态', minWidth: 200, key: 'orderStatus',slot: 'orderStatus', align: 'center' },
                { title: '中心', minWidth: 200, key: 'centerName', align: 'center' },
                { title: '申请服务商', minWidth: 200, key: 'warehouseName', align: 'center' },
                { title: '建站服务商', minWidth: 200, key: 'stationNetName', align: 'center' },
                { title: '运维单号', minWidth: 200, key: 'woId', align: 'center' },
                { title: '回购单号', minWidth: 200, key: 'purchaseNo', align: 'center' },
                { title: '提交时间', minWidth: 200, key: 'submitDate', align: 'center' },
                { title: '记账凭证号', minWidth: 200, key: 'accountingNo', align: 'center' },
                { title: '记账时间', minWidth: 200, key: 'accountingDate', align: 'center' },
                { title: '记账状态', minWidth: 200, key: 'accountingStatus', align: 'center',slot: 'accountingStatus' },
                { title: '记账描述', minWidth: 200, key: 'accountingDesc', align: 'center' },
                { title: '操作', minWidth: 120, align: 'center', fixed: 'right', slot: 'option' }
            ],
            enums: {
                accountingStatus: {
                    "0": "成功",
                    "1": "失败"
                }
            }
        }
    },
    mounted() {
        this.getDict()
        this.getList()
    },
    methods: {
        getDict(){
            this.orderStatusList.forEach(item => {
                this.orderStatusMap[item.value] = item.label
            })
            this.orderSourceList.forEach(item => {
                this.orderSourceMap[item.value] = item.label
            })
        },
        // 导出
        exportFn() {
            let datas = this.$_.cloneDeep(this.searchObj);
            datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
            delete datas.pageNum
            delete datas.pageSize
            API.exportList(datas).then(res => {
                let binaryData = [];
                let link = document.createElement('a');
                binaryData.push(res.data);
                link.style.display = 'none';
                link.href = window.URL.createObjectURL(new Blob(binaryData));
                link.setAttribute('download', '服务商借件订单.xlsx');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            })
        },
        // 新增
        add() {
            this.$refs.addEditPopRef.isAdd = true
            this.$refs.addEditPopRef.modelTitle = '新增'
            this.$refs.addEditPopRef.modelEdit = true
        },
        operation(){
            if(this.tableSelect.length!==1){
                this.$Message.warning('请选择一条数据')
                return
            }
            if(this.tableSelect[0].orderStatus!='6'){
                this.$Message.warning('请选择“待运维商认责”的数据')
                return
            }
            this.chooseItemId = this.tableSelect[0].id
        },
        supplier(){
            if(this.tableSelect.length!==1){
                this.$Message.warning('请选择一条数据')
                return
            }
            if(this.tableSelect[0].orderStatus!='9'){
                this.$Message.warning('请选择“待供应商认责”的数据')
                return
            }
            this.chooseItemId = this.tableSelect[0].id
        },
        // 编辑
        edit() {
          if(this.tableSelect.length!==1){
              this.$Message.warning('请选择一条数据')
              return
          }
          this.$refs.addEditPopRef.isAdd = false
          this.chooseItemId = this.tableSelect[0].id
          this.$refs.addEditPopRef.modelTitle = '编辑'
          this.$refs.addEditPopRef.modelEdit = true
          this.$nextTick(()=>{
              this.$refs.addEditPopRef.getDetail()
          })
        },
        // 提交
        submit() {
          if(this.tableSelect.length!==1){
            this.$Message.warning('请选择一条数据')
            return
          }
          if(this.tableSelect[0].orderStatus !== '0' && this.tableSelect[0].orderStatus !== '2'){
              this.$Message.warning('只有“初始”或“中心经理驳回”时，才允许提交订单！')
              return
          }
          this.$message.confirm('是否确认提交?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            loading: true,
            type: 'warning',
            beforeClose: (action, instance, done) => {
              if (action === 'confirm') {
                instance.confirmButtonLoading = true
                API.submitOrder({id: this.tableSelect[0].id}).then(res=>{
                  if(res.data.success){
                    this.$Message.success(res.data.result);
                    instance.confirmButtonLoading = false
                    done()
                    this.getList()
                  }else{
                    done()
                    this.$Message.warning(res.data.error);
                  }
                }).catch(() => {})
                  .finally(() => {
                    // 接口回调结束，关闭按钮加载
                    instance.confirmButtonLoading = false;
                  });
              }else{
                done()
              }
            }
          }).then(res=>{
            return new Promise((resolve, reject) => {

            })
          })
        },
        // 取消
        cancel() {
            this.$message.confirm('是否确认取消?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                loading: true,
                beforeClose: (action, instance, done) => {
                    if (action === 'confirm') {
                        instance.confirmButtonLoading = true
                        API.cancelOrder({id: this.tableSelect[0].id,orderStatus: 'h'}).then(res=>{
                            if(res.data.success){
                                this.$Message.success(res.data.result);
                                instance.confirmButtonLoading = false
                                done()
                                this.getList()
                            }else{
                                done()
                                this.$Message.warning(res.data.error);
                            }
                        }).catch(() => {})
                            .finally(() => {
                                // 接口回调结束，关闭按钮加载
                                instance.confirmButtonLoading = false;
                            });
                    }else{
                        done()
                    }
                }
            }).then(() => {
                this.$Message.success('取消成功!');
            })
        },
        // 删除
        del() {
          if(this.tableSelect.length!==1){
            this.$Message.warning('请选择一条数据')
            return
          }
          this.$message.confirm('是否确认删除?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            loading: true,
            type: 'warning',
            beforeClose: (action, instance, done) => {
              if (action === 'confirm') {
                instance.confirmButtonLoading = true
                API.delOrder({id: this.tableSelect[0].id}).then(res=>{
                  if(res.data.success){
                    this.$Message.success(res.data.result);
                    instance.confirmButtonLoading = false
                    done()
                    this.getList()
                  }else{
                    done()
                    this.$Message.warning(res.data.error);
                  }
                }).catch(() => {})
                  .finally(() => {
                    // 接口回调结束，关闭按钮加载
                    instance.confirmButtonLoading = false;
                  });
              }else{
                done()
              }
            }
          }).then(res=>{
            return new Promise((resolve, reject) => {

            })
          })
        },
        // 打开详情
        openDetail(row) {
          this.chooseItemId = row.id
          this.$refs.detailsRef.show = true
          this.$nextTick(()=>{
            this.$refs.detailsRef.getDetail()
          })
        },
        // 查询按钮
        queryList() {
            this.searchObj.pageNum = 1;
            this.getList();
        },

        // 重置按钮
        emptyList() {
            this.searchObj = {
                submintDateRange: "",
                orderNo: "",
                woId: "",
                orderType: "",
                orderStatus: "",
                branchName: "",
                stationCode:"",
                ownerName:"",
                pageNum: 1,
                pageSize: 10
            };
            this.submintDate = [];
        },
        // 选择日期
        selectDate(date, refObj) {
            this.submintDate = date;
            this.searchObj[refObj + "Start"] = date[0];
            this.searchObj[refObj + "End"] = date[1];
        },
        // 获取工单列表
        getList() {
            this.tableSelect = []
            let datas = this.$_.cloneDeep(this.searchObj);
            datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
            delete datas.pageNum
            delete datas.pageSize
            const page = { page: this.searchObj.pageNum, rows: this.searchObj.pageSize }
            API.getList(page,datas).then((res) => {
                let request = res.data.result;
                if (res.data.success && (request.content.length > 0)) {
                    this.totalElements = request.totalElements;
                    this.commList = request.content;
                } else {
                    this.totalElements = 0;
                    this.commList = [];
                }
            });
        },
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.pageNum = curr;
            this.getList();
        },
        jumpto(path, id, count) {
            this.$router.push({
                path,
                query: { id, count },
            });
        }
    }
}
</script>

<style lang="scss" scoped>
@import 'style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
