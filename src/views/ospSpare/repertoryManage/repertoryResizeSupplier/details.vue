<template>
    <Modal width="80%" v-model="show" title="详情" :footer-hide="true" :styles="{ top: '5vh' }"
           :mask-closable="false">
        <div style="overflow-y: auto; width: 100%; height: 80vh; background: #eee; padding: 20px">
            <Card :bordered="false">
                <div class="cus_list" style="min-height: calc(80vh - 72px)">
                    <div class="list_son">
<!--                        <div class="step">-->
<!--                            <Steps class="stepdiv" :current="typeNode('orderStatusList', infoList.status)">-->
<!--                                <Step v-for="(item, index) in infoList.processLogs" :key="index" :content="timeChange(item.createdAt)"-->
<!--                                      :title="item.desc"></Step>-->
<!--                            </Steps>-->
<!--                        </div>-->
                    </div>
                    <div class="cus_reg">
                        <div class="cus_form" style="padding-top: 0">
                            <p class="cus_title">主单信息</p>
                            <el-descriptions class="margin-top" :column="3" border>
                              <el-descriptions-item>
                                <template slot="label">
                                  调整单号
                                </template>
                                {{infoList?.sourceOrderId}}
                              </el-descriptions-item>
                              <el-descriptions-item>
                                <template slot="label">
                                  审批单号
                                </template>
                                {{infoList?.businessKey}}
                              </el-descriptions-item>
                              <el-descriptions-item>
                                <template slot="label">
                                  调整类型
                                </template>
                                  <span v-if="infoList?.tranType=='1'">入库</span>
                                  <span v-else-if="infoList?.tranType=='2'">出库</span>
                                  <span v-else-if="infoList?.tranType=='3'">新件入库</span>
                                  <span v-else-if="infoList?.tranType=='4'">旧件入库</span>
                                  <span v-else-if="infoList?.tranType=='5'">调拨入库</span>
                                  <span v-else-if="infoList?.tranType=='6'">借件入库</span>
                                  <span v-else-if="infoList?.tranType=='7'">调整入库</span>
                                  <span v-else-if="infoList?.tranType=='8'">调整出库</span>
                                  <span v-else-if="infoList?.tranType=='9'">性能故障入库</span>
                              </el-descriptions-item>
                              <el-descriptions-item>
                                <template slot="label">
                                  申请仓库名称
                                </template>
                                {{infoList?.warehouseName}}
                              </el-descriptions-item>
                              <el-descriptions-item>
                                <template slot="label">
                                  提交时间
                                </template>
                                {{infoList?.createdate}}
                              </el-descriptions-item>
                              <el-descriptions-item>
                                <template slot="label">
                                  调整状态
                                </template>
                                <span v-if="infoList.ifClose=='0'">未关闭</span>
                                <span v-if="infoList.ifClose=='1'">已关闭</span>
                              </el-descriptions-item>
                              <el-descriptions-item>
                                <template slot="label">
                                  审批状态
                                </template>
                                  <span v-if="infoList?.auditStatus=='0'">未提交</span>
                                  <span v-if="infoList?.auditStatus=='1'">已提交</span>
                                  <span v-if="infoList?.auditStatus=='2'">分中心通过</span>
                                  <span v-if="infoList?.auditStatus=='21'">中心驳回</span>
                                  <span v-if="infoList?.auditStatus=='3'">总部通过</span>
                                  <span v-if="infoList?.auditStatus=='31'">总部驳回</span>
                              </el-descriptions-item>
                              <el-descriptions-item>
                                <template slot="label">
                                  审批时间
                                </template>
                                {{infoList?.auditDate}}
                              </el-descriptions-item>
                              <el-descriptions-item>
                                <template slot="label">
                                  申请原因
                                </template>
                                {{infoList?.remarks}}
                              </el-descriptions-item>

                            </el-descriptions>
                            <p class="cus_title">明细信息</p>
                          <Table size="small" border ref="tableRef" :columns="tableColumn" :data="commList">
                          <template slot-scope="{ index }" slot="sn">
                            {{ index + 1 }}
                          </template>
                          </Table>
                          <p class="cus_title">附件信息</p>
                          <img class="preview" v-if="infoList.fileUrl" :src="infoList.fileUrl" @click="toTarget(infoList.fileUrl)" title="点击查看原图" />
                        </div>
                    </div>
                </div>
            </Card>
        </div>
    </Modal>
</template>

<script>
import _data from "@/views/ospSpare/_edata"; // 数据字典
import API from "@/api/ospSpare/repertoryManage/repertoryResize";

export default {
  data() {
    return {
        show: false,
      infoList: {},
      orderStatusList: _data.orderStatusList,
      sourceArr: _data.SourceArr,
      count: '',
      orderId:'',
      tableColumn: [
        { title: '序号',  minWidth: 80, align: 'center', slot: 'sn' },
        { title: '备件编码', minWidth: 100, key: 'materialCode', align: 'center' },
        { title: '备件描述', minWidth: 100, key: 'materialDesc', align: 'center' },
        { title: '备件唯一码', minWidth: 100, key: 'batchNum', align: 'center' },
        { title: '单位', minWidth: 100, key: 'materialUnit', align: 'center' },
        { title: '单价', minWidth: 100, key: 'materialPrice', align: 'center' },
        { title: '调整数量', minWidth: 100, key: 'amount', align: 'center' },
      ],
      commList: [],
    };
  },
  watch: {
    show(val) {
      if (val) {
        this.getInfo();
      }
    }
  },
  methods: {
    openImg(url) {
      window.open(url);
    },

    typeVal(list, value) {
      const obj = {};
      this[list].map((e) => {
        obj[e.value] = e.label || e.value;
      });
      // console.log(obj[value]);
      return obj[value];
    },

    typeNode(list, value) {
      const obj = {};
      this[list].map((e) => {
        obj[e.value] = e.node;
      });
      return obj[value];
    },

    timeChange(params) {
      // console.log(params);
      let dates = "";
      if (params) {
        dates = params.replace("T", " ");
      }
      return dates;
    },
    // 获取工单详情
    // 获取工单详情
    getInfo() {
      let params={'id':this.orderId}
      API.getInfo(params).then(res => {
        this.infoList = res.data?.result
        this.commList = res.data?.result?.spTranSnDetail||[]
      })
    },
  },
};
</script>

<style scoped lang="scss">
@import "@/style/_cus_list.scss";
@import "@/style/_cus_reg.scss";

.step {
  position: relative;
  margin: 0 20px;
  padding: 12px 6px 6px;
  border: 1px solid #e8eaec;

  .stepdiv {
    width: 80%;
    margin: 0 auto;
    @extend pt;
  }
}

.cus_title {
    margin-top: 20px;
    margin-bottom: 6px;
}
</style>
