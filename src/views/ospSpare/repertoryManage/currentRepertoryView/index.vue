<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏备件管理</Breadcrumb-item>
            <Breadcrumb-item to="/ospSpare/">入库管理</Breadcrumb-item>
            <Breadcrumb-item>现有库存查看-供应商</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">仓库名称：</p>
                    <Input v-model="searchObj.warehosueName" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">备件编码：</p>
                    <Input v-model="searchObj.materialCode" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">备件描述：</p>
                    <Input v-model="searchObj.materialDesc" placeholder="请输入..." clearable style="width: 200px" />
                </div>
<!--                <div class="condition">-->
<!--                    <p class="condition_p">仓库级别：</p>-->
<!--                    <Select v-model="searchObj.orderStatus" clearable style="width: 200px" placeholder="请选择..." >-->
<!--                        <Option  :value="1" :key="1">一级</Option>-->
<!--                        <Option  :value="2" :key="2">二级</Option>-->
<!--                        <Option  :value="3" :key="3">三级</Option>-->
<!--                    </Select>-->
<!--                </div>-->
            </div>
            <div class="list_but" style="display: flex; justify-content: space-between; width: 100%;">
                <div class="condition">
                    <Button @click="exportFn" type="success">导出</Button>
                </div>
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                </div>
            </div>
            <div class="commListDiv">
                <Table size="small" border ref="tableRef" :columns="tableColumn" :data="commList" @on-selection-change="selected => this.tableSelect = selected">
                    <template slot-scope="{ index }" slot="sn">
                        {{ index + 1 }}
                    </template>
                    <!--                    <template slot-scope="{ index }" slot="orderStatus">-->
                    <!--                        {{ orderStatusMap[commList[index].orderStatus] }}-->
                    <!--                    </template>-->
                    <template slot-scope="{ index }" slot="option">
                        <Button type="info" ghost size="small" @click="openDetail(commList[index])" style="margin: 3px">详情</Button>
                    </template>
                </Table>
            </div>
            <Page style="margin: 20px" @on-change="changeCurrent" :current="searchObj.page"
                  :total="totalElements" show-total show-elevator />
        </div>
        <detail ref="detailsRef" />
    </div>
</template>

<script>
import _data from "@/views/ospSpare/_edata"; // 数据字典
import API from "@/api/ospSpare/repertoryManage/currentRepertoryView";
import detail from './details'
export default {
    name: "serviceProviderOrder",
    components: { detail },
    data() {
        return {
            searchObj: {
                warehosueName: "",
                materialCode: "",
                materialDesc: "",
                warehosueLevel: "",
                pageNum: 1,
                pageSize: 10
            },
            formObj: {},
            staffList: [],
            totalElements: 0,
            submintDate: [],
            status: _data.status,
            orderStatusList: _data.orderStatusList,
            sourceArr: _data.SourceArr,
            faultCodeTypeList: _data.faultCodeTypeList,
            modelEdit: false,
            modelTitle: '新增',
            modelEditAdd: false,
            tableColumn: [
                { type: 'selection', minWidth: 60, title: '选择', align: 'center' },
                { title: '序号',  minWidth: 80, align: 'center', slot: 'sn' },
                { title: '仓库名称', minWidth: 180, key: 'warehouseName', align: 'center' },
                // { title: '中心名称', minWidth: 180, key: 'suptBranchName', align: 'center' },
                { title: '备件编码', minWidth: 160, key: 'materialCode', align: 'center' },
                { title: '备件描述', minWidth: 180, key: 'materialDesc', align: 'center' },
                { title: '可用库存量', minWidth: 100, key: 'storgeAmnt', align: 'center' },
                { title: '锁定库存量', minWidth: 100, key: 'lockedAmnt', align: 'center' },
                { title: '库存金额', minWidth: 100, key: 'price', align: 'center' },
                { title: '库龄', minWidth: 100, key: 'storageAge', align: 'center' },
                { title: '操作', minWidth: 120, align: 'center', fixed: 'right', slot: 'option' }
            ],
            commList: [],
            tableSelect:[],//表格选择数据
        }
    },
    mounted() {
        this.getList()
    },
    methods: {
        // 导出
        exportFn() {
            let datas = this.$_.cloneDeep(this.searchObj);
            datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
            delete datas.pageNum
            delete datas.pageSize
            //默认写死1 供应商
            datas.type ='1'
            API.exportList(datas).then(res => {
                let binaryData = [];
                let link = document.createElement('a');
                binaryData.push(res.data);
                link.style.display = 'none';
                link.href = window.URL.createObjectURL(new Blob(binaryData));
                link.setAttribute('download', '现有库存查看.xlsx');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            })
        },
        // 取消
        cancel() {
            this.$message.confirm('是否确认取消?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$Message.success('取消成功!');
            })
        },
        // 删除
        del() {
            this.$message.confirm('此操作将永久删除该文件, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$Message.success('删除成功!');
            })
        },
        // 打开详情
        openDetail(row) {
            this.$refs.detailsRef.dataSource = row
            this.$refs.detailsRef.show = true
        },
        // 查询按钮
        queryList() {
            this.searchObj.pageNum = 1;
            this.getList();
        },

        // 重置按钮
        emptyList() {
            this.searchObj = {
                warehosueName: "",
                materialCode: "",
                materialDesc: "",
                warehosueLevel: "",
                pageNum: 1,
                pageSize: 10
            };
            this.submintDate = [];
        },
        // 选择日期
        selectDate(date, refObj) {
            this.submintDate = date;
            this.searchObj[refObj + "Start"] = date[0];
            this.searchObj[refObj + "End"] = date[1];
        },
        // 获取工单列表
        getList() {
            this.$Message.loading({
                content: "查询中...",
                duration: 0,
            });
            let datas = this.$_.cloneDeep(this.searchObj);
            datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
            delete datas.pageNum
            delete datas.pageSize
            const page = { page: this.searchObj.pageNum, rows: this.searchObj.pageSize }
            // 将所有选中项清空
            this.$refs.tableRef.selectAll(false);
            //默认写死 1 供应商
            datas.type ='1'

            API.getList(page, datas).then(res => {
                this.$Message.destroy();
                this.commList = res.data.result.content
                this.totalElements = res.data.result.totalElements
            })
        },
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.pageNum = curr;
            this.getList();
        }
    }
}
</script>

<style lang="scss" scoped>
@import '@/style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
