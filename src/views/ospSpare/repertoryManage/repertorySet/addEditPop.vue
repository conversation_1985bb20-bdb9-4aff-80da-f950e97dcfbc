<template>
    <Modal width="1000" v-model="modelEdit" draggable sticky scrollable :title="modelTitle" :footer-hide="true"
           :mask-closable="false">
        <div style="position: relative; width: 100%; height: 40vh;">
            <div style="padding-bottom: 40px; width: 100%; height: 60vh; overflow-y: auto">
                <Form :label-width="100" ref="formItem" inline>
                    <FormItem label="服务商名称">
                        <Input type="text" maxlength="15" v-model="warehouseName" clearable placeholder="服务商名称"
                               style="width: 360px" disabled />
                    </FormItem>
                    <FormItem label="备件编码">
                        <Select v-model="materialId" :remote-method="getMaterialList" :loading="materialLoading" filterable style="width: 360px" :disabled="modelTitle === '编辑'">
                            <Option v-for="item in materialList" :value="item.id" :key="item.id">{{ item.materialCode }}</Option>
                        </Select>
                    </FormItem>
                    <FormItem label="安全库存数量">
                        <InputNumber v-model="storeSafe" :min="0" placeholder="安全库存数量" style="width: 360px" @on-blur="storeNumBlur" />
                    </FormItem>
                    <FormItem label="最高库存数量">
                        <InputNumber v-model="storeMax" :min="0" placeholder="最高库存数量" style="width: 360px" @on-blur="storeNumBlur" />
                    </FormItem>
                    <FormItem label="是否启用">
                        <Select v-model="usingFlag" filterable style="width: 360px">
                            <Option value="0">未启用</Option>
                            <Option value="1">启用</Option>
                        </Select>
                    </FormItem>
                </Form>
            </div>
            <div style="position: absolute; z-index: 99; right: 20px; bottom: -30px; text-align: right; margin: 30px 0">
                <Button type="primary"
                        :disabled="!materialId"
                        @click="debounce(submitPop)">提交</Button>
                <Button type="default" style="margin-left: 10px"
                        @click="modelEdit = false">取消</Button>
            </div>
        </div>
    </Modal>
</template>

<script>
import dict from '@/api/ospSpare'
import API from "@/api/ospSpare/repertoryManage/repertorySet";
export default {
    name: "addEdit",
    data() {
        return {
            modelEdit: false,
            modelTitle: '新增',
            rowId: '',
            warehouseName: '', // 服务商名称
            storeId: '', // 仓库id
            materialId: '', // 备件编码id
            storeSafe: 0, // 安全库存数
            storeMax: 0, // 最高库存数量
            usingFlag: '1', // 是否启用
            materialList: [], // 备件编码 下拉列表
            selectTableData: [],
            selectTableTotal: 0, // 添加明细列表总条数
            suppName: '',
            throttle: null,
            materialLoading: false, // 备件编码远程搜素加载中
        }
    },
    watch: {
        modelEdit(val) {
            if (val) {
                if (this.modelTitle === '新增') this.add_init()
                else this.edit_init()
            }
        }
    },
    methods: {
        add_init() {
            this.warehouseName = ''
            this.materialId = ''
            this.storeSafe = 0
            this.storeMax = 0
            this.usingFlag = '1'
            //  获取当前登录账号对应的供应商id
            const member_id = JSON.parse(window.localStorage.getItem('logins')).member_id

            // 获取当前登录账号的仓库id
            API.queryStoreId(member_id).then(res => {
                this.warehouseName = res.data.result.warehouseName
                this.storeId = res.data.result.id + ''
            })
        },
        // 编辑订单初始化
        edit_init() {
            API.getDetail(this.rowId).then(res => {
                this.materialList = [{ id: res.data.result.materialId, materialCode: res.data.result.materialCode }]
                this.warehouseName = res.data.result.warehouesName
                this.storeId = res.data.result.storeId
                this.storeSafe = res.data.result.storeSafe
                this.storeMax = res.data.result.storeMax
                this.usingFlag = res.data.result.usingFlag
                this.$nextTick(() => {
                    this.materialId = res.data.result.materialId
                })
            })
        },
        storeNumBlur() {
            if (!this.storeSafe) this.storeSafe = 0
            if (!this.storeMax) this.storeMax = 0
        },
        getMaterialList(query) {
            this.materialLoading = true
            // materialList 获取备件编码列表
            clearTimeout(this.throttle)
          this.throttle = setTimeout(() => {
              let params={
                  materialCode: query,
                  ifModule:'0',
                  productStatus:'1'
              }
              dict.getMaterialList(params).then(res => {
                  this.materialLoading = false
                  this.materialList = res.data.result
              }).catch(e => {
                  this.materialLoading = false
              })
          }, 600)
        },
        submitPop() {
            const data = {
                storeId: this.storeId,
                materialId: this.materialId,
                storeSafe: this.storeSafe,
                storeMax: this.storeMax,
                usingFlag: this.usingFlag
            }
            this.$Message.loading({
                content: "提交中...",
                duration: 0,
            });
            if (this.modelTitle === '新增') {
                API.addList(data).then(res => {
                    if (res.data.success) {
                        this.$Message.destroy();
                        this.$Message.success(res.data.result);
                        this.$emit('getList')
                        this.modelEdit = false
                    } else {
                        this.$Message.destroy();
                        this.$Message.error(res.data.error);
                    }
                })
            } else {
                data.id = this.rowId
                API.editSave(data).then(res => {
                    if (res.data.success) {
                        this.$Message.destroy();
                        this.$Message.success(res.data.result);
                        this.$emit('getList')
                        this.modelEdit = false
                    } else {
                        this.$Message.destroy();
                        this.$Message.error(res.data.error);
                    }
                })
            }
        }
    }
}
</script>

<style lang="scss" scoped>
@import 'style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
