<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏备件管理</Breadcrumb-item>
            <Breadcrumb-item to="/ospSpare/">库存管理</Breadcrumb-item>
            <Breadcrumb-item>安全仓库设置</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">服务商名称：</p>
                    <Select v-model="searchObj.storeId" filterable clearable style="width: 200px">
                        <Option v-for="item in warehouseList" :value="item.id" :key="item.id">{{ item.warehouseName }}</Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">备件编码：</p>
                    <Input v-model="searchObj.materialCode" placeholder="请输入备件编码" clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">备件描述：</p>
                    <Input v-model="searchObj.materialDesc" placeholder="请输入备件编码" clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">仓库等级：</p>
                    <Select v-model="searchObj.warehoseLevel" clearable style="width: 200px">
                        <Option v-for="item in stockLevel" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                </div>
            </div>
            <div class="list_but" style="display: flex; justify-content: space-between; width: 100%;">
                <div class="condition">
                    <Button @click="add" type="primary">新增</Button>
                    <Button @click="edit" type="primary" :disabled="tableSelect.length !== 1">编辑</Button>
                    <Button @click="importFn" type="success">导入</Button>
                    <Button @click="exportFn" type="error">导出</Button>
                    <Button @click="del" type="error" :disabled="tableSelect.length !== 1">删除</Button>
                </div>
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                </div>
            </div>
            <div class="commListDiv">
                <Table size="small" border ref="tableRef" :columns="tableColumn" :data="commList" @on-selection-change="selected => this.tableSelect = selected">
                    <template slot-scope="{ index }" slot="sn">
                        {{ index + 1 }}
                    </template>
                    <template slot-scope="{ index }" slot="usingFlag">
                        {{ commList[index].usingFlag === '1' ? '启用' : '未启用' }}
                    </template>
                </Table>
            </div>
            <Page style="margin: 20px" @on-change="changeCurrent" :current="searchObj.page"
                  :total="totalElements" show-total show-elevator />
        </div>
        <!-- 添加 -->
        <Modal width="600" v-model="modelEditAdd" draggable sticky scrollable title="导入" :footer-hide="true" :styles="{ top: 'calc(100px + 4vh)' }"
               :mask-closable="false">
            <div style="position: relative; width: 100%; height: auto;">
                <Upload
                    multiple
                    type="drag"
                    :headers="{Authorization: `Bearer ${token}`}"
                    :action=actionUrl
                    :on-success="uploadThen"
                    :on-error="uploadThen"
                >
                    <div style="padding: 20px 0">
                        <Icon type="ios-cloud-upload" size="150" style="color: #3399ff"></Icon>
                        <p>点击或拖动文件到这里上传</p>
                    </div>
                </Upload>
                <Button type="success" long @click="downloadTemplate" style="margin-top: 20px">下载安全库存导入模板</Button>
            </div>
        </Modal>
        <detail ref="detailsRef" />
        <addEditPop ref="addEditPopRef" @getList="getList" />
    </div>
</template>

<script>
import _data from "@/views/ospSpare/_edata"; // 数据字典
import addEditPop from "./addEditPop";
import detail from './details'
import API from "@/api/ospSpare/repertoryManage/repertorySet";
import dict from '@/api/ospSpare'
export default {
    name: "serviceProviderOrder",
    components: { addEditPop, detail },
    data() {
        return {
            searchObj: {
                warehoseLevel: "",
                storeId: "",
                materialCode: "",
                materialDesc: "",
                pageNum: 1,
                pageSize: 10
            },
            totalElements: 0,
            stockLevel: _data.stockLevel,
            warehouseList: [],
            commList: [],
            tableColumn: [
                { type: 'selection', minWidth: 60, title: '选择', align: 'center' },
                { title: '序号', minWidth: 80, align: 'center', slot: 'sn' },
                { title: '中心名称', minWidth: 180, key: 'centerName', align: 'center' },
                { title: '仓库名称', minWidth: 180, key: 'warehouesName', align: 'center' },
                { title: '备件编码', minWidth: 180, key: 'materialCode', align: 'center' },
                { title: '备件描述', minWidth: 180, key: 'materialDesc', align: 'center' },
                { title: '安全库存', minWidth: 180, key: 'storeSafe', align: 'center' },
                { title: '最高库存', minWidth: 180, key: 'storeMax', align: 'center' },
                { title: '已订货数量', minWidth: 180, key: 'orderAmnt', align: 'center' },
                { title: '在途数量', minWidth: 180, key: 'wayinAmnt', align: 'center' },
                { title: '库存数量', minWidth: 180, key: 'storeAmnt', align: 'center' },
                { title: '创建时间', minWidth: 180, key: 'createDate', align: 'center' },
                { title: '创建人', minWidth: 180, key: 'createBy', align: 'center' },
                { title: '修改时间', minWidth: 180, key: 'updateDate', align: 'center' },
                { title: '修改人', minWidth: 180, key: 'updateBy', align: 'center' },
                { title: '启用状态', minWidth: 180, key: 'usingFlag', align: 'center', slot: 'usingFlag' },
                { title: '在途更新时间', minWidth: 180, key: 'computeDate', align: 'center' }
            ],
            tableSelect: [],
            modelEditAdd: false,
            token: '',
            actionUrl: `${process.env.VUE_APP_BASE_API}/merchant/repairs/spWarehouseAttach/importData`,
            headers: { Authorization: `Bearer ${JSON.parse(window.localStorage.getItem("logins")).access_token}` },
        }
    },
    mounted() {
        this.getDict()
        this.getList()
        this.token = JSON.parse(window.localStorage.getItem("logins"))?.access_token || 'unAuthor'
    },
    methods: {
        // 新增
        add() {
            this.$refs.addEditPopRef.modelTitle = '新增'
            this.$refs.addEditPopRef.modelEdit = true
        },
        // 编辑
        edit() {
            this.$refs.addEditPopRef.modelTitle = '编辑'
            this.$refs.addEditPopRef.rowId = this.tableSelect[0].id
            this.$refs.addEditPopRef.modelEdit = true
        },
        // 导入
        importFn() {
            this.modelEditAdd = true
        },
        uploadThen(e) {
            console.log(e)
            if (e.success) {
                this.$Message.success(e.result);
                this.modelEditAdd = false
                this.queryList()
            } else {
                this.$Message.error(e.error);
            }
        },
        // 下载导入模板
        downloadTemplate() {
            API.exportTemplateList().then(res => {
                let binaryData = [];
                let link = document.createElement('a');
                binaryData.push(res.data);
                link.style.display = 'none';
                link.href = window.URL.createObjectURL(new Blob(binaryData));
                link.setAttribute('download', '安全库存模板.xlsx');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            })
        },
        // 导出
        exportFn() {
            let datas = this.$_.cloneDeep(this.searchObj);
            datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
            delete datas.pageNum
            delete datas.pageSize
            API.exportList(datas).then(res => {
                let binaryData = [];
                let link = document.createElement('a');
                binaryData.push(res.data);
                link.style.display = 'none';
                link.href = window.URL.createObjectURL(new Blob(binaryData));
                link.setAttribute('download', '安全库存列表.xlsx');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            })
        },
        // 删除
        del() {
            this.$message.confirm('此操作将永久删除该文件, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$Message.loading({
                    content: "删除中...",
                    duration: 0,
                });
                API.delList(this.tableSelect[0].id).then(res => {
                    if(res.data.success) {
                        this.$Message.destroy();
                        this.$Message.success(res.data.result);
                        this.getList()
                    } else {
                        this.$Message.destroy();
                        this.$Message.error(res.data.error);
                    }
                })
            })
        },
        // 打开详情
        openDetail(row) {
            this.$refs.detailsRef.show = true
        },
        // 查询按钮
        queryList() {
            this.searchObj.pageNum = 1;
            this.getList();
        },

        // 重置按钮
        emptyList() {
            this.searchObj = {
                warehoseLevel: "",
                storeId: "",
                materialCode: "",
                materialDesc: "",
                pageNum: 1,
                pageSize: 10
            };
        },
        // 获取工单列表
        getList() {
            this.$Message.loading({
                content: "查询中...",
                duration: 0,
            });
            let datas = this.$_.cloneDeep(this.searchObj);
            datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
            delete datas.pageNum
            delete datas.pageSize
            const page = { page: this.searchObj.pageNum, rows: this.searchObj.pageSize }
            // 将所有选中项清空
            this.$refs.tableRef.selectAll(false);
            API.getList(page, datas).then(res => {
                this.$Message.destroy();
                this.commList = res.data.result.content
                this.totalElements = res.data.result.totalElements
            })
        },
        // 获取下拉数据
        getDict() {
            // 获取服务商数据
            dict.getWarehouse({ activeFlag: '1', warehouseLevel: '3' }).then(res => {
                const list = []
                console.log(res)
                res.data.result.forEach(item => {
                    if (list.indexOf(item.warehouseCode) === -1 && item.warehouseCode) {
                        list.push(item.warehouseCode)
                        this.warehouseList.push(item)
                    }
                })
            })
        },
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.pageNum = curr;
            this.getList();
        },
        jumpto(path, id, count) {
            this.$router.push({
                path,
                query: { id, count },
            });
        }
    }
}
</script>

<style lang="scss" scoped>
@import 'style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
