<template>
    <Modal width="1200" v-model="modelEdit" draggable sticky scrollable :title="modelTitle" :footer-hide="true"
           :mask-closable="false">
        <div style="position: relative; width: 100%; height: 60vh;">
            <div style="padding-bottom: 40px; width: 100%; height: 60vh; overflow-y: auto">
                <Form :model="formItem" :label-width="100" ref="formItem" inline>
                    <FormItem label="申请仓库">
                        <Input type="text" maxlength="15" v-model="formItem.warehouseName" clearable placeholder="申请仓库"
                               style="width: 360px" disabled />
                    </FormItem>
                    <FormItem label="调整类型">
                        <Select v-model="formItem.tranType" placeholder="调整类型" style="width: 200px" :disabled="modelTitle == '编辑'">
<!--                            <Option value="7">调整入库</Option>-->
                            <Option value="8">调整出库</Option>
                        </Select>
                    </FormItem>
                </Form>
                <div class="list_but" style="margin-bottom: 10px">
                    <div class="condition">
                        <Button @click="modelEditAdd = true" size="small" type="primary">添加</Button>
                        <Button style="margin-left: 6px" @click="del" size="small" type="error" :disabled="!subTableSelect.length">删除</Button>
                    </div>
                </div>
                <div class="commListDiv">
                    <Table size="small" border ref="subTableRef" :columns="subTableColumn" :data="subTableData" @on-selection-change="selected => this.subTableSelect = selected">
<!--                        <template slot-scope="{ index }" slot="amount">-->
<!--                            <InputNumber :min="0" v-model="subTableData[index].amount" style="text-align: center" @on-change="tableInputChange" />-->
<!--                        </template>-->
                    </Table>
                </div>
                <!-- 添加 -->
                <Modal width="1000" v-model="modelEditAdd" draggable sticky scrollable :title="modelTitle" :footer-hide="true" :styles="{ top: 'calc(100px + 4vh)' }"
                       :mask-closable="false">
                    <div style="position: relative; width: 100%; height: 50vh;">
                        <div style="padding-bottom: 40px; width: 100%; height: 50vh; overflow-y: auto">
                            <Form :model="addDetailQuery" :label-width="100" ref="formItem" inline>
                                <FormItem label="备件编码">
                                    <Input type="text" maxlength="15" v-model="addDetailQuery.materialCode" clearable placeholder="备件编码"
                                           style="width: 260px" />
                                </FormItem>
                                <FormItem label="备件描述" prop="duty">
                                    <Input type="text" maxlength="15" v-model="addDetailQuery.materialDesc" clearable placeholder="备件描述"
                                           style="width: 260px" />
                                </FormItem>
                            </Form>
                            <div class="list_but" style="margin-bottom: 10px">
                                <div class="condition">
                                    <Button @click="debounce(getDetailList)" size="small" type="primary">查询</Button>
                                    <Button @click="debounce(emptyList)" style="margin-left: 6px" size="small">重置</Button>
                                </div>
                            </div>
                            <div class="commListDiv">
                                <Table size="small" border ref="selectTableRef"  :columns="selectTableColumn" :data="selectTableData" @on-selection-change="selected => this.selectTableSelect = selected">
                                    <template slot-scope="{ index }" slot="productStatus">
                                        {{ productMap[selectTableData[index].productStatus] }}
                                    </template>
                                </Table>
                            </div>
                            <Page style="margin: 20px; text-align: right" @on-change="changeCurrent" :current="queryPage.page"
                                  :total="selectTableTotal" show-total show-elevator />
                        </div>
                        <div style="position: absolute; z-index: 100; right: 20px; bottom: -30px; text-align: right; margin: 30px 0">
                            <Button type="primary"
                                    :disabled="!selectTableSelect.length"
                                    @click="debounce(submitSelect)">确定</Button>
                            <Button type="default" style="margin-left: 10px"
                                    @click="modelEditAdd = false">取消</Button>
                        </div>
                    </div>
                </Modal>
            </div>
            <div style="position: absolute; z-index: 99; right: 20px; bottom: -30px; text-align: right; margin: 30px 0">
                <Button type="primary"
                        :disabled="!subTableData.length"
                        @click="debounce(submitPop)">提交</Button>
                <Button type="default" style="margin-left: 10px"
                        @click="modelEdit = false">取消</Button>
            </div>
        </div>
    </Modal>
</template>

<script>
import API from "@/api/ospSpare/repertoryManage/repertoryResize";
import _data from "@/views/ospSpare/_edata";
import AreaChange from "@/utils/areaChange"; // 数据字典

export default {
    name: "addEdit",
    data() {
        return {
            modelEdit: false,
            modelTitle: '新增',
            orderId: '',
            data: {},
            queryPage: { // 查询条件 No页数 Size每页大小
                page: 1,
                rows: 10
            },
            formItem: {
                suptBranchCode: '',
                warehouseName: '',
                tranType: '8'
            },
            subTableSelect: [],
            subTableData: [],
            subTableColumn: [
                {type: 'selection', width: 60, title: '选择', align: 'center'},
                {title: '备件编码', key: 'materialCode', align: 'center'},
                {title: '备件名称', key: 'materialDesc', align: 'center'},
                {title: '备件唯一码', key: 'batchNum', align: 'center'},
                {title: '备件单价', key: 'materialPrice', align: 'center'},
                {title: '调整数量', key: 'amount', align: 'center'}
            ],
            modelEditAdd: false,
            addDetailQuery: {
                materialCode: '',
                materialDesc: ''
            },
            selectTableSelect: [],
            selectTableColumn: [
                {type: 'selection', width: 60, title: '选择', align: 'center'},
                {title: '备件编码', key: 'materialCode', align: 'center'},
                {title: '备件描述', key: 'materialDesc', width:180, align: 'center'},
                {title: '备件品牌', key: 'materialBrand', align: 'center'},
                {title: '备件分类', key: 'materialType', align: 'center'},
                {title: '备件价格', key: 'price', align: 'center'},
                {title: '库存数量', key: 'storgeAmnt', align: 'center'},
                {title: '备件唯一码', key: 'batchNum',width:160, align: 'center'}
            ],
            selectTableData: [], // 添加明细列表
            selectTableTotal: 0, // 添加明细列表总条数
            productMap: {}
        }
    },
    watch: {
        // 打开新增弹窗
        modelEdit(val) {
            if (val) {
                _data.productStatus.forEach(item => {
                    this.productMap[item.value] = item.label
                })
                this.subTableData = []
                this.$refs.subTableRef.selectAll(false);
                this.selectTableData = []
                this.$refs.selectTableRef.selectAll(false);
                if (this.modelTitle === '新增') {
                  this.add_init()
                }
                else{
                  //编辑
                  this.edit_init()
                }

            }
        },
        // 打开添加明细弹窗
        modelEditAdd(val) {
            if (val) {
                this.getDetailList()
            }
        }
    },
    methods: {
        // 新增订单初始化
        add_init() {
            this.warehouseName = ''
            this.tranType = '8'
            const member_id = JSON.parse(window.localStorage.getItem('logins')).member_id
            API.addLoading(member_id).then(res => {
                this.formItem.warehouseName = res.data.result.warehouseName
                this.formItem.branchId = res.data.result.branchId
                this.formItem.warehouseId = res.data.result.id
                this.formItem.suptBranchCode = res.data.result.suptBranchCode
                this.data = res.data.result
            })
        },
        // 编辑订单初始化
        edit_init() {
          API.getInfo({'id':this.orderId}).then(res=>{
            if(res.data.success){
              this.formItem.warehouseName=res.data.result.warehouseName
              this.formItem.warehouseId=res.data.result.warehouseId
              this.formItem.branchId=res.data.result.branchId
              this.formItem.storeId = res.data.result.storeId
              this.formItem.tranType=res.data.result.tranType
             this.subTableData= res.data.result.spTranSnDetail
            }
          })
        },
        del() {
            this.$Message.success('删除成功!');
            this.subTableData = this.subTableData.filter(item => !this.subTableSelect.some(v => v.materialCode === item.materialCode))
            this.$refs.subTableRef.selectAll(false);
        },
        changeCurrent(e) {
            this.queryPage.page = e
            this.getDetailList()
        },
        // 获取添加明细接口
        getDetailList() {
            let data = this.addDetailQuery
            if (this.modelTitle === '编辑') data.deptmentId = this.data.suptWarehouseId
            if(this.formItem.tranType=='7'){
              data.productStatus='1' // 生产状态
              API.getDetailList(this.queryPage, data).then(res => {
                this.selectTableTotal = res.data.result.totalElements
                this.selectTableData = res.data.result.content
              })
            }
            if(this.formItem.tranType=='8'){
              let params={
                'warehouseId':this.formItem.warehouseId,
                'materialCode':this.addDetailQuery.materialCode,
                'materialDesc':this.addDetailQuery.materialDesc,
              }
              API.cdMaterial(params).then(res=>{
                if(res.data.success){
                  this.selectTableData=res.data.result
                }
              })
            }

        },
        // 清空明细信息查询条件
        emptyList() {
            this.addDetailQuery = {
                materialCode: '',
                materialDesc: ''
            }
        },
        tableInputChange(e) {
            this.$refs.subTableRef.selectAll(false);
        },
        submitSelect() {
            const selectList = []
            this.selectTableSelect.forEach(item => {
                const _item = item
                _item.storeageId = _item.id
                _item.materialPrice = _item.price
                _item.amount = _item.storgeAmnt
                selectList.push(_item)
            })
            // 重复选中的订单
            const reqOrder = selectList.filter(item => this.subTableData.some(v => v.materialCode === item.materialCode))
            // 没有重复 可追加上的订单
            const addOrder = selectList.filter(item => !this.subTableData.some(v => v.materialCode === item.materialCode))
            // 重复订单提示标记
            let reqMsg = []
            reqOrder.forEach(item => {
                reqMsg.push(item.materialCode)
            })
            reqMsg = reqMsg.toString()
            if (addOrder.length && !reqOrder.length) {
                this.subTableData.push(...addOrder)
                this.modelEditAdd = false
                this.$refs.selectTableRef.selectAll(false);
                this.$Message.success('添加成功!');
            } else if (addOrder.length && reqOrder.length) {
                this.subTableData.push(...addOrder)
                this.modelEditAdd = false
                this.$refs.selectTableRef.selectAll(false);
                this.$Message.success('添加成功!去除重复订单：' + reqMsg);
            } else if (!addOrder.length && reqOrder.length) {
                this.$Message.error
                ('当前选中订单已添加：' + reqMsg);
            }
        },
        submitPop() {
            if (this.modelTitle === '新增') {
                const spTranSnDetail = []
                this.subTableData.forEach(item => {
                    spTranSnDetail.push({
                        materialId: item.materialId,
                        batchNum: item.batchNum,
                        materialPrice: item.materialPrice,
                        amount:item.amount,
                        getAmnt :item.amount,
                        orderAmnt:item.amount,
                        storeageId:item.storeageId,
                    })
                })
                const data = {
                    storeId: this.data.id,
                    tranType: this.formItem.tranType,
                    branchId: this.formItem.branchId,
                    spTranSnDetail: spTranSnDetail,
                }
                this.$Message.loading({
                    content: "提交中...",
                    duration: 0,
                });
                API.addSave(data).then(res => {
                    if (res.data.success) {
                        this.$Message.destroy();
                        this.$Message.success(res.data.message || '添加成功！');
                        this.modelEdit = false
                        setTimeout(() => {
                          this.$emit('getList')
                        }, 100);
                    } else {
                        this.$Message.destroy();
                        this.$Message.error(res.data.error);
                    }
                })
            } else {
              //编辑
                const spTranSnDetail = []

                this.subTableData.forEach(item => {
                    spTranSnDetail.push({
                        id: item.materialId ? item.id : null,
                        materialId: item.materialId ? item.materialId : item.id,
                        materialCode: item.materialCode,
                        materialDesc: item.materialDesc,
                        materialUnit: item.materialUnit,
                        standard: item.sendStandard,
                        materialPrice: item.settlePrice,
                        requireAmount: item.requireAmount,
                        suptWarehouseId: item.deptmentId,
                        amount:item.amount,
                        getAmnt :item.amount,
                        orderAmnt:item.amount,
                        storeageId:item.storeageId,
                    })
                })
                const data = {
                    id: this.orderId,
                    storeId:this.formItem.storeId,
                    spTranSnDetail: spTranSnDetail,
                }
                this.$Message.loading({
                    content: "提交中...",
                    duration: 0,
                });
                API.editSave(data).then(res => {
                    if (res.data.success) {
                        this.$Message.destroy();
                        this.$Message.success(res.data.result);
                        this.modelEdit = false
                        setTimeout(() => {
                          this.$emit('getList')
                        }, 100);
                    } else {
                        this.$Message.destroy();
                        this.$Message.error(res.data.error);
                    }
                })
            }
        }
    }
}
</script>

<style lang="scss" scoped>
@import 'style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
