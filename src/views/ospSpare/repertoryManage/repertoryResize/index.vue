<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏备件管理</Breadcrumb-item>
            <Breadcrumb-item to="/ospSpare/">库存管理</Breadcrumb-item>
            <Breadcrumb-item>库存调整</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">调整单号：</p>
                    <Input v-model="searchObj.tranCode" placeholder="调整单号" clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">备件编码：</p>
                    <Input v-model="searchObj.materialCode" placeholder="请输入备件编码" clearable style="width: 200px" />
                </div>
<!--                <div class="condition">
                    <p class="condition_p">审批单号：</p>
                    <Input v-model="searchObj.businessKey" placeholder="审批单号" clearable style="width: 200px" />
                </div>-->
<!--                <div class="condition">
                    <p class="condition_p">调整类型：</p>
                    <Select v-model="searchObj.tranType" placeholder="调整类型" clearable style="width: 200px">
                        <Option v-for="item in tranSnType" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                </div>-->
                <div class="condition">
                    <p class="condition_p">调整状态：</p>
                    <Select v-model="searchObj.ifClose" placeholder="调整状态" clearable style="width: 200px">
                        <Option value="1">已关闭</Option>
                        <Option value="0">未关闭</Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">创建时间：</p>
                    <DatePicker ref="formDateSh" :value="createDate" @on-change="
                        (data) => {
                          return selectDate2(data, 'createDate');
                        }
                      " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择" style="width: 240px ">
                    </DatePicker>
                </div>
            </div>
            <div class="list_but">
<!--                <div class="condition">
                    <p class="condition_p">申请仓库：</p>
                    <Select v-model="searchObj.warehouseId" filterable clearable style="width: 200px">
                        <Option v-for="item in warehouseList" :value="item.id" :key="item.id">{{ item.warehouseName }}</Option>
                    </Select>
                </div>-->
<!--                <div class="condition">
                    <p class="condition_p">审批状态：</p>
                    <Select v-model="searchObj.auditStatus" clearable style="width: 200px">
                        <Option value="0">未提交</Option>
                        <Option value="1">已提交</Option>
                        <Option value="2">分中心通过</Option>
                        <Option value="21">中心驳回</Option>
                        <Option value="3">总部通过</Option>
                        <Option value="31">总部驳回</Option>
                    </Select>
                </div>-->
<!--                <div class="condition">
                    <p class="condition_p">审批时间：</p>
                    <DatePicker ref="formDateSh" :value="backDate" @on-change="
                        (data) => {
                          return selectDate(data, 'backDate');
                        }
                      " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择审批时间" style="width: 240px ">
                    </DatePicker>
                </div>-->

            </div>
<!--            <div class="list_but">-->
<!--                <div class="condition">-->
<!--                    <p class="condition_p">备件编码：</p>-->
<!--                    <Input v-model="searchObj.materialCode" placeholder="备件编码" clearable style="width: 200px" />-->
<!--                </div>-->
<!--            </div>-->
            <div class="list_but" style="display: flex; justify-content: space-between; width: 100%;">
                <div class="condition">
                    <Button @click="add" type="primary">新增</Button>
                    <Button
                        @click="edit"
                        type="primary"
                        :disabled="tableSelect.length !== 1 || tableSelect[0].ifClose !== '1' || tableSelect[0].backStatus !== '0' || tableSelect[0].tranType !== '8'"
                    >
                        编辑
                    </Button>
                    <Button @click="submit" type="success" :disabled="tableSelect.length !== 1 || tableSelect[0].ifClose == '1'">提交</Button>
                    <Button @click="del" type="error" :disabled="tableSelect.length !== 1">删除</Button>
                    <Button @click="exportFn" type="error">导出</Button>
                </div>
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                </div>
            </div>
            <div class="commListDiv">
                <Table size="small" border ref="tableRef" :columns="tableColumn" :data="commList" @on-selection-change="selected => this.tableSelect = selected">
                    <template slot-scope="{ index }" slot="sn">
                        {{ index + 1 }}
                    </template>
                    <template slot-scope="{ index }" slot="option">
                        <Button type="info" ghost size="small" @click="openDetail(commList[index])" style="margin: 3px">详情</Button>
                    </template>
                    <template slot-scope="{ index }" slot="tranType">
                        {{ tranSnTypeMap[commList[index].tranType] }}
                    </template>
<!--                    调整状态-->
                    <template slot-scope="{ index }" slot="ifClose">
                        <span v-if="commList[index].ifClose=='0'">未关闭</span>
                        <span v-if="commList[index].ifClose=='1'">已关闭</span>
                    </template>
<!--                    审批状态-->
                    <template slot-scope="{ index }" slot="auditStatus">
                        <span v-if="commList[index].auditStatus=='0'">未提交</span>
                        <span v-if="commList[index].auditStatus=='1'">已提交</span>
                        <span v-if="commList[index].auditStatus=='2'">分中心通过</span>
                        <span v-if="commList[index].auditStatus=='21'">中心驳回</span>
                        <span v-if="commList[index].auditStatus=='3'">总部通过</span>
                        <span v-if="commList[index].auditStatus=='31'">总部驳回</span>
                    </template>
                </Table>
            </div>
            <Page style="margin: 20px" @on-change="changeCurrent" :current="searchObj.page"
                  :total="totalElements" show-total show-elevator />
        </div>
        <detail ref="detailsRef" />
        <addEditPop ref="addEditPopRef" @getList="getList" />
    </div>
</template>

<script>
import _data from "@/views/ospSpare/_edata"; // 数据字典
import addEditPop from "./addEditPop";
import detail from './details'
import API from "@/api/ospSpare/repertoryManage/repertoryResize";
import dict from "@/api/ospSpare";
export default {
    name: "serviceProviderOrder",
    components: { addEditPop, detail },
    data() {
        return {
            searchObj: {
                tranCode: "",
                businessKey: "",
                tranType: "",
                ifClose: "",
                warehouseId: "",
                materialCode: "",
                backStatus: "",
                backDateStart: "",
                backStartEnd: "",
                createDateStart: "",
                createDateEnd: "",
                pageNum: 1,
                pageSize: 10
            },
            formObj: {},
            staffList: [],
            totalElements: 0,
            backDate: [],
          createDate: [],
            tranSnType: _data.tranSnType,
            tranSnTypeMap: {},
            warehouseList: [],
            commList: [],
            tableColumn: [
                { type: 'selection', minWidth: 60, title: '选择', align: 'center' },
                { title: '序号', minWidth: 80, align: 'center', slot: 'sn' },
                { title: '调整单号', minWidth: 180, key: 'tranCode', align: 'center' },
                // { title: '审批单号', minWidth: 180, key: 'businessKey', align: 'center' },
                { title: '调整类型', minWidth: 180, key: 'tranType', align: 'center', slot: 'tranType' },
                { title: '中心名称', minWidth: 180, key: 'centerName', align: 'center' },
                // { title: '申请仓库编码', minWidth: 180, key: 'warehouseCode', align: 'center' },
                { title: '申请仓库名称', minWidth: 180, key: 'warehouseName', align: 'center' },
                { title: '提交时间', minWidth: 180, key: 'submintDate', align: 'center' },
                { title: '调整状态', minWidth: 180, key: 'ifClose', align: 'center',slot: 'ifClose'  },
                // { title: '审批状态', minWidth: 180, key: 'auditStatus', align: 'center',slot: 'auditStatus' },
                // { title: '审批人', minWidth: 180, key: 'auditBy', align: 'center' },
                // { title: '审批时间', minWidth: 180, key: 'auditDate', align: 'center' },
                { title: '创建时间', minWidth: 180, key: 'createDate', align: 'center' },
                { title: '申请原因', minWidth: 180, key: 'remarks', align: 'center' },
                { title: '附件', minWidth: 180, key: 'fileUrl', align: 'center' },
                { title: '操作', minWidth: 120, align: 'center', fixed: 'right', slot: 'option' }
            ],
            tableSelect: [],
        }
    },
    mounted() {
        this.getDict()
        this.getList()
    },
    methods: {
        // 导出
        exportFn() {
            let datas = this.$_.cloneDeep(this.searchObj);
            datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
            delete datas.pageNum
            delete datas.pageSize
            API.exportList(datas).then(res => {
                let binaryData = [];
                let link = document.createElement('a');
                binaryData.push(res.data);
                link.style.display = 'none';
                link.href = window.URL.createObjectURL(new Blob(binaryData));
                link.setAttribute('download', '库存调整列表.xlsx');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            })
        },
        // 新增
        add() {
            this.$refs.addEditPopRef.modelTitle = '新增'
            this.$refs.addEditPopRef.modelEdit = true
        },
        // 编辑
        edit() {
            this.$refs.addEditPopRef.modelTitle = '编辑'
            this.$refs.addEditPopRef.orderId = this.tableSelect[0].id
            this.$refs.addEditPopRef.modelEdit = true
        },
        // 提交
        submit() {
            this.$message.confirm('是否确认提交?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
              let params={
                "ifClose":this.tableSelect[0].ifClose,
                "auditStatus":this.tableSelect[0].auditStatus,
                "tranType":this.tableSelect[0].tranType,
                "id":this.tableSelect[0].id,
              }
              API.submitStockAdjustment(params).then(res=>{
                console.log(res);
                if(res.data.success){
                  this.$Message.success('提交成功!');
                  setTimeout(() => {
                    this.getList()
                  }, 1000)
                }else{
                  this.$Message.error(res.data.error)
                }
              }).catch(err=>{
                this.$Message.error(err.data.error)
              })

            })
        },
        // 删除
        del() {
            this.$message.confirm('此操作将永久删除该文件, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
              API.delStockAdjustment({'id':this.tableSelect[0].id}).then(res=>{
                if(res.data.success){
                  this.$Message.success('删除成功!');
                  setTimeout(() => {
                    this.getList()
                  }, 1000)
                }else{
                  this.$Message.error(res.data.error)
                }
              }).catch(err=>{
                this.$Message.error(err.data.error)
              })
            })
        },
        // 打开详情
        openDetail(row) {
            this.$refs.detailsRef.orderId = row.id
            this.$refs.detailsRef.show = true
        },
        // 查询按钮
        queryList() {
            this.searchObj.pageNum = 1;
            this.getList();
        },

        // 重置按钮
        emptyList() {
            this.searchObj = {
                orderCode: "",
                woId: "",
                orderType: "",
                orderStatus: "",
                branchName: "",
                backDateStart: '',
                backStartEnd: '',
                createDateStart: '',
                createDateEnd: '',
                pageNum: 1,
                pageSize: 10
            };
            this.backDate = [];
            this.createDate = [];
        },
        // 选择日期
        selectDate(date, refObj) {
            this[refObj] = date
            this.searchObj[refObj + "Start"] = date[0];
            this.searchObj[refObj + "End"] = date[1];
        },
      selectDate2(date, refObj) {
        this[refObj] = date
        this.searchObj[refObj + "Start"] = date[0];
        this.searchObj[refObj + "End"] = date[1];
      },
        // 获取工单列表
        getList() {
            let datas = this.$_.cloneDeep(this.searchObj);
            datas = this.$_.omitBy(datas, item => item === '' || this.$_.isUndefined(item))
            delete datas.pageNum
            delete datas.pageSize
            const page = { page: this.searchObj.pageNum, rows: this.searchObj.pageSize }
            // 将所有选中项清空
            this.$refs.tableRef.selectAll(false);
            API.getList(page, datas).then(res => {
                this.commList = res.data.result.content
                this.totalElements = res.data.result.totalElements
            })
        },
        // 获取下拉数据
        getDict() {
            // 订单类型map
            this.tranSnType.forEach(item => {
                this.tranSnTypeMap[item.value] = item.label
            })// 获取服务商数据
            dict.getWarehouse({ activeFlag: '1', warehouseLevel: '3' }).then(res => {
                const list = []
                res.data.result.forEach(item => {
                    if (list.indexOf(item.warehouseCode) === -1 && item.warehouseCode) {
                        list.push(item.warehouseCode)
                        this.warehouseList.push(item)
                    }
                })
            })
        },
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.pageNum = curr;
            this.getList();
        },
        jumpto(path, id, count) {
            this.$router.push({
                path,
                query: { id, count },
            });
        }
    }
}
</script>

<style lang="scss" scoped>
@import 'style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
</style>
