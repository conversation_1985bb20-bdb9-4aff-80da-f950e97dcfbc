<template>
    <div class="cus_list">
        <div class="head">
            <p class="return" @click="goBack">返回</p>
        </div>
        <div class="msg">
            <div class="flex-column-center">
                <img src="https://cdn01.rrsjk.com/images/acf6bc8c-4c53-4fcc-86b6-dceb72f6b05e.png" width="200" height="200">
                <span class="msg_title">中止运营</span>
                <p v-if="this.renewalData.businessType !== 2" style="padding: 50px 0; font-size: 16px;">您的运维合同已于{{
                    renewalData.contractEndTime ?
                    renewalData.contractEndTime.substr(0, 10) : '' }}到期，终止运维服务，若有疑问请联系平台运营工作人员</p>
                <p v-else style="padding: 50px 0; font-size: 16px;">当前服务商没有已续约的工商业运维项目,终止运维服务，若有疑问请联系平台运营工作人员</p>
                <Button v-if="this.renewalData.businessType !== 2" type="info" @click="view">查看合同</Button>
            </div>
        </div>
    </div>
</template>
<script>
import API, { contractStatus, getLastSingedUrl } from '@/api/osp';
import _data from "@/views/osp/_edata";
export default {
    data() {
        return {
            contractData: {},
            renewalData: {},
        };
    },
    mounted() {
        this.renewal()
    },
    methods: {
        renewal() {
            contractStatus().then(res => {
                if (res.data.result) {
                    this.renewalData = res.data.result
                } else {
                    this.$Message.error(res.error);
                }
            })
        },
        view() {
            getLastSingedUrl().then(res => {
                if (res.data.result) {
                    window.open(res.data.result);
                } else {
                    this.$Message.error(res.data.error);
                }
            })
        },
        goBack(){
            window.history.back();
        }
    }
};
</script>
<style lang="scss" scoped>
@import '@/style/_cus_list.scss';
.head{
    width: 100%;
    height: 70px;
    background: #3b6c99;
    .return{
        width: 100px;
        font-size: 16px;
        color: #fff;
        line-height: 70px;
        padding-left: 30px;
        cursor: pointer;
    }
}
.msg {
    width: 100%;
    min-height: $min-height;
    margin-top: 150px;

    .msg_title {
        font-size: 20px;
        font-weight: bolder;
    }
}

.res_btn {
    padding: 20px 0;
    text-align: center;
}

.delTitle {
    font-size: 16px;
    font-weight: bolder;
    width: 100%;
    text-align: center;
}

.prompt {
    color: red;
    font-weight: bolder;
}
</style>
