<template>
    <div class="cus_list">
        <!-- v-if="this.discontinue" -->
        <div>
            <Breadcrumb class="bread">
                <Breadcrumb-item to="/index/">光伏运维管理 </Breadcrumb-item>
                <Breadcrumb-item to="/">基本信息</Breadcrumb-item>
                <Breadcrumb-item>公司合同管理</Breadcrumb-item>
            </Breadcrumb>
            <div class="list_son">
                <div class="list_but">
                    <div class="condition">
                        <Button @click="debounce(queryList)" type="primary">查询</Button>
                    </div>
                </div>
                <div class="commListDiv">
                    <table class="cus_table" border="0" cellspacing="0" cellpadding="0" width="100%">
                        <tr>
                            <th align="center">合同名称</th>
                            <th align="center">合同编号</th>
                            <th align="center">合同类型</th>
                            <th align="center">签署时间</th>
                            <th align="center">合同起止日期</th>
                            <th align="center">状态</th>
                            <th class="fixedRight" align="center">操作</th>
                        </tr>
                        <tbody v-for="(item, index) in commList" :key="index">
                            <tr>
                                <td align="left" width="150" style="min-width: 150px;">{{ item.contractName || '-' }}
                                </td>
                                <td align="center" width="100">{{ item.contractNo || '-' }}</td>
                                <td align="center" width="100">{{ typeVal("typeList", item.type) || '-' }}</td>
                                <td align="center" width="100" style="min-width: 120px;">
                                    {{ item.signedAt?.replace('T', ' ') || '-' }}
                                </td>
                                <td align="center" width="150" style="min-width: 120px;">
                                    {{
                                        ((item.startAt && item.startAt.substr(0, 10)) || '') +
                                        '-' +
                                        ((item.endAt && item.endAt.substr(0, 10)) || '')
                                    }}
                                </td>
                                <td align="center" width="100" style="min-width: 100px;">
                                    {{ item.status === 'WAIT_SIGN' ? '待签约' : '已签约' || '-' }}
                                </td>
                                <td class="fixedRight" align="center" width="100" style="min-width: 100px;">
                                    <Button v-if="item.status === 'WAIT_SIGN'" type="success" ghost size="small"
                                        @click="toSign(item)">签署合同
                                    </Button>
                                    <Button v-if="item.status === 'SIGNED'" type="info" ghost size="small"
                                        @click="toContract(item)">查看合同
                                    </Button>
                                </td>
                            </tr>
                        </tbody>
                        <tbody v-if="!commList.length">
                            <tr>
                                <td colspan="99" align="center">暂无数据</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <Page style="margin:20px;" @on-change="changeCurrent" :page-size="searchObj.pageSize"
                    :current="searchObj.pageNum" :total="totalElements" show-total show-elevator />
            </div>
        </div>
        <!-- <div class="msg" v-else>
            <div class="flex-column-center">
                <img src="https://cdn01.rrsjk.com/images/acf6bc8c-4c53-4fcc-86b6-dceb72f6b05e.png" width="200" height="200">
                <span class="msg_title">中止运营</span>
                <p v-if="this.renewalData.businessType !== 2" style="padding: 50px 0; font-size: 16px;">您的运维合同已于{{
                    renewalData.contractEndTime ?
                    renewalData.contractEndTime.substr(0, 10) : '' }}到期，终止运维服务，若有疑问请联系平台运营工作人员</p>
                <p v-else style="padding: 50px 0; font-size: 16px;">当前服务商没有已续约的工商业运维项目,终止运维服务，若有疑问请联系平台运营工作人员</p>
                <Button v-if="this.renewalData.businessType !== 2" type="info" @click="view">查看合同</Button>
            </div>
        </div> -->
        <Modal v-model="modalView" :draggable="false" title="签署合同" width="1100" footer-hide :mask-closable="false"
            class-name="vertical-center-modal">
            <div v-if="pdfUrl">
                <iframe id="iframe" class="pdf" :src="pdfUrl" width="100%" height="600" frameborder="0"></iframe>
                <Checkbox v-model="pdfConfirm" style="margin: 20px 0;">已确认合同</Checkbox>
                <div class="res_btn"><Button type="success" size="large" @click="confirmContract"
                        style="width: 200px;">签署合同</Button></div>
            </div>
            <p v-else>该项目未查询到合同信息</p>
        </Modal>
        <Modal width="350" v-model="promptModal" draggable sticky scrollable title="提示" :footer-hide="true"
            :mask-closable="false">
            <p class="delTitle">主合同未签署请先签署主合同</p>
            <div style="text-align: center; margin: 30px 0 10px 0">
                <Button type="default" size="large" style="width: 100px; margin-right: 30px"
                    @click="promptModal = false">取消</Button>
                <Button type="success" size="large" style="width: 100px" @click="debounce(marginContract)">确认</Button>
            </div>
        </Modal>
        <Modal width="350" v-model="registerModel" draggable sticky scrollable title="提示" :footer-hide="true"
            :mask-closable="false">
            <p class="">{{ registerErrorInfo }}</p>
            <div style="text-align: center; margin: 30px 0 10px 0">
                <Button type="default" size="large" style="width: 100px; margin-right: 30px"
                    @click="registerModel = false">取消</Button>
                <Button type="success" size="large" style="width: 100px" @click="debounce(register)">确认</Button>
            </div>
        </Modal>
        <Modal width="600" v-model="renewalModel" draggable sticky scrollable title="续签合同提示" :footer-hide="true"
            :mask-closable="false">
            <Form class="form" ref="renewalData" :model="renewalData">
                <FormItem style="padding: 0; margin: 0;" type="text" label="公司名称:" prop="name" :label-width="120">
                    {{ renewalData.name }}
                </FormItem>
                <FormItem style="padding: 0; margin: 0;" label="运维业务类型:" prop="businessType" :label-width="120">
                    {{ identityType[renewalData.businessType] || '-' }}
                </FormItem>
                <FormItem style="padding: 0; margin: 0;" label="原合同起止日期:" prop="startAt" :label-width="120">
                    {{ renewalData.startAt ? renewalData.startAt.substr(0, 10) : '' }} -
                    {{ renewalData.endAt ? renewalData.endAt.substr(0, 10) : '' }}
                </FormItem>
                <FormItem style="padding: 0; margin: 0;" label="新合同起止日期:" prop="newStartAt" :label-width="120">
                    {{ renewalData.newStartAt ? renewalData.newStartAt.substr(0, 10) : '' }} -
                    {{ renewalData.renewalEndTime ? renewalData.renewalEndTime.substr(0, 10) : '' }}
                </FormItem>
            </Form>
            <p class="prompt">特别提示：需在 {{ renewalData.contractEndTime ? renewalData.contractEndTime.substr(0, 10) : '' }}
                前完成续签，请尽早完成</p>
            <div style="text-align: center; margin: 30px 0 10px 0">
                <Button type="default" size="large" style="width: 100px; margin-right: 30px"
                    @click="renewalModel = false">取消</Button>
                <Button type="success" size="large" style="width: 100px" @click="debounce(renewalSigning)">立即签约</Button>
            </div>
        </Modal>
        <Modal width="600" v-model="qualsModel" draggable sticky scrollable title="续签合同提示" :footer-hide="true"
            :mask-closable="false">
            <Form class="form" ref="qualsData" :model="qualsData">
                <FormItem style="padding: 0; margin: 0;" type="text" label="公司名称:" prop="name" :label-width="120">
                    {{ qualsData.name || '-' }}
                </FormItem>
                <FormItem style="padding: 0; margin: 0;" label="运维业务类型:" prop="businessType" :label-width="120">
                    {{ qualsData.businessType || '-' }}
                </FormItem>
                <FormItem style="padding: 0; margin: 0;" label="原合同起止日期:" prop="startAt" :label-width="120">
                    {{ qualsData.startAt ? qualsData.startAt.substr(0, 10) : '' }} -
                    {{ qualsData.signedAt ? qualsData.signedAt.substr(0, 10) : '' }}
                </FormItem>
                <FormItem style="padding: 0; margin: 0;" label="新合同起止日期:" prop="newStartAt" :label-width="120">
                    {{ qualsData.newStartAt ? qualsData.newStartAt.substr(0, 10) : '' }} -
                    {{ qualsData.newEndAt ? qualsData.newEndAt.substr(0, 10) : '' }}
                </FormItem>
            </Form>
            <p class="prompt">特别提示：请尽快前完成续签新合同，未签订新合同期间，不结算（不跑账单）原合同执行到2024年12月31日，结算费用2025年1月起全部执行新结算标准</p>
            <div style="text-align: center; margin: 30px 0 10px 0">
                <Button type="default" size="large" style="width: 100px; margin-right: 30px"
                    @click="qualsModel = false">取消</Button>
                <Button type="success" size="large" style="width: 100px" @click="debounce(qualsSigning)">立即签约</Button>
            </div>
        </Modal>
    </div>
</template>
<script>
import API, { contractStatus, getLastSingedUrl, qualsNewContractSign, qualsSignDetail } from '@/api/osp';
import APV from '@/api/apv';
import _data from "@/views/osp/_edata";
export default {
    data() {
        return {
            registerModel: false,
            promptModal: false,
            modalView: false,
            searchObj: {
                pageNum: 1,
                pageSize: 10
            },
            totalElements: 0,
            commList: [],
            pdfUrl: '',
            signYear: '',
            type: '',
            pdfConfirm: false,
            typeList: _data.typeList,
            identityType: _data.identityType,
            registerErrorInfo: '',
            authStatus: '',
            registStatus: '',
            contractData: {},
            renewalData: {},
            qualsData: {},
            qualsModel: false,
            renewalModel: false,
            discontinue: true,
        };
    },

    created() {
        this.queryList();
    },

    mounted() {
        document.addEventListener('visibilitychange', this.handleVisiable)
        this.renewal()
        this.qualsPrompt()
    },

    destroyed() {
        document.removeEventListener('visibilitychange', this.handleVisiable)
    },

    methods: {
        handleVisiable(e) {
            let that = this
            if (e.target.visibilityState === 'visible') {
                that.queryList();
                // 2s 后再查一遍
                setTimeout(() => { that.queryList() }, 2000)
            }
        },

        jumpto(path) {
            this.$router.push({
                path
            });
        },

        // 切换分页
        changeCurrent(curr) {
            this.searchObj.pageNum = curr;
            this.getCommList();
        },

        // 查询按钮
        queryList() {
            this.searchObj.pageNum = 1;
            this.getCommList();
        },
        //续约提示
        qualsPrompt() {
            qualsNewContractSign().then(res => {
                if (res.data.success) {
                    this.qualsModel = res.data.result
                    qualsSignDetail().then(res => {
                        if (res.data.success) {
                            this.qualsData = res.data.result
                        } else {
                            this.$Message.error(res.data.error);
                        }
                    })
                } else {
                    // this.$Message.error(res.data.error);
                }
            })
        },
        qualsSigning() {
            this.qualsModel = false
        },
        //续约
        renewal() {
            contractStatus().then(res => {
                if (res.data.result) {
                    this.renewalData = res.data.result
                    if (this.renewalData.businessType === 1 || this.renewalData.businessType === 3) {
                        if (this.renewalData.renewalStatus === 3) {
                            this.renewalModel = true
                        } else if (this.renewalData.status === 8) {
                            this.discontinue = false
                            this.renewalModel = false
                        } else {
                            this.discontinue = true
                            this.renewalModel = false
                        }
                    } else {
                        this.renewalModel = false
                    }
                } else {
                    this.$Message.error(res.error);
                }
            })
        },
        renewalSigning() {
            this.renewalModel = false
        },
        view() {
            getLastSingedUrl().then(res => {
                if (res.data.result) {
                    window.open(res.data.result);
                } else {
                    this.$Message.error(res.data.error);
                }
            })
        },
        // 列表
        getCommList() {
            let datas = this.searchObj;
            API.ospList(datas).then(res => {
                let request = res.data.result;
                if (res.data.success) {
                    this.totalElements = request.totalElements;
                    this.commList = request.content;
                } else {
                    this.totalElements = 0;
                    this.commList = [];
                }
            });
        },

        toContract(item) {
            let params = {
                contractNo: item.contractNo
            }
            API.viewDetail(params).then(res => {
                if (res.data.success) {
                    if (!res.data.result) {
                        this.$Message.warning("该项目未查询到合同信息");
                        return
                    }
                    res.data.result && window.open(res.data.result)
                } else {
                    this.$Message.error(res.data.error)
                }
            })
        },

        registerInfo() {
            const type = {
                companyType: 'OPERATION_PROVIDER',
            }
            APV.detail(type).then(res => {
                if (res.data.success && res.data.result) {
                    this.authStatus = res.data.result.authStatus
                    this.registStatus = res.data.result.registStatus
                }
            });
        },
        toSign(item) {
            console.log(item, 'item');

            this.contractData = item
            // this.registerInfo()
            // this.commList.forEach((items,i) => {})
            if (item.isRenewal === 0 || item.type === "OP_NEW_MASTER") {
                let datas = {
                    id: item.id
                }
                API.opAuthority(datas).then(res => {
                    this.modalView = false
                    this.$Message.destroy()
                    if (res.data.success) {
                        this.$Modal.confirm({
                            title: '提示',
                            content: '即将跳转法大大签署',
                            onOk: () => {
                                window.open(res.data.result)
                            },
                            onCancel: () => { }
                        })
                    } else {
                        this.$Message.error(res.data.error);
                        // this.registerErrorInfo = res.data.error
                        // if (this.authStatus === 'NO' && this.registStatus === 'NO') {
                        //     this.registerModel = true
                        // }
                    }
                }).catch(() => {
                    this.$Message.destroy()
                    this.$Message.error("获取服务失败，请重试");
                })
            } else {
                if (item.status === "WAIT_SIGN" && item.type === "OP_MASTER") {
                    this.promptModal = true
                } else if (item.type === "OP_AUTHORITY") {
                    this.registerInfo()
                    let par = {
                        id: item.id
                    }
                    API.checkContractStatus(par).then(res => {
                        if (res.data.success) {
                            let datas = {
                                id: item.id
                            }
                            API.opAuthority(datas).then(res => {
                                this.modalView = false
                                this.$Message.destroy()
                                if (res.data.success) {
                                    this.$Modal.confirm({
                                        title: '提示',
                                        content: '即将跳转法大大签署',
                                        onOk: () => {
                                            window.open(res.data.result)
                                        },
                                        onCancel: () => { }
                                    })
                                } else {
                                    this.$Message.error(res.data.error);
                                    // this.registerErrorInfo = res.data.error
                                    // if (this.authStatus === 'NO' && this.registStatus === 'NO') {
                                    //     this.registerModel = true
                                    // }
                                }
                            }).catch(() => {
                                this.$Message.destroy()
                                this.$Message.error("获取服务失败，请重试");
                            })
                        } else {
                            this.$Message.error(res.data.error);
                        }
                    })
                }
            }
        },
        register() {
            this.$router.push({
                name: "ospRegister",
            });
        },
        marginContract() {
            let item = this.contractData
            this.$router.push({
                name: "ospReg",
                params: item
            });
        },

        typeVal(list, value) {
            const obj = {};
            this[list].map((e) => {
                obj[e.value] = e.label || e.value;
            });
            return obj[value];
        },
    }
};
</script>
<style lang="scss" scoped>
@import '@/style/_cus_list.scss';

.msg {
    width: 100%;
    min-height: $min-height;
    margin-top: 150px;

    .msg_title {
        font-size: 20px;
        font-weight: bolder;
    }
}

.res_btn {
    padding: 20px 0;
    text-align: center;
}

.delTitle {
    font-size: 16px;
    font-weight: bolder;
    width: 100%;
    text-align: center;
}

.prompt {
    color: red;
    font-weight: bolder;
    font-size: 15px;
    text-align: center;
}
</style>
