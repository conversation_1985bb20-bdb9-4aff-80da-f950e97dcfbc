<template>
  <div style="height: calc(100vh - 100px)">
    <Breadcrumb class="bread">
      <Breadcrumb-item to="/index/">光伏运维管理</Breadcrumb-item>
      <Breadcrumb-item to="/osp/">消息管理</Breadcrumb-item>
      <Breadcrumb-item>消息管理</Breadcrumb-item>
    </Breadcrumb>
    <div class="wrap">
      <div class="cus-header" ref="headerRef">
        <Form :model="formSearch" :label-width="100">
          <!-- 基础查询条件 -->
          <div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
            <FormItem label="标题">
              <Input v-model="formSearch.subject" placeholder="输入标题" clearable />
            </FormItem>
            <FormItem label="状态">
              <Select v-model="formSearch.status" placeholder="请选择状态" clearable>
                <Option v-for="item in statusOptions" :key="item.value" :value="item.value">{{ item.label }}</Option>
              </Select>
            </FormItem>

            <FormItem label="创建时间">
              <DatePicker v-model="dateRange" type="datetimerange" placeholder="选择时间范围" style="width: 100%" @on-change="onDateChange" />
            </FormItem>
            <FormItem label="创建人">
              <Input v-model="formSearch.createdBy" placeholder="输入创建人" clearable />
            </FormItem>

            <!-- 查询按钮组 -->
            <div class="search-buttons">
              <Button type="default" @click="onReset">重置</Button>
              <Button type="primary" @click="queryList" style="margin-left: 8px">查询</Button>
              <Button type="text" @click="toggleExpand" style="margin-left: 10px">
                {{ isExpanded ? "收起" : "展开" }}
                <Icon :type="isExpanded ? 'ios-arrow-up' : 'ios-arrow-down'" />
              </Button>
            </div>
          </div>
        </Form>
      </div>
      <div class="message-container">
        <div class="message-left">
          <div class="message-tabs">
            <Tabs v-model="activeTab" @on-click="handleTabChange">
              <TabPane label="系统消息" name="SYSTEM"></TabPane>
              <TabPane label="业务消息" name="BUSINESS"></TabPane>
            </Tabs>
          </div>
          <div class="message-list">
            <Card v-for="(item, index) in listArr" :key="index" class="message-card" :class="{ 'active-message': selectedMessage && selectedMessage.id === item.id }" @click.native="selectMessage(item)">
              <div class="message-title">
                <span class="subject-text">{{ item.subject }}</span>
                <Tag v-if="item.readStatus === 'READ' || item.readStatus === 'UNREAD'" :color="item.readStatus === 'READ' ? 'success' : 'error'" class="status-tag">
                  {{ item.readStatus === "READ" ? "已读" : "未读" }}
                </Tag>
              </div>
              <div class="message-info">
                <span>{{ item.createdByName }}</span>
                <span>{{ item.createdAt }}</span>
              </div>
            </Card>
            <div v-if="listArr.length === 0" class="empty-message-list">
              暂无消息
            </div>
            <div class="cus-pages">
              <Page :total="total" :current="pagination.pageNum" :page-size="pagination.pageSize" @on-change="changeCurrent" @on-page-size-change="changeSize" simple></Page>
            </div>
          </div>
        </div>
        <div class="message-detail" v-if="selectedMessage">
          <div class="detail-title">{{ selectedMessage.subject }}</div>
          <div class="detail-info">
            <div>创建人: {{ selectedMessage.createdByName }}</div>
            <div>创建时间: {{ selectedMessage.createdAt }}</div>
          </div>
          <div class="detail-content" v-html="selectedMessage.content"></div>
        </div>
        <div class="message-detail empty-detail" v-else>
          <div class="empty-text">请选择一条消息查看详情</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import API from "@/api/maintainance";
import _ from "lodash";
import _D from "@/views/osp/_edata";
import { mapActions, mapGetters } from "vuex";

export default {
  name: "MessageManagement",
  data() {
    return {
      isExpanded: false,
      activeTab: "SYSTEM",
      selectedMessage: null,
      dateRange: [],
      formSearch: {
        subject: "",
        status: null,
        createdBy: "",
        messageType: "SYSTEM",
        startTime: "",
        endTime: "",
      },
      loading: false,
      listArr: [],
      total: 0,
      statusOptions: [
        { value: "READ", label: "已读" },
        { value: "UNREAD", label: "未读" },
      ],
      pagination: {
        pageSize: 10,
        pageNum: 1,
      },
    };
  },
  computed: {
    ...mapGetters("dict/maintainance", ["getDictByType", "getDictMapByType"]),
  },
  methods: {
    ...mapActions("dict/maintainance", {
      fetchMaintainanceDict: "fetchDict",
    }),
    onDateChange(dates) {
      this.formSearch.startTime = dates[0];
      this.formSearch.endTime = dates[1];
    },
    // 切换标签页
    handleTabChange(name) {
      this.formSearch.messageType = name;
      this.selectedMessage = null;
      this.queryList();
    },

    // 选择消息
    async selectMessage(message) {
      this.selectedMessage = message;

      // 如果消息未读，标记为已读
      if (message.readStatus === "UNREAD") {
        try {
          await API.markMessageRead({ messageId: message.id });
          const index = this.listArr.findIndex((item) => item.id === message.id);
          if (index !== -1) {
            this.listArr[index].readStatus = "READ";
            this.$set(this.listArr, index, { ...this.listArr[index], readStatus: "READ" });
          }
        } catch (error) {
          console.error("标记消息已读失败:", error);
          this.$Message.error("标记消息已读失败");
        }
      }

      // 获取消息详情
      try {
        const response = await API.getMessageDetail({ id: message.id });
        if (response.data?.result) {
          this.selectedMessage = response.data.result;
        }
      } catch (error) {
        console.error("获取消息详情失败:", error);
        this.$Message.error("获取消息详情失败");
      }
    },

    async getList() {
      this.loading = true;
      try {
        const params = {
          ...this.formSearch,
          pageSize: this.pagination.pageSize,
          pageNum: this.pagination.pageNum,
        };
        const response = await API.getMessagePage(params);
        const result = response.data?.result;
        if (result) {
          this.listArr = result.content;
          this.total = result.totalElements || 0;

          // 默认选中第一条消息
          if (this.listArr.length > 0) {
            this.selectMessage(this.listArr[0]);
          } else {
            this.selectedMessage = null;
          }
        } else {
          this.listArr = [];
          this.total = 0;
          this.selectedMessage = null;
        }
      } catch (error) {
        console.error("获取消息列表失败:", error);
        this.listArr = [];
        this.total = 0;
        this.selectedMessage = null;
        this.$Message.error("获取消息列表失败，请稍后再试");
      } finally {
        this.loading = false;
      }
    },
    queryList() {
      this.pagination.pageNum = 1;
      this.getList();
    },
    changeSize(size) {
      this.pagination.pageSize = size;
      this.getList();
    },
    changeCurrent(current) {
      this.pagination.pageNum = current;
      this.getList();
    },
    onReset: _.throttle(
      function () {
        this.dateRange = [];
        Object.assign(this.formSearch, {
          subject: "",
          status: null,
          createdBy: "",
          messageType: this.activeTab, // 保持当前标签页类型
          startTime: "",
          endTime: "",
        });
        this.queryList();
      },
      1000,
      {
        trailing: false,
      }
    ),
    toggleExpand() {
      this.isExpanded = !this.isExpanded;
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
@import "@/style/_cus_list.scss";

:deep(.ivu-tabs-bar) {
  background-color: white;
  margin-bottom: 0px;
}

:deep(.ivu-tabs-ink-bar.ivu-tabs-ink-bar-animated) {
  width: 74px !important;
}

.wrap {
  height: calc(100% - 52.38px);
  display: flex;
  gap: 12px;
  padding: 0px 12px;
  flex-direction: column;
  overflow: hidden;
  background-color: white;
}

.cus-header {
  margin-bottom: 0px;

  .form-item-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    width: 100%;

    .ivu-form-item:nth-child(n + 3):not(:last-child) {
      display: none;
    }

    &.is-expanded {
      .ivu-form-item:nth-child(n + 3):not(:last-child) {
        display: flex;
      }
    }
  }

  .search-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    grid-column: -2 / -1;

    .ivu-btn-text {
      box-shadow: none !important;
      border: none !important;
    }
  }

  .ivu-form-item {
    display: flex;
    width: 100%;
    margin-bottom: 0px;

    .ivu-input-wrapper,
        .ivu-select,
        .ivu-date-picker {
      width: 100%;
    }

    :deep(.ivu-form-item-content) {
      width: calc(100% - 100px) !important;
      margin-left: 0px !important;
    }
  }
}

.message-container {
  display: flex;
  flex: 1;
  overflow: hidden;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #e8eaec;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.message-left {
  width: 30%;
  display: flex;
  flex-direction: column;
  border-right: 2px solid #e8eaec;
  overflow: hidden;
}

.message-tabs {
  border-bottom: 1px solid #e8eaec;
  background-color: #f8f8f9;
}

.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  display: flex;
  flex-direction: column;
}

.message-card {
  margin: 10px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
  }

  :deep(.ivu-card-body) {
    padding: 12px;
  }
}

.message-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  margin-bottom: 8px;
  font-size: 14px;
}

.subject-text {
  flex-grow: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 8px;
}

.status-tag {
  flex-shrink: 0;
}

.message-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}

.message-detail {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
}

.detail-title {
  font-size: 24px;
  font-weight: 500;
  text-align: center;
  margin-bottom: 30px;
}

.detail-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40px;
  color: #666;
  font-size: 14px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 20px;
}

.detail-content {
  line-height: 1.8;
  white-space: pre-wrap;
  font-size: 14px;

  :deep(p) {
    margin-bottom: 1em;
  }

  :deep(img) {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 10px auto;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  :deep(h1),
  :deep(h2),
  :deep(h3),
  :deep(h4),
  :deep(h5),
  :deep(h6) {
    margin-top: 1.5em;
    margin-bottom: 0.8em;
    font-weight: 600;
  }

  :deep(ul),
  :deep(ol) {
    list-style-type: decimal;
    padding-left: 20px;
    margin-bottom: 1em;
  }

  :deep(li) {
    margin-bottom: 0.5em;
  }

  :deep(blockquote) {
    margin: 1em 0;
    padding: 10px 15px;
    border-left: 4px solid #dfe2e5;
    background-color: #f8f9fa;
    color: #6a737d;
  }
}

.active-message {
  background-color: #f0f7ff !important;
  border-left: 3px solid #2d8cf0;
}

.empty-detail {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f9;
}

.empty-text {
  color: #999;
  font-size: 16px;
}

.empty-message-list {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
  font-size: 14px;
}

.cus-pages {
  margin-top: auto;
  padding: 15px;
  text-align: center;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
}
</style>
