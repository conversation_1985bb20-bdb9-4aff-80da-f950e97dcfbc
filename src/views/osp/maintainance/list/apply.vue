<template>
    <div style="height: calc(100vh - 100px)">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏运维管理</Breadcrumb-item>
            <Breadcrumb-item to="/osp/">工单大厅</Breadcrumb-item>
            <Breadcrumb-item>备件申请</Breadcrumb-item>
        </Breadcrumb>
        <div class="wrap">
            <div class="cus-header" ref="headerRef">
                <Form :model="formSearch" :label-width="100">
                    <!-- 基础查询条件 -->
                    <div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
                        <FormItem label="运维单号">
                            <Input v-model="formSearch.orderCode" placeholder="输入运维单号" clearable />
                        </FormItem>

                        <FormItem label="电站编码">
                            <Input v-model="formSearch.stationCode" placeholder="输入电站编码" clearable />
                        </FormItem>

                        <FormItem label="电站名称">
                            <Input v-model="formSearch.stationName" placeholder="输入电站名称" clearable />
                        </FormItem>

                        <FormItem label="逆变器SN码">
                            <Input v-model="formSearch.inverterSn" placeholder="输入逆变器SN码" clearable />
                        </FormItem>

                        <FormItem label="分中心">
                            <Select v-model="formSearch.subCenterCode" placeholder="选择分中心" clearable
                                style="width: 100%;">
                                <Option v-for="item in subCenterOptions" :key="item.value" :value="item.value">{{
                                    item.label }}</Option>
                            </Select>
                        </FormItem>

                        <FormItem label="工单来源">
                            <Select v-model="formSearch.orderSource" placeholder="选择工单来源" clearable
                                style="width: 100%;">
                                <Option v-for="item in sourceOptions" :key="item.value" :value="item.value">{{
                                    item.label }}</Option>
                            </Select>
                        </FormItem>

                        <FormItem label="工单类型">
                            <Select v-model="formSearch.orderType" placeholder="选择工单类型" clearable style="width: 100%;">
                                <Option v-for="item in workOrderTypeOptions" :key="item.value" :value="item.value">{{
                                    item.label }}</Option>
                            </Select>
                        </FormItem>

                        <FormItem label="工单状态">
                            <Select v-model="formSearch.orderStatus" placeholder="选择工单状态" clearable
                                style="width: 100%;">
                                <Option v-for="item in workOrderStatusOptions" :key="item.value" :value="item.value">{{
                                    item.label }}</Option>
                            </Select>
                        </FormItem>

                        <FormItem label="生成工单时间">
                            <DatePicker v-model="createTime" type="daterange" format="yyyy-MM-dd"
                                placeholder="开始时间 - 结束时间" @on-change="dateChange" clearable style="width: 100%;" />
                        </FormItem>

                        <!-- 查询按钮组 -->
                        <div class="search-buttons">
                            <Button type="default" @click="onReset">重置</Button>
                            <Button type="primary" @click="queryList" style="margin-left: 8px;">查询</Button>
                            <Button type="text" @click="toggleExpand" style="margin-left: 10px;">
                                {{ isExpanded ? '收起' : '展开' }}
                                <Icon :type="isExpanded ? 'ios-arrow-up' : 'ios-arrow-down'" />
                            </Button>
                        </div>
                    </div>
                </Form>
            </div>
            <div class="cus-main" ref="mainRef">
                <div class="cus-list" ref="cusListRef">
                    <Table :data="listArr" :columns="tableColumns" :loading="loading" class="cus-table" ref="tableRef">
                        <template slot="stationCode" slot-scope="{ row }">
                            <span>{{ row.stationCode || '-' }}</span>
                        </template>
                        <template slot="inverterSn" slot-scope="{ row }">
                            <span>{{ row.inverterSn || '-' }}</span>
                        </template>
                        <template slot="orderSource" slot-scope="{ row }">
                            <span>{{ getDictLabel('work_order_source', row.orderSource) }}</span>
                        </template>
                        <template slot="orderType" slot-scope="{ row }">
                            <span>{{ getDictLabel('work_order_type', row.orderType) }}</span>
                        </template>
                        <template slot="orderStatus" slot-scope="{ row }">
                            <span>{{ getDictLabel('work_order_status', row.orderStatus) }}</span>
                        </template>
                        <template slot="faultDescription" slot-scope="{ row }">
                            <span>{{ row.faultDescription || '-' }}</span>
                        </template>
                        <template slot="stationType" slot-scope="{ row }">
                            <span>{{ detStationType[row.stationType] || '-' }}</span>
                        </template>
                        <template slot="subCenterName" slot-scope="{ row }">
                            <span>{{ row.subCenterName || '-' }}</span>
                        </template>
                        <template slot="stationName" slot-scope="{ row }">
                            <span>{{ row.stationName || '-' }}</span>
                        </template>
                        <template slot="stationPhone" slot-scope="{ row }">
                            <span>{{ row.stationPhone || '-' }}</span>
                        </template>
                        <template slot="regionName" slot-scope="{ row }">
                            <span>{{ row.regionName || '-' }}</span>
                        </template>
                        <template slot="address" slot-scope="{ row }">
                            <span>{{ row.address || '-' }}</span>
                        </template>
                        <template slot="action" slot-scope="{ row }">
                            <div class="center">
                                <a type="text" size="small" @click="viewOrder(row)">查看</a>
                                <a type="text" size="small" @click="handleApply(row)">申请备件</a>
                            </div>
                        </template>
                    </Table>
                    <Page class="cus-pages" :page-size-opts="[10, 20, 30]" :page-size="pagination.pageSize"
                        :current="pagination.pageNum" :total="total" @on-page-size-change="changeSize"
                        @on-change="changeCurrent" show-total show-sizer show-elevator ref="pageRef" />
                </div>
            </div>
        </div>
        <SpareApplyDialog ref="spareApplyRef" @getList="getList" :row-data="rowData" />
    </div>
</template>

<script>
import API from '@/api/maintainance';
import _ from 'lodash';
import _D from '@/views/osp/_edata';
import { mapActions, mapGetters } from 'vuex';
import SpareApplyDialog from '../components/SpareApply.vue';

export default {
    name: 'MaintainanceListApply',
    components: { SpareApplyDialog },
    data() {
        return {
            operateDrawerVisible: false,
            currentOperateData: null,
            currentOrderData: {},
            closeOrderDialogVisible: false,
            currentCloseOrderCode: '',
            isExpanded: false,
            createTime: [],
            formSearch: {
                createTimeEnd: null,
                createTimeStart: null,
                orderCode: '',
                stationCode: '',
                opName: '',
                stationName: '',
                inverterSn: '',
                subCenterCode: '',
                opType: '',
                orderSource: '',
                orderType: '',
                orderStatus: '',
                specialFlag: '',
                orderStatusQuery: '',
            },
            loading: false,
            listArr: [],
            total: 0,
            pagination: {
                pageSize: 10,
                pageNum: 1,
            },
            subCenterOptions: _D.subCenterList,
            identityType: _D.identityType,
            capitalBelongOptions: _D.property,
            businessType: _D.businessType,
            isWarranty: _D.isWarranty,
            detStationType: _D.detStationType,
            tableColumns: [
                { type: 'index', title: '序号', width: 80, align: 'center', fixed: 'left' },
                { title: '运维单号', key: 'orderCode', width: 200, align: 'center', fixed: 'left' },
                { title: '电站编码', key: 'stationCode', width: 200, align: 'center', slot: 'stationCode' },
                { title: '逆变器SN码', key: 'inverterSn', width: 150, align: 'center', slot: 'inverterSn' },
                { title: '工单来源', key: 'orderSource', width: 120, align: 'center', slot: 'orderSource' },
                { title: '工单类型', key: 'orderType', width: 120, align: 'center', slot: 'orderType' },
                { title: '工单状态', key: 'orderStatus', width: 120, align: 'center', slot: 'orderStatus' },
                { title: '故障现象', key: 'faultDescription', width: 150, align: 'center', slot: 'faultDescription' },
                { title: '电站类型', key: 'stationType', width: 120, align: 'center', slot: 'stationType' },
                { title: '所属分中心', key: 'subCenterName', width: 120, align: 'center', slot: 'subCenterName' },
                { title: '电站业主', key: 'stationName', width: 120, align: 'center', slot: 'stationName' },
                { title: '业主联系方式', key: 'stationPhone', width: 150, align: 'center', slot: 'stationPhone' },
                { title: '区域', key: 'regionName', width: 120, align: 'center', slot: 'regionName' },
                { title: '详细地址', key: 'address', width: 200, align: 'center', slot: 'address' },
                { title: '操作', key: 'action', width: 160, align: 'center', fixed: 'right', slot: 'action' }
            ],
            rowData: null
        };
    },
    computed: {
        ...mapGetters('dict/maintainance', [
            'getDictByType',
            'getDictMapByType'
        ]),
        sourceOptions() {
            return this.getDictByType ? this.getDictByType('work_order_source') : [];
        },
        workOrderTypeOptions() {
            return this.getDictByType ? this.getDictByType('work_order_type') : [];
        },
        workOrderStatusOptions() {
            return this.getDictByType ? this.getDictByType('work_order_status') : [];
        },
    },
    methods: {
        ...mapActions('dict/maintainance', {
            fetchMaintainanceDict: 'fetchDict'
        }),
        getDictLabel(dictType, value) {
            const dictMap = this.getDictMapByType(dictType);
            return dictMap ? (dictMap[value] || '-') : '-';
        },
        async getList() {
            this.loading = true;
            try {
                const params = {
                    ...this.formSearch,
                    pageSize: this.pagination.pageSize,
                    pageNum: this.pagination.pageNum,
                };
                const response = await API.getMySubmittedWorkOrderPage(params);
                const result = response.data?.result;
                if (result) {
                    this.listArr = result.content || [];
                    this.total = result.totalElements || 0;
                } else {
                    this.listArr = [];
                    this.total = 0;
                }
            } catch (error) {
                console.error("Error fetching list:", error);
                this.listArr = [];
                this.total = 0;
                this.$Message.error('获取列表失败，请稍后再试'); // iView Message
            } finally {
                this.loading = false;
            }
        },
        queryList() {
            this.pagination.pageNum = 1;
            this.getList();
        },
        changeSize(size) {
            this.pagination.pageSize = size;
            this.getList();
        },
        changeCurrent(current) {
            this.pagination.pageNum = current;
            this.getList();
        },
        dateChange(formattedDate, dateType) {
            if (formattedDate && formattedDate.length === 2) {
                this.formSearch.createTimeStart = formattedDate[0];
                this.formSearch.createTimeEnd = formattedDate[1];
            } else {
                this.formSearch.createTimeStart = null;
                this.formSearch.createTimeEnd = null;
            }
        },
        onReset: _.throttle(
            function () {
                this.createTime = [];
                Object.assign(this.formSearch, {
                    createTimeEnd: null,
                    createTimeStart: null,
                    orderCode: '',
                    stationCode: '',
                    opName: '',
                    stationName: '',
                    inverterSn: '',
                    subCenterCode: '',
                    opType: '',
                    orderSource: '',
                    orderType: '',
                    orderStatus: '',
                    specialFlag: '',
                    orderStatusQuery: ''
                });
                this.queryList();
            },
            3000,
            {
                trailing: false
            }
        ),
        exportList: _.throttle(
            function () {
                this.$Modal.confirm({
                    title: '提示',
                    content: '请确认是否导出此筛选条件下的列表?',
                    onOk: () => {
                        const params = _.cloneDeep(this.formSearch);
                        let exportParams = { ...params };
                        API.exportWorkOrderList(exportParams)
                            .then(res => {
                                let binaryData = [];
                                let link = document.createElement('a');
                                binaryData.push(res);
                                link.style.display = 'none';
                                link.href = window.URL.createObjectURL(new Blob(binaryData, { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }));
                                link.setAttribute('download', '运维工单列表.xlsx');
                                document.body.appendChild(link);
                                link.click();
                                document.body.removeChild(link);
                                window.URL.revokeObjectURL(link.href);
                            })
                            .catch(err => {
                                console.error("Export error:", err);
                                this.$Message.error('导出失败，请稍后再试');
                            });
                    }
                });
            },
            3000,
            {
                trailing: false
            }
        ),
        toggleExpand() {
            this.isExpanded = !this.isExpanded;
        },
        dealOrder(row) {
            this.$router.push({
                path: '/maintainance/detail',
                query: {
                    orderCode: row.orderCode
                }
            });
        },
        viewOrder(row) {
            this.$router.push({
                path: '/maintainance/detail',
                query: {
                    orderCode: row.orderCode,
                    action: 'view'
                }
            });
        },
        handleApply(row) {
            this.rowData = row;
            this.$refs.spareApplyRef.modelEdit = true
            this.$nextTick(() => {
                this.$refs.spareApplyRef.getList()
            })
        },
    },
    mounted() {
        this.getList();
        this.fetchMaintainanceDict([
            'work_order_type',
            'work_order_status',
            'work_order_source',
            'close_order_reason',
            'audit_reject_reason'
        ]);
    },
};
</script>

<style lang="scss" scoped>
@import "@/style/_cus_list.scss";

:deep(.ivu-tabs-bar) {
    background-color: white;
    margin-bottom: 12px;
}

:deep(.ivu-tabs-ink-bar.ivu-tabs-ink-bar-animated) {
    width: 74px !important;
}

.wrap {
    height: calc(100% - 52.38px);
    display: flex;
    padding: 0px 12px;
    flex-direction: column;
    overflow: hidden;
    background-color: white;
}

.cus-header {
    margin-bottom: 0px;

    .form-item-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
        width: 100%;

        // Adjust for iView FormItem if necessary
        .ivu-form-item:nth-child(n+3):not(:last-child) {
            display: none;
        }

        &.is-expanded {
            .ivu-form-item:nth-child(n+3):not(:last-child) {
                display: flex; // or block, depending on iView FormItem display
            }
        }
    }

    .search-buttons {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        grid-column: -2 / -1;

        .ivu-btn-text {
            box-shadow: none !important;
            border: none !important;
        }
    }

    // Adjust for iView FormItem if necessary
    .ivu-form-item {
        display: flex;
        width: 100%;
        margin-bottom: 0px; // iView FormItem might have default margin

        .ivu-input-wrapper,
        // For Input
        .ivu-select,
        // For Select
        .ivu-date-picker {
            // For DatePicker
            width: 100%;
        }

        :deep(.ivu-form-item-content) {
            width: calc(100% - 100px) !important;
            margin-left: 0px !important;
        }
    }
}

.cus-main {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;

    // >.ivu-btn {
    //     margin-bottom: 10px;
    // }

    .cus-list {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: hidden;
        position: relative;

        .cus-table {
            flex: 1; // Table itself should take up the height calculated for it
            height: 0;
            overflow: auto;
            width: 100%;

            :deep(.ivu-table-fixed-body) {
                background-color: white;
                height: calc(100% - 56px);
            }

            :deep(.ivu-table-body) {
                height: calc(100% - 40px);
            }

            :deep(.ivu-table-tip) {
                height: calc(100% - 40px);
            }

            .center {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 12px;
            }
        }

        .cus-pages {
            margin-top: 10px;
            text-align: right;
        }
    }
}
</style>
