<template>
    <div style="height: calc(100vh - 100px)">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏运维管理</Breadcrumb-item>
            <Breadcrumb-item to="/osp/">培训考试</Breadcrumb-item>
            <Breadcrumb-item>考试答题</Breadcrumb-item>
        </Breadcrumb>
        <div class="wrap">
            <div class="exam-container">
                <div v-if="examData" class="exam-paper">
                    <div class="exam-header">
                        <h1>{{ examData.name || '测试试卷' }}</h1>
                        <div class="header-info">
                            <div class="info-item">
                                <Icon type="ios-time" />
                                <span>剩余时间: {{ formattedTime }}</span>
                            </div>
                            <div class="info-item">
                                <Icon type="ios-document" />
                                <span>总分: {{ examData.totalScore || 100 }}分</span>
                            </div>
                            <div class="info-item">
                                <Icon type="ios-list" />
                                <span>总题数: {{ questions.length }}题</span>
                            </div>
                        </div>
                    </div>

                    <div class="progress-section">
                        <div class="progress-info">
                            <div>
                                <span>当前进度: {{ currentQuestionIndex + 1 }}/{{ questions.length }}</span>
                                <span style="margin-left: 16px;">已答: {{ answeredCount }}题</span>
                            </div>
                            <Select v-model="currentQuestionIndex" placeholder="跳转题目" size="small" style="width: 120px">
                                <Option
                                    v-for="(item, index) in questions"
                                    :key="item.id"
                                    :label="`第 ${index + 1} 题`"
                                    :value="index"
                                />
                            </Select>
                        </div>
                        <Progress :percent="progressPercentage" :stroke-width="10" :show-text="false" />
                    </div>

                    <div class="question-area" v-if="currentQuestion">
                        <div class="question-title">
                            <span class="question-number">{{ currentQuestionIndex + 1 }}.</span>
                            <div class="question-main">
                                <p>{{ currentQuestion.title }}</p>
                                <Tag type="border">{{ questionTypeLabel }}</Tag>
                            </div>
                        </div>

                        <div class="question-options">
                            <RadioGroup v-if="currentQuestion.type === 'SINGLE'" v-model="userAnswers[currentQuestion.id]" class="option-list">
                                <Radio
                                    v-for="option in currentQuestion.options"
                                    :key="option.optionKey"
                                    :label="option.optionKey"
                                    class="option-item"
                                >
                                    <span class="option-label">{{ option.optionKey }}</span>
                                    <span class="option-content">{{ option.content }}</span>
                                </Radio>
                            </RadioGroup>
                            <CheckboxGroup v-if="currentQuestion.type === 'MULTIPLE'" v-model="userAnswers[currentQuestion.id]" class="option-list">
                                <Checkbox
                                    v-for="option in currentQuestion.options"
                                    :key="option.optionKey"
                                    :label="option.optionKey"
                                    class="option-item"
                                >
                                    <span class="option-label">{{ option.optionKey }}</span>
                                    <span class="option-content">{{ option.content }}</span>
                                </Checkbox>
                            </CheckboxGroup>
                        </div>
                    </div>

                    <div class="exam-footer">
                        <Button @click="goToPrev" :disabled="currentQuestionIndex === 0">
                            <Icon type="ios-arrow-back" />
                            上一题
                        </Button>
                        <Button type="primary" @click="goToNext" v-if="currentQuestionIndex < questions.length - 1">
                            下一题
                            <Icon type="ios-arrow-forward" />
                        </Button>
                        <Button type="success" @click="submitExam" v-else>
                            <Icon type="ios-checkmark" />
                            交卷
                        </Button>
                        <Button @click="handleClose">
                            关闭
                        </Button>
                    </div>
                </div>
                <div v-else-if="!loading" class="empty-state">
                    <Icon type="ios-document" size="64" color="#c5c8ce" />
                    <p>无法加载试卷</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import API from '@/api/maintainance';

export default {
    name: 'MaintainanceExam',
    data() {
        return {
            loading: true,
            examData: null,
            questions: [],
            currentQuestionIndex: 0,
            userAnswers: {},
            examDuration: 3600, // 默认60分钟
            remainingTime: 3600,
            timerInterval: null,
        };
    },
    computed: {
        formattedTime() {
            const minutes = Math.floor(this.remainingTime / 60);
            const seconds = this.remainingTime % 60;
            return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
        },
        answeredCount() {
            return Object.values(this.userAnswers).filter(answer => {
                if (Array.isArray(answer)) {
                    return answer.length > 0;
                }
                return answer !== null;
            }).length;
        },
        progressPercentage() {
            if (this.questions.length === 0) return 0;
            return ((this.currentQuestionIndex + 1) / this.questions.length) * 100;
        },
        currentQuestion() {
            return this.questions[this.currentQuestionIndex] || null;
        },
        questionTypeLabel() {
            if (!this.currentQuestion) return '';
            const typeMap = {
                'SINGLE': '单选题',
                'MULTIPLE': '多选题',
            };
            return typeMap[this.currentQuestion.type] || '未知题型';
        }
    },
    async mounted() {
        const answerId = this.$route.query.answerId;

        if (!answerId) {
            this.$Message.error('未找到答题ID');
            this.loading = false;
            setTimeout(() => this.$router.back(), 2000);
            return;
        }
        await this.fetchExamAnswer(answerId);
    },
    beforeDestroy() {
        clearInterval(this.timerInterval);
    },

    methods: {

        async fetchExamAnswer(answerId) {
            this.loading = true;
            try {
                const res = await API.getExamAnswerDetail({ answerId });
                if (res.data.success && res.data.result) {
                    this.examData = {
                        name: res.data.result.examName,
                        duration: res.data.result.duration,
                        ...res.data.result
                    };

                    if (res.data.result.duration && res.data.result.startTime) {
                        this.examDuration = res.data.result.duration * 60;
                        const startTime = new Date(res.data.result.startTime).getTime();
                        const now = Date.now();
                        const elapsedSeconds = Math.floor((now - startTime) / 1000);
                        this.remainingTime = Math.max(0, this.examDuration - elapsedSeconds);
                    }

                    this.questions = res.data.result.questions;

                    this.initializeAnswers(res.data.result.answers || {});

                    if (this.remainingTime > 0) {
                        this.startTimer();
                    }
                } else {
                    this.$Message.error(res.error || '获取考试详情失败');
                }
            } catch (error) {
                console.log(error);
                this.$Message.error('请求考试详情时出错');
            } finally {
                this.loading = false;
            }
        },

        initializeAnswers(existingAnswers = {}) {
            const answers = {};
            this.questions.forEach(q => {
                const existing = existingAnswers[q.id];
                if (q.type === 'MULTIPLE') {
                    answers[q.id] = existing ? existing.split(',').filter(Boolean) : [];
                } else {
                    answers[q.id] = existing || null;
                }
            });
            this.userAnswers = answers;
        },
        startTimer() {
            this.timerInterval = setInterval(() => {
                if (this.remainingTime > 0) {
                    this.remainingTime--;
                } else {
                    clearInterval(this.timerInterval);
                    this.$Message.warning('考试时间到，已自动交卷');
                    this.submitExam();
                }
            }, 1000);
        },
        goToPrev() {
            if (this.currentQuestionIndex > 0) {
                this.currentQuestionIndex--;
            }
        },
        goToNext() {
            console.log
            if (this.currentQuestionIndex < this.questions.length - 1) {
                this.currentQuestionIndex++;
            }
        },

        submitExam() {
            this.$Modal.confirm({
                title: '提示',
                content: '确认要交卷吗？',
                onOk: async () => {
                    clearInterval(this.timerInterval);
                    this.loading = true;
                    const answerData = Object.keys(this.userAnswers).reduce((acc, key) => {
                        const answer = this.userAnswers[key];
                        if(Array.isArray(answer)) {
                            acc[key] = answer.join(',');
                        } else {
                            acc[key] = answer;
                        }
                        return acc;
                    }, {});
                    const submission = {
                        id: this.$route.query.answerId,
                        answerData,
                    };
                    try {
                        const res = await API.submitExamAnswer(submission);
                        if (res.data.success) {
                            this.$Message.success('交卷成功！');
                            this.$router.push('/maintainance/training/exam/my');
                        } else {
                            this.$Message.error(res.data.error || '提交试卷失败');
                        }
                    } catch (error) {
                        this.$Message.error('提交试卷时出错');
                    } finally {
                        this.loading = false;
                    }
                }
            });
        },
        handleClose() {
            this.$Modal.confirm({
                title: '提示',
                content: '确定要退出考试吗？',
                onOk: async () => {
                    const answerData = Object.keys(this.userAnswers).reduce((acc, key) => {
                        const answer = this.userAnswers[key];
                        if(Array.isArray(answer)) {
                            acc[key] = answer.join(',');
                        } else {
                            acc[key] = answer;
                        }
                        return acc;
                    }, {});
                    const submission = {
                        id: this.$route.query.answerId,
                        answerData,
                    };
                    API.updateExamAnswer(submission);
                    this.$router.back();
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
@import "@/style/_cus_list.scss";

.wrap {
    height: calc(100% - 52.38px);
    display: flex;
    padding: 0px 12px;
    flex-direction: column;
    overflow: hidden;
    background-color: white;
}

.exam-container {
    background-color: #f4f4f5;
    height: 100%;
    box-sizing: border-box;
}

.exam-paper {
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
    padding: 24px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.exam-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 24px;

    h1 {
        font-size: 24px;
        font-weight: 500;
        color: #000000;
        margin: 0;
    }

    .header-info {
        display: flex;
        gap: 24px;
        color: #4b5563;
        font-size: 16px;

        .info-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
    }
}

.progress-section {
    padding: 16px 0;

    .progress-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #4b5563;
        font-size: 14px;
        margin-bottom: 10px;
    }
}

.question-area {
    padding: 24px 0;
    min-height: 400px;
    flex: 1;
    overflow-y: auto;
}

.question-title {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    margin-bottom: 24px;

    .question-number {
        font-size: 18px;
        font-weight: 500;
        color: #000000;
    }

    .question-main {
        p {
            font-size: 18px;
            font-weight: 500;
            color: #000000;
            margin: 0 0 8px 0;
        }
    }
}

.question-options {
    .option-list {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    .option-item {
        border: 1px solid #d1d5db;
        border-radius: 8px;
        padding: 16px;
        width: 100%;
        height: auto;
        display: flex;
        align-items: center;
        margin-right: 0;

        &.ivu-radio-checked,
        &.ivu-checkbox-checked {
            border-color: #2d8cf0;
            background-color: #eff6ff;
        }
    }

    :deep(.ivu-radio-inner),
    :deep(.ivu-checkbox-inner) {
        display: none;
    }

    :deep(.ivu-radio),
    :deep(.ivu-checkbox) {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 16px;
        color: #374151;
        font-weight: normal;
    }

    .option-label {
        width: 32px;
        height: 32px;
        border: 2px solid #d1d5db;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
    }

    .option-item.ivu-radio-checked .option-label,
    .option-item.ivu-checkbox-checked .option-label {
        border-color: #2d8cf0;
        color: #2d8cf0;
    }
}

.exam-footer {
    display: flex;
    justify-content: center;
    gap: 20px;
    padding-top: 24px;
    border-top: 1px solid #e5e7eb;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #c5c8ce;

    p {
        margin-top: 16px;
        font-size: 16px;
    }
}
</style>
