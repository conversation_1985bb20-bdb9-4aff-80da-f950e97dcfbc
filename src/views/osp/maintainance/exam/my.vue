<template>
    <div style="height: calc(100vh - 100px)">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏运维管理</Breadcrumb-item>
            <Breadcrumb-item to="/osp/">培训考试</Breadcrumb-item>
            <Breadcrumb-item>我的考试</Breadcrumb-item>
        </Breadcrumb>
        <div class="wrap">
            <div class="cus-header" ref="headerRef">
                <Form :model="formSearch" :label-width="100">
                    <!-- 基础查询条件 -->
                    <div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
                        <FormItem label="考试名称">
                            <Input v-model="formSearch.name" placeholder="输入考试名称" clearable />
                        </FormItem>
                        <FormItem label="考试状态">
                            <Select v-model="formSearch.userExamStatus" placeholder="选择状态" clearable>
                                <Option v-for="item in userExamStatusOptions" :key="item.value" :value="item.value">{{
                                    item.label }}</Option>
                            </Select>
                        </FormItem>

                        <!-- 查询按钮组 -->
                        <div class="search-buttons">
                            <Button type="default" @click="onReset">重置</Button>
                            <Button type="primary" @click="queryList" style="margin-left: 8px;">查询</Button>
                            <Button type="text" @click="toggleExpand" style="margin-left: 10px;">
                                {{ isExpanded ? '收起' : '展开' }}
                                <Icon :type="isExpanded ? 'ios-arrow-up' : 'ios-arrow-down'" />
                            </Button>
                        </div>
                    </div>
                </Form>
            </div>
            <div class="cus-main" ref="mainRef">
                <div class="cus-list" ref="cusListRef">
                    <div style="height: 12px;"></div>
                    <Table :data="listArr" :columns="tableColumns" :loading="loading" class="cus-table" ref="tableRef">
                        <template slot="name" slot-scope="{ row }">
                            <span>{{ row.name || '-' }}</span>
                        </template>
                        <template slot="totalScore" slot-scope="{ row }">
                            <span>{{ row.totalScore || '-' }}</span>
                        </template>
                        <template slot="duration" slot-scope="{ row }">
                            <span>{{ row.duration || '-' }}</span>
                        </template>
                        <template slot="startTime" slot-scope="{ row }">
                            <span>{{ row.startTime && row.startTime.replace('T', ' ') || '-' }}</span>
                        </template>
                        <template slot="endTime" slot-scope="{ row }">
                            <span>{{ row.endTime && row.endTime.replace('T', ' ') || '-' }}</span>
                        </template>
                        <template slot="userExamStatus" slot-scope="{ row }">
                            <Tag :color="userExamStatusTagMap[row.userExamStatus] || 'default'">
                                {{ userExamStatusMap[row.userExamStatus] || '-' }}
                            </Tag>
                        </template>
                        <template slot="userScore" slot-scope="{ row }">
                            <span>{{ row.userScore || '-' }}</span>
                        </template>
                        <template slot="action" slot-scope="{ row }">
                            <div class="center">
                                <a v-if="getActionText(row)" type="text" size="small" @click="handleExamAction(row)">{{ getActionText(row) }}</a>
                            </div>
                        </template>
                    </Table>
                    <Page class="cus-pages" :page-size-opts="[10, 20, 30]" :page-size="pagination.pageSize"
                        :current="pagination.pageNum" :total="total" @on-page-size-change="changeSize"
                        @on-change="changeCurrent" show-total show-sizer show-elevator ref="pageRef" />
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import API from '@/api/maintainance';
import _ from 'lodash';

export default {
    name: 'MaintainanceExamMy',
    data() {
        return {
            isExpanded: false,
            formSearch: {
                name: '',
                userExamStatus: null,
            },
            loading: false,
            listArr: [],
            total: 0,
            pagination: {
                pageSize: 10,
                pageNum: 1,
            },
            tableColumns: [
                { type: 'index', title: '序号', width: 80, align: 'center', fixed: 'left' },
                { title: '考试名称', key: 'name', width: 200, align: 'center', slot: 'name', fixed: 'left' },
                { title: '总分', key: 'totalScore', width: 100, align: 'center', slot: 'totalScore' },
                { title: '考试时长(分钟)', key: 'duration', width: 150, align: 'center', slot: 'duration' },
                { title: '开始时间', key: 'startTime', width: 180, align: 'center', slot: 'startTime' },
                { title: '结束时间', key: 'endTime', width: 180, align: 'center', slot: 'endTime' },
                { title: '考试状态', key: 'userExamStatus', width: 120, align: 'center', slot: 'userExamStatus' },
                { title: '我的得分', key: 'userScore', width: 100, align: 'center', slot: 'userScore' },
                { title: '操作', key: 'action', width: 120, align: 'center', fixed: 'right', slot: 'action' }
            ],
            userExamStatusOptions: [
                { label: '未开始', value: 'NOT_STARTED' },
                { label: '进行中', value: 'IN_PROGRESS' },
                { label: '已完成', value: 'COMPLETED' },
            ],
            userExamStatusMap: {
                NOT_STARTED: '未开始',
                IN_PROGRESS: '进行中',
                COMPLETED: '已完成',
            },
            userExamStatusTagMap: {
                NOT_STARTED: 'default',
                IN_PROGRESS: 'blue',
                COMPLETED: 'green',
            }
        };
    },
    methods: {
        async getList() {
            this.loading = true;
            try {
                const params = {
                    ...this.formSearch,
                    pageSize: this.pagination.pageSize,
                    pageNum: this.pagination.pageNum,
                };
                const response = await API.getCurrentUserExamPage(params);
                const result = response.data?.result;
                if (result) {
                    this.listArr = result.content || [];
                    this.total = result.totalElements || 0;
                } else {
                    this.listArr = [];
                    this.total = 0;
                }
            } catch (error) {
                console.error("Error fetching list:", error);
                this.listArr = [];
                this.total = 0;
                this.$Message.error('获取列表失败，请稍后再试');
            } finally {
                this.loading = false;
            }
        },
        queryList() {
            this.pagination.pageNum = 1;
            this.getList();
        },
        changeSize(size) {
            this.pagination.pageSize = size;
            this.getList();
        },
        changeCurrent(current) {
            this.pagination.pageNum = current;
            this.getList();
        },
        onReset: _.throttle(
            function () {
                Object.assign(this.formSearch, {
                    name: '',
                    userExamStatus: null,
                });
                this.queryList();
            },
            3000,
            {
                trailing: false
            }
        ),
        toggleExpand() {
            this.isExpanded = !this.isExpanded;
        },
        getActionText(row) {
            if (row.userExamStatus === 'COMPLETED') {
                return '查看试卷';
            }
            if (row.userExamStatus === 'NOT_STARTED') {
                return '开始考试';
            }
            if (row.userExamStatus === 'IN_PROGRESS') {
                return '继续考试';
            }
            return '查看试卷';
        },
        async handleExamAction(row) {
            if (row.userExamStatus === 'NOT_STARTED' || row.userExamStatus === 'IN_PROGRESS') {
                let result;
                if(row.userExamStatus === 'NOT_STARTED') {
                    const res = await API.getOrCreateExamAnswer({ examId: row.id });
                    result = res.result;
                }
                this.$router.push({
                    path: '/maintainance/training/exam/do',
                    query: { examId: row.id, answerId: row.answerId || result.id },
                });
            } else if (row.userExamStatus === 'COMPLETED') {
                this.$router.push({
                    path: '/maintainance/training/exam/score',
                    query: { answerId: row.answerId },
                });
            }
        }
    },
    activated() {
        this.getList();
    },
};
</script>

<style lang="scss" scoped>
@import "@/style/_cus_list.scss";

:deep(.ivu-tabs-bar) {
    background-color: white;
    margin-bottom: 12px;
}

:deep(.ivu-tabs-ink-bar.ivu-tabs-ink-bar-animated) {
    width: 74px !important;
}

.wrap {
    height: calc(100% - 52.38px);
    display: flex;
    padding: 0px 12px;
    flex-direction: column;
    overflow: hidden;
    background-color: white;
}

.cus-header {
    margin-bottom: 0px;

    .form-item-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
        width: 100%;

        // Adjust for iView FormItem if necessary
        .ivu-form-item:nth-child(n+3):not(:last-child) {
            display: none;
        }

        &.is-expanded {
            .ivu-form-item:nth-child(n+3):not(:last-child) {
                display: flex; // or block, depending on iView FormItem display
            }
        }
    }

    .search-buttons {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        grid-column: -2 / -1;

        .ivu-btn-text {
            box-shadow: none !important;
            border: none !important;
        }
    }

    // Adjust for iView FormItem if necessary
    .ivu-form-item {
        display: flex;
        width: 100%;
        margin-bottom: 0px; // iView FormItem might have default margin

        .ivu-input-wrapper,
        // For Input
        .ivu-select,
        // For Select
        .ivu-date-picker {
            // For DatePicker
            width: 100%;
        }

        :deep(.ivu-form-item-content) {
            width: calc(100% - 100px) !important;
            margin-left: 0px !important;
        }
    }
}

.cus-main {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;

    .cus-list {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: hidden;
        position: relative;

        .cus-table {
            flex: 1; // Table itself should take up the height calculated for it
            height: 0;
            overflow: auto;
            width: 100%;

            :deep(.ivu-table-fixed-body) {
                background-color: white;
                height: calc(100% - 56px);
            }

            :deep(.ivu-table-body) {
                height: calc(100% - 40px);
                overflow-x: auto;
            }

            :deep(.ivu-table-tip) {
                height: calc(100% - 40px);
            }

            .center {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 12px;
            }
        }

        .cus-pages {
            margin-top: 10px;
            text-align: right;
        }
    }
}
</style>
