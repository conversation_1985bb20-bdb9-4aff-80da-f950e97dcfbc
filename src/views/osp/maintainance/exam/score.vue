<template>
    <div style="height: calc(100vh - 100px)">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏运维管理</Breadcrumb-item>
            <Breadcrumb-item to="/osp/">培训考试</Breadcrumb-item>
            <Breadcrumb-item>考试结果</Breadcrumb-item>
        </Breadcrumb>
        <div class="wrap">
            <div class="exam-score-container" v-if="examData">
                <div class="page-header">
                    <h1>{{ examData.examName }} - 考试结果</h1>
                </div>

                <div class="result-summary">
                    <div class="score-circle" :class="passStatus.class">
                        <div class="score">{{ examData.score }}</div>
                        <div class="total-score">满分{{ examData.totalScore }}</div>
                    </div>
                    <div class="result-text">{{ passStatus.text }}</div>
                    <div class="congrats-message">恭喜您完成本次考试，{{ passStatus.text }}！</div>
                </div>

                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value correct">{{ correctCount }}</div>
                        <div class="stat-label">答对题数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value incorrect">{{ incorrectCount }}</div>
                        <div class="stat-label">答错题数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value duration">{{ examDuration }}</div>
                        <div class="stat-label">答题用时</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value accuracy">{{ accuracy }}%</div>
                        <div class="stat-label">正确率</div>
                    </div>
                </div>

                <div class="section">
                    <h2 class="section-title">答题情况</h2>
                    <div class="answer-sheet">
                        <div
                            v-for="(question, index) in examData.questions"
                            :key="question.id"
                            class="answer-item"
                            :class="{ 'correct': question.correct, 'incorrect': !question.correct }"
                        >
                            {{ index + 1 }}
                        </div>
                    </div>
                </div>

                <div class="section" v-if="incorrectQuestions.length > 0">
                    <h2 class="section-title">错题分析</h2>
                    <div class="analysis-list">
                        <div v-for="(question) in incorrectQuestions" :key="question.id" class="analysis-item">
                            <div class="question-title">
                                <span class="question-index">{{ getQuestionIndex(question.id) }}.</span>
                                {{ question.title }} ({{ question.score }}分)
                            </div>
                            <div class="options-list">
                                <RadioGroup :value="question.userAnswer" disabled>
                                    <Radio
                                        v-for="option in question.optionList"
                                        :key="option.id"
                                        :label="option.optionKey"
                                        class="option-item"
                                        :class="getOptionClass(question, option.optionKey)"
                                    >
                                        {{ option.optionKey }}. {{ option.content }}
                                    </Radio>
                                </RadioGroup>
                            </div>
                            <div class="answer-analysis">
                                <div class="answer-row">
                                    <span class="user-answer">您的答案：{{ question.userAnswer || '未作答' }}</span>
                                    <span class="correct-answer">正确答案：{{ question.answer }}</span>
                                </div>
                                <div class="analysis-text">解析：{{ question.analysis }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="action-buttons">
                    <Button type="primary" @click="goHome">返回考试首页</Button>
                </div>
            </div>
            <div v-else class="loading-container">
                <Spin size="large" />
                <p>加载中...</p>
            </div>
        </div>
    </div>
</template>

<script>
import API from '@/api/maintainance';

export default {
    name: 'MaintainanceExamScore',
    data() {
        return {
            examData: null,
        };
    },
    computed: {
        answerId() {
            return this.$route.query.answerId || null;
        },
        passStatus() {
            if (!this.examData) return { text: '', class: '' };
            if (this.examData.result === 1) {
                return { text: '考试通过', class: 'pass' };
            }
            const score = this.examData.score;
            const totalScore = this.examData.totalScore;
            if (totalScore === 0) return { text: '考试完成', class: 'fail' };

            const rate = score / totalScore;
            if (rate >= 0.9) return { text: '优秀', class: 'pass' };
            if (rate >= 0.8) return { text: '良好', class: 'pass' };
            if (rate >= 0.6) return { text: '及格', class: 'pass' };

            return { text: '考试未通过', class: 'fail' };
        },
        correctCount() {
            if (!this.examData || !this.examData.questions) return 0;
            return this.examData.questions.filter(q => q.correct).length;
        },
        incorrectCount() {
            if (!this.examData || !this.examData.questions) return 0;
            return this.examData.questions.filter(q => !q.correct).length;
        },
        accuracy() {
            if (!this.examData || !this.examData.questions || this.examData.questions.length === 0) return 0;
            return ((this.correctCount / this.examData.questions.length) * 100).toFixed(0);
        },
        incorrectQuestions() {
            if (!this.examData || !this.examData.questions) return [];
            return this.examData.questions.filter(q => !q.correct);
        },
        examDuration() {
            if (!this.examData || !this.examData.startTime || !this.examData.endTime) {
                return '--';
            }
            const startTime = new Date(this.examData.startTime).getTime();
            const endTime = new Date(this.examData.endTime).getTime();
            if (isNaN(startTime) || isNaN(endTime) || endTime < startTime) {
                return '--';
            }
            const durationMinutes = Math.round((endTime - startTime) / (1000 * 60));
            return `${durationMinutes}分钟`;
        }
    },
    methods: {
        async fetchExamResult() {
            if (!this.answerId) {
                this.$Message.error('缺少考试记录ID');
                return;
            }
            try {
                const res = await API.getExamAnswerDetail({ answerId: this.answerId });
                if (res.success && res.result) {
                    this.examData = res.result;
                } else {
                    this.$Message.error(res.error || '获取考试结果失败');
                }
            } catch (error) {
                this.$Message.error('请求异常，请稍后再试');
            }
        },
        getQuestionIndex(questionId) {
            if (!this.examData || !this.examData.questions) return -1;
            return this.examData.questions.findIndex(q => q.id === questionId) + 1;
        },
        getOptionClass(question, optionKey) {
            const correctAnswer = question.answer;
            const userAnswer = question.userAnswer;
            if (correctAnswer && correctAnswer.includes(optionKey)) {
                return 'correct-option';
            }
            if (userAnswer && userAnswer.includes(optionKey) && (!correctAnswer || !correctAnswer.includes(optionKey))) {
                return 'wrong-option';
            }
            return '';
        },
        goHome() {
            this.$router.push('/maintainance/training/exam/my');
        }
    },
    mounted() {
        this.fetchExamResult();
    }
};
</script>

<style lang="scss" scoped>
@import "@/style/_cus_list.scss";

.wrap {
    height: calc(100% - 52.38px);
    display: flex;
    padding: 0px 12px;
    flex-direction: column;
    overflow: hidden;
    background-color: white;
}

.exam-score-container {
    padding: 24px;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
    overflow-y: auto;
    flex: 1;

    .page-header {
        border-bottom: 1px solid #e5e7eb;
        padding-bottom: 24px;
        margin-bottom: 32px;
        h1 {
            font-size: 24px;
            font-weight: 500;
            color: #303133;
        }
    }

    .result-summary {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 40px;

        .score-circle {
            width: 160px;
            height: 160px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            border-width: 12px;
            border-style: solid;

            &.pass {
                border-color: #19be6b;
            }
            &.fail {
                border-color: #ed4014;
            }

            .score {
                font-size: 48px;
                color: #303133;
                font-weight: bold;
                line-height: 1;
            }
            .total-score {
                font-size: 16px;
                color: #606266;
                margin-top: 8px;
            }
        }

        .result-text {
            font-size: 20px;
            color: #303133;
            font-weight: bold;
            margin-top: 24px;
        }
        .congrats-message {
            font-size: 14px;
            color: #606266;
            margin-top: 8px;
        }
    }

    .stats-grid {
        display: flex;
        justify-content: space-between;
        gap: 20px;
        margin-bottom: 40px;

        .stat-item {
            flex: 1;
            background-color: #f5f7fa;
            border-radius: 4px;
            padding: 16px;
            text-align: center;
            .stat-value {
                font-size: 32px;
                font-weight: bold;
                line-height: 1.5;
                &.correct {
                    color: #19be6b;
                }
                &.incorrect {
                    color: #ed4014;
                }
                &.duration {
                    color: #2d8cf0;
                }
                &.accuracy {
                    color: #303133;
                }
            }
            .stat-label {
                font-size: 16px;
                color: #606266;
                margin-top: 8px;
            }
        }
    }

    .section {
        margin-bottom: 24px;
        .section-title {
            font-size: 20px;
            color: #303133;
            padding-bottom: 12px;
            border-bottom: 1px solid #dcdfe6;
            margin-bottom: 20px;
        }
    }

    .answer-sheet {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        .answer-item {
            width: 40px;
            height: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
            border: 1px solid;
            border-radius: 4px;
            font-size: 16px;

            &.correct {
                background-color: rgba(25, 190, 107, 0.1);
                border-color: #19be6b;
                color: #303133;
            }
            &.incorrect {
                background-color: rgba(237, 64, 20, 0.1);
                border-color: #ed4014;
                color: #303133;
            }
        }
    }

    .analysis-list {
        .analysis-item {
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            padding: 24px;
            margin-bottom: 20px;

            .question-title {
                font-size: 16px;
                color: #303133;
                margin-bottom: 16px;
                .question-index {
                    color: #2d8cf0;
                    margin-right: 4px;
                }
            }

            .options-list {
                margin-bottom: 16px;
                padding-left: 20px;

                :deep(.ivu-radio-group) {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 10px;

                    .ivu-radio {
                        color: #303133;
                    }
                    .correct-option {
                        color: #19be6b !important;
                    }
                    .wrong-option {
                        color: #ed4014 !important;
                    }
                }
            }

            .answer-analysis {
                background-color: #ecf5ff;
                padding: 15px;
                border-radius: 4px;
                font-size: 14px;
                .answer-row {
                    margin-bottom: 8px;
                    display: flex;
                    gap: 24px;
                    .user-answer {
                        color: #303133;
                    }
                    .correct-answer {
                        color: #19be6b;
                    }
                }
                .analysis-text {
                    color: #606266;
                }
            }
        }
    }

    .action-buttons {
        display: flex;
        justify-content: center;
        gap: 24px;
        margin-top: 40px;
    }
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #c5c8ce;

    p {
        margin-top: 16px;
        font-size: 16px;
    }
}
</style>
