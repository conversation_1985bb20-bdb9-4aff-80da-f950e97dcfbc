<template>
  <Tabs value="workorder" type="card" class="section-card">
    <TabPane label="工单列表" name="workorder">
      <div class="wrap">
        <div class="cus-header">
          <Form :model="formSearch" :label-width="100">
            <div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
              <FormItem label="运维单号">
                <Input v-model="formSearch.orderCode" placeholder="输入运维单号" clearable />
              </FormItem>
              <FormItem label="工单类型">
                <Select v-model="formSearch.orderType" placeholder="选择工单类型" clearable style="width: 100%;">
                  <Option v-for="item in workOrderTypeOptions" :key="item.value" :label="item.label"
                    :value="item.value" />
                </Select>
              </FormItem>
              <FormItem label="工单来源">
                <Select v-model="formSearch.orderSource" placeholder="选择工单来源" clearable style="width: 100%;">
                  <Option v-for="item in sourceOptions" :key="item.value" :label="item.label"
                    :value="item.value" />
                </Select>
              </FormItem>
              <FormItem label="生成工单时间">
                <DatePicker v-model="createTime" type="daterange" format="yyyy-MM-dd"
                  placeholder="开始时间 - 结束时间" @on-change="dateChange" clearable style="width: 100%;" />
              </FormItem>
              <div class="search-buttons">
                <Button type="default" @click="onReset">重置</Button>
                <Button type="primary" @click="queryList" style="margin-left: 8px;">查询</Button>
                <Button type="text" @click="toggleExpand" style="margin-left: 10px;">
                  {{ isExpanded ? '收起' : '展开' }}
                  <Icon :type="isExpanded ? 'ios-arrow-up' : 'ios-arrow-down'" />
                </Button>
              </div>
            </div>
          </Form>
        </div>
        <div class="cus-main">
          <div class="cus-list">
            <Table :data="listArr" :columns="tableColumns" :loading="loading" class="cus-table">
              <template slot="stationCode" slot-scope="{ row }">
                <span>{{ row.stationCode || '-' }}</span>
              </template>
              <template slot="inverterSn" slot-scope="{ row }">
                <span>{{ row.inverterSn || '-' }}</span>
              </template>
              <template slot="orderSource" slot-scope="{ row }">
                <span>{{ getDictLabel('work_order_source', row.orderSource) }}</span>
              </template>
              <template slot="orderType" slot-scope="{ row }">
                <span>{{ getDictLabel('work_order_type', row.orderType) }}</span>
              </template>
              <template slot="orderStatus" slot-scope="{ row }">
                <span>{{ getDictLabel('work_order_status', row.orderStatus) }}</span>
              </template>
              <template slot="faultDescription" slot-scope="{ row }">
                <span>{{ row.faultDescription || '-' }}</span>
              </template>
              <template slot="specialFlag" slot-scope="{ row }">
                <span>{{ pmSpecialFlag[row.specialFlag] || '-' }}</span>
              </template>
              <template slot="stationType" slot-scope="{ row }">
                <span>{{ detStationType[row.stationType] || '-' }}</span>
              </template>
              <template slot="isWarranty" slot-scope="{ row }">
                <span>{{ isWarranty[row.isWarranty] || '-' }}</span>
              </template>
              <template slot="opBusinessType" slot-scope="{ row }">
                <span>{{ businessType[row.opBusinessType] || '-' }}</span>
              </template>
              <template slot="action" slot-scope="{ row }">
                <div class="center">
                  <a type="text" size="small" @click="viewOrder(row)">查看</a>
                </div>
              </template>
            </Table>
            <Page class="cus-pages" v-if="total"
              :page-size-opts="[10, 20, 30]" :page-size="pagination.pageSize" :current="pagination.pageNum"
              :total="total" @on-page-size-change="changeSize" @on-change="changeCurrent"
              show-sizer show-total show-elevator />
          </div>
        </div>
      </div>
    </TabPane>
  </Tabs>
</template>

<script>
import API from '@/api/maintainance';
import _D from '@/views/osp/_edata';
import _ from 'lodash';


export default {
  name: 'WorkOrderList',
  props: {
    stationCode: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      pmSpecialFlag: _D.pmSpecialFlag,
      businessType: _D.businessType,
      isWarranty: _D.isWarranty,
      detStationType: _D.detStationType,
      isExpanded: false,
      createTime: '',
      formSearch: {
        createTimeEnd: null,
        createTimeStart: null,
        orderCode: '',
        stationCode: this.stationCode,
        orderSource: '',
        orderType: '',
      },
      loading: false,
      listArr: [],
      total: 0,
      pagination: {
        pageNum: 1,
        pageSize: 10
      },
      tableColumns: [
        { type: 'index', title: '序号', width: 80, align: 'center', fixed: 'left' },
        { title: '运维单号', key: 'orderCode', width: 200, align: 'center', fixed: 'left' },
        { title: '电站编码', key: 'stationCode', width: 200, align: 'center', slot: 'stationCode' },
        { title: '逆变器SN码', key: 'inverterSn', width: 150, align: 'center', slot: 'inverterSn' },
        { title: '工单来源', key: 'orderSource', width: 120, align: 'center', slot: 'orderSource' },
        { title: '工单类型', key: 'orderType', width: 120, align: 'center', slot: 'orderType' },
        { title: '工单状态', key: 'orderStatus', width: 120, align: 'center', slot: 'orderStatus' },
        { title: '故障现象', key: 'faultDescription', width: 150, align: 'center', slot: 'faultDescription' },
        // { title: '模式', key: 'mode', width: 120, align: 'center' },
        // { title: '资产所属', key: 'specialFlag', width: 120, align: 'center', slot: 'specialFlag' },
        // { title: '电站类型', key: 'stationType', width: 120, align: 'center', slot: 'stationType' },
        { title: '所属项目公司', key: 'projectCompanyName', width: 150, align: 'center' },
        { title: '运维商', key: 'opName', width: 150, align: 'center' },
        { title: '所属分中心', key: 'subCenterName', width: 120, align: 'center' },
        { title: '电站业主', key: 'stationName', width: 120, align: 'center' },
        { title: '业主联系方式', key: 'stationPhone', width: 150, align: 'center' },
        { title: '区域', key: 'regionName', width: 120, align: 'center' },
        { title: '详细地址', key: 'address', width: 200, align: 'center' },
        // { title: '是否在质保期', key: 'isWarranty', width: 120, align: 'center', slot: 'isWarranty' },
        // { title: '运维业务类型', key: 'opBusinessType', width: 150, align: 'center', slot: 'opBusinessType' },
        { title: '操作', key: 'action', width: 160, align: 'center', fixed: 'right', slot: 'action' }
      ]
    };
  },
  computed: {
    sourceOptions() {
      // 简化版本，返回空数组或者从本地数据获取
      return [];
    },
    workOrderTypeOptions() {
      // 简化版本，返回空数组或者从本地数据获取
      return [];
    }
  },
  methods: {
    getDictLabel(value) {
      // 简化版本，直接返回value或'-'
      return value || '-';
    },

    dateChange(e) {
      this.formSearch.createTimeStart = e ? e[0] : null;
      this.formSearch.createTimeEnd = e ? e[1] : null;
    },

    onReset: _.throttle(
      function() {
        this.createTime = '';
        const stationCode = this.formSearch.stationCode;
        Object.assign(this.formSearch, {
          createTimeEnd: null,
          createTimeStart: null,
          orderCode: '',
          orderSource: '',
          orderType: '',
        });
        this.formSearch.stationCode = stationCode;
        this.queryList();
      },
      3000,
      { trailing: false }
    ),

    viewOrder(row) {
      this.$router.push({
        path: '/maintainance/faultWorkOrder/detail',
        query: {
          orderCode: row.orderCode,
          action: 'view'
        }
      });
    },

    toggleExpand() {
      this.isExpanded = !this.isExpanded;
    },

    async getList() {
      this.loading = true;
      try {
        const res = await API.getWorkOrderPage({
          ...this.formSearch,
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSiz,
        });
        if (res.data.success) {
          this.listArr = res.data.result.content || [];
          this.total = res.data.result.totalElements || 0;
        }
      } catch (error) {
        console.error('获取工单列表失败:', error);
        this.$Message.error('获取工单列表失败');
      } finally {
        this.loading = false;
      }
    },

    queryList() {
      this.pagination.pageNum = 1;
      this.getList();
    },

    changeSize(pageSize) {
      this.pagination.pageSize = pageSize;
      this.pagination.pageNum = 1;
      this.getList();
    },

    changeCurrent(pageNum) {
      this.pagination.pageNum = pageNum;
      this.getList();
    }
  },

  mounted() {
    this.getList();
  }
};
</script>

<style lang="scss" scoped>
@import "@/style/_cus_list.scss";

.section-card {
  margin-bottom: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  background: transparent;

  ::v-deep .ivu-tabs-content {
    background: transparent;
  }

  ::v-deep .ivu-tabs-tabpane {
    background: transparent;
  }
}

.wrap {
  height: 600px;
  display: flex;
  padding: 0px 12px;
  flex-direction: column;
  overflow: hidden;
  background-color: #f8f9fa;
}

.cus-header {
  margin-bottom: 0px;
  padding: 20px;
  background: #fff;
  border-radius: 4px;
  margin-bottom: 12px;

  .form-item-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    width: 100%;

    // Adjust for iView FormItem if necessary
    .ivu-form-item:nth-child(n+3):not(:last-child) {
      display: none;
    }

    &.is-expanded {
      .ivu-form-item:nth-child(n+3):not(:last-child) {
        display: flex; // or block, depending on iView FormItem display
      }
    }
  }

  .search-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    grid-column: -2 / -1;

    .ivu-btn-text {
      box-shadow: none !important;
      border: none !important;
    }
  }

  .ivu-form-item {
    display: flex;
    width: 100%;
    margin-bottom: 0px;

    .ivu-input-wrapper,
    .ivu-select,
    .ivu-date-picker {
      width: 100%;
    }

    :deep(.ivu-form-item-content) {
      width: calc(100% - 100px) !important;
      margin-left: 0px !important;
    }
  }
}

.cus-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;

  .cus-list {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    position: relative;
    background: #fff;
    border-radius: 4px;
    padding: 20px;

    .cus-table {
      flex: 1;
      height: 400px;
      overflow: auto;
      width: 100%;

      :deep(.ivu-table-fixed-body) {
        background-color: white;
        height: calc(100% - 56px);
      }

      :deep(.ivu-table-body) {
        height: calc(100% - 40px);
      }

      :deep(.ivu-table-tip) {
        height: calc(100% - 40px);
      }

      .center {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
      }
    }

    .cus-pages {
      margin-top: 10px;
    }
  }
}
</style>
