<template>
  <div style="height: calc(100vh - 100px)">
    <Breadcrumb class="bread">
      <Breadcrumb-item to="/index/">光伏运维管理</Breadcrumb-item>
      <Breadcrumb-item to="/osp/">报表大厅</Breadcrumb-item>
      <Breadcrumb-item>报表管理</Breadcrumb-item>
    </Breadcrumb>
    <div class="wrap">
      <div class="cus-header" ref="headerRef">
        <Form :model="formSearch" :label-width="100">
          <!-- 基础查询条件 -->
          <div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
            <FormItem label="报表">
              <Select v-model="formSearch.reportConfigId" @on-change="handleReportConfigChange">
                <Option v-for="item in reportConfigOptions" :key="item.value" :value="item.value">{{ item.label }}</Option>
              </Select>
            </FormItem>

            <!-- 查询按钮组 -->
            <div class="search-buttons">
              <!-- <Button type="default" @click="onReset">重置</Button> -->
              <Button type="primary" @click="queryList" style="margin-left: 8px">查询</Button>
              <!-- <Button type="text" @click="toggleExpand" style="margin-left: 10px;">
                                {{ isExpanded ? '收起' : '展开' }}
                                <Icon :type="isExpanded ? 'ios-arrow-up' : 'ios-arrow-down'" />
                            </Button> -->
            </div>
          </div>
        </Form>
      </div>
      <div class="cus-main" ref="mainRef">
        <div class="cus-list" ref="cusListRef">
          <Table :data="listArr" :columns="tableColumns" :loading="loading" class="cus-table" ref="tableRef"> </Table>
          <Page class="cus-pages" :page-size-opts="[10, 20, 30]" :page-size="pagination.pageSize" :current="pagination.pageNum" :total="total" @on-page-size-change="changeSize" @on-change="changeCurrent" show-total show-sizer show-elevator ref="pageRef" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import API from "@/api/maintainance";
import _ from "lodash";
import _D from "@/views/osp/_edata";
import { mapActions, mapGetters } from "vuex";

export default {
  name: "MaintainanceReport",
  data() {
    return {
      isExpanded: false,
      createTime: [],
      formSearch: {
        reportConfigId: "",
      },
      loading: false,
      listArr: [],
      total: 0,
      reportConfigOptions: [],
      pagination: {
        pageSize: 10,
        pageNum: 1,
      },
      tableColumns: [],
    };
  },
  methods: {
    async fetchReportConfigList() {
      try {
        const response = await API.getReportCenterConfigList();
        if (response.data?.result) {
          this.reportConfigOptions = response.data.result.map((item) => {
            return {
              label: item.reportName,
              value: item.id,
            };
          });
        }
      } catch (error) {
        console.error("获取报表配置失败:", error);
        this.$Message.error("获取报表配置失败");
      }
    },
    async getList() {
      this.loading = true;
      try {
        const params = {
          ...this.formSearch,
          pageSize: this.pagination.pageSize,
          pageNum: this.pagination.pageNum,
        };
        const response = await API.getReportCenterConfigData(params);
        const result = response.data?.result;
        if (result) {
          this.listArr = result.content || [];
          this.total = result.totalElements || 0;
        } else {
          this.listArr = [];
          this.total = 0;
        }
      } catch (error) {
        console.error("Error fetching list:", error);
        this.listArr = [];
        this.total = 0;
        this.$Message.error("获取列表失败，请稍后再试"); // iView Message
      } finally {
        this.loading = false;
      }
    },
    queryList() {
      this.pagination.pageNum = 1;
      this.getList();
    },
    changeSize(size) {
      this.pagination.pageSize = size;
      this.getList();
    },
    changeCurrent(current) {
      this.pagination.pageNum = current;
      this.getList();
    },
    onReset: _.throttle(
      function () {
        this.createTime = [];
        Object.assign(this.formSearch, {
          name: "",
          createdBy: "",
          solutionType: null, // 确保初始状态包含所有可能的查询字段
          categoryId: null,
        });
        this.queryList();
      },
      3000,
      {
        trailing: false,
      }
    ),
    toggleExpand() {
      this.isExpanded = !this.isExpanded;
    },
    fetchReportConfigDetail() {
      API.getReportConfig({ configId: this.formSearch.reportConfigId }).then((res) => {
        this.tableColumns = res.data.result.fields.map((item) => {
          return {
            key: item.fieldCode,
            title: item.fieldName,
            minWidth: Math.max(120, item.fieldName.length * 18 + 30),
          };
        });
        this.tableColumns.unshift({ type: "index", title: "序号", width: 80, align: "center", fixed: "left" });
      });
    },
    handleReportConfigChange(val) {
      console.log(val);
      this.formSearch.reportConfigId = val;
      this.fetchReportConfigDetail();
      this.getList();
    },
  },
  async mounted() {
    await this.fetchReportConfigList();
    this.formSearch.reportConfigId = this.reportConfigOptions[0].value;
    this.fetchReportConfigDetail();
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
@import "@/style/_cus_list.scss";

:deep(.ivu-tabs-bar) {
  background-color: white;
  margin-bottom: 12px;
}

:deep(.ivu-tabs-ink-bar.ivu-tabs-ink-bar-animated) {
  width: 74px !important;
}

.wrap {
  height: calc(100% - 52.38px);
  display: flex;
  gap: 12px;
  padding: 0px 12px;
  flex-direction: column;
  overflow: hidden;
  background-color: white;
}

.cus-header {
  margin-bottom: 0px;

  .form-item-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    width: 100%;

    // Adjust for iView FormItem if necessary
    .ivu-form-item:nth-child(n + 3):not(:last-child) {
      display: none;
    }

    &.is-expanded {
      .ivu-form-item:nth-child(n + 3):not(:last-child) {
        display: flex; // or block, depending on iView FormItem display
      }
    }
  }

  .search-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    grid-column: -2 / -1;

    .ivu-btn-text {
      box-shadow: none !important;
      border: none !important;
    }
  }

  // Adjust for iView FormItem if necessary
  .ivu-form-item {
    display: flex;
    width: 100%;
    margin-bottom: 0px; // iView FormItem might have default margin

    .ivu-input-wrapper,
        // For Input
        .ivu-select,
        // For Select
        .ivu-date-picker {
      // For DatePicker
      width: 100%;
    }

    :deep(.ivu-form-item-content) {
      width: calc(100% - 100px) !important;
      margin-left: 0px !important;
    }
  }
}

.cus-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;

  // >.ivu-btn {
  //     margin-bottom: 10px;
  // }

  .cus-list {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    position: relative;

    .cus-table {
      flex: 1; // Table itself should take up the height calculated for it
      height: 0;
      overflow: auto;
      width: 100%;

      :deep(.ivu-table-fixed-body) {
        background-color: white;
        height: calc(100% - 56px);
      }

      :deep(.ivu-table-body) {
        height: calc(100% - 40px);
      }

      :deep(.ivu-table-tip) {
        height: calc(100% - 40px);
      }

      .center {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
      }
    }

    .cus-pages {
      margin-top: 10px;
      text-align: right;
    }
  }
}
</style>
