<template>
    <Modal v-model="modalVisible" title="选择服务兵" :loading="loading" @on-ok="handleOk" @on-cancel="handleCancel"
        width="600">
        <Form ref="formRef" :model="formData" :rules="formRules" :label-width="100">
            <FormItem label="服务兵" prop="staffNo">
                <Select v-model="formData.staffNo" placeholder="请选择服务兵" clearable filterable>
                    <Option v-for="staff in staffList" :key="staff.staffNo" :value="staff.staffNo">
                        {{ staff.staffName }} ({{ staff.staffNo }})
                    </Option>
                </Select>
            </FormItem>
        </Form>
    </Modal>
</template>

<script>
import API from '@/api/maintainance';

export default {
    name: 'SelectStaff',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        orderData: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            modalVisible: this.visible,
            loading: false, // 控制Modal确认按钮的loading状态，防止重复提交
            formData: {
                staffNo: ''
            },
            formRules: {
                staffNo: [
                    { required: true, message: '请选择派工人员', trigger: 'change' }
                ]
            },
            staffList: [] // 存储员工列表
        };
    },
    watch: {
        visible(newVal) {
            this.modalVisible = newVal;
            if (newVal) {
                // 弹窗打开时获取员工列表
                this.fetchStaffList();
            } else {
                // 关闭时重置表单和loading状态
                this.resetForm();
            }
        },
        modalVisible(newVal) {
            // 当弹窗状态改变时，通知父组件
            this.$emit('update:visible', newVal);
            if (!newVal) {
                // 如果是通过点击关闭按钮或遮罩层关闭，也重置
                this.resetForm();
            }
        }
    },
    methods: {
        async fetchStaffList() {
            try {
                const response = (await API.getWorkOrderStaffList()).data;
                const result = response.result;
                if (response.success === true) {
                    // 确保接口返回的数据结构包含 staffNo 和 staffName
                    this.staffList = result || [];
                } else {
                    this.staffList = [];
                    this.$Message.error(response?.message || '获取员工列表失败');
                }
            } catch (error) {
                console.error("获取员工列表失败:", error);
                this.staffList = [];
                this.$Message.error('获取员工列表失败，请稍后再试');
            }
        },
        handleOk() {
            this.$refs.formRef.validate(async (valid) => { // 改为 async
                if (valid) {
                    this.loading = true; // 开始加载，禁用按钮
                    const payload = {
                        orderCode: this.orderData.orderCode,
                        staffNo: this.formData.staffNo
                    };
                    try {
                        // 调用实际的派单接口
                        const response = (await API.assignWorkOrder(payload)).data;
                        const reuslt = response.result;
                        if (response.success) {
                            this.$emit('on-select', payload); // 触发选择事件，传递数据
                            this.$Message.success('指派成功');
                            this.modalVisible = false; // 手动关闭弹窗
                        } else {
                            this.$Message.error(response?.error || '指派失败');
                            this.$nextTick(() => { this.loading = false; });
                        }
                    } catch (error) {
                        console.error("指派失败:", error);
                        this.$Message.error('指派失败，请稍后再试');
                        this.$nextTick(() => { this.loading = false; });
                    }
                } else {
                    this.$Message.error('请选择派工人员');
                    // 校验失败，需要手动恢复按钮状态
                    this.loading = false;
                    this.$nextTick(() => { this.loading = false; });
                }
            });
        },
        handleCancel() {
            console.log('取消选择');
            this.modalVisible = false; // 关闭弹窗 (watch会处理重置)
        },
        resetForm() {
            if (this.$refs.formRef) {
                this.$refs.formRef.resetFields();
            }
            this.staffList = []; // 清空员工列表
            this.loading = false; // 重置loading状态
        }
    }
};
</script>

<style scoped>
/* 可以添加样式 */
</style>
