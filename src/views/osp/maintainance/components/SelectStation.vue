<template>
    <Modal :value="visible" title="选择电站" width="70%" :mask-closable="false" @on-cancel="handleClose">
        <div class="wrap">
            <div class="cus-header">
                <Form :model="formSearch" :label-width="100">
                    <div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
                        <FormItem label="电站编码">
                            <Input v-model="formSearch.stationCode" placeholder="输入电站编码" clearable />
                        </FormItem>
                        <FormItem label="业主姓名">
                            <Input v-model="formSearch.name" placeholder="输入业主姓名" clearable />
                        </FormItem>
                        <FormItem label="联系方式">
                            <Input v-model="formSearch.phone" placeholder="输入联系方式" clearable />
                        </FormItem>
                        <div class="search-buttons">
                            <Button type="default" @click="onReset">重置</Button>
                            <Button type="primary" @click="queryList" style="margin-left: 8px;">查询</Button>
                            <Button type="text" @click="toggleExpand" style="margin-left: 10px;">
                                {{ isExpanded ? '收起' : '展开' }}
                                <Icon :type="isExpanded ? 'ios-arrow-up' : 'ios-arrow-down'" />
                            </Button>
                        </div>
                    </div>
                </Form>
            </div>
            <div class="cus-main" ref="mainRef">
                <div class="cus-list" ref="cusListRef">
                    <Table :data="listArr" :columns="tableColumns" :loading="loading" class="cus-table"
                        @on-row-dblclick="handleRowDblClick">
                    </Table>
                    <Page class="cus-pages" v-if="total > 0" show-sizer show-total show-elevator
                        :page-size-opts="[10, 20, 30]" :page-size="pagination.pageSize" :current="pagination.pageNum"
                        :total="total" @on-page-size-change="changeSize" @on-change="changeCurrent" />
                </div>
            </div>
        </div>
        <div slot="footer">
            <Button @click="handleClose">取 消</Button>
        </div>
    </Modal>
</template>

<script>
import API from '@/api/maintainance'; // 确保API路径正确

export default {
    name: 'SelectStationModal',
    props: {
        visible: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            isExpanded: false,
            formSearch: {
                name: '',
                phone: '',
                stationCode: '',
                // opName: '',
                // mode: ''
            },
            loading: false,
            listArr: [],
            total: 0,
            pagination: {
                pageSize: 10,
                pageNum: 1,
            },
            tableColumns: [
                { type: 'index', title: '序号', width: 80, align: 'center' },
                { title: '电站编码', key: 'stationCode', width: 150, align: 'center' },
                { title: '业主姓名', key: 'name', width: 120, align: 'center' },
                { title: '联系方式', key: 'phone', width: 120, align: 'center' },
                { title: '详细地址', key: 'address', minWidth: 180, align: 'center', tooltip: true },
                { title: '省份', key: 'provinceName', width: 120, align: 'center', tooltip: true },
                { title: '城市', key: 'cityName', width: 120, align: 'center', tooltip: true },
                {
                    title: '模式',
                    key: 'mode',
                    width: 100,
                    align: 'center',
                    render: (h, params) => {
                        const modeText = params.row.mode === 'HR' ? '华润' : (params.row.mode === 'YX' ? '越秀' : '-');
                        return h('span', modeText);
                    }
                },
                { title: '运维服务商', key: 'opName', width: 150, align: 'center', tooltip: true },
                { title: '装机功率(kW)', key: 'power', width: 120, align: 'center' },
                {
                    title: '创建日期',
                    key: 'createdAt',
                    width: 180,
                    align: 'center',
                    render: (h, params) => {
                        return h('span', params.row.createdAt ? params.row.createdAt.replace('T', ' ') : '-');
                    }
                }
            ]
        };
    },
    watch: {
        visible(val) {
            if (val) {
                this.getList();
            }
        }
    },
    methods: {
        async getList() {
            this.loading = true;
            try {
                const params = {
                    ...this.formSearch,
                    pageSize: this.pagination.pageSize,
                    pageNum: this.pagination.pageNum,
                };
                // 根据项目实际API返回结构调整
                const res = await API.getStationPage(params);
                if (res.data && res.data.success) {
                    const result = res.data.result;
                    this.listArr = result.content || [];
                    this.total = result.totalElements || (result.totalPages * this.pagination.pageSize) || 0;
                } else {
                    this.listArr = [];
                    this.total = 0;
                    this.$Message.error(res.data.error || '获取电站列表失败');
                }
            } catch (error) {
                console.error("Error fetching station list:", error);
                this.listArr = [];
                this.total = 0;
                this.$Message.error('获取电站列表失败，请稍后再试');
            } finally {
                this.loading = false;
            }
        },
        queryList() {
            this.pagination.pageNum = 1;
            this.getList();
        },
        onReset() {
            this.formSearch = {
                name: '',
                phone: '',
                stationCode: '',
            };
            this.queryList();
        },
        toggleExpand() {
            this.isExpanded = !this.isExpanded;
        },
        handleClose() {
            this.$emit('update:visible', false);
        },
        handleRowDblClick(row) {
            this.$emit('selected', row);
            this.handleClose();
        },
        changeSize(size) {
            this.pagination.pageSize = size;
            this.getList();
        },
        changeCurrent(current) {
            this.pagination.pageNum = current;
            this.getList();
        }
    },
    mounted() {
        if (this.visible) {
            this.getList();
        }
    }
};
</script>

<style lang="scss" scoped>
// 尝试引入项目已有的SCSS列表和头部样式，如果路径和内容兼容
// @import "~@/style/_cus_header.scss"; // 假设有SCSS版本或通用样式
@import "~@/style/_cus_list.scss"; // 项目规则中提到这个

.wrap {
    height: 60vh;
    padding: 0px;
    display: flex;
    padding: 0px 12px;
    flex-direction: column;
    overflow: hidden;
    background-color: white;
}

.cus-header {
    margin-bottom: 10px;

    .form-item-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr); // 与参考文件一致
        gap: 12px;
        width: 100%;

        // iView FormItem的展开/收起逻辑
        .ivu-form-item:nth-child(n+3):not(:last-child) {
            // 根据实际渲染的FormItem数量调整
            display: none;
        }

        &.is-expanded {
            .ivu-form-item:nth-child(n+3):not(:last-child) {
                display: block; // 或者 flex，取决于FormItem内部结构
            }
        }
    }

    .search-buttons {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        grid-column: -2 / -1;

        .ivu-btn-text {
            box-shadow: none !important;
            border: none !important;
        }
    }

    .ivu-form-item {
        width: 100%;
        margin-bottom: 0px; // iView FormItem 默认有 margin-bottom

        .ivu-input-wrapper,
        .ivu-select,
        .ivu-date-picker {
            width: 100%;
        }
    }
}

.cus-main {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    padding: 0px;

    .cus-list {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: hidden;
        position: relative;

        .cus-table {
            flex: 1; // Table itself should take up the height calculated for it
            height: 0;
            overflow: auto;
            width: 100%;

            :deep(.ivu-table-fixed-body) {
                background-color: white;
                height: calc(100% - 56px);
            }

            :deep(.ivu-table-body) {
                height: calc(100% - 40px);
            }

            :deep(.ivu-table-tip) {
                height: calc(100% - 40px);
            }

            .center {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 12px;
            }
        }

        .cus-pages {
            margin-top: 10px;
            text-align: right; // iView Page组件通常右对齐
        }
    }
}
</style>
