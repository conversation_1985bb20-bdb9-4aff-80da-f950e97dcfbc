<template>
    <div class="custom-descriptions" :class="{ 'is-bordered': bordered }">
        <div v-if="title" class="descriptions-title">{{ title }}</div>
        <div class="descriptions-view" :style="gridStyle">
            <slot></slot>
        </div>
    </div>
</template>

<script>
export default {
    name: 'Descriptions',
    props: {
        title: {
            type: String,
            default: ''
        },
        column: {
            type: Number,
            default: 1
        },
        bordered: {
            type: Boolean,
            default: false
        },
        size: {
            type: String,
            default: 'default',
            validator: value => ['small', 'default', 'large'].includes(value)
        }
    },
    computed: {
        gridStyle() {
            return {
                display: 'grid',
                gridTemplateColumns: `repeat(${this.column}, 1fr)`,
                gap: this.bordered ? '0' : '12px 32px'
            }
        }
    },
    provide() {
        return {
            descriptions: this
        }
    }
};
</script>

<style lang="scss" scoped>
// 引入项目变量，如果 $border-color-1 定义在这里
// @import "~@/style/_variables.scss"; // 确保路径正确

.custom-descriptions {
    $border-color: #e8eaec; // 使用项目变量 $border-color-1 或直接写值
    $title-color: #17233d; // 类似 iView 标题颜色
    $text-color: #515a6e; // 类似 iView 常规文本颜色
    $label-bg-color: #f8f8f9; // 类似 iView 表格头/标签背景
    $base-font-size: 14px;
    $small-font-size: 13px; // 模拟 Element Small
    $large-font-size: 16px; // 模拟 Element Large (Title用)

    font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
    font-size: $base-font-size;
    line-height: 1.5;
    color: $text-color;
    margin: 0;
    padding: 0;

    .descriptions-title {
        margin-bottom: 16px; // 保持与 Element 相似间距
        padding-bottom: 8px;
        color: $title-color;
        font-weight: bold; // iView 风格通常标题加粗
        font-size: $large-font-size;
        border-bottom: 1px solid $border-color;
    }

    .descriptions-view {
        width: 100%;
        // gap 在 script 中动态设置
    }

    // 边框模式
    &.is-bordered {
        border: 1px solid $border-color;
        border-radius: 4px; // 保持圆角

        .descriptions-title {
            margin: 0;
            padding: 12px 16px; // 调整 padding
            background: $label-bg-color; // 使用浅灰色背景
            border-bottom: 1px solid $border-color;
        }

        .descriptions-view {
            // bordered 模式下，view 通常没有额外 padding，由 item 控制
            padding: 0;
            background-color: #FFFFFF;
            border-radius: 0 0 4px 4px;
        }
    }

    // 尺寸调整 - 非 bordered 模式下，只影响字体大小
    // bordered 模式下，影响内部 item 的 padding (将在 DescriptionItem.vue 中处理)
    &.size-small {
        font-size: $small-font-size;

        .descriptions-title {
            padding: 10px 16px; // 调整小尺寸标题 padding
            font-size: 15px; // 调整小尺寸标题字号
        }
    }

    &.size-large {
        font-size: $base-font-size; // Large 模式下基础字号不变或稍大

        .descriptions-title {
            padding: 14px 16px; // 调整大尺寸标题 padding
            font-size: $large-font-size; // 大尺寸标题字号
        }
    }
}
</style>
