<template>
	<Drawer :title="form.id ? '编辑工单' : '提报工单'" :value="dialogVisible" width="50%" :mask-closable="false"
		@on-close="handleClose" :inner="true">
		<Form ref="formRef" :model="form" :rules="rules" :label-width="100" class="drawer-form">
			<FormItem label="电站编码" prop="stationCode">
				<Input v-model="form.stationCode" placeholder="请选择电站编码" readonly>
				<Button slot="append" @click="openSelectStationDialog">选择</Button>
				</Input>
			</FormItem>
			<FormItem label="故障名称" prop="configId">
				<Select v-model="form.configId" placeholder="请选择故障" clearable filterable style="width: 100%;">
					<Option v-for="item in faultCategoryOptions" :key="item.value" :value="item.value">{{ item.label }}
					</Option>
				</Select>
			</FormItem>
			<FormItem label="现场照片" prop="photos">
				<CusMultiUpload :imgUrl.sync="form.photos" :maxCount="5" accept="image/*" tip="最多上传5张图片" :canDel="true"
					:showList="true" type="image" />
			</FormItem>
			<FormItem label="问题描述" prop="problemDesc">
				<Input v-model="form.problemDesc" type="textarea" :rows="3" placeholder="请输入问题描述" />
			</FormItem>
		</Form>
		<div class="drawer-footer">
			<Button @click="handleClose" :disabled="isSubmitting" style="margin-right: 8px;">取 消</Button>
			<Button type="primary" @click="submitForm" :loading="isSubmitting">确 定</Button>
		</div>
		<SelectStation :visible.sync="selectStationDialogVisible" @selected="handleStationSelected" />
	</Drawer>
</template>

<script>
import SelectStation from './SelectStation.vue';
import API from '@/api/maintainance';
import CusMultiUpload from '@/components/public/cusMultiUpload.vue'; // 引入项目上传组件

export default {
	name: 'OperateWorkOrderDrawer',
	components: {
		SelectStation,
		CusMultiUpload
	},
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		data: { // 用于编辑时传递数据
			type: Object,
			default: () => ({})
		}
	},
	data() {
		return {
			dialogVisible: this.visible,
			selectStationDialogVisible: false,
			isSubmitting: false,
			faultCategoryOptions: [],
			form: {
				id: '',
				configId: null,
				orderSource: 'MANUAL_REPORT',
				photos: [], // 内部使用数组
				problemDesc: '',
				stationCode: ''
			},
			rules: {
				configId: [
					{ required: true, message: '请选择故障', trigger: 'change', type: 'number' }
				],
				problemDesc: [
					{ max: 500, message: '问题描述不能超过500个字符', trigger: 'blur' }
				],
				photos: [
					{ required: true, type: 'array', message: '请上传图片', trigger: 'change' }
				],
				stationCode: [
					{ required: true, message: '请选择电站编码', trigger: 'change' },
				]
			}
		};
	},
	watch: {
		visible(newVal) {
			this.dialogVisible = newVal;
			if (newVal) {
				if (this.data && this.data.id) { // 编辑模式
					this.form = { ...this.form, ...this.data }; // 先合并
					// photos 处理
					if (typeof this.data.photos === 'string') {
						this.form.photos = this.data.photos ? this.data.photos.split(',') : [];
					} else if (Array.isArray(this.data.photos)) {
						this.form.photos = [...this.data.photos];
					} else {
						this.form.photos = [];
					}
				}
				this.fetchFaultCategories();
			}
		},
	},
	methods: {
		async fetchFaultCategories() {
			try {
				const res = await API.getWorkOrderConfigList({ orderType: 'report' });
				if (res.data && res.data.success && res.data.result) {
					this.faultCategoryOptions = res.data.result.map(item => ({
						value: item.id,
						label: item.configName
					}));
				} else {
					this.$Message.error(res.data.error || '获取故障列表失败');
					this.faultCategoryOptions = [];
				}
			} catch (error) {
				console.error('获取故障列表失败:', error);
				this.$Message.error('获取故障列表接口调用失败');
				this.faultCategoryOptions = [];
			}
		},
		submitForm() {
			if (this.isSubmitting) return;
			this.$refs.formRef.validate(async (valid) => {
				if (valid) {
					this.$Modal.confirm({
						title: '提示',
						content: '您确定要提交吗？',
						onOk: async () => {
							this.isSubmitting = true;
							try {
								const photosString = Array.isArray(this.form.photos) ? this.form.photos.join(',') : this.form.photos;
								const params = { ...this.form, photos: photosString };
								if (!this.form.id) { // 新建时删除空的id
									delete params.id;
								}

								const res = await API.submitWorkOrder(params); // 确保API方法名正确
								if (res.data && res.data.success) {
									this.$Message.success('提报成功');
									this.$emit('success');
									this.handleClose();
								} else {
									this.$Message.error(res.data.error || '提报失败');
								}
							} catch (err) {
								console.error('提交失败:', err);
								this.$Message.error('操作失败，请稍后再试');
							} finally {
								this.isSubmitting = false;
							}
						}
					});
				} else {
					// 校验失败提示已由Form组件处理
				}
			});
		},
		resetForm() {
			this.form = {
				id: '',
				configId: null,
				orderSource: 'MANUAL_REPORT',
				photos: [],
				problemDesc: '',
				stationCode: ''
			};
			if (this.$refs.formRef) {
				this.$refs.formRef.resetFields();
			}
		},
		openSelectStationDialog() {
			this.selectStationDialogVisible = true;
		},
		handleStationSelected(station) {
			if (station && station.stationCode) {
				this.form.stationCode = station.stationCode;
			}
		},
		handleClose() {
			this.$emit('update:visible', false);
			this.resetForm();
		}
	},
};
</script>

<style lang="scss" scoped>
.drawer-form {
	height: calc(100% - 70px); // 减去footer的高度
	overflow-y: auto;
	padding: 16px; // 统一padding
}

.drawer-footer {
	// iView Drawer 的 footer 是通过 slot="footer" 或直接放置在内容底部
	// 如果不用slot="footer"，可以这样模拟固定底部
	width: 100%;
	position: absolute;
	bottom: 0;
	left: 0; // 需要配合Drawer的padding或父级定位
	padding: 10px 16px;
	border-top: 1px solid #e8eaec;
	background: #fff;
	text-align: right;
	box-sizing: border-box;
}
</style>
