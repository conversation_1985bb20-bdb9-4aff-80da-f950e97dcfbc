<template>
    <Modal v-model="dialogVisible" title="关单确认" :width="500" :mask-closable="false" @on-cancel="handleClose">
        <Form :model="closeOrderForm" :rules="rules" :label-width="100" ref="closeOrderFormRef">
            <FormItem label="关单原因" prop="closeReason">
                <Select v-model="closeOrderForm.closeReason" placeholder="请选择关单原因" style="width: 100%;">
                    <Option v-for="item in closeReasons" :key="item.value" :value="item.value">{{ item.label }}</Option>
                </Select>
            </FormItem>
            <FormItem label="关单备注" prop="closeDesc">
                <Input v-model="closeOrderForm.closeDesc" type="textarea" :rows="3" placeholder="请输入关单备注" />
            </FormItem>
        </Form>
        <div slot="footer">
            <Button @click="handleClose" :disabled="isSubmitting">取消</Button>
            <Button type="primary" @click="submitCloseOrder" :loading="isSubmitting">确定</Button>
        </div>
    </Modal>
</template>

<script>
import API from '@/api/maintainance';
import { mapGetters, mapActions } from 'vuex';

export default {
    name: 'CloseOrderDialog',
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        orderCode: {
            type: [String, Number],
            required: true,
        },
    },
    data() {
        return {
            dialogVisible: this.visible,
            closeOrderForm: {
                closeDesc: '',
                closeReason: '',
            },
            rules: {
                closeReason: [{ required: true, message: '请选择关单原因', trigger: 'change' }],
                closeDesc: [{ max: 500, message: '备注不能超500个字符', trigger: 'blur' }],
            },
            isSubmitting: false,
        };
    },
    computed: {
        ...mapGetters('dict/maintainance', [
            'getDictByType'
        ]),
        closeReasons() {
            return this.getDictByType ? this.getDictByType('close_order_reason') : [];
        }
    },
    watch: {
        visible(newVal) {
            this.dialogVisible = newVal;
            if (newVal) {
                this.closeOrderForm.closeDesc = '';
                this.closeOrderForm.closeReason = '';
            }
        }
    },
    methods: {
        ...mapActions('dict/maintainance', {
            fetchMaintainanceDict: 'fetchDict'
        }),
        handleClose() {
            this.$emit('update:visible', false);
            this.$refs.closeOrderFormRef.resetFields();
        },
        submitCloseOrder() {
            if (!this.$refs.closeOrderFormRef) return;

            this.$refs.closeOrderFormRef.validate(async (valid) => {
                if (valid) {
                    this.$Modal.confirm({
                        title: '提示',
                        content: '确认要关闭此工单吗?',
                        okText: '确定',
                        cancelText: '取消',
                        onOk: async () => {
                            this.isSubmitting = true;
                            try {
                                const params = {
                                    orderCode: this.orderCode,
                                    closeReason: this.closeOrderForm.closeReason,
                                    closeDesc: this.closeOrderForm.closeDesc,
                                };
                                const res = await API.closeWorkOrder(params);
                                if (res.data.success) {
                                    this.$Message.success('工单关闭成功');
                                    this.$emit('closed');
                                    this.handleClose();
                                } else {
                                    this.$Message.error(res.data.error || '关单失败');
                                }
                            } catch (error) {
                                // console.error('关单操作失败:', error);
                                this.$Message.error('关单操作时发生错误');
                            } finally {
                                this.isSubmitting = false;
                            }
                        },
                        onCancel: () => {
                            // 用户点击了确认框的取消按钮
                        }
                    });
                } else {
                    this.$Message.error('请完成必填项');
                }
            });
        }
    }
};
</script>
