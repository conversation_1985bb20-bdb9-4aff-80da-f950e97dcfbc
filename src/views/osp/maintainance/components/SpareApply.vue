<template>
    <div>
    <Modal width="1000" v-model="modelEdit" draggable sticky scrollable title="备件信息" :footer-hide="true" :styles="{ top: 'calc(100px + 5vh)' }"
           :mask-closable="false" :loading="loading">
        <div class="list_but" style="margin-bottom: 10px;">
            <div class="condition">
                <Button @click="handleApply" size="small" type="primary">备件申请</Button>
                <Button @click="handleCheck" style="margin-left: 10px" size="small" :disabled="checkDisabled" type="primary">备件登记</Button>
                <Button @click="handleStatement" style="margin-left: 10px" size="small" type="primary">备件结单</Button>
                <Button @click="handleExpress" style="margin-left: 10px" :disabled="expressDisabled" size="small" type="primary">快递查询</Button>
                <Button @click="updateExpress" style="margin-left: 10px" size="small" type="primary">上传附件</Button>
            </div>
        </div>

        <div class="commListDiv">
            <Table size="small" border ref="subTableRef" height="400px" :columns="tableColumn" :data="tableData" @on-selection-change="selected => this.tableSelectd = selected">
                <template slot-scope="{ index }" slot="sn">
                    {{ index + 1 }}
                </template>
                <template slot-scope="{ index }" slot="fileUrl">
                    <Button v-if="tableData[index].fileList && tableData[index].fileList.length>0" @click="handleFJView(tableData[index].fileList)" size="small" ghost type="info">附件查看</Button>
                </template>
            </Table>
        </div>
        <!-- <Page style="margin: 20px; text-align: right" @on-change="changeCurrent" :current="queryPage.page"
              :total="selectTableTotal" show-total show-elevator /> -->
        <!-- 备件信息 -->
        <Modal width="1000" v-model="applyShow" draggable sticky scrollable title="备件信息" :mask-closable="false" >
            <div style="position: relative; width: 100%; height: 60vh;">
                <div style="padding-bottom: 40px; width: 100%; height: 60vh; overflow-y: auto">

                    <Form class="form_deep" :model="applyForm" :rules="ruleValidate" :label-width="100" ref="applyForm" inline>
                        <FormItem label="运维工单号">
                            <Input type="text" maxlength="15" v-model="applyForm.serviceInfoId" clearable placeholder="运维工单号"
                                   style="width: 260px" disabled />
                        </FormItem>
                        <FormItem label="备件编码">
                            <Input type="text" maxlength="15" :value="materialCode" clearable placeholder="备件编码"
                                   style="width: 260px" disabled />
                        </FormItem>
                        <FormItem label="申请数量" prop="applyNum">
                            <Input v-model="applyForm.applyNum" style="width: 260px" placeholder="申请数量" />
                        </FormItem>
                        <FormItem label="收货地址" prop="duty">
                            <div class="AddressTitle">
                                <div style="height: 20px;">{{applyForm.province}}{{applyForm.city}}{{applyForm.region}} </div>
                                <div style="height: 20px;">{{applyForm.receiveAddress}} </div>
                                <div style="height: 20px;">{{applyForm.linkMan}} {{applyForm.phone}} </div>
                                <span @click="handleChangeAddress" class="AddrssButtom"  >修改地址</span>
                            </div>
                        </FormItem>
                    </Form>

                    <Divider />
                    <Form :model="materialForm" :label-width="100" ref="materialForm" inline>
                        <FormItem label="备件品牌">
                            <Select v-model="materialForm.materialBrand" clearable style="width: 260px">
                                <Option v-for="item in options.materialBrand" :value="item.dictValue" :key="item.dictValue">{{ item.dictLabel }}</Option>
                            </Select>
                        </FormItem>
                        <FormItem label="备件类别">
                            <Select v-model="materialForm.materialType" clearable style="width: 260px">
                                <Option v-for="item in options.materialType" :value="item.dictValue" :key="item.dictValue">{{ item.dictLabel }}</Option>
                            </Select>
                        </FormItem>
                        <FormItem label="备件名称">
                            <Input type="text" maxlength="15" v-model="materialForm.materialDesc" clearable placeholder="备件名称"
                                   style="width: 260px" />
                        </FormItem>
                        <FormItem label="备件编码">
                            <Input type="text" maxlength="15" v-model="materialForm.materialCode" clearable placeholder="备件编码"
                                   style="width: 260px" />
                        </FormItem>
                        <Button @click="searchMaterial" type="primary">查询</Button>
                    </Form>

                    <div class="list_but" style="margin-bottom: 10px; text-align: right">

                    </div>
                    <div class="commListDiv">
                        <Table size="small" border ref="subTableRef" :columns="tableApplyColumn" :data="tableApplyData" @on-selection-change="selected => this.tableApplySelectd = selected">
                            <template slot-scope="{ row }" slot="productStatus">
                                {{ enums.productStatus[row.productStatus + ''] }}
                            </template>
                            <template slot-scope="{ row }" slot="providerCode">
                                {{ enums.providerCode[row.providerCode + ''] }}
                            </template>
                            <!--   备件品牌-->
                            <template slot-scope="{ row }" slot="materialBrand">
                                {{materialBrandMap[row.materialBrand]}}
                            </template>
                            <!--     备件分类-->
                            <template slot-scope="{ row }" slot="materialType">
                                {{materialTypeMap[row.materialType]}}
                            </template>
                            <template slot-scope="{ index }" slot="fileList">
                                <Button  v-if="tableApplyData[index].fileList && tableApplyData[index].fileList.length>0" @click="handleFJView(tableApplyData[index].fileList)" size="small" ghost type="info">附件查看</Button>
                            </template>
                        </Table>
                    </div>
                    <Page style="margin: 20px; text-align: right" @on-change="changeCurrentMaterial" :current="materialPage.page"
                          :total="materialTotal" show-total show-elevator />

                </div>
            </div>
            <template #footer>
               <div style="text-align: right">
                   <Button @click="submitApply" type="primary">提交</Button>
                   <Button @click="applyShow = false">取消</Button>
               </div>
            </template>
        </Modal>
        <Modal width="1200" v-model="checkShow" draggable sticky scrollable title="备件登记"
               :mask-closable="false" :loading="checkLoading">
            <div style="position: relative; width: 100%; height: 300px;">
                <div style="padding-bottom: 40px; width: 100%; height: 300px; overflow-y: auto">
                    <Form class="form_deep" :model="checkForm" :rules="ruleCheckValidate" :label-width="115" ref="checkForm" inline>
                        <div class="list_but">
                            <FormItem label="运维工单号">
                                <Input type="text" maxlength="15" v-model="checkForm.serviceInfoId" clearable placeholder="运维工单号"
                                       style="width: 260px" disabled />
                            </FormItem>
                            <FormItem label="备件编码">
                                <Input type="text" maxlength="15" :value="checkForm.partNo" clearable placeholder="备件编码"
                                       style="width: 260px" disabled />
                            </FormItem>
                            <FormItem label="备件名称">
                                <Input v-model="checkForm.materialDesc" style="width: 260px" placeholder="备件名称" disabled/>
                            </FormItem>
                        </div>
                        <div class="list_but">
                            <FormItem label="申请时间">
                                <Input type="text" maxlength="15" v-model="checkForm.createDate" clearable placeholder="申请时间"
                                       style="width: 260px" disabled />
                            </FormItem>
                            <FormItem label="检出仓库">
                                <Input type="text" maxlength="15" :value="checkForm. poolId" clearable placeholder="检出仓库"
                                       style="width: 260px" disabled />
                            </FormItem>
                            <FormItem label="出库仓库">
                                <Input v-model="checkForm.warehouseName" style="width: 260px" placeholder="出库仓库" disabled/>
                            </FormItem>
                        </div>
                        <div class="list_but">
                            <FormItem label="登记状态" prop="useStatus">
                                <Select v-model="checkForm.useStatus" clearable style="width: 260px">
                                    <Option value="0" label="未用" />
                                    <Option value="1" label="已用" />
                                    <Option value="3" label="性能故障" />
                                </Select>
                            </FormItem>
<!--                            <FormItem label="旧件编码" prop="badPartCode">
                                <Input type="text" v-model="checkForm.badPartCode" clearable placeholder="旧件编码"
                                       style="width: 260px"  />
                            </FormItem>-->
                            <FormItem label="性能故障描述">
                                <Input v-model="checkForm.partFault" style="width: 260px" placeholder="性能故障描述" :disabled="checkForm.useStatus !== '3'"/>
                            </FormItem>
                            <FormItem>
                                <template #label>
                                    <Checkbox v-model="checkForm.oldpartReturn">无旧件返回</Checkbox>
                                </template>
                                <Input v-model="checkForm.oldReturnResult" style="width: 260px" placeholder="无旧件返还描述" :disabled="!checkForm.oldpartReturn" />
                            </FormItem>
                            <FormItem label="附件">
                                <Upload ref="upload" class="inlinebox" :show-upload-list="false" :action="actionUrl" :headers="headers" :on-progress="progress" :on-success=" (response, file, fileList) => { return imgFileUploadSuccess(response, file, fileList); } " :format="['jpg', 'jpeg', 'png']" :max-size="18432" :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize">
                                    <div class="file_pics">
                                        <Button v-if="fileUrls" type="info" style="margin-bottom: 15px">上传附件</Button>
                                        <div v-if="fileUrls" class="demo-upload-list" >
                                            <img :src="fileUrls" loading="lazy" width="100%"  alt=""/>
                                            <div class="demo-upload-list-cover" @click.stop>
                                                <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleView()"></Icon>
                                                <Icon type="ios-trash-outline" size="20" color="#fff" @click.stop="handleRemove()"></Icon>
                                            </div>
                                        </div>
                                        <Button v-else type="info">上传附件</Button>
                                    </div>
                                </Upload>
                            </FormItem>
                        </div>

                    </Form>
                </div>
            </div>
            <template #footer>
                <div style="text-align: right">
                    <Button @click="submitCheck" type="primary">保存</Button>
                    <Button @click="handleSubmit" type="primary">提交</Button>
                    <Button @click="checkShow = false">取消</Button>
                </div>
            </template>
        </Modal>
        <Modal width="1200" v-model="newCheckShow" draggable sticky scrollable title="备件登记"
               :mask-closable="false" :loading="checkLoading">
            <div style="position: relative; width: 100%; height: 300px;">
                <div style="padding-bottom: 40px; width: 100%; height: 300px; overflow-y: auto">
                    <Form class="form_deep" :model="checkForm" :rules="ruleCheckValidate" :label-width="115" ref="checkForm" inline>
                        <div class="list_but">
                            <FormItem label="运维工单号">
                                <Input type="text" maxlength="15" v-model="checkForm.serviceInfoId" clearable placeholder="运维工单号"
                                       style="width: 260px" disabled />
                            </FormItem>
                            <FormItem label="备件编码">
                                <Input type="text" maxlength="15" :value="checkForm.partNo" clearable placeholder="备件编码"
                                       style="width: 260px" disabled />
                            </FormItem>
                            <FormItem label="备件名称">
                                <Input v-model="checkForm.materialDesc" style="width: 260px" placeholder="备件名称" disabled/>
                            </FormItem>
                        </div>
                        <div class="list_but">
                            <FormItem label="申请时间">
                                <Input type="text" maxlength="15" v-model="checkForm.createDate" clearable placeholder="申请时间"
                                       style="width: 260px" disabled />
                            </FormItem>
                            <FormItem label="检出仓库">
                                <Input type="text" maxlength="15" :value="checkForm. poolId" clearable placeholder="检出仓库"
                                       style="width: 260px" disabled />
                            </FormItem>
                            <FormItem label="出库仓库">
                                <Input v-model="checkForm.warehouseName" style="width: 260px" placeholder="出库仓库" disabled/>
                            </FormItem>
                        </div>
                        <div class="list_but">
                            <FormItem label="登记状态" prop="useStatus">
                                <Select v-model="checkForm.useStatus" clearable style="width: 260px" disabled>
                                    <Option value="0" label="未用" />
                                    <Option value="1" label="已用" />
                                    <Option value="3" label="性能故障" />
                                </Select>
                            </FormItem>
                            <!--                            <FormItem label="旧件编码" prop="badPartCode">
                                                            <Input type="text" v-model="checkForm.badPartCode" clearable placeholder="旧件编码"
                                                                   style="width: 260px"  />
                                                        </FormItem>-->
                            <FormItem label="原SN编码">
                                <Input v-model="checkForm.partFault" style="width: 260px" placeholder=" " disabled/>
                            </FormItem>
                            <FormItem label="新SN编码">
                                <Input v-model="checkForm.oldReturnResult" style="width: 260px" placeholder=" " disabled/>
                            </FormItem>
                        </div>

                    </Form>
                </div>
            </div>
            <template #footer>
                <div style="text-align: right">
                    <Button @click="newSubmitCheck" type="primary">保存</Button>
                    <Button @click="newHandleSubmit" type="primary">提交</Button>
                    <Button @click="newCheckShow = false">取消</Button>
                </div>
            </template>
        </Modal>
<!--        快递查询-->
        <Modal width="1200" v-model="expressShow" draggable sticky scrollable title="快递查询"
               :mask-closable="false" :loading="checkLoading">
            <div style="position: relative; width: 100%; height: 300px;">
                <div style="padding-bottom: 40px; width: 100%; height: 300px; overflow-y: auto">
                    <div style="display: flex; justify-content: center; align-items: center; padding: 20px; border: 1px solid #eee !important; border-radius: 8px !important;">
                        <Steps  direction="vertical" style="width: 88%">
                            <Step v-for="(item,index) in subTableData" :title="item.time" :key="index" :content="item.context" />
                        </Steps>
                    </div>
                </div>
            </div>
            <template #footer>
                <div style="text-align: right">
                    <Button @click="expressShow = false">关闭</Button>
                </div>
            </template>
        </Modal>
<!--        附件查看-->
        <Modal v-model="visible" width="800px" reset-drag-position draggable sticky title="预览" footer-hide class-name="vertical-center-modal">
            <img class="preview" v-if="visibleImg" width="700" :src="visibleImg" @click="toTarget(visibleImg)" title="点击查看原图" alt="">
        </Modal>
<!--        附件上传-->
        <Modal v-model="filUploadeVisible" width="800px" reset-drag-position draggable sticky title="附件上传" class-name="vertical-center-modal">
            <Form class="form_deep" :model="orderWoForm" :rules="ruleOrderWo" :label-width="115" ref="orderWoForm" inline>
                <div class="list_but">
                    <FormItem label="损坏件" prop="damagedPart">
                        <Upload ref="upload" class="inlinebox"  v-model="orderWoForm.damagedPart" :show-upload-list="false" :action="actionUrl" :headers="headers" :on-progress="progress" :on-success=" (response, file, fileList) => { return imgFileUploadSuccess(response, file, fileList,'1'); } " :format="['jpg', 'jpeg', 'png']" :max-size="18432" :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize">
                            <div class="file_pics">
                                <Button v-if="orderWoForm?.damagedPart" icon="ios-cloud-upload-outline" type="info" style="margin-bottom: 15px">上传附件</Button>
                                <div v-if="orderWoForm?.damagedPart" class="demo-upload-list" >
                                    <img :src="orderWoForm?.damagedPart" loading="lazy" width="100%"  alt=""/>
                                    <div class="demo-upload-list-cover" @click.stop>
                                        <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleOrderWoView('1')"></Icon>
                                        <Icon type="ios-trash-outline" size="20" color="#fff" @click.stop="handleOrderWoRemove('1')"></Icon>
                                    </div>
                                </div>
                                <Button v-else icon="ios-cloud-upload-outline" type="info">上传附件</Button>
                            </div>
                        </Upload>
                    </FormItem>
                    <FormItem label="并网箱" prop="parallelMeshBox">
                        <Upload ref="upload" class="inlinebox"  v-model="orderWoForm.parallelMeshBox" :show-upload-list="false" :action="actionUrl" :headers="headers" :on-progress="progress" :on-success=" (response, file, fileList) => { return imgFileUploadSuccess(response, file, fileList,'2'); } " :format="['jpg', 'jpeg', 'png']" :max-size="18432" :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize">
                            <div class="file_pics">
                                <Button v-if="orderWoForm?.parallelMeshBox" icon="ios-cloud-upload-outline" type="info" style="margin-bottom: 15px">上传附件</Button>
                                <div v-if="orderWoForm?.parallelMeshBox" class="demo-upload-list" >
                                    <img :src="orderWoForm?.parallelMeshBox" loading="lazy" width="100%"  alt=""/>
                                    <div class="demo-upload-list-cover" @click.stop>
                                        <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleOrderWoView('2')"></Icon>
                                        <Icon type="ios-trash-outline" size="20" color="#fff" @click.stop="handleOrderWoRemove('2')"></Icon>
                                    </div>
                                </div>
                                <Button v-else icon="ios-cloud-upload-outline" type="info">上传附件</Button>
                            </div>
                        </Upload>
                    </FormItem>
                    <FormItem label="并网箱铭牌" prop="Nameplate">
                        <Upload ref="upload" class="inlinebox"  v-model="orderWoForm.Nameplate" :show-upload-list="false" :action="actionUrl" :headers="headers" :on-progress="progress" :on-success=" (response, file, fileList) => { return imgFileUploadSuccess(response, file, fileList,'3'); } " :format="['jpg', 'jpeg', 'png']" :max-size="18432" :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize">
                            <div class="file_pics">
                                <Button v-if="orderWoForm?.Nameplate" icon="ios-cloud-upload-outline" type="info" style="margin-bottom: 15px">上传附件</Button>
                                <div v-if="orderWoForm?.Nameplate" class="demo-upload-list" >
                                    <img :src="orderWoForm?.Nameplate" loading="lazy" width="100%"  alt=""/>
                                    <div class="demo-upload-list-cover" @click.stop>
                                        <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleOrderWoView('3')"></Icon>
                                        <Icon type="ios-trash-outline" size="20" color="#fff" @click.stop="handleOrderWoRemove('3')"></Icon>
                                    </div>
                                </div>
                                <Button v-else icon="ios-cloud-upload-outline" type="info">上传附件</Button>
                            </div>
                        </Upload>
                    </FormItem>
                </div>
            </Form>
            <template #footer>
                <div style="text-align: right">
                    <Button @click="handleOrderWoSubmit" type="primary">提交</Button>
                    <Button @click="filUploadeVisible = false">取消</Button>
                </div>
            </template>
        </Modal>
    </Modal>
    <Address ref="addressRef" @choseAddress="choseAddress"/>
    </div>
</template>

<script>
import API from "@/api/osp/operation";
import maintainanceAPI from "@/api/maintainance";
import OSP from "@/api/osp";
import Common from "@/api/ospSpare/common";
import Address from "@/views/osp/operation/address.vue";
export default {
    name: "SpareApply",
    components:{Address},
    props: {
        rowData: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        const validateNum = (rule, value, callback) => {
            if (!value || value.trim().length === 0) {
                callback(new Error('申请数量不能为空'));
            } else {
                if (!/(^[0-9]*[1-9][0-9]*$)/.test(value)) {
                    callback(new Error('申请数量只能录入正整数，申请数量大于0'));
                } else {
                    callback();
                }
            }
        };
        return {
            loading: false,
            applyLoading: false,
            checkLoading: false,
            modelEdit: false,
            visible: false,
            filUploadeVisible: false, // 附件上传标记
            tableSelectd: [], // 选中的列表
            tableData: [], // 列表数据
            tableColumn: [ // 列表配置
                {type: 'selection', width: 60, title: '选择', align: 'center'},
                {title: '序号', width: 80, align: 'center', slot: 'sn' },
                {title: '备件网单号', key: 'id', width: 160, align: 'center'},
                {title: '运维单号', key: 'serviceInfoId', width: 180, align: 'center'},
                {title: '备件编码', key: 'partNo', width: 160, align: 'center'},
                {title: '备件名称', key: 'materialDesc', width: 130, align: 'center'},
                {title: '组件SN', key: 'actualBatchNum', width: 180, align: 'center'},
                {title: '检出仓库', key: 'poolId', width: 130, align: 'center'},
                {title: '出库仓库', key: 'warehouseName', width: 130, align: 'center'},
                {title: '快递公司', key: 'transportName', width: 130, align: 'center'},
                {title: '快递单号', key: 'transferCode', width: 130, align: 'center'},
                {title: '审批状态', key: 'auditStatus', width: 100, align: 'center'},
                {title: '服务商出库时间', key: 'netOutDate', width: 160, align: 'center'},
                {title: '登记状态', key: 'useStatus',width: 100, align: 'center'},
                {title: '登记时间', key: 'partSendTime', width: 160, align: 'center'},
                {title: '备件价格', key: 'noexchangeSettlePrice',width: 100, align: 'center'},
                {title: '附件', key: 'fileUrl', align: 'center',width: 100,slot: 'fileUrl' },
                {title: '申请时间', key: 'createDate', width: 160, align: 'center'}
            ],
            selectTableTotal: 0, // 添加明细列表总条数
            queryPage: { // 查询条件 No页数 Size每页大小
                page: 1,
                rows: 10
            },
            orderCode: '', // 运维单号
            /* ============ 备件申请 ===================== */
            applyShow: false,
            applyForm: {
                orderCode: '',
                applyNum: "",
                province:'',
                city:'',
                region:'',
                receiveAddress:'',
            },
            materialForm: {
                materialBrand: '',
                materialType: '',
                materialCode: '',
                materialDesc: ''
            },
            ruleValidate: {
                applyNum: [
                    { validator: validateNum, trigger: 'blur' }
                ]
            },
            tableApplySelectd: [], // 选中的列表
            tableApplyData: [], // 列表数据
            tableApplyColumn: [ // 列表配置
                {type: 'selection', width: 60, title: '选择', align: 'center'},
                {title: '备件编码', key: 'materialCode', align: 'center'},
                {title: '备件名称', key: 'materialDesc', align: 'center'},
                {title: '备件规格', key: 'materialUnit', align: 'center'},
                {title: '备件品牌', key: 'materialBrand', align: 'center',slot: 'materialBrand'},
                {title: '备件分类', key: 'materialType', align: 'center',slot: 'materialType'},
                {title: '供应商名称', slot: 'providerCode', align: 'center'},
                {title: '生产状态', slot: 'productStatus', align: 'center'},
                {title: '备件价格', key: 'settlePrice', align: 'center'},
                {title: '附件', key: 'fileList', align: 'center',slot:'fileList'}
            ],
            materialTotal: 0, // 添加明细列表总条数
            materialPage: { // 查询条件 No页数 Size每页大小
                page: 1,
                rows: 10
            },
            options: {
                materialBrand: [],
                materialType: []
            },
            materialBrandMap:{},
            materialTypeMap:{},
            enums: {
                materialBrand: {},
                materialType: {},
                providerCode: {},
                productStatus: {
                    "0": "停产",
                    "1": "生产"
                }
            },
            /* ============ 备件申请END ===================== */
            /* ============ 备件登记 ===================== */
            checkShow: false,
            newCheckShow: false,
            checkForm: {
                oldpartReturn:false
            },
            notReturn: false,
            ruleCheckValidate: {
                useStatus: [
                    { required: true, message: '请选择申请类型', trigger: 'change' }
                ],
                /*badPartCode: [
                    { required: true, message: '旧件编码不能为空', trigger: 'blur' }
                ],*/
                partFault: [
                    { required: true, message: '性能故障描述不能为空', trigger: 'blur' }
                ],
                oldReturnResult: [
                    { required: true, message: '无旧件返还描述不能为空', trigger: 'blur' }
                ],
                fileUrls: [
                    { required: this.oldReturnResult, message: '请上传附件', trigger: 'blur' }
                ]
            },
            /* ============ 备件登记END ===================== */
            userInfo: {},
            /* ============ 快递查询 ===================== */
            expressShow:false,
            subTableData:[],
            fileUrls:[],
            visibleImg: '',
            actionUrl: `${process.env.VUE_APP_BASE_API}/merchant/oss/image/upload.do`,
            headers: { Authorization: `Bearer ${JSON.parse(window.localStorage.getItem('logins')).access_token}` },
            orderWoForm:{
                damagedPart:null,
                parallelMeshBox:null,
                Nameplate:null,
            },
            ruleOrderWo:{
                damagedPart: [
                    { required: true, message: '请上传附件!', trigger: 'blur' }
                ],
                parallelMeshBox: [
                    { required: true, message: '请上传附件!', trigger: 'blur' }
                ],
                Nameplate: [
                    { required: true, message: '请上传附件!', trigger: 'blur' }
                ],
            }

        }
    },
    computed: {
        materialCode () {
            return this.tableApplySelectd.length > 0 ? this.tableApplySelectd[0].materialCode : ""
        },
        checkDisabled() {
            return this.tableSelectd.length !== 1 || this.tableSelectd[0].useStatus =='取消' || this.tableSelectd[0].useStatus =='货损货差'
        },
        expressDisabled(){
            return this.tableSelectd.length !== 1 || !this.tableSelectd[0].transport || !this.tableSelectd[0].transferCode || this.tableSelectd[0].transport =='' || this.tableSelectd[0].transferCode ==''
        }
    },
    mounted() {
        this.getSelect()
        const userStr = window.localStorage.getItem('userInfo')
        if (userStr) {
            this.userInfo = JSON.parse(userStr)
        }
    },
    watch:{
        applyShow(val) {
            if (val) {
                this.getAddress()
            }
        },
    },
    methods: {
       /* 附件提交 */
        handleOrderWoSubmit(){
            this.$refs.orderWoForm.validate((valid) => {
                if (valid){
                    let data={
                        serviceInfoId:this.rowData.orderCode,
                        fileList:[
                            {
                            "detailId":"damagedPart",
                            'fileUrl':this.orderWoForm.damagedPart
                            },
                            {
                                "detailId":"parallelMeshBox",
                                'fileUrl':this.orderWoForm.parallelMeshBox
                            },
                            {
                                "detailId":"Nameplate",
                                'fileUrl':this.orderWoForm.Nameplate
                            }
                        ]
                    }
                    maintainanceAPI.savePartFile(data).then(res=>{
                        if(res.data.success){
                            this.$Message.success('附件上传成功');
                            this.filUploadeVisible=false
                        }else{
                            this.$Message.error('附件上传失败');
                        }
                    }).catch(err=>{
                        console.log(err);
                        this.$Message.error('附件上传失败');
                    })
                }

            })

        },
        /*上传附件*/
        updateExpress(){
            this.filUploadeVisible=true
        },
        // 选择地址回显
        choseAddress(addressObj){
            this.applyForm.linkMan=addressObj.linkMan
            this.applyForm.phone=addressObj.phone
            this.applyForm.province=addressObj.province
            this.applyForm.city=addressObj.city
            this.applyForm.region=addressObj.region
            this.applyForm.receiveAddress=addressObj.receiveAddress
        },
        getAddress(){
            const member_id = JSON.parse(window.localStorage.getItem('logins')).member_id
            API.addAddressLoading(member_id).then(res => {
                this.applyForm.phone = res.data.result.warehouseAddress.phone
                this.applyForm.linkMan = res.data.result.warehouseAddress.linkMan
                this.applyForm.province = res.data.result.warehouseAddress.province
                this.applyForm.city = res.data.result.warehouseAddress.city
                this.applyForm.region = res.data.result.warehouseAddress.region
                this.applyForm.receiveAddress = res.data.result.warehouseAddress.receiveAddress
                // this.data = res.data.result.warehouse
            })

        },
        //打开 修改地址 弹框
        handleChangeAddress(){
            this.$refs.addressRef.modelEdit = true
        },
        progress() {
            this.$Message.loading({
                content: '上传中...',
                duration: 0
            });
        },
        imgFileUploadSuccess(response, file, fileList,type) {
            let that = this;
            if(type ==='1'){
                this.orderWoForm.damagedPart = response.result[0].imageUrl;
            }else if(type ==='2'){
                this.orderWoForm.parallelMeshBox = response.result[0].imageUrl;
            }else if(type ==='3'){
                this.orderWoForm.Nameplate = response.result[0].imageUrl;
            }else{
                this.fileUrls = response.result[0].imageUrl;
            }
            setTimeout(() => {
                that.$Message.destroy();
            }, 500);
        },
        handleFormatError(file) {
            this.$Notice.warning({
                title: '上传格式不正确',
                desc: '请上传正确的格式文件'
            });
        },

        handleMaxSize (file) {
            this.$Notice.warning({
                title: '上传文件过大',
                desc: file.name + '文件太大，请压缩后上传.'
            });
        },
        handleView (index, inx, tag) {
            this.visible = true;
            this.visibleImg = this.fileUrls;
            this.visibleName = '附件照片';
        },
        handleFJView (fileList) {
            this.visible = true;
            this.visibleImg = fileList[0].fileUrl;
            this.visibleName = '附件照片';
        },
        handleOrderWoView (type) {
            this.visibleName = '附件照片';
            if(type ==='1'){
                this.visibleImg = this.orderWoForm.damagedPart;
            }else if(type ==='2'){
                this.visibleImg = this.orderWoForm.parallelMeshBox;
            }else if(type ==='3'){
                this.visibleImg = this.orderWoForm.Nameplate;
            }
            this.visible = true;
        },
        handleRemove (index, inx, tag) {
            this.fileUrls = ''
        },
        handleOrderWoRemove (type) {
            if(type ==='1'){
                this.orderWoForm.damagedPart = null;
            }else if(type ==='2'){
                this.orderWoForm.parallelMeshBox = null;
            }else if(type ==='3'){
                this.orderWoForm.Nameplate = null;
            }
        },
        toTarget(url) {
            window.open(url)
        },
        getList() {
            this.loading = true
            OSP.getSpareList(this.queryPage, { serviceInfoId: this.rowData.orderCode }).then(res => {
                this.loading = false
                let request = res.data.result;
                if (res.data.success) {
                    this.selectTableTotal = request.totalElements;
                    this.tableData = request.content;
                } else {
                    this.selectTableTotal = 0;
                    this.tableData = [];
                }
            }).catch(e => {
                console.log(e);
                this.loading = false
                this.$Message.error("获取服务失败，请重试");
            })
            OSP.orderWoPartPageAndFile(this.queryPage, { serviceInfoId: this.rowData.orderCode }).then(resp=>{
                if(resp.data.success && resp.data.result.fileList.length>0) {
                    let fileList = resp.data.result.fileList
                    fileList.forEach(item => {
                        if (item.detailId === 'damagedPart') {
                            this.orderWoForm.damagedPart = item.fileUrl
                        } else if (item.detailId === 'parallelMeshBox') {
                            this.orderWoForm.parallelMeshBox = item.fileUrl
                        } else if (item.detailId === 'Nameplate') {
                            this.orderWoForm.Nameplate = item.fileUrl
                        }
                    })
                }else{
                    this.orderWoForm={
                        damagedPart:null,
                        parallelMeshBox:null,
                        Nameplate:null
                    }
                }
            })
        },
        // 主列表分页
        changeCurrent(e) {
            this.queryPage.page = e
            // this.getDetailList()
            this.getList()
        },
        // 备件列表分页
        changeCurrentMaterial(e) {
            this.materialPage.page = e
            this.getMaterial()
        },
        // 获取下拉品牌materialBrand
        // 分类materialType
        getSelect() {
          const pageText = "?page=1&rows=9999"
          Promise.all([
              Common.getDict({dictType: 'materialBrand'}),
              Common.getDict({dictType: 'materialType'}),
              Common.getWarehouse({}, pageText),
          ]).then(res => {
              this.options.materialBrand = res[0].data?.result || []
              this.options.materialType = res[1].data?.result || []
              let warehouses = res[2].data?.result?.content || []
              warehouses.forEach(item => {
                  this.enums.providerCode[item.warehouseCode] = item.warehouseName
              })
              if(this.options.materialBrand.length>0){
                  this.options.materialBrand.forEach(item=>{
                      this.materialBrandMap[item.dictValue]=item.dictLabel
                  })
              }
              if(this.options.materialType.length>0){
                  this.options.materialType.forEach(item2=>{
                      this.materialTypeMap[item2.dictValue]=item2.dictLabel
                  })
              }
          }).catch(e => {
              console.log(e);
              this.$Message.error('获取下拉失败')
          })
        },
        // 点击申请
        handleApply() {
            const params = {
                serviceInfoId: this.rowData.orderCode
            }
            maintainanceAPI.applyPartCheck(params).then(res => {
                this.loading = false
                const resData = res.data
                if (resData.success) {
                    this.applyShow = true
                    this.applyForm.serviceInfoId= this.rowData.orderCode;
                    this.applyForm.applyNum="";
                    this.materialForm = {
                        materialBrand: '',
                        materialType: '',
                        materialCode: '',
                        materialDesc: ''
                    }
                    this.getMaterial()
                } else {
                    this.$Message.error(resData.error || resData.message || '操作失败');
                }
            }).catch(e => {
                this.loading = false
                console.log(e);
                this.$Message.error("获取服务失败，请重试");
            })
        },
        // 备件搜索
        searchMaterial() {
            this.materialPage.page = 1
            this.getMaterial()
        },
        // 获取备件列表
        getMaterial() {
            const params = {
                materialCode: this.materialForm.materialCode,
                materialDesc: this.materialForm.materialDesc,
                materialType: this.materialForm.materialType,
                materialBrand: this.materialForm.materialBrand,
            }
            const pageText = "?page=" + this.materialPage.page + "&rows=" + this.materialPage.rows
            this.applyLoading = true
            Common.getMaterial(params, pageText).then(res => {
                this.applyLoading = false
                this.tableApplySelectd = []
                let request = res.data.result;
                if (res.data.success) {
                    this.materialTotal = request.totalElements;
                    this.tableApplyData = request.content;
                } else {
                    this.materialTotal = 0;
                    this.tableApplyData = [];
                }
            }).catch(e => {
                console.log(e);
                this.applyLoading = false
                this.$Message.error("获取服务失败，请重试");
            })
        },
        submitApply() {
            if (!this.tableApplySelectd.length) {
                this.$Message.error("请选择备件");
                return
            }
            if (this.tableApplySelectd.length > 1) {
                this.$Message.error("只能选择一个备件");
                return
            }
            this.$refs.applyForm.validate((valid) => {
                if (valid) {
                    const params = {
                        applyNum: this.applyForm.applyNum * 1,
                        serviceInfoId: this.applyForm.serviceInfoId,
                        materialId: this.tableApplySelectd[0].id,
                        memberId: this.userInfo?.memberId,
                        applySource: "PC",
                        province: this.applyForm.province,
                        city: this.applyForm.city,
                        region: this.applyForm.region,
                        linkMan: this.applyForm.linkMan,
                        phone: this.applyForm.phone,
                        receiveAddress: this.applyForm.receiveAddress,
                    }
                    this.applyLoading = true
                    maintainanceAPI.applyPart(params).then(res => {
                        this.applyLoading = false
                        if (res.data.success) {
                            this.applyShow = false
                            this.$Message.success("申请成功")
                            this.getList()
                        } else {
                            this.$Message.error(res.data.error || res.data.message || '操作失败');
                        }
                    }).catch(e => {
                        console.log(e)
                        this.applyLoading = false
                        this.$Message.error("获取服务失败，请重试");
                    })
                } else {
                }
            })
        },
        // 快递查询
        handleExpress(){
            let tableData=JSON.parse(JSON.stringify(this.tableSelectd[0]))
            let params={
                'com': tableData.transport,
                'nu': tableData.transferCode
            }
            API.ExpressRouting(params).then(res => {
                if(res.data.success){
                    this.subTableData = res.data?.result||[]
                    this.expressShow=true
                }else{
                    this.$Message.error('暂无快递信息')
                }
            }).catch(err=>{
                this.$Message.error(err.data.error)
            })
        },
        // 备件结单
        handleStatement(){
            this.$message.confirm('请确认是否结单?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$Message.loading({
                    content: "结单中...",
                    duration: 0,
                });
                API.newAndOldPartsCreate(this.rowData.orderCode).then(res => {
                    this.$Message.destroy()
                    if(res.data.success) {
                        this.$Message.success(res.data.result)
                        this.getList()
                    } else {
                        this.$Message.error(res.data.error)
                    }
                })
            }).catch(err=>{
                // this.$Message.error(err.data.error);
            })
        },
        // 备件登记
        handleCheck() {
            this.checkForm = {}
            const params = {
                serviceInfoId: this.tableSelectd[0].serviceInfoId,
                id: this.tableSelectd[0].id
            }
            this.loading = true
            API.sparePartsRegistrationCheck(params).then(res => {
                this.loading = false
                const resData = res.data
                // this.checkForm.badPartCode = this.checkForm.badPartCode || this.checkForm.partNo
                if (resData.success) {
                    if(this.tableSelectd[0].poolId==='借件出库'){
                        this.newCheckShow = true
                    }else{
                        this.checkShow = true
                    }
                    this.checkForm = this.$_.cloneDeep(this.tableSelectd[0])
                    const useStatus = this.checkForm.useStatus
                    switch (useStatus) {
                        case '未用':
                            this.checkForm.useStatus = '0';
                            break
                        case '已用':
                            this.checkForm.useStatus = '1';
                            break
                        case '性能故障':
                            this.checkForm.useStatus = '3';
                            break
                        default:
                            this.checkForm.useStatus = '';
                            break
                    }
                    if(this.tableSelectd[0].poolId==='借件出库' && !this.checkForm.useStatus){
                        this.checkForm.useStatus = '1';
                    }
                } else {
                    this.$Message.error(resData.error || resData.message || '操作失败');
                }
            }).catch(e => {
                this.loading = false
                console.log(e);
                this.$Message.error("获取服务失败，请重试");
            })
        },
        //备件登记 - 提交
        newHandleSubmit(){
            let fileArr=[]
            if(this.fileUrls && this.fileUrls.length>0 && this.fileUrl!=''){
                fileArr.push({
                    fileUrl:this.fileUrls
                })
            }

            const params = {
                id: this.checkForm.id,
                serviceInfoId: this.checkForm.serviceInfoId,
                requireStoreId: this.checkForm.requireStoreId,
                materialId: this.checkForm.materialId,
                oldpartReturn: this.checkForm.oldpartReturn ? '1' : '0',
                oldReturnResult: this.checkForm.oldpartReturn ? this.checkForm.oldReturnResult : '',
                applyBy: this.userInfo?.memberId,
                useStatus: this.checkForm.useStatus,
                partFault: this.checkForm.useStatus === '3' ? this.checkForm.partFault : '',
                fileList: fileArr
                // badPartCode: this.checkForm.badPartCode,
            }
            this.checkLoading = true
            API.sparePartsRegistrationSubmit(params).then(res => {
                this.checkLoading = false
                if (res.data.success) {
                    this.newCheckShow = false
                    this.$Message.success('登记成功')
                    this.getList()
                } else {
                    this.$Message.error(res.data.error || res.data.message || '操作失败');
                }
            }).catch(e => {
                console.log(e)
                this.checkLoading = false
                this.$Message.error("获取服务失败，请重试");
            })
        },
        handleSubmit(){
            if (this.checkForm.oldpartReturn && this.fileUrls.length<1) {
                this.$Message.error("勾选无旧件返还时必须上传附件")
                return
            }
            if (this.checkForm.oldpartReturn && !this.checkForm.oldReturnResult) {
                this.$Message.error("请填写无旧件返还描述")
                return
            }
            if (this.checkForm.useStatus === '3' && !this.checkForm.partFault) {
                this.$Message.error("请填写性能故障描述")
                return
            }
            this.$refs.checkForm.validate((valid) => {
                if (valid) {
                    let fileArr=[]
                    if(this.fileUrls && this.fileUrls.length>0 && this.fileUrl!=''){
                        fileArr.push({
                            fileUrl:this.fileUrls
                        })
                    }

                    const params = {
                        id: this.checkForm.id,
                        serviceInfoId: this.checkForm.serviceInfoId,
                        requireStoreId: this.checkForm.requireStoreId,
                        materialId: this.checkForm.materialId,
                        oldpartReturn: this.checkForm.oldpartReturn ? '1' : '0',
                        oldReturnResult: this.checkForm.oldpartReturn ? this.checkForm.oldReturnResult : '',
                        applyBy: this.userInfo?.memberId,
                        useStatus: this.checkForm.useStatus,
                        partFault: this.checkForm.useStatus === '3' ? this.checkForm.partFault : '',
                        fileList: fileArr
                        // badPartCode: this.checkForm.badPartCode,
                    }
                    this.checkLoading = true
                    API.sparePartsRegistrationSubmit(params).then(res => {
                        this.checkLoading = false
                        if (res.data.success) {
                            this.checkShow = false
                            this.$Message.success('登记成功')
                            this.getList()
                        } else {
                            this.$Message.error(res.data.error || res.data.message || '操作失败');
                        }
                    }).catch(e => {
                        console.log(e)
                        this.checkLoading = false
                        this.$Message.error("获取服务失败，请重试");
                    })
                } else {
                }
            })
        },
        // 备件登记-保存 建站上发货
        newSubmitCheck(){
            let fileArr=[]
            if(this.fileUrls && this.fileUrls.length>0 && this.fileUrl!=''){
                fileArr.push({
                    fileUrl:this.fileUrls
                })
            }
            const params = {
                id: this.checkForm.id,
                serviceInfoId: this.checkForm.serviceInfoId,
                requireStoreId: this.checkForm.requireStoreId,
                oldpartReturn: this.checkForm.oldpartReturn ? '1' : '0',
                oldReturnResult: this.checkForm.oldpartReturn ? this.checkForm.oldReturnResult : '',
                applyBy: this.userInfo?.memberId,
                useStatus: this.checkForm.useStatus,
                partFault: this.checkForm.useStatus === '3' ? this.checkForm.partFault : '',
                fileList: fileArr
                // badPartCode: this.checkForm.badPartCode,
            }
            this.checkLoading = true
            API.sparePartsRegistration(params).then(res => {
                this.checkLoading = false
                if (res.data.success) {
                    this.newCheckShow = false
                    this.$Message.success('登记成功')
                    this.getList()
                } else {
                    this.$Message.error(res.data.error || res.data.message || '操作失败');
                }
            }).catch(e => {
                console.log(e)
                this.checkLoading = false
                this.$Message.error("获取服务失败，请重试");
            })
        },
        //备件登记 - 保存
        submitCheck() {
            if (this.checkForm.oldpartReturn && this.fileUrls.length<1) {
                this.$Message.error("勾选无旧件返还时必须上传附件")
                return
            }
            if (this.checkForm.oldpartReturn && !this.checkForm.oldReturnResult) {
                this.$Message.error("请填写无旧件返还描述")
                return
            }
            if (this.checkForm.useStatus === '3' && !this.checkForm.partFault) {
                this.$Message.error("请填写性能故障描述")
                return
            }
            this.$refs.checkForm.validate((valid) => {
                if (valid) {
                    let fileArr=[]
                    if(this.fileUrls && this.fileUrls.length>0 && this.fileUrl!=''){
                        fileArr.push({
                            fileUrl:this.fileUrls
                        })
                    }

                    const params = {
                        id: this.checkForm.id,
                        serviceInfoId: this.checkForm.serviceInfoId,
                        requireStoreId: this.checkForm.requireStoreId,
                        oldpartReturn: this.checkForm.oldpartReturn ? '1' : '0',
                        oldReturnResult: this.checkForm.oldpartReturn ? this.checkForm.oldReturnResult : '',
                        applyBy: this.userInfo?.memberId,
                        useStatus: this.checkForm.useStatus,
                        partFault: this.checkForm.useStatus === '3' ? this.checkForm.partFault : '',
                        fileList: fileArr
                        // badPartCode: this.checkForm.badPartCode,
                    }
                    this.checkLoading = true
                    API.sparePartsRegistration(params).then(res => {
                        this.checkLoading = false
                        if (res.data.success) {
                            this.checkShow = false
                            this.$Message.success('登记成功')
                            this.getList()
                        } else {
                            this.$Message.error(res.data.error || res.data.message || '操作失败');
                        }
                    }).catch(e => {
                        console.log(e)
                        this.checkLoading = false
                        this.$Message.error("获取服务失败，请重试");
                    })
                } else {
                }
            })
        },
    }
}
</script>
<style lang="scss" scoped>
@import '@/style/_cus_list.scss';

::v-deep .el-message-box {
    z-index: 9999 !important;
}
::v-deep .form_deep .ivu-form-item {
    margin-bottom: 0 !important;
}
::v-deep .ivu-btn-text {
    border: 1px solid #2d8cf0;
}
.res_btn {
    padding: 20px 0;
    text-align: center;
}
.list_but {
    margin-bottom: 20px;
}
.demo-upload-list {
    @extend .flex-row-center-center;
    position: relative;
    width: 100px;
    height: 100px;
    //height: 100%;
    overflow: hidden;

    &:hover .demo-upload-list-cover {
        display: flex;
    }
}
.demo-upload-list-cover {
    @extend.flex-row-center-around;
    display: none;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,.6);
    z-index: 999;
    i{
        &:hover{
            cursor: pointer;
        }
    }
}
.remark{
    text-align: left;
    vertical-align: middle;
    float: left;
    font-size: 14px;
    color: #515a6e;
    line-height: 1;
    padding: 10px 12px 10px 0;
    box-sizing: border-box;
    p{
        margin-bottom: 24px;
        vertical-align: top;
        zoom: 1;
    }
}
.AddressTitle{
    position: relative;
    width: 260px;
    height: 80px;
    background-color: #f3f3f3;
    opacity: 1;
    cursor: not-allowed;
    color: #ccc;
    padding: 4px 7px;
    border: 1px solid #dcdee2;
    border-radius: 4px;
    //margin-top: 10px;
}
.AddrssButtom{
    position: absolute;
    left: -67px;
    top: 35px;
    color: #2d8cf0;
    cursor: pointer;
}
.commListDiv {
    :deep(.ivu-table-fixed-body) {
        background-color: white;
        height: calc(400px - 56px);
    }

    :deep(.ivu-table-body) {
        height: calc(400px - 40px);
    }

    :deep(.ivu-table-tip) {
        height: calc(400px - 40px);
    }
}
</style>
