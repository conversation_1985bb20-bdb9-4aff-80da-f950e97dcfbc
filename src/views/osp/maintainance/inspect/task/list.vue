<template>
    <div style="height: calc(100vh - 100px)">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏运维管理</Breadcrumb-item>
            <Breadcrumb-item to="/osp/">巡检管理</Breadcrumb-item>
            <Breadcrumb-item>巡检任务</Breadcrumb-item>
        </Breadcrumb>
        <div class="wrap">
            <Tabs v-model="activeTab">
                <TabPane label="待处理" name="TO_PROCESS"></TabPane>
                <TabPane label="已处理" name="HANDLED"></TabPane>
            </Tabs>
            <div class="cus-header" ref="headerRef">
                <Form :model="formSearch" :label-width="100">
                    <!-- 基础查询条件 -->
                    <div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
                        <FormItem label="电站类型">
                            <Select v-model="formSearch.stationType" placeholder="选择电站类型" clearable
                                style="width: 100%;">
                                <Option v-for="item in stationTypeOptions" :key="item.value" :value="item.value">{{
                                    item.label }}</Option>
                            </Select>
                        </FormItem>

                        <FormItem label="巡检类型">
                            <Select v-model="formSearch.inspectionType" placeholder="选择巡检类型" clearable
                                style="width: 100%;">
                                <Option v-for="item in inspectionTypeOptions" :key="item.value" :value="item.value">{{
                                    item.label }}</Option>
                            </Select>
                        </FormItem>
                        <!-- <FormItem label="电站类型">
                            <Select v-model="formSearch.stationType" placeholder="选择电站类型" clearable style="width: 100%;"
                                @on-change="getInspectionPlanList">
                                <Option v-for="item in stationTypeOptions" :key="item.value" :value="item.value">{{
                                    item.label }}</Option>
                            </Select>
                        </FormItem>

                        <FormItem label="巡检类型">
                            <Select v-model="formSearch.inspectionType" placeholder="选择巡检类型" clearable
                                style="width: 100%;" @on-change="getInspectionPlanList">
                                <Option v-for="item in inspectionTypeOptions" :key="item.value" :value="item.value">{{
                                    item.label }}</Option>
                            </Select>
                        </FormItem> -->

                        <FormItem label="任务编号">
                            <Input v-model="formSearch.orderCode" placeholder="输入任务编号" clearable />
                        </FormItem>

                        <FormItem label="电站编码">
                            <Input v-model="formSearch.stationCode" placeholder="输入电站编码" clearable />
                        </FormItem>

                        <FormItem label="巡检计划">
                            <Select v-model="formSearch.planId" placeholder="选择巡检计划" clearable style="width: 100%;">
                                <Option v-for="item in planOptions" :key="item.value" :value="item.value">{{ item.label
                                }}</Option>
                            </Select>
                        </FormItem>

                        <FormItem label="运维商">
                            <Input v-model="formSearch.opName" placeholder="输入运维商" clearable />
                        </FormItem>

                        <FormItem label="任务时间">
                            <DatePicker v-model="createTime" type="daterange" format="yyyy-MM-dd"
                                placeholder="开始时间 - 结束时间" @on-change="dateChange" clearable style="width: 100%;" />
                        </FormItem>
                        <FormItem label="是否超时">
                            <Select v-model="formSearch.overTime" placeholder="选择是否超时" clearable style="width: 100%;">
                                <Option key="yes" label="是" value="true" />
                                <Option key="no" label="否" value="false" />
                            </Select>
                        </FormItem>
                        <!-- 查询按钮组 -->
                        <div class="search-buttons">
                            <Button type="default" @click="onReset">重置</Button>
                            <Button type="primary" @click="queryList" style="margin-left: 8px;">查询</Button>
                            <Button type="text" @click="toggleExpand" style="margin-left: 10px;">
                                {{ isExpanded ? '收起' : '展开' }}
                                <Icon :type="isExpanded ? 'ios-arrow-up' : 'ios-arrow-down'" />
                            </Button>
                        </div>
                    </div>
                </Form>
            </div>
            <div class="cus-main" ref="mainRef">
                <div class="cus-list" ref="cusListRef">
                    <Table :data="listArr" :columns="tableColumns" :loading="loading" class="cus-table" ref="tableRef">
                        <template slot="stationCode" slot-scope="{ row }">
                            <span>{{ row.stationCode || '-' }}</span>
                        </template>
                        <template slot="stationName" slot-scope="{ row }">
                            <span>{{ row.stationName || '-' }}</span>
                        </template>
                        <template slot="stationType" slot-scope="{ row }">
                            <span>{{ getDictLabel('station_type', row.stationType) }}</span>
                        </template>
                        <template slot="inspectionType" slot-scope="{ row }">
                            <span>{{ getDictLabel('inspection_type', row.inspectionType) }}</span>
                        </template>
                        <template slot="planName" slot-scope="{ row }">
                            <span>{{ row.planName || '-' }}</span>
                        </template>
                        <template slot="startDate" slot-scope="{ row }">
                            <span>{{ row.startDate || '-' }}</span>
                        </template>
                        <template slot="endDate" slot-scope="{ row }">
                            <span>{{ row.endDate || '-' }}</span>
                        </template>
                        <template slot="opName" slot-scope="{ row }">
                            <span>{{ row.opName || '-' }}</span>
                        </template>
                        <template slot="overTime" slot-scope="{ row }">
                            <span>{{ typeof row.overTime === 'boolean' ? (row.overTime ? '是' : '否') : '-' }}</span>
                        </template>
                        <template slot="action" slot-scope="{ row }">
                            <!-- <a v-if="activeTab === 'TO_PROCESS'" type="text" size="small"
                                @click="handleTask(row)">处理</a> -->
                            <a type="text" size="small" @click="viewTask(row)">查看</a>
                        </template>
                    </Table>
                    <Page class="cus-pages" :page-size-opts="[10, 20, 30]" :page-size="pagination.pageSize"
                        :current="pagination.pageNum" :total="total" @on-page-size-change="changeSize"
                        @on-change="changeCurrent" show-total show-sizer show-elevator ref="pageRef" />
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import API from '@/api/maintainance';
import _ from 'lodash';
import _D from '@/views/osp/_edata';
import { mapActions, mapGetters } from 'vuex';

export default {
    name: 'MaintainanceList',
    data() {
        return {
            currentOrderData: {},
            isExpanded: false,
            activeTab: 'TO_PROCESS',
            planOptions: [],
            createTime: [],
            formSearch: {
                startDateEnd: null,
                startDateBegin: null,
                orderCode: '',
                stationCode: '',
                inspectionType: '',
                stationName: '',
                planId: '',
                stationType: 'COMMON',
                overTime: null,
            },
            loading: false,
            listArr: [],
            total: 0,
            pagination: {
                pageSize: 10,
                pageNum: 1,
            },
            tableColumns: [
                { type: 'index', title: '序号', width: 80, align: 'center', fixed: 'left' },
                { title: '任务编号', key: 'orderCode', width: 200, align: 'center', fixed: 'left' },
                { title: '电站编码', key: 'stationCode', width: 160, align: 'center', slot: 'stationCode' },
                { title: '电站名称', key: 'stationName', width: 100, align: 'center', slot: 'stationName' },
                { title: '电站类型', key: 'stationType', width: 120, align: 'center', slot: 'stationType' },
                { title: '巡检类型', key: 'inspectionType', width: 120, align: 'center', slot: 'inspectionType' },
                { title: '巡检计划', key: 'planName', width: 120, align: 'center', slot: 'planName' },
                { title: '开始时间', key: 'startDate', width: 160, align: 'center', slot: 'startDate' },
                { title: '结束时间', key: 'endDate', width: 160, align: 'center', slot: 'endDate' },
                { title: '是否超时', key: 'overTime', width: 120, align: 'center', slot: 'overTime' },
                { title: '运维商', key: 'opName', width: 200, align: 'center', slot: 'opName' },
                { title: '操作', key: 'action', width: 80, align: 'center', fixed: 'right', slot: 'action' }
            ]
        };
    },
    computed: {
        ...mapGetters('dict/maintainance', [
            'getDictByType',
            'getDictMapByType'
        ]),
        stationTypeOptions() {
            return this.getDictByType ? this.getDictByType('station_type') : [];
        },
        inspectionTypeOptions() {
            return this.getDictByType ? this.getDictByType('inspection_type') : [];
        },
    },
    watch: {
        activeTab() {
            this.queryList();
        },
    },
    methods: {
        ...mapActions('dict/maintainance', {
            fetchMaintainanceDict: 'fetchDict'
        }),
        getDictLabel(dictType, value) {
            const dictMap = this.getDictMapByType(dictType);
            return dictMap ? (dictMap[value] || '-') : '-';
        },
        async getList() {
            this.loading = true;
            try {
                const params = {
                    ...this.formSearch,
                    pageSize: this.pagination.pageSize,
                    pageNum: this.pagination.pageNum,
                    orderStatus: this.activeTab
                };
                const response = await API.getInspectionWorkOrderPage(params);
                const result = response.data?.result;
                if (result) {
                    this.listArr = result.content || [];
                    this.total = result.totalElements || 0;
                } else {
                    this.listArr = [];
                    this.total = 0;
                }
            } catch (error) {
                this.listArr = [];
                this.total = 0;
                this.$Message.error('获取列表失败，请稍后再试'); // iView Message
            } finally {
                this.loading = false;
            }
        },
        queryList() {
            this.pagination.pageNum = 1;
            this.getList();
        },
        changeSize(size) {
            this.pagination.pageSize = size;
            this.getList();
        },
        changeCurrent(current) {
            this.pagination.pageNum = current;
            this.getList();
        },
        dateChange(formattedDate) {
            if (formattedDate && formattedDate.length === 2) {
                this.formSearch.startDateBegin = formattedDate[0];
                this.formSearch.startDateEnd = formattedDate[1];
            } else {
                this.formSearch.startDateBegin = null;
                this.formSearch.startDateEnd = null;
            }
        },
        onReset: _.throttle(
            function () {
                this.createTime = [];
                Object.assign(this.formSearch, {
                    startDateEnd: null,
                    startDateBegin: null,
                    orderCode: '',
                    stationCode: '',
                    inspectionType: '',
                    stationName: '',
                    planId: '',
                    stationType: 'COMMON',
                    overTime: null
                });
                this.queryList();
            },
            3000,
            {
                trailing: false
            }
        ),
        exportList: _.throttle(
            function () {
                this.$Modal.confirm({
                    title: '提示',
                    content: '请确认是否导出此筛选条件下的列表?',
                    onOk: () => {
                        const params = _.cloneDeep(this.formSearch);
                        let exportParams = { ...params };
                        if (['TO_PROCESS', 'HANDLED', 'FINISHED', 'CLOSED'].includes(this.activeTab)) {
                            exportParams.orderStatus = this.activeTab;
                        }
                        API.exportWorkOrderList(exportParams)
                            .then(res => {
                                let binaryData = [];
                                let link = document.createElement('a');
                                binaryData.push(res);
                                link.style.display = 'none';
                                link.href = window.URL.createObjectURL(new Blob(binaryData, { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }));
                                link.setAttribute('download', '运维工单列表.xlsx');
                                document.body.appendChild(link);
                                link.click();
                                document.body.removeChild(link);
                                window.URL.revokeObjectURL(link.href);
                            })
                            .catch(err => {
                                console.error("Export error:", err);
                                this.$Message.error('导出失败，请稍后再试');
                            });
                    }
                });
            },
            3000,
            {
                trailing: false
            }
        ),
        toggleExpand() {
            this.isExpanded = !this.isExpanded;
        },
        handleTask(row) {
            this.$router.push({
                path: '/maintainance/inspect/task/detail',
                query: {
                    orderCode: row.orderCode
                }
            });
        },
        viewTask(row) {
            this.$router.push({
                path: '/maintainance/inspect/task/detail',
                query: {
                    orderCode: row.orderCode,
                    action: 'view'
                }
            });
        }
    },
    mounted() {
        this.getList();
        this.fetchMaintainanceDict([
            'inspection_type',
            'station_type',
        ]);
    },
};
</script>

<style lang="scss" scoped>
@import "@/style/_cus_list.scss";

:deep(.ivu-tabs-bar) {
    background-color: white;
    margin-bottom: 12px;
}

:deep(.ivu-tabs-ink-bar.ivu-tabs-ink-bar-animated) {
    width: 74px !important;
}

.wrap {
    height: calc(100% - 52.38px);
    display: flex;
    padding: 0px 12px;
    flex-direction: column;
    overflow: hidden;
    background-color: white;
}

.cus-header {
    margin-bottom: 10px;

    .form-item-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
        width: 100%;

        // Adjust for iView FormItem if necessary
        .ivu-form-item:nth-child(n+3):not(:last-child) {
            display: none;
        }

        &.is-expanded {
            .ivu-form-item:nth-child(n+3):not(:last-child) {
                display: flex; // or block, depending on iView FormItem display
            }
        }
    }

    .search-buttons {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        grid-column: -2 / -1;

        .ivu-btn-text {
            box-shadow: none !important;
            border: none !important;
        }
    }

    // Adjust for iView FormItem if necessary
    .ivu-form-item {
        display: flex;
        width: 100%;
        margin-bottom: 0px; // iView FormItem might have default margin

        .ivu-input-wrapper,
        // For Input
        .ivu-select,
        // For Select
        .ivu-date-picker {
            // For DatePicker
            width: 100%;
        }

        :deep(.ivu-form-item-content) {
            width: calc(100% - 100px) !important;
            margin-left: 0px !important;
        }
    }
}

.cus-main {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;

    .cus-list {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: hidden;
        position: relative;

        .cus-table {
            flex: 1;
            height: 0;
            overflow: auto;
            width: 100%;

            :deep(.ivu-table-fixed-body) {
                background-color: white;
                height: calc(100% - 56px);
            }

            :deep(.ivu-table-body) {
                height: calc(100% - 40px);
            }

            :deep(.ivu-table-tip) {
                height: calc(100% - 40px);
            }

            .center {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 12px;
            }
        }

        .cus-pages {
            margin-top: 10px;
            text-align: right; // iView Page component often aligns right by default or looks better this way
        }
    }
}
</style>
