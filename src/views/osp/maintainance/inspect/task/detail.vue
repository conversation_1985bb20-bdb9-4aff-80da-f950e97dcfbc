<template>
    <div class="fault-detail-page">
        <Button type="text" icon="md-undo" @click="$router.go(-1)">返回</Button>
        <!-- 故障工单信息 -->
        <Card class="section-card">
            <p slot="title">巡检任务</p>
            <Descriptions :column="3" bordered>
                <DescriptionItem label="任务编号">{{ formData.orderCode || '-' }}</DescriptionItem>
                <DescriptionItem label="任务状态">{{ getDictLabel('work_order_status', formData.orderStatus) || '-' }}</DescriptionItem>
                <DescriptionItem label="电站类型">{{ getDictLabel('station_type', formData.stationType) }}</DescriptionItem>
                <DescriptionItem label="巡检类型">{{ getDictLabel('inspection_type', formData.inspectionType) }}</DescriptionItem>
                <DescriptionItem label="巡检计划">{{ formData.planName || '-' }}</DescriptionItem>
                <DescriptionItem label="开始时间">{{ formData.startDate || '-' }}</DescriptionItem>
                <DescriptionItem label="结束时间">{{ formData.endDate || '-' }}</DescriptionItem>
                <DescriptionItem label="是否超时" :span="2">{{ typeof formData.overTime === 'boolean' ? (formData.overTime ? '是' : '否') : '-' }}</DescriptionItem>
            </Descriptions>
        </Card>

        <!-- 电站信息 -->
        <Card class="section-card">
            <p slot="title">电站信息</p>
            <Descriptions :column="2" bordered>
                <DescriptionItem label="电站编码">{{ formData.stationCode || '-' }}</DescriptionItem>
                <DescriptionItem label="逆变器SN码">
                    <a v-if="formData.inverterSn" type="text">
                        {{ formData.inverterSn }}
                    </a>
                    <span v-else>-</span>
                </DescriptionItem>
                <DescriptionItem label="业主姓名">{{ formData.stationName || '-' }}</DescriptionItem>
                <DescriptionItem label="手机号">{{ formData.stationPhone || '-' }}</DescriptionItem>
                <DescriptionItem label="电站模式">{{ formData.stationMode || '-' }}</DescriptionItem>
                <DescriptionItem label="关联资方">{{ formData.specialFlag || '-' }}</DescriptionItem>
                <DescriptionItem label="所属分中心">{{ formData.subCenterName || '-' }}</DescriptionItem>
                <DescriptionItem label="服务商类别">{{ formData.opType || '-' }}</DescriptionItem>
                <DescriptionItem label="运维商名称">{{ formData.opName || '-' }}</DescriptionItem>
                <!-- <DescriptionItem label="是否质保期内">{{ formData.isWarranty || '-' }}</DescriptionItem> -->
                <DescriptionItem label="区域">{{ (formData.provinceName || '') + (formData.cityName || '') +
                    (formData.regionName || '') || '-' }}</DescriptionItem>
                <DescriptionItem label="详细地址" :span="2">{{ formData.address || '-' }}</DescriptionItem>
            </Descriptions>
        </Card>


        <Card class="section-card">
            <p slot="title">巡检项</p>
            <Form ref="handleFormRef" :model="formData" :rules="handleFormRules" label-position="top">
                <div v-for="(item, idx) in formData.handleItems" :key="'cfg' + idx">
                    <FormItem v-if="item.resultType === 'text'" :label="item.checkItem"
                        :prop="'handleItems.' + idx + '.resultContent'"
                        :rules="[{ required: true, message: `请输入${item.checkItem}`, trigger: 'blur' }]">
                        <Input v-model="item.resultContent" type="textarea" :rows="3"
                            :placeholder="`请输入${item.checkItem}`" :disabled="!editable" />
                    </FormItem>

                    <FormItem v-if="item.resultType === 'select'" :label="item.checkItem"
                        :prop="'handleItems.' + idx + '.resultContent'"
                        :rules="[{ required: true, message: `请选择${item.checkItem}`, trigger: 'change' }]">
                        <Select v-model="item.resultContent" :placeholder="`请选择${item.checkItem}`"
                            :disabled="!editable">
                            <Option value="良好">良好</Option>
                            <Option value="损坏">损坏</Option>
                        </Select>
                    </FormItem>
                    <FormItem v-if="item.resultType === 'image'" :label="item.checkItem"
                        :prop="'handleItems.' + idx + '.resultContent'" :rules="[{
                            required: true, message: `请上传${item.checkItem}照片`, trigger: 'change', validator: (rule, value, callback) => {
                                if (!value || (Array.isArray(value) && value.length === 0)) {
                                    callback(new Error(`请上传${item.checkItem}照片`));
                                } else {
                                    callback();
                                }
                            }
                        }]">
                        <Row :gutter="20" style="width: 100%;">
                            <Col :span="6">
                            <FormItem label="示例照片">
                                <img :src="item.exampleImage"
                                    style="width: 100px; height: 100px; background-color: #eee; object-fit: cover; cursor: pointer;"
                                    @click="handlePreview(item.exampleImage)" alt="示例图" />
                            </FormItem>
                            </Col>
                            <Col :span="18">
                            <FormItem label="上传照片" :required="false">
                                <cusMultiUpload :imgUrl.sync="item.resultContent" :type="'image'" :canDel="editable"
                                    :disabled="!editable" />
                            </FormItem>
                            </Col>
                        </Row>
                    </FormItem>
                </div>
                <FormItem label="备注" prop="remark">
                    <Input v-model="formData.remark" type="textarea" :rows="3" placeholder="请输入备注"
                        :disabled="!editable" />
                </FormItem>
            </Form>
        </Card>

        <!-- 底部操作按钮 -->
        <div class="action-buttons" v-if="action !== 'view'">
            <Button v-if="['TO_PROCESS'].includes(formData.orderStatus)" type="success"
                @click="handleSubmit">提交</Button>
        </div>

        <Modal title="图片预览" v-model="previewImageVisible">
            <img :src="previewImageUrl" v-if="previewImageVisible" style="width: 100%">
        </Modal>

    </div>
</template>

<script>
import API from '@/api/maintainance';
import cusMultiUpload from '@/components/public/cusMultiUpload.vue';
import { mapActions, mapGetters } from 'vuex';
import Descriptions from '../../components/Descriptions.vue';
import DescriptionItem from '../../components/DescriptionItem.vue';

export default {
    name: 'FaultDetail',
    components: {
        cusMultiUpload,
        Descriptions,
        DescriptionItem,
    },
    data() {
        return {
            previewImageVisible: false,
            previewImageUrl: '',
            action: this.$route.query.action,
            activeTabName: 'processInfo',
            formData: {
                address: '',
                businessType: '',
                cityId: '',
                cityName: '',
                configItems: [],
                createdAt: '',
                endDate: '',
                handleItems: [],
                handleTime: '',
                handler: '',
                handlerId: '',
                id: '',
                inspectionType: '',
                opMemberId: '',
                opName: '',
                orderCode: '',
                orderName: '',
                orderStatus: '',
                orderType: '',
                overTime: null,
                planId: '',
                planName: '',
                provinceId: '',
                provinceName: '',
                regionId: '',
                regionName: '',
                remark: '',
                specialFlag: '',
                startDate: '',
                stationCode: '',
                stationId: '',
                stationMode: '',
                stationName: '',
                stationPhone: '',
                stationType: '',
                streetId: '',
                streetName: '',
                subCenterCode: '',
                subCenterName: ''
            },
            handleFormRules: {}, // 初始化表单校验规则
        };
    },
    computed: {
        ...mapGetters('dict/maintainance', [
            'getDictByType',
            'getDictMapByType'
        ]),
        editable() {
            return ['TO_PROCESS'].includes(this.formData.orderStatus) && this.action !== 'view';
        },
    },
    methods: {
        ...mapActions('dict/maintainance', {
            fetchMaintainanceDict: 'fetchDict'
        }),
        getDictLabel(dictType, value) {
            const dictMap = this.getDictMapByType(dictType);
            return dictMap ? (dictMap[value] || '-') : '-';
        },
        async getDetail() {
            const orderCode = this.$route.query.orderCode;
            if (!orderCode) {
                this.$Message.error('缺少工单标识');
                this.$router.back();
                return;
            }
            this.formData.orderCode = orderCode;

            try {
                const res = (await API.getInspectionWorkOrder(orderCode)).data;
                if (res.success) {
                    Object.assign(this.formData, res.result);
                    if (!['TO_HEAD_DISPATCH', 'TO_SUB_CENTER_DISPATCH', 'TO_ASSIGN', 'CLOSED'].includes(this.formData.orderStatus)) {
                        this.activeTabName = 'workOrderHandle';
                    } else {
                        this.activeTabName = 'processInfo';
                    }
                    if (Array.isArray(this.formData.handleItems) && this.formData.handleItems.length === 0) {
                        this.formData.handleItems = this.formData.configItems?.map(item => ({ ...item, resultContent: item.resultType === 'image' ? [] : null })) || [];
                    } else {
                        this.formData.handleItems = this.formData.handleItems?.map(item => ({ ...item, resultContent: item.resultType === 'image' ? item.resultContent.split(',') : item.resultContent })) || [];
                    }
                    if (this.formData.createdAt) {
                        this.formData.createdAt = this.formData.createdAt.replace('T', ' ');
                    }
                } else {
                    this.$Message.error(res.error || '获取详情失败');
                }
            } catch (error) {
                console.error('获取详情失败:', error);
                this.$Message.error('获取详情数据时发生错误');
            }
        },
        async handleSubmit() {
            if (this.$refs.handleFormRef) {
                this.$refs.handleFormRef.validate(async (valid) => {
                    if (valid) {
                        this.showSubmitConfirmModal();
                    } else {
                        this.$Message.error('请检查表单填写是否正确！');
                    }
                });
            } else {
                // 如果表单引用不存在（例如“工单处理”标签页未激活或未渲染），直接显示确认框
                this.showSubmitConfirmModal();
            }
        },
        showSubmitConfirmModal() {
            this.$Modal.confirm({
                title: '提示',
                content: '确认提交任务结果吗?',
                onOk: async () => {
                    const params = {
                        orderCode: this.formData.orderCode,
                        handleCheckItems: this.formData.configItems?.map(item => {
                            if (item.resultType === 'image' && Array.isArray(item.resultContent)) {
                                return {
                                    ...item,
                                    resultContent: item.resultContent.join(','),
                                };
                            }
                            return item;
                        }) || [],
                        remark: this.formData.remark
                    };

                    try {
                        const res = (await API.handleInspectionWorkOrder(params)).data;
                        if (res.success) {
                            this.$Message.success('提交成功');
                            this.$router.back();
                        } else {
                            this.$Message.error(res.error || '提交失败');
                        }
                    } catch (error) {
                        console.error('提交失败:', error);
                        this.$Message.error('提交处理结果时发生错误');
                    }
                },
                onCancel: () => {
                    // 取消提交
                }
            });
        },
        handleCancel() {
            this.$router.back();
        },
        handlePreview(url) {
            this.previewImageUrl = url;
            this.previewImageVisible = true;
        },
        openAssignModal() {
            this.selectStaffModalVisible = true;
        },
        handleStaffAssigned() {
            // 指派成功后，SelectStaff 组件会自行关闭，并触发此事件
            // 此处返回列表页
            this.$Message.success('指派成功，即将返回列表页');
            setTimeout(() => {
                this.$router.back();
            }, 1500); // 延迟一点给用户看提示
        },
    },
    created() {
        this.getDetail();
        this.fetchMaintainanceDict([
            "station_type",
            "inspection_type",
            'work_order_status',
        ]);
    }
};
</script>

<style scoped lang="scss">
.fault-detail-page {
    background-color: white;
    padding: 16px;
}

.cus-back {
    margin-bottom: 16px;
}

.section-card {
    margin-bottom: 16px;
}

.card-header {
    font-weight: bold;
}

.action-buttons {
    text-align: center;
    margin-top: 20px;

    .ivu-btn {
        margin-left: 8px;
    }
}

.image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
    font-size: 14px;
}

.time {
    font-size: 14px;
    font-weight: bold;
}

.content {
    padding-left: 5px;
}

.flex-column {
    display: flex;
    flex-direction: column;
    gap: 8px;
}
</style>
