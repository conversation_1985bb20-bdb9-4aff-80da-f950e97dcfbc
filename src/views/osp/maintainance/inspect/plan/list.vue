<template>
    <div style="height: calc(100vh - 100px)">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏运维管理</Breadcrumb-item>
            <Breadcrumb-item to="/osp/">巡检管理</Breadcrumb-item>
            <Breadcrumb-item>巡检计划</Breadcrumb-item>
        </Breadcrumb>
        <div class="wrap">
            <Tabs v-model="activeTab">
                <TabPane label="户用" name="COMMON"></TabPane>
                <TabPane label="工商业" name="CM"></TabPane>
                <TabPane label="公共租赁" name="PUB_BUILD"></TabPane>
            </Tabs>
            <div class="cus-header" ref="headerRef">
                <Form :model="formSearch" :label-width="100">
                    <!-- 基础查询条件 -->
                    <div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
                        <FormItem label="巡检类型">
                            <Select v-model="formSearch.inspectionType" placeholder="请选择巡检类型" clearable>
                                <Option v-for="item in inspectionTypeOptions" :key="item.value" :value="item.value">{{
                                    item.label }}</Option>
                            </Select>
                        </FormItem>
                        <FormItem label="省">
                            <Select v-model="formSearch.provinceId" placeholder="请选择省份" clearable
                                @on-change="provinceChange">
                                <Option v-for="item in provinceOptions" :key="item.id" :value="item.id">{{
                                    item.name }}</Option>
                            </Select>
                        </FormItem>
                        <FormItem label="市">
                            <Select v-model="formSearch.cityId" placeholder="请选择城市" clearable @on-change="cityChange">
                                <Option v-for="item in cityOptions" :key="item.id" :value="item.id">{{ item.name
                                }}</Option>
                            </Select>
                        </FormItem>
                        <FormItem label="区">
                            <Select v-model="formSearch.regionId" placeholder="请选择区域" clearable>
                                <Option v-for="item in regionOptions" :key="item.id" :value="item.id">{{
                                    item.name }}</Option>
                            </Select>
                        </FormItem>
                        <FormItem label="巡检进度">
                            <div style="display: flex;width: 100%;">
                                <div style="flex: 1;width: 0;">
                                    <InputNumber v-model="formSearch.progressMin" :min="0"
                                        :max="formSearch.progressMax || 100" placeholder="最小进度" controls-position="right"
                                        style="width: 100%" />
                                </div>
                                <div style="margin: 0px 4px;">~</div>
                                <div style="flex: 1;width: 0;">
                                    <InputNumber v-model="formSearch.progressMax" :min="formSearch.progressMin || 0"
                                        :max="100" placeholder="最大进度" controls-position="right" style="width: 100%" />
                                </div>
                            </div>
                        </FormItem>

                        <!-- 查询按钮组 -->
                        <div class="search-buttons">
                            <Button type="default" @click="onReset">重置</Button>
                            <Button type="primary" @click="queryList" style="margin-left: 8px;">查询</Button>
                            <Button type="text" @click="toggleExpand" style="margin-left: 10px;">
                                {{ isExpanded ? '收起' : '展开' }}
                                <Icon :type="isExpanded ? 'ios-arrow-up' : 'ios-arrow-down'" />
                            </Button>
                        </div>
                    </div>
                </Form>
            </div>
            <div class="cus-main" ref="mainRef">
                <div class="cus-list" ref="cusListRef">
                    <Table :data="listArr" :columns="tableColumns" :loading="loading" class="cus-table" ref="tableRef">
                        <template slot="planName" slot-scope="{ row }">
                            <span>{{ row.planName || '-' }}</span>
                        </template>
                        <template slot="specialFlag" slot-scope="{ row }">
                            <span>{{ row.specialFlag || '-' }}</span>
                        </template>
                        <template slot="stationCount" slot-scope="{ row }">
                            <span>{{ row.stationCount || 0 }}</span>
                        </template>
                        <template slot="inspectedCount" slot-scope="{ row }">
                            <span>{{ row.inspectedCount || 0 }}</span>
                        </template>
                        <template slot="progress" slot-scope="{ row }">
                            <span>{{ row.progress || 0 }}%</span>
                        </template>
                    </Table>
                    <Page class="cus-pages" :page-size-opts="[10, 20, 30]" :page-size="pagination.pageSize"
                        :current="pagination.pageNum" :total="total" @on-page-size-change="changeSize"
                        @on-change="changeCurrent" show-total show-sizer show-elevator ref="pageRef" />
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import API from '@/api/maintainance';
import { getRegionList } from "@/api";
import _ from 'lodash';
import _D from '@/views/osp/_edata';
import { mapActions, mapGetters } from 'vuex';

export default {
    name: 'MaintainanceSolution',
    data() {
        return {
            isExpanded: false,
            createTime: [],
            formSearch: {
                provinceId: null,
                cityId: null,
                regionId: null,
                inspectionType: null,
                stationType: null,
                opName: '',
                specialFlag: '',
                planId: null,
                opMemberId: null,
                subCenterList: [],
                progressMin: null,
                progressMax: null,
            },
            loading: false,
            listArr: [],
            total: 0,
            provinceOptions: [],
            cityOptions: [],
            regionOptions: [],
            pagination: {
                pageSize: 10,
                pageNum: 1,
            },
            activeTab: 'COMMON',
            tableColumns: [
                { type: 'index', title: '序号', width: 80, align: 'center' },
                { title: '巡检计划', key: 'planName', align: 'center', slot: 'planName' },
                { title: '电站数量', key: 'stationCount', align: 'center', slot: 'stationCount' },
                { title: '已巡检数量', key: 'inspectedCount', width: 200, align: 'center', slot: 'inspectedCount' },
                { title: '巡检进度', key: 'progress', align: 'center', slot: 'progress' }
            ]
        };
    },
    computed: {
        ...mapGetters('dict/maintainance', [
            'getDictByType',
            'getDictMapByType'
        ]),
        inspectionTypeOptions() {
            return this.getDictByType ? this.getDictByType('inspection_type') : [];
        },
        stationTypeOptions() {
            return this.getDictByType ? this.getDictByType('station_type') : [];
        },
    },
    watch: {
        activeTab() {
            this.queryList();
        },
    },
    methods: {
        ...mapActions('dict/maintainance', {
            fetchMaintainanceDict: 'fetchDict'
        }),
        async initDicts() {
            await this.fetchMaintainanceDict('inspection_type');
            await this.fetchMaintainanceDict('station_type');
        },
        getDictLabel(dictType, value) {
            const dictMap = this.getDictMapByType(dictType);
            return dictMap ? (dictMap[value] || '-') : '-';
        },
        getAreaList() {
            getRegionList().then(res => {
                if (res.data.success && res.data.result) {
                    for (let i in res.data.result.province_list) {
                        this.provinceOptions.push({ id: i, name: res.data.result.province_list[i] });
                    }
                    this.addressList = res.data.result;
                }
            });
        },
        provinceChange(item) {
            this.cityOptions = [];
            this.formSearch.cityId = '';
            this.formSearch.regionId = '';
            this.regionOptions = [];
            if (!item) return;
            const str = item.substring(0, 2);
            for (let i in this.addressList.city_list) {
                if (str === String(i).substring(0, 2)) {
                    this.cityOptions.push({ id: i, name: this.addressList.city_list[i] });
                }
            }
        },
        cityChange(item) {
            this.regionOptions = [];
            this.formSearch.regionId = '';
            if (!item) return;
            const str = item.substring(0, 4);
            for (let i in this.addressList.county_list) {
                if (str === String(i).substring(0, 4)) {
                    this.regionOptions.push({ id: i, name: this.addressList.county_list[i] });
                }
            }
        },
        async getList() {
            this.loading = true;
            try {
                const params = {
                    ...this.formSearch,
                    pageSize: this.pagination.pageSize,
                    pageNum: this.pagination.pageNum,
                    stationType: this.activeTab
                };
                const response = await API.getInspectionWorkOrderStatistics(params);
                const result = response.data?.result;
                if (result) {
                    this.listArr = result.content || [];
                    this.total = result.totalElements || 0;
                } else {
                    this.listArr = [];
                    this.total = 0;
                }
            } catch (error) {
                this.listArr = [];
                this.total = 0;
                this.$Message.error('获取列表失败，请稍后再试'); // iView Message
            } finally {
                this.loading = false;
            }
        },
        queryList() {
            this.pagination.pageNum = 1;
            this.getList();
        },
        changeSize(size) {
            this.pagination.pageSize = size;
            this.getList();
        },
        changeCurrent(current) {
            this.pagination.pageNum = current;
            this.getList();
        },
        onReset: _.throttle(
            function () {
                this.createTime = []; // Reset for iView DatePicker range
                Object.assign(this.formSearch, {
                    provinceId: null,
                    cityId: null,
                    regionId: null,
                    inspectionType: null,
                    stationType: null,
                    opName: '',
                    specialFlag: '',
                    planId: null,
                    opMemberId: null,
                    subCenterList: [],
                    progressMin: null,
                    progressMax: null,
                });
                this.queryList();
            },
            3000,
            {
                trailing: false
            }
        ),
        toggleExpand() {
            this.isExpanded = !this.isExpanded;
        },
        goView(row) {
            this.$router.push({
                path: '/maintainance/solution/view',
                query: {
                    id: row.id
                }
            });
        }
    },
    mounted() {
        this.initDicts();
        this.getList();
        this.getAreaList();
    },
};
</script>

<style lang="scss" scoped>
@import "@/style/_cus_list.scss";

:deep(.ivu-tabs-bar) {
    background-color: white;
    margin-bottom: 12px;
}

:deep(.ivu-tabs-ink-bar.ivu-tabs-ink-bar-animated) {
    width: 74px !important;
}

.wrap {
    height: calc(100% - 52.38px);
    display: flex;
    padding: 0px 12px;
    flex-direction: column;
    overflow: hidden;
    background-color: white;
}

.cus-header {
    margin-bottom: 10px;

    .form-item-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
        width: 100%;

        // Adjust for iView FormItem if necessary
        .ivu-form-item:nth-child(n+3):not(:last-child) {
            display: none;
        }

        &.is-expanded {
            .ivu-form-item:nth-child(n+3):not(:last-child) {
                display: flex; // or block, depending on iView FormItem display
            }
        }
    }

    .search-buttons {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        grid-column: -2 / -1;

        .ivu-btn-text {
            box-shadow: none !important;
            border: none !important;
        }
    }

    // Adjust for iView FormItem if necessary
    .ivu-form-item {
        display: flex;
        width: 100%;
        margin-bottom: 0px; // iView FormItem might have default margin

        .ivu-input-wrapper,
        // For Input
        .ivu-select,
        // For Select
        .ivu-date-picker {
            // For DatePicker
            width: 100%;
        }

        :deep(.ivu-form-item-content) {
            width: calc(100% - 100px) !important;
            margin-left: 0px !important;
        }
    }
}

.cus-main {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;

    // >.ivu-btn {
    //     margin-bottom: 10px;
    // }

    .cus-list {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: hidden;
        position: relative;

        .cus-table {
            flex: 1; // Table itself should take up the height calculated for it
            height: 0;
            overflow: auto;
            width: 100%;

            :deep(.ivu-table-fixed-body) {
                background-color: white;
                height: calc(100% - 56px);
            }

            :deep(.ivu-table-body) {
                height: calc(100% - 40px);
                overflow-y: auto;
            }

            :deep(.ivu-table-tip) {
                height: calc(100% - 40px);
            }

            .center {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 12px;
            }
        }

        .cus-pages {
            margin-top: 10px;
            text-align: right;
        }
    }
}
</style>
