<!-- 结算账单 -->
<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏运维管理</Breadcrumb-item>
            <Breadcrumb-item to="/osp/">结算管理</Breadcrumb-item>
            <Breadcrumb-item>结算账单</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">账单月份：</p>
                    <Select v-model="searchObj.billMonth" clearable style="width: 120px">
                        <Option v-for="item in monthList" :value="item.value" :key="item.value">{{ item.label }}
                        </Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">账单年份：</p>
                    <DatePicker type="year" v-model="searchObj.billYear" placeholder="请选择" @on-change="(data) => {
                        return selectDate(data);
                    }" format="yyyy" style=" width: 200px" />
                </div>
            </div>
            <div class="list_but">
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                    <Button @click="debounce(exportList)" type="success">导出</Button>
                </div>
            </div>
            <div class="commListDiv">
                <table class="cus_table" border="0" cellspacing="0" cellpadding="0" width="100%">
                    <tr>
                        <th align="center">序号</th>
                        <th align="center">账单编号</th>
                        <th align="center">服务商</th>
                        <th align="center">应结金额</th>
                        <th align="center">账单月份</th>
                        <th align="center">账单季度</th>
                        <th align="center">账单年份</th>
                        <th align="center">结算状态</th>
                        <th align="center">基础费用</th>
                        <th align="center">低效占比激励</th>
                        <th align="center">低效整改激励</th>
                        <th align="center">一单一议</th>
                        <th align="center">正激励小计</th>
                        <th align="center">超期考核</th>
                        <th align="center">闭环率考核</th>
                        <th align="center">巡检考核</th>
                        <th align="center">反馈规范性考核</th>
                        <th align="center">负激励结转</th>
                        <th align="center">负激励小计</th>
                        <th align="center">月度运维电站数量(&lt;15kw)</th>
                        <th align="center">月度运维电站数量(≥15kw)</th>
                        <th align="center">服务商签章确认</th>
                        <th align="center">签章确认时间</th>
                        <th align="center">账单生成时间</th>
                        <th align="center" class="fixedRight">操作</th>
                    </tr>
                    <tr v-for="(item, index) in this.commList" :key="index">
                        <td align="center" width="60">{{ index + 1 }}</td>
                        <td align="center" width="150">{{ item.billCode }}</td>
                        <td align="center" width="150">{{ item.spName }}</td>
                        <td align="center" width="100">{{ item.settleAmount }}</td>
                        <td align="center" width="100">{{ item.billMonth }}</td>
                        <td align="center" width="100">{{ item.billQuarter }}</td>
                        <td align="center" width="100">{{ item.billYear }}</td>
                        <td align="center" width="100">{{ typeVal("settlementStatus", item.settleStatus) }}</td>
                        <td align="center" width="100">{{ item.basicAmount }}</td>
                        <td align="center" width="100">
                            <span class="positive" v-if="item.positive1">{{ item.positive1 }}</span>
                            <span v-else>{{ '0' }}</span>
                        </td>
                        <td align="center" width="100">
                            <span class="positive" v-if="item.positive2">{{ item.positive2 }}</span>
                            <span v-else>{{ '0' }}</span>
                        </td>
                        <td align="center" width="100">
                            <span class="positive" v-if="item.positive3">{{ item.positive3 }}</span>
                            <span v-else>{{ '0' }}</span>
                        </td>
                        <td align="center" width="100">{{ item.postAll }}</td>
                        <td align="center" width="100">
                            <span class="negative" v-if="item.negative1">{{ '-' + item.negative1 }}</span>
                            <span v-else>{{ '0' }}</span>
                        </td>
                        <td align="center" width="100">
                            <span class="negative" v-if="item.negative2">{{ '-' + item.negative2 }}</span>
                            <span v-else>{{ '0' }}</span>
                        </td>
                        <td align="center" width="100">
                            <span class="negative" v-if="item.negative3">{{ '-' + item.negative3 }}</span>
                            <span v-else>{{ '0' }}</span>
                        </td>
                        <td align="center" width="100">
                            <span class="negative" v-if="item.negative4">{{ '-' + item.negative4 }}</span>
                            <span v-else>{{ '0' }}</span>
                        </td>
                        <td align="center" width="100">{{ item.lastMonthNegative }}</td>
                        <td align="center" width="100">{{ item.negativeAll }}</td>
                        <td align="center" width="100">{{ item.lowStation || '0' }}</td>
                        <td align="center" width="100">{{ item.highStation || '0' }}</td>
                        <td align="center" width="100">{{ item.lightSpOpsSettleDccs?.signedUrl ? '已签章' : '待签章' }}</td>
                        <td align="center" width="100">
                            {{ item.lightSpOpsSettleDccs?.signAt && item.lightSpOpsSettleDccs?.signAt.replace("T", " ")
                                || '-'
                            }}
                        </td>
                        <td align="center" width="100">{{ item.createdAt && item.createdAt.replace("T", " ") || '-' }}
                        </td>
                        <td align="center" class="fixedRight" width="100">
                            <Button type="info" ghost size="small" v-if="!item.lightSpOpsSettleDccs?.signedUrl"
                                @click="toSign(item.id)" style="margin: 3px">确认签章</Button>
                            <Button type="info" ghost size="small" v-else-if="item.lightSpOpsSettleDccs?.signedUrl"
                                @click="getDetail(item.id)" style="margin: 3px">详情</Button>
                            <Button type="info" ghost size="small" @click="jumpto('/settleBills/detail/', item.id)"
                                style="margin: 3px">账单的电站详情</Button>
                        </td>
                    </tr>
                    <tr v-if="!commList.length">
                        <td colspan="99" align="center">暂无数据</td>
                    </tr>
                </table>
            </div>
            <Page style="margin:20px;" @on-change="changeCurrent" :current="searchObj.page" :total="totalElements"
                show-total show-elevator />
        </div>
        <Modal v-model="modalView" draggable title="确认账单" width="1100" footer-hide :mask-closable="false"
            class-name="vertical-center-modal">
            <div v-if="pdfUrl">
                <iframe id="iframe" class="pdf" :src="pdfUrl" width="100%" height="600" frameborder="0"></iframe>
                <Checkbox v-model="pdfConfirm" style="margin: 20px 0;">已确认账单</Checkbox>
                <div class="res_btn">
                    <Button size="large" @click="cancleContract" style="width: 200px;margin: 0 10px;">取消</Button>
                    <Button type="success" size="large" @click="confirmContract"
                        style="width: 200px;margin:  0 10px;">确认签章</Button>
                </div>
            </div>
            <p v-else>该项目未查询到账单信息</p>
        </Modal>
    </div>
</template>
<script>
import _data from "@/views/osp/_edata"; // 数据字典
import API from "@/api/osp";

export default {
    name: "settleBills",
    data() {
        return {
            searchObj: {
                page: 1,
                size: 10,
                billMonth: '',
                billYear: ''
            },
            pdfConfirm: false,
            depositBalance: 0,
            pdfUrl: '',
            commId: '',
            modalView: false,
            page: 1,
            totalElements: 0,
            commList: [],
            monthList: _data.monthList,
            settlementStatus: _data.settlementStatus,
        };
    },

    mounted() {
        this.queryList();
    },

    methods: {
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.page = curr;
            this.getCommList();
        },

        // 查询按钮
        queryList() {
            this.searchObj.page = 1;
            this.getCommList();
        },

        // 重置按钮
        emptyList() {
            this.searchObj = {
                page: 1,
                size: 10,
                billMonth: "",
                billYear: "",
            };
        },

        // 选择日期
        selectDate(date) {
            this.searchObj.billYear = date
        },

        // 列表
        getCommList() {
            let datas = this.searchObj;
            API.getBillsList(datas).then((res) => {
                let request = res.data.result;
                if (request.content.length > 0 && res.data.success) {
                    this.totalElements = request.totalElements;
                    this.commList = request.content;
                } else {
                    this.totalElements = 0;
                    this.commList = [];
                }
            });
        },

        toSign(id) {
            let datas = {
                id: id,
            }
            API.checkConfirm(datas).then(res => {
                if (res.data.success) {
                    let datas = {
                        id: id,
                    }
                    API.getContractPdf(datas).then(res => {
                        if (res.data.success) {
                            if (res.data.result) {
                                this.pdfUrl = res.data.result.signBeforeUrl
                                this.commId = id
                                this.modalView = true
                            } else {
                                this.$Message.warning("该项目未查询到账单信息");
                            }
                        } else {
                            this.$Message.error(res.data.error);
                            this.pdfUrl = ''
                        }
                    })
                } else {
                    this.$Message.error(res.data.error);
                }
            })

        },

        confirmContract() {
            if (!this.pdfConfirm) {
                this.$Message.warning("请先确认账单");
                return
            }
            this.$Message.loading({
                content: '获取服务中，请稍后...',
                duration: 0
            })
            let datas = {
                id: this.commId
            }
            API.signEleContract(datas).then(res => {
                this.modalView = false
                this.$Message.destroy()
                if (res.data.success) {
                    this.$Modal.confirm({
                        title: '提示',
                        content: '即将跳转法大大签署',
                        onOk: () => {
                            window.open(res.data.result)
                        },
                        onCancel: () => { }
                    })
                } else {
                    this.$Message.error(res.data.error);
                }
            }).catch(() => {
                this.$Message.destroy()
                this.$Message.error("获取服务失败，请重试");
            })
        },

        cancleContract() {
            this.modelValue = false
            this.commId = ''
            this.pdfUrl = ''
            this.pdfConfirm = false
        },
        getDetail(id) {
            let datas = {
                id: id,
            }
            API.getContractPdf(datas).then(res => {
                if (res.data.success) {
                    if (res.data.result) {
                        window.open(res.data.result.signedUrl)
                    } else {
                        this.$Message.warning("该项目未查询到账单信息");
                    }
                } else {
                    this.$Message.warning(res.data.error)
                }
            })
        },

        jumpto(path, id) {
            this.$router.push({
                path,
                query: { id },
            });
        },

        // 导出
        exportList() {
            let datas = this.searchObj;
            let { page, size, ...datasEx } = { ...datas };
            API.billExport(datasEx)
                .then((res) => {
                    let binaryData = [];
                    let link = document.createElement("a");
                    binaryData.push(res.data);
                    link.style.display = "none";
                    link.href = window.URL.createObjectURL(new Blob(binaryData));
                    link.setAttribute("download", "结算帐单(户用).xlsx");
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                })
                .catch(() => {
                    this.$Message.error("导出失败");
                });
        },

        typeVal(list, value) {
            const obj = {};
            this[list].map((e) => {
                obj[e.value] = e.label || e.value;
            });
            return obj[value];
        },
    },
};
</script>
<style lang="scss" scoped>
@import "@/style/_cus_reg.scss";
@import "@/style/_cus_list.scss";

.res_btn {
    text-align: center;
}

.negative {
    color: #00cc66;
}

.positive {
    color: #ff0000;
}
</style>
