<!-- 结算账单 -->
<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏运维管理</Breadcrumb-item>
            <Breadcrumb-item to="/osp/">结算管理</Breadcrumb-item>
            <Breadcrumb-item>账单的电站详情</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <Button @click="debounce(exportList)" type="success">导出</Button>
                </div>
            </div>
            <div class="commListDiv">
                <table class="cus_table" border="0" cellspacing="0" cellpadding="0" width="100%">
                    <tr>
                        <th align="center">电站编号</th>
                        <th align="center">状态</th>
                        <th align="center">模式</th>
                        <th align="center">服务商名称</th>
                        <th align="center">业主姓名</th>
                        <th align="center">电站类型</th>
                        <th align="center">备案方式</th>
                        <th align="center">计划功率</th>
                        <th align="center">装机功率</th>
                        <th align="center">所属项目公司</th>
                        <th align="center">分中心</th>
                        <th align="center">终审一验时间</th>
                        <th align="center">完成时间</th>
                    </tr>
                    <tr v-for="(item, index) in this.commList" :key="index">
                        <td align="center" width="150" style="min-width: 100px">{{ item.stationCode || '-' }}</td>
                        <td align="center" width="100" style="min-width: 100px">{{ typeVal("detailStatus", item.status) ||
                            '-' }}</td>
                        <td align="center" width="120" style="min-width: 120px">{{ typeVal("detailMode", item.mode) || '-'
                        }}</td>
                        <td align="center" width="100" style="min-width: 120px">{{ item.spName || '-' }}</td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.name || '-' }}</td>
                        <td align="center" width="100" style="min-width: 100px">{{ typeVal("detStationType",
                            item.stationType) || '-' }}</td>
                        <td align="center" width="100" style="min-width: 100px">{{ typeVal("detFieldMethod",
                            item.fieldMethod) || '-' }}</td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.planPower || '-' }}</td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.power || '-' }}</td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.projectCompanyCode || '-' }}</td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.subCenterName || '-' }}</td>
                        <td align="center" width="200" style="min-width: 180px">{{ item.firstAuditAt &&
                            item.firstAuditAt.replace("T", " ") || '-' }}
                        </td>
                        <td align="center" width="200" style="min-width: 180px">{{ item.finishAt &&
                            item.finishAt.replace("T", " ") || '-' }}</td>
                    </tr>
                    <tr v-if="!commList.length">
                        <td colspan="99" align="center">暂无数据</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</template>
<script>
import _data from "@/views/osp/_edata"; // 数据字典
import API from "@/api/osp";

export default {
    name: "settleBillsDetail",
    data() {
        return {
            id: '',
            commList: [],
            detailMode: _data.detailMode,
            detailStatus: _data.detailStatus,
            detStationType: _data.detStationType,
            detFieldMethod: _data.detFieldMethod,
        };
    },

    mounted() {
        let sn = (this.id = this.$route.query?.id);
        this.id = sn
        this.getCommList(sn);
    },

    methods: {
        // 列表
        getCommList(id) {
            let datas = {
                id: id,
            };
            API.getBillDetails(datas).then((res) => {
                if (res.data) {
                    this.commList = res.data;
                } else {
                    this.commList = [];
                    this.$message.error('暂无数据');
                }
            });
        },

        // 导出
        exportList() {
            let datas = {
                id: this.id,
            };
            API.billDetailExport(datas)
                .then((res) => {
                    let binaryData = [];
                    let link = document.createElement("a");
                    binaryData.push(res.data);
                    link.style.display = "none";
                    link.href = window.URL.createObjectURL(new Blob(binaryData));
                    link.setAttribute("download", `账单内电站列表.xlsx`);
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                })
                .catch(() => {
                    this.$Message.error("导出失败");
                });
        },

        typeVal(list, value) {
            const obj = {};
            this[list].map((e) => {
                obj[e.value] = e.label || e.value;
            });
            return obj[value];
        },
    },
};
</script>
<style lang="scss" scoped>
@import "@/style/_cus_reg.scss";
@import "@/style/_cus_list.scss";
</style>
