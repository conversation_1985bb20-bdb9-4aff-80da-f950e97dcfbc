<!-- 运维商政策兑现+(户用) -->
<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏运维管理</Breadcrumb-item>
            <Breadcrumb-item to="/osp/">结算管理</Breadcrumb-item>
            <Breadcrumb-item>运维商政策兑现+(户用)</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">运维单号：</p>
                    <Input v-model="searchObj.operationNo" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">政策兑现单号：</p>
                    <Input v-model="searchObj.positiveNo" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">电站编码：</p>
                    <Input v-model="searchObj.stationCode" placeholder="请输入..." clearable style="width: 200px" />
                </div>

                <div class="condition">
                    <p class="condition_p">政策兑现类型：</p>
                    <Select v-model="searchObj.positiveStimulateType" clearable style="width: 150px">
                        <Option v-for="item in settPolicyPlusType" :value="item.value" :key="item.value">{{ item.label }}
                        </Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">渠道来源：</p>
                    <Select v-model="searchObj.source" clearable style="width: 150px">
                        <Option v-for="item in channelSource" :value="item.value" :key="item.value">{{ item.label }}
                        </Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">导入时间：</p>
                    <DatePicker ref="formDateSh" :value="date" @on-change="(data) => {
                        return selectDate(data, 'create');
                    }
                        " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择"
                        style="width: 240px">
                    </DatePicker>
                </div>
            </div>
            <div class="list_but">
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                    <Button @click="debounce(exportList)" type="success">导出</Button>
                </div>
            </div>
            <div class="commListDiv">
                <table class="cus_table" border="0" cellspacing="0" cellpadding="0" width="100%">
                    <tr>
                        <th align="center">序号</th>
                        <th align="center">政策兑现单号</th>
                        <th align="center">渠道来源</th>
                        <th align="center">电站编码</th>
                        <th align="center">电站业主名字</th>
                        <th align="center">电站所属分中心</th>
                        <th align="center">兑现金额</th>
                        <th align="center">政策兑现类型</th>
                        <th align="center">见证性资料</th>
                        <th align="center">描述</th>
                        <th align="center">运维单号<br />非必填</th>
                        <th align="center">工单状态<br />非必填</th>
                        <th align="center">创建时间<br />非必填</th>
                        <th align="center">工单关闭页面时间<br />非必填</th>
                        <th align="center">超期状态<br />非必填</th>
                        <th align="center">超期天数<br />非必填</th>
                        <th align="center">导入时间</th>
                    </tr>
                    <tr v-for="(item, index) in this.commList" :key="index">
                        <td align="center" width="60">{{ index + 1 }}</td>
                        <td align="center" width="150">{{ item.positiveNo || '-' }}</td>
                        <td align="center" width="150">
                            {{ typeVal('channelSource', item.source) || "-" || '-' }}
                        </td>
                        <td align="center" width="100">{{ item.stationCode || '-' }}</td>
                        <td align="center" width="100">{{ item.stationName || '-' }}</td>
                        <td align="center" width="100">{{ item.subCenterName || '-' }}</td>
                        <td align="center" width="100">{{ item.exchangeMoney || '-' }}</td>
                        <td align="center" style="min-width: 150px">
                            {{ typeVal('settPolicyPlusType', item.positiveStimulateType) || "-" || '-' }}
                        </td>
                        <td align="center" width="100">
                            <a v-if="item.significantFileUrl" :href="item.significantFileUrl" target="_blank">查看</a>
                        </td>
                        <td align="center" width="100">{{ item.describe || '-' }}</td>
                        <td align="center" width="150">{{ item.operationNo || '-' }}</td>
                        <td align="center" width="100">{{ item.orderStatus || '-' }}</td>
                        <td align="center" style="min-width: 160px">
                            {{ item.orderCloseTime && item.orderCloseTime.replace("T", " ") || '-' }}
                        </td>
                        <td align="center" style="min-width: 160px">
                            {{ item.orderCreateTime && item.orderCreateTime.replace("T", " ") || '-' }}
                        </td>
                        <td align="center" width="100">
                            {{ typeVal('overdueStatus', item.exceedStatus) || "-" || '-' }}
                        </td>
                        <td align="center" width="150">{{ item.exceedDays || '-' }}</td>
                        <td align="center" style="min-width: 160px">
                            {{ item.importTime && item.importTime.replace("T", " ") || '-' }}
                        </td>
                    </tr>
                    <tr v-if="!commList.length">
                        <td colspan="99" align="center">暂无数据</td>
                    </tr>
                </table>
            </div>
            <Page style="margin:20px;" @on-change="changeCurrent" :current="searchObj.page" :total="totalElements"
                show-total show-elevator />
        </div>
    </div>
</template>
<script>
import _data from "@/views/osp/_edata"; // 数据字典
import API from "@/api/osp";
import { getDicts } from "@/api/public/dict";

export default {
    name: "settPositiveMotivation",
    data() {
        return {
            searchObj: {
                page: 1,
                size: 10,
                importEndTime: "",
                positiveNo: "",
                importStartTime: "",
                positiveStimulateType: "",
                operationNo: "",
                stationCode: "",
                source: "",
            },
            totalElements: 0,
            date: [],
            commList: [],
            overdueStatus:[],
            channelSource:[],
            settPolicyPlusType: [],

        };
    },

    async created() {
        this.overdueStatus = await getDicts("osp/motivate", "overdueStatus").then(e => e?.list)
        this.channelSource = await getDicts("osp/motivate", "channelSource").then(e => e?.list)
        this.settPolicyPlusType = await getDicts("osp/motivate", "settPolicyPlusType").then(e => e?.list)
    },

    mounted() {
        this.queryList();
    },

    methods: {
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.page = curr;
            this.getCommList();
        },

        // 查询按钮
        queryList() {
            this.searchObj.page = 1;
            this.getCommList();
        },

        // 选择日期
        selectDate(date, refObj) {
            this.searchObj.importStartTime = date[0];
            this.searchObj.importEndTime = date[1];
        },

        // 列表
        getCommList() {
            let datas = this.searchObj;
            API.settPolicyPlusFind(datas).then((res) => {
                let request = res.data.result;
                if (request.content.length > 0 && res.data.success) {
                    this.totalElements = request.totalElements;
                    this.commList = request.content;
                } else {
                    this.totalElements = 0;
                    this.commList = [];
                }
            });
        },
        // 重置按钮
        emptyList() {
            this.searchObj = {
                page: 1,
                size: 10,
                importEndTime: "",
                positiveNo: "",
                importStartTime: "",
                positiveStimulateType: "",
                operationNo: "",
                stationCode: "",
                source: "",
            };
            this.date = [];
        },
        // 导出
        exportList() {
            let datas = this.searchObj;
            let { page, size, ...datasEx } = { ...datas };
            API.settPolicyPlusExport(datasEx)
                .then((res) => {
                    let binaryData = [];
                    let link = document.createElement("a");
                    binaryData.push(res.data);
                    link.style.display = "none";
                    link.href = window.URL.createObjectURL(new Blob(binaryData));
                    link.setAttribute("download", "运维商政策兑现+(户用).xlsx");
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                })
                .catch(() => {
                    this.$Message.error("导出失败");
                });
        },

        typeVal(list, value) {
            const obj = {};
            this[list].map((e) => {
                obj[e.value] = e.label || e.value;
            });
            return obj[value];
        },
    },
};
</script>
<style lang="scss" scoped>
@import "@/style/_cus_reg.scss";
@import "@/style/_cus_list.scss";

.res_btn {
    text-align: center;
}

.negative {
    color: #00cc66;
}

.positive {
    color: #ff0000;
}
</style>
