<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏运维管理</Breadcrumb-item>
            <Breadcrumb-item to="/osp/">结算管理</Breadcrumb-item>
            <Breadcrumb-item>特殊费用列表(户用)</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">运维单号：</p>
                    <Input v-model="searchObj.operationNo" placeholder="请输入..." clearable style="width: 150px" />
                </div>
                <div class="condition">
                    <p class="condition_p">正激励单号：</p>
                    <Input v-model="searchObj.positiveNo" placeholder="请输入..." clearable style="width: 150px" />
                </div>
                <div class="condition">
                    <p class="condition_p">电站编码：</p>
                    <Input v-model="searchObj.stationCode" placeholder="请输入..." clearable style="width: 150px" />
                </div>
                <div class="condition">
                    <p class="condition_p">审核状态：</p>
                    <Select v-model="searchObj.auditStatus" clearable style="width: 120px">
                        <Option v-for="item in auditStatus" :value="item.value" :key="item.value">
                            {{ item.label }}
                        </Option>
                    </Select>
                </div>
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                </div>
            </div>
            <div class="prompt">
                <Alert show-icon :type="'warning'" style=" line-height: 1.8">
                    <Icon type="ios-alert" slot="icon"></Icon>
                    <span>操作提示：</span><br />
                    <span>
                        上传附件见证性资料资料：特殊费用呈报（线下中心经理签字确认）、维修前照片、维修后照片、购买凭证（发票、收据）、费用转账记录、赔偿协议、其他见证性资料
                    </span>
                </Alert>
            </div>
            <div class="list_but">
                <div class="condition">
                    <Button type="primary" @click="importData">导入</Button>
                    <Button @click="debounce(importTemplate)">下载导入模版</Button>
                    <Button @click="debounce(applyFor)">下载申请单模版</Button>
                    <Button @click="debounce(reconciliate)">下载资产损害赔偿和解协议模版</Button>
                </div>
            </div>
            <div class="commListDiv">
                <table class="cus_table" border="0" cellspacing="0" cellpadding="0" width="100%">
                    <tr>
                        <th align="center">序号</th>
                        <th align="center">正激励单号</th>
                        <th align="center">电站编码</th>
                        <th align="center">电站业主名称</th>
                        <th align="center">分中心名称</th>
                        <th align="center">运维商编码</th>
                        <th align="center">运维商名称</th>
                        <th align="center">兑现金额(元)</th>
                        <th align="center">类型</th>
                        <th align="center">见证性资料</th>
                        <th align="center">描述</th>
                        <th align="center">运维单号</th>
                        <th align="center">工单状态</th>
                        <th align="center">导入时间</th>
                        <th align="center">审核状态</th>
                        <th align="center">初审</th>
                        <th align="center">初审驳回原因</th>
                        <th align="center">初审时间</th>
                        <th align="center">一审</th>
                        <th align="center">一审时间</th>
                        <th align="center">二审</th>
                        <th align="center">二审时间</th>
                        <th align="center">三审</th>
                        <th align="center">三审时间</th>
                        <th align="center" class="fixedRight">操作</th>
                    </tr>
                    <tr v-for="(item, index) in this.commList" :key="index">
                        <td align="center" width="60">{{ index + 1 }}</td>
                        <td align="center" width="150">{{ item.positiveNo || '-' }}</td>
                        <td align="center" width="150">{{ item.stationCode || '-' }}</td>
                        <td align="center" width="150">{{ item.stationName || '-' }}</td>
                        <td align="center" width="100">{{ item.subCenterName || '-' }}</td>
                        <td align="center" width="100">{{ item.spCode || '-' }}</td>
                        <td align="center" width="100" style="min-width: 150px">{{ item.spName || '-' }}</td>
                        <td align="center" width="100">{{ item.exchangeMoney || '-' }}</td>
                        <td align="center" width="100" style="min-width: 100px">
                            {{ typeVal('specialTypeList', item.positiveStimulateType) || '-' }}
                        </td>
                        <td align="center" width="100">
                            <a v-if="item.significantFileUrl" :href="item.significantFileUrl">查看</a>
                            <span v-else>-</span>
                        </td>
                        <td align="center" width="100" style="min-width: 100px">
                            <Tooltip max-width="200" :content="item.describe" placement="top">
                                <p class="describe">{{ item.describe || '-' }}</p>
                            </Tooltip>
                        </td>
                        <td align="center" width="100">{{ item.operationNo || '-' }}</td>
                        <td align="center" width="100">{{ item.orderStatus || '-' }}</td>
                        <td align="center" width="100" style="min-width: 200px">
                            {{ item.importTime && item.importTime.replace("T", " ") || '-' }}
                        </td>
                        <td align="center" width="100">{{ typeVal('auditStatus', item.auditStatus) || '-' }}</td>
                        <td align="center" width="100">{{ item.preliminaryAudiBy || '-' }}</td>
                        <td align="center" width="100">{{ item.preliminaryRemark || '-' }}</td>
                        <td align="center" width="100" style="min-width: 200px">
                            {{ item.preliminaryAudiAt && item.preliminaryAudiAt.replace("T", " ") || '-' }}
                        </td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.firstAudiBy || '-' }}</td>
                        <td align="center" width="100" style="min-width: 200px">
                            {{ item.firstAudiAt && item.firstAudiAt.replace("T", " ") || '-' }}
                        </td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.secondAudiBy || '-' }}</td>
                        <td align="center" width="100" style="min-width: 200px">
                            {{ item.secondAudiAt && item.secondAudiAt.replace("T", " ") || '-' }}
                        </td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.thirdAudiBy || '-' }}</td>
                        <td align="center" width="100" style="min-width: 200px">
                            {{ item.thirdAudiAt && item.thirdAudiAt.replace("T", " ") || '-' }}
                        </td>
                        <td align="center" class="fixedRight" width="200" style="min-width: 200px">
                            <Button type="info" ghost size="small" style="margin: 3px" @click="uploadFileData(item)"
                                v-if="item.auditStatus === 'WAIT_PRELIMINARY_AUDI' || item.auditStatus === 'PRELIMINARY_AUDI_REJECT'">上传见证性资料</Button>
                            <Button type="info" ghost size="small" style="margin: 3px" @click="modify(item)"
                                v-if="item.auditStatus === 'PRELIMINARY_AUDI_REJECT'">修改</Button>
                            <Button type="error" ghost size="small" style="margin: 3px" @click="delInfo(item.id)"
                                v-if="item.auditStatus === 'WAIT_PRELIMINARY_AUDI'">删除</Button>
                        </td>
                    </tr>
                    <tr v-if="!commList.length">
                        <td colspan="99" align="center">暂无数据</td>
                    </tr>
                </table>
            </div>
            <Page style="margin:20px;" @on-change="changeCurrent" :current="searchObj.page" :total="totalElements"
                show-total show-elevator />
        </div>
        <Modal v-model="modalFileView" draggable title="导入" width="500" footer-hide :mask-closable="false"
            class-name="vertical-center-modal">
            <div style="position: relative; width: 100%; height: auto;">
                <Upload multiple type="drag" :headers=headers :action=actionUrls :on-success="uploadThen"
                    :on-error="uploadThen">
                    <div style="padding: 20px 0">
                        <!-- <Icon type="ios-cloud-upload" size="150" style="color: #3399ff"></Icon> -->
                        <p>点击这里上传导入文件</p>
                    </div>
                </Upload>
            </div>
        </Modal>
        <Modal v-model="modalImgView" draggable title="上传见证性资料" width="500" footer-hide :mask-closable="false"
            class-name="vertical-center-modal">

            <div style="padding-bottom: 40px; width: 100%; overflow-y: auto">
                <Upload class="inlinebox" accept=".zip" :show-upload-list="false"
                    :action="UploadClass.actionUrl('file')" :headers="UploadClass.headers"
                    :before-upload="UploadClass.progress"
                    :on-success="(response) => { uploads.significantFileUrl = response.result[0].fileUrl; uploads.importFileName = response.result[0].fileName; return UploadClass.upSuccess() }"
                    :format="['zip']" :max-size="102400" :on-format-error="UploadClass.formatError"
                    :on-exceeded-size="UploadClass.maxSizeError">
                    <Button type="primary" icon="ios-folder">{{ uploads.significantFileUrl ? '重新' : ''
                        }}上传（.zip）</Button>
                </Upload>
                <span v-if="uploads.significantFileUrl">
                    <Icon type="md-document" /> {{ uploads.importFileName || "见证性资料压缩包.zip" }}
                </span>
                <Icon v-if="uploads.significantFileUrl" type="ios-close-circle-outline" color="#dd3c3c"
                    @click="uploads.significantFileUrl = '', uploads.importFileName = ''"
                    style="margin-left: 10px;cursor: pointer;" />
                <p class="tips" style="margin-top: 20px;text-align: left;line-height: 1.5;">
                    <Icon type="md-information-circle" />
                    请上传见证性资料压缩包
                </p>
            </div>
            <div style="text-align: center; margin: 10px 0">
                <Button type="primary" @click="debounce(submitSelect)">保存</Button>
                <Button type="default" style="margin-left: 10px" @click="debounce(handleCancel)">取消</Button>
            </div>
        </Modal>
        <Modal width="600" v-model="modifyInfo" draggable sticky scrollable title="修改" :footer-hide="true"
            :mask-closable="false">
            <div style="width: 100%; margin-top: 10px;">
                <Form :model="formItem" :label-width="150" style="width: 100%;" ref="formItem" inline>
                    <FormItem label="运维单号：" prop="operationNo" style="width: 100%"
                        :rules="{ required: true, message: ' ' }">
                        <Input type="text" v-model="formItem.operationNo" @on-blur="stationCodeInput" clearable
                            placeholder="请输入运维单号" />
                    </FormItem>
                    <FormItem label="电站编码" prop="stationCode" style="width: 100%"
                        :rules="{ required: true, message: ' ' }">
                        <Input type="text" disabled v-model="formItem.stationCode" clearable placeholder="请输入电站编码" />
                    </FormItem>
                    <FormItem label="电站业主名称：" prop="stationName" style="width: 100%"
                        :rules="{ required: true, message: ' ' }">
                        <Input type="text" disabled v-model="formItem.stationName" clearable />
                    </FormItem>
                    <FormItem label="兑现金额(元)：" prop="exchangeMoney" style="width: 100%"
                        :rules="{ required: true, message: ' ' }">
                        <Input type="text" v-model="formItem.exchangeMoney" clearable placeholder="请输入兑现金额" />
                    </FormItem>
                    <FormItem label="描述：" prop="describe" style="width: 100%" :rules="{ required: true, message: ' ' }">
                        <Input class="item_large" type="textarea" style="width: 100%" v-model="formItem.describe"
                            clearable placeholder="请填写" />
                    </FormItem>
                </Form>
                <div style="text-align: center; margin: 30px 0">
                    <Button type="default" size="large" style="width: 120px; margin-right: 30px"
                        @click="modifyInfo = false">取消</Button>
                    <Button type="primary" size="large" style="width: 120px" @click="submit">
                        提交
                    </Button>
                </div>
            </div>

        </Modal>
    </div>
</template>
<script>
import _data from "@/views/osp/_edata"; // 数据字典
import API from "@/api/osp";
import { getFindByPage, updateData, findByChange, specialDel } from "@/api/osp";
import { UploadClass } from '@/utils/common';
export default {
    name: "specialCharges",
    data() {
        return {
            searchObj: {
                page: 1,
                size: 10,
                positiveNo: '',
                operationNo: '',
                stationCode: '',
                auditStatus: '',
            },
            uploads: {
                id: '',
                significantFileUrl: '',
            },
            formItem: {
                id: '',
                stationCode: '',
                stationName: '',
                exchangeMoney: '',
                operationNo: '',
                describe: '',
            },
            getId: '',
            modalFileView: false,
            modalImgView: false,
            modifyInfo: false,
            totalElements: 0,
            commList: [],
            getInfoObj: {},
            UploadClass,
            actionUrls: `${process.env.VUE_APP_BASE_API}/merchant/positive/importData.do`,
            actionUrl: `${process.env.VUE_APP_BASE_API}/merchant/oss/image/upload.do`,
            headers: { Authorization: `Bearer ${JSON.parse(window.localStorage.getItem('logins')).access_token}` },
            auditStatus: _data.specialAuditStatusList,
            specialTypeList: _data.specialTypeList,
        };
    },

    mounted() {
        this.queryList();
    },

    methods: {
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.page = curr;
            this.getCommList();
        },

        // 查询按钮
        queryList() {
            this.searchObj.page = 1;
            this.getCommList();
        },

        // 重置按钮
        emptyList() {
            this.searchObj = {
                page: 1,
                size: 10,
                positiveNo: "",
                operationNo: "",
                stationCode: "",
                auditStatus: "",
            };
        },

        // 列表
        getCommList() {
            let datas = this.searchObj;
            getFindByPage(datas).then((res) => {
                let request = res.data.result;
                if (request.content.length > 0 && res.data.success) {
                    this.totalElements = request.totalElements;
                    this.commList = request.content;
                } else {
                    this.totalElements = 0;
                    this.commList = [];
                }
            });
        },
        delInfo(id) {
            let datas = {
                id: id
            }
            let that = this
            this.$Modal.confirm({
                title: '删除',
                content: '<p>请确认是否删除</p>',
                onOk: () => {
                    specialDel(datas).then(res => {
                        if (res.data.success) {
                            this.$Message.success('删除成功');
                            that.queryList();
                        } else {
                            that.$Message.error(res.data.error);
                        }
                    });
                },
            })
        },
        //修改
        modify(item) {
            // FX20240603141823837
            this.getInfo(item.operationNo)
            const {
                id,
                stationCode,
                stationName,
                exchangeMoney,
                operationNo,
                describe,
            } = item
            this.formItem = {
                id,
                stationCode,
                stationName,
                exchangeMoney,
                operationNo,
                describe,
            };
            this.modifyInfo = true
            // this.formItem.id = item.id
            // this.formItem.stationCode = item.stationCode
            // this.formItem.stationName = item.stationName
            // this.formItem.exchangeMoney = item.exchangeMoney
            // this.formItem.operationNo = item.operationNo
            // this.formItem.describe = item.describe
            this.stationCodeInput()
        },
        getInfo(operationNo) {
            let datas = {
                workOrderSn: operationNo,
            };
            API.getOrderDetails(datas).then((res) => {
                if (res.data.success) {
                    this.getInfoObj = res.data.result;
                } else {
                    this.$Message.error(res.data.error);
                }
            });
        },
        stationCodeInput() {
            this.getInstalledPower(this.formItem.operationNo)
        },
        getInstalledPower(workOrderSn) {
            if (workOrderSn) {
                const params = {
                    stationCode: workOrderSn
                }
                API.findByStationCode(params).then(res => {
                    if (res.data.success) {
                        this.formItem.stationCode = res.data.result.stationCode
                        this.formItem.stationName = res.data.result.stationName
                    } else {
                        this.formItem.stationCode = ""
                        this.formItem.stationName = ""
                        this.$Message.error(res.data.error);
                    }
                })
            } else {
                this.$Message.error('请输入运维工单号');
            }

        },
        //修改提交
        submit() {
            const params = this.formItem;
            for (let key in params) {
                if (typeof params[key] === 'string') {
                    params[key] = params[key].trim();
                }
            }
            if (!params.stationCode) {
                this.$Message.error('电站编码不能为空');
            } else if (!params.exchangeMoney) {
                this.$Message.error('兑现金额不能为空');
            } else if (!params.operationNo) {
                this.$Message.error('运维单号不能为空');
            } else if (!params.describe) {
                this.$Message.error('描述不能为空');
            } else {
                findByChange(params).then(res => {
                    if (res.data.success) {
                        this.$Message.success('提交成功');
                        this.queryList();
                        this.modifyInfo = false;
                    } else {
                        this.$Message.error(res.data.error);
                    }
                });
            }


        },
        //导入
        importData() {
            this.modalFileView = true
        },
        uploadThen(e) {
            if (e.success) {
                this.$Message.success("导入成功");
                this.modalFileView = false;
                this.queryList();
            } else {
                this.$Message.error(e.error);
            }
        },
        // 上传见证性资料
        uploadFileData(item) {
            this.modalImgView = true
            this.getId = item.id
        },
        //保存
        submitSelect() {
            let datas = {
                id: this.getId,
                significantFileUrl: this.uploads.significantFileUrl
            }
            updateData(datas).then(res => {
                if (res.data.success) {
                    this.$Message.success("上传成功");
                    this.modalImgView = false
                    this.queryList()
                } else {
                    this.$Message.warning(res.data.error)
                }
            })
        },
        // 取消
        handleCancel() {
            this.modalImgView = false
            this.uploads.significantFileUrl = ''
        },
        //下载模板
        importTemplate() {
            window.open(`${process.env.VUE_APP_BASE_API}/merchant/positive/downTemplate`);
        },
        //下载申请模板
        applyFor() {
            const link = document.createElement('a');
            link.href = 'https://cdn01.rrsjk.com/images/特殊运维费用申请单(模板）.doc';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },
        //下载资产损害赔偿和解协议模版
        reconciliate() {
            const link = document.createElement('a');
            link.href = 'https://cdn01.rrsjk.com/images/财产损害赔偿和解协议（模板）.docx';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },
        typeVal(list, value) {
            const obj = {};
            this[list].map((e) => {
                obj[e.value] = e.label || e.value;
            });
            return obj[value];
        },
    },
};
</script>
<style lang="scss" scoped>
@import "@/style/_cus_reg.scss";
@import "@/style/_cus_list.scss";

.describe {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    /* 鼠标变小手 */
}

.prompt {
    margin: 10px 20px;
}
</style>
