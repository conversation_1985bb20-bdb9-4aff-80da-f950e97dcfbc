/*
 * @Author: lufy
 * @FilePath: /src/views/apv/_edata.js
 * @Description: apv 数据字典
 * @remark: 历史优化原因，新增字典全部按对象 Object 类型处理。
 */

export default {
    /**
     * @description: 性别
     */
    genderList: [
        {
            value: "WOMAN",
            label: "女"
        },
        {
            value: "MAN",
            label: "男"
        }
    ],
    /**
     * @description: 实名认证
     */
    realNameAuth: [
        {
            value: "YES",
            label: "是"
        },
        {
            value: "NO",
            label: "否"
        }
    ],
    /**
     * @description: 员工状态
     */
    staffStatusList: [
        {
            value: "ONTHEJOB",
            label: "在职"
        },
        {
            value: "QUIT",
            label: "离职"
        },
        {
            value: "TOBEEMPLOYED",
            label: "待入职"
        }
    ],
    /**
     * @description: 审核状态
     */
    auditStatusList: [
        {
            value: "ADOPT",
            label: "审核通过"
        },
        {
            value: "REJECTED",
            label: "审核驳回"
        },
        {
            value: "TWOADOPT",
            label: "审核通过"
        },
        {
            value: "TWOREJECTED",
            label: "审核驳回"
        },
        {
            value: "CHECKPENDING",
            label: "待审核"
        }
    ],
    auditStatusLists: [
        {
            value: "TWOADOPT" || "ADOPT",
            label: "审核通过"
        },
        {
            value: "TWOREJECTED" || "REJECTED",
            label: "审核驳回"
        },
        {
            value: "CHECKPENDING",
            label: "待审核"
        }
    ],
    /**
     * @description: 故障类型
     */
    faultCodeTypeList: [
        {
            value: "WARN_CODE",
            label: "告警码"
        },
        {
            value: "ERROR_CODE",
            label: "故障码"
        }
    ],
    /***
     * @description: 工单流程来源
     */
    recordSource: [
        {
            value: "MERCHANT",
            label: "运维商"
        },
        {
            value: "MINI_PROGRAM",
            label: "运维商"
        },
        {
            value: "HDS",
            label: "总部"
        }
    ],
    /**
     * @description: 工单状态
     */
    orderStatusList: [
        {
            value: "WAIT_DISPATCH",
            label: "已派单",
            node: 0
        },
        {
            value: "DISPATCHED",
            label: "已派工",
            node: 1
        },
        {
            value: "SERVICE",
            label: "服务中",
            node: 2
        },
        {
            value: "COMPLETE",
            label: "已完工",
            node: 3
        },
        {
            value: "CLOSED",
            label: "已关闭",
            node: 4
        }
    ],
    /**
     * @description: 月份
     */
    monthList: [
        {
            value: "1",
            label: "一月"
        },
        {
            value: "2",
            label: "二月"
        },
        {
            value: "3",
            label: "三月"
        },
        {
            value: "4",
            label: "四月"
        },
        {
            value: "5",
            label: "五月"
        },
        {
            value: "6",
            label: "六月"
        },
        {
            value: "7",
            label: "七月"
        },
        {
            value: "8",
            label: "八月"
        },
        {
            value: "9",
            label: "九月"
        },
        {
            value: "10",
            label: "十月"
        },
        {
            value: "11",
            label: "十一月"
        },
        {
            value: "12",
            label: "十二月"
        }
    ],
    /**
     * @description: 账单状态
     */
    settlementStatus: [
        {
            value: "HAD_PAY",
            label: "已结算"
        },
        {
            value: "WAITING_PAY",
            label: "待结算"
        }
    ],
    /***
     * @description: 工单来源
     */
    SourceArr: [
        {
            value: "STATION_ALARM",
            label: "电站报警"
        },
        {
            value: "OWNER_REPAIR",
            label: "业主报修"
        },
        {
            value: "CUSTOMER_SERVICE",
            label: "客服工单"
        },
        {
            value: "LOW_POWER_STATION",
            label: "低效电站"
        },
        {
            value: "TRIPPING_OPERATION_STATION",
            label: "频繁跳闸工单"
        }
    ],
    /***
     * @description: 工单来源
     */
    detailStatus: [
        {
            value: "ENABLE",
            label: "已完成"
        }
    ],
    /***
     * @description: 工单来源
     */
    detailMode: [
        {
            value: "HR",
            label: "BT"
        },
        {
            value: "YX",
            label: "户用EPC(YX)"
        },
        {
            value: "ZH",
            label: "户用EPC(ZH)"
        },
        {
            value: "EPC",
            label: "工商业EPC"
        }
    ],
    /***
     * @description: 工单来源
     */
    detStationType: [
        {
            value: "COMMON",
            label: "普通户用"
        },
        {
            value: "HOUSEHOLD",
            label: "户用租赁"
        },
        {
            value: "PUB_BUILD",
            label: "公共租赁"
        },
        {
            value: "WHOLE_VILLAGE",
            label: "整村推进"
        }
    ],
    /***
     * @description: 工单来源
     */
    detFieldMethod: [
        {
            value: "COMPANY",
            label: "公司备案"
        },
        {
            value: "HOUSEHOLD",
            label: "户用备案"
        }
    ],
    /***
     * @description: 入库状态
     */
    ifClose: [
        {
            value: "0",
            label: "未关闭"
        },
        {
            value: "1",
            label: "已关闭"
        },
        {
            value: "a",
            label: "提交审批"
        },
        {
            value: "b",
            label: "转库存关单失败"
        }
    ],
    /***
     * @description: 采购订单状态
     */
    procurementOrderType: [
        {
            value: "0",
            label: "初始"
        },
        {
            value: "1",
            label: "已提交"
        },
        {
            value: "2",
            label: "交款成功"
        },
        {
            value: "4",
            label: "审批通过"
        },
        {
            value: "5",
            label: "审批驳回"
        },
        {
            value: "6",
            label: "退款成功"
        },
        {
            value: "7",
            label: "退款失败"
        }
    ],
    /***
     * @description: 申请状态
     */
    advanceStatus: [
        {
            value: "0",
            label: "初始"
        },
        {
            value: "1",
            label: "已提交"
        },
        {
            value: "2",
            label: "关闭"
        }
    ],
    /***
     * @description: 申请类型
     */
    advanceType: [
        {
            value: "0",
            label: "交款"
        },
        {
            value: "1",
            label: "退款"
        }
    ],
    /***
     * @description: 公司类型
     */
    companyTypeList: [
        {
            value: "1",
            label: "上市公司"
        },
        {
            value: "2",
            label: "股份有限公司"
        },
        {
            value: "3",
            label: "有限责任公司"
        },
        {
            value: "4",
            label: "合伙经营"
        },
        {
            value: "5",
            label: "个人独资"
        },
        {
            value: "6",
            label: "个体户"
        },
        {
            value: "7",
            label: "国有企业"
        },
        {
            value: "8",
            label: "集体企业"
        },
        {
            value: "9",
            label: "有限公司"
        }
    ],
    /**
     * @description: 纳税人类型
     */
    taxpayerTypeList: [
        {
            value: "1",
            label: "一般纳税人"
        },
        {
            value: "2",
            label: "小规模纳税人"
        },
        {
            value: "3",
            label: "个体户"
        },
        {
            value: "4",
            label: "个人"
        }
    ],
    /**
     * @description: 分中心
     */
    subCenterList: [
        {
            value: "GFBJ",
            label: "北京"
        },
        {
            value: "GFCC",
            label: "长春"
        },
        {
            value: "GFCD",
            label: "成都"
        },
        {
            value: "GFCQ",
            label: "重庆"
        },
        {
            value: "GFCS",
            label: "长沙"
        },
        {
            value: "GFDL",
            label: "大连"
        },
        {
            value: "GFFZ",
            label: "福州"
        },
        {
            value: "GFGY",
            label: "贵阳"
        },
        {
            value: "GFGZ",
            label: "广州"
        },
        {
            value: "GFHEB",
            label: "哈尔滨"
        },
        {
            value: "GFHF",
            label: "合肥"
        },
        {
            value: "GFHHHT",
            label: "内蒙"
        },
        {
            value: "GFHK",
            label: "海口"
        },
        {
            value: "GFHZ",
            label: "杭州"
        },
        {
            value: "GFJIN",
            label: "济宁"
        },
        {
            value: "GFJN",
            label: "济南"
        },
        {
            value: "GFJZ",
            label: "锦州"
        },
        {
            value: "GFKM",
            label: "昆明"
        },
        {
            value: "GFLZ",
            label: "兰州"
        },
        {
            value: "GFNB",
            label: "宁波"
        },
        {
            value: "GFNC",
            label: "南昌"
        },
        {
            value: "GFNJ",
            label: "南京"
        },
        {
            value: "GFNN",
            label: "南宁"
        },
        {
            value: "GFQD",
            label: "青岛"
        },
        {
            value: "GFSH",
            label: "上海"
        },
        {
            value: "GFSJZ",
            label: "石家庄"
        },
        {
            value: "GFSY",
            label: "沈阳"
        },
        {
            value: "GFSZ",
            label: "深圳"
        },
        {
            value: "GFTJ",
            label: "天津"
        },
        {
            value: "GFTS",
            label: "唐山"
        },
        {
            value: "GFTY",
            label: "太原"
        },
        {
            value: "GFWH",
            label: "武汉"
        },
        {
            value: "GFWX",
            label: "无锡"
        },
        {
            value: "GFXA",
            label: "西安"
        },
        {
            value: "GFXF",
            label: "襄樊"
        },
        {
            value: "GFXJ",
            label: "新疆"
        },
        {
            value: "GFXM",
            label: "厦门"
        },
        {
            value: "GFXN",
            label: "西宁"
        },
        {
            value: "GFXZ",
            label: "徐州"
        },
        {
            value: "GFYC",
            label: "银川"
        },
        {
            value: "GFYT",
            label: "烟台"
        },
        {
            value: "GFZZ",
            label: "郑州"
        }
    ],
    ospMarginType: [
        {
            value: "ADD",
            label: "交押金"
        },
        {
            value: "SUBTRACT",
            label: "扣押金"
        }
    ],
    payChannel: [
        {
            value: "TRANSFER",
            label: "转账汇款"
        },
        {
            value: " ALIPAY",
            label: "支付宝"
        },
        {
            value: " WECHAT",
            label: "微信"
        }
    ],
    payStatus: [
        {
            value: "TO_PAY",
            label: "待支付"
        },
        {
            value: "WAIT",
            label: "待确认"
        },
        {
            value: "REJECT",
            label: "已驳回"
        },
        {
            value: "CONFIRMED",
            label: "已确认"
        }
    ],
    /**
     * @description: 合同类型
     */
    typeList: [
        {
            value: "OP_MASTER",
            label: "主合同"
        },
        {
            value: "OP_AUTHORITY",
            label: "服务区域授权补充协议合同"
        },
        {
            value: "OP_NEW_MASTER",
            label: "新政策主合同"
        }
    ],
    dutyList: [
        {
            value: "OPERATIONS_ENGINEER",
            label: "运维工程师"
        },
        {
            value: "OPERATIONS_SUPERVISOR",
            label: "运维主管"
        },
        {
            value: "OPERATION_SCHEDULING",
            label: "运维调度"
        }
    ],
    identityType: {
        1: "户用运维",
        2: "工商业运维",
        3: "户用运维 工商业运维"
    },
    /**
     * @description: 运维知识库
     */
    //运维知识库类型
    knowledgeType: {
        OPERATIONGUIDANCE: "运维指导",
        TROUBLESHOOTING: "故障排除",
        OPERATIONCASES: "运维案例",
        INEFFICIENTIMPROVEMENT: "低效改善",
        FAULTCODES: "故障代码"
    },
    //运维知识库业务类别
    businessType: {
        0: "建站",
        1: "运维"
    },
    changeDataStatus: [
        {
            value: "WAITE_AUDIT",
            label: "待中心经理审核"
        },
        {
            value: "AUDIT_REJECT",
            label: "审核驳回"
        },
        {
            value: "WITH_TECHNICAL_ACCEPTANCE",
            label: "待技术验收"
        },
        {
            value: "TECHNICAL_ACCEPTANCE_REJECT",
            label: "技术验收驳回"
        },
        {
            value: "TECHNICAL_ACCEPTANCE_OK",
            label: "验收通过"
        },
        {
            value: "STOP",
            label: "流程终止"
        }
    ],
    recommendation: [
        {
            value: "AUDIT_OK",
            label: "审核通过"
        },
        {
            value: "AUDIT_REJECT",
            label: "审核驳回"
        },
        {
            value: "ACCEPTANCE_OK",
            label: "验收通过"
        },
        {
            value: "ACCEPTANCE_REJECT",
            label: "验收驳回"
        }
    ],
    infoCategory: [
        {
            value: "OPERATION",
            label: "光伏运维"
        }
    ],
    changeInverter: [
        {
            value: "INVERTER_CHANGE",
            label: "更改逆变器序列号"
        }
    ],
    infoContent: [
        {
            value: "CREATE",
            label: "创建提报信息"
        },
        {
            value: "AUDIT",
            label: "平台审核"
        },
        {
            value: "ACCEPTANCE",
            label: "技术验收"
        },
        {
            value: "UPDATE",
            label: "提报信息更新"
        },
        {
            value: "STOP",
            label: "流程终止"
        },
        {
            value: "AUDIT_REJECT",
            label: "审核驳回"
        }
    ],
    pmHouseTypeList: {
        flat: "平顶屋",
        slope: "斜顶屋",
        ground: "院内地面",
        hwdm: "户外地面",
        yghb: "渔光互补",
        nghb: "农光互补",
        ygf: "阳光房",
        epcSteel: "彩钢瓦",
        epcBeton: "混凝土",
        epcFloor: "地面",
        double_slope: "双面坡",
        enterprise_one: "企业厂房-彩钢瓦屋顶"
    },
    pmInstallList: {
        epcFixture: "夹具",
        epcGroundSupport: "地面支架",
        epcPrefabricate: "预制基础",
        enterprise_one_install: "厂房-彩钢瓦-平铺",
        flatSun: "平顶屋-阳光屋顶",
        flatToBolt: "平屋顶-平改坡（膨胀螺栓固定）",
        flatToBase: "平屋顶-平改坡（底梁固定）",
        flatToWeight: "平屋顶-平改坡（配重块）",
        flatBolt: "平屋顶-膨胀螺栓固定",
        flatWeight: "平屋顶-配重块",
        groundPileFix: "院内地面-柱桩固定",
        waterFloating: "水面漂浮",
        pileFilx: "柱桩固定",
        slopePull: "斜屋顶-前拉后拽",
        slopePullShort: "斜屋顶-前拉后拽+短柱",
        sunlightRoomFix: "阳光房-化学锚栓固定/膨胀螺栓",
        slopeHook: "斜屋顶-挂钩固定安装",
        slope_one: "双面坡-南北坡-前拉后拽",
        slope_two: "双面坡-南北坡-前拉后拽+短柱",
        slope_three: "双面坡-南北坡-双坡挂钩",
        slope_four: "双面坡-南北坡-双坡挂钩+水槽",
        slope_five: "双面坡-东西坡-前拉后拽",
        slope_six: "双面坡-东西坡-前拉后拽+短柱",
        slope_seven: "双面坡-东西坡-双坡挂钩",
        slope_eight: "双面坡-东西坡-双坡挂钩+水槽",
        slope_nine: "双面坡-南北坡-前拉后拽-N",
        slope_ten: "双面坡-南北坡-双坡挂钩-N"
    },
    filingMethod: {
        COMPANY: "公司备案",
        HOUSEHOLD: "户用备案"
    },
    specialAuditStatusList: [
        {
            value: "WAIT_PRELIMINARY_AUDI",
            label: "待初审"
        },
        {
            value: "PRELIMINARY_AUDI_REJECT",
            label: "初审驳回"
        },
        {
            value: "WAIT_FIRST_AUDIT",
            label: "待一审"
        },
        {
            value: "FIRST_AUDIT_REJECT",
            label: "一审驳回"
        },
        {
            value: "WAIT_SECOND_AUDIT",
            label: "待二审"
        },
        {
            value: "SECOND_AUDIT_REJECT",
            label: "二审驳回"
        },
        {
            value: "SECOND_AUDIT_PASS",
            label: "二审通过"
        },
        {
            value: "WAIT_THIRD_AUDIT",
            label: "待三审"
        },
        {
            value: "THIRD_AUDIT_REJECT",
            label: "三审驳回"
        },
        {
            value: "THIRD_AUDIT_PASS",
            label: "三审通过"
        }
    ],
    specialTypeList: [
        {
            value: "POSITIVE_ONE",
            label: "低效整改激励"
        },
        {
            value: "POSITIVE_TWO",
            label: "低效占比激励"
        },
        {
            value: "POSITIVE_NOTIFICATION",
            label: "正激励通报"
        },
        {
            value: "POSITIVE_THREE",
            label: "一单一议"
        }
    ]
};
