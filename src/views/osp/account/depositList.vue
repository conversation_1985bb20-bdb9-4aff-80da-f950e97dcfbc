<template>
  <div class="cus_list">
    <Breadcrumb class="bread">
      <Breadcrumb-item to="/index/">光伏运维管理</Breadcrumb-item>
      <Breadcrumb-item to="/depositList">账户管理</Breadcrumb-item>
      <Breadcrumb-item>备件保证金</Breadcrumb-item>
    </Breadcrumb>
    <div class="list_son">
      <div class="list_but">
        <div class="deposit">
          <p>
            <Icon type="ios-information-circle-outline" size="20" color="#c7003a" /> 当前保证金余额<em class="bail">{{ depositBalance }}</em
            >元
            <Button type="primary" @click="jumpto('/depositList/bailPay/')" >缴纳保证金</Button>
          </p>
        </div>
      </div>
      <div class="commListDiv">
        <table class="cus_table" border="0" cellspacing="0" cellpadding="0" width="100%">
          <tr>
            <th align="left">流水号</th>
            <th align="center">交易概述</th>
            <th align="center">类型</th>
            <th align="center">金额</th>
            <th align="center">支付方式</th>
            <th align="center">交易时间</th>
            <th align="center">入账时间</th>
            <th align="center">状态</th>
          </tr>
          <tbody v-for="(item, index) in commList" :key="index">
            <tr>
              <td align="left" width="120">{{ item.depositNo }}</td>
              <td align="center" width="100">{{ item.tradeDesc }}</td>
              <td align="center" width="100">{{ bailType[item.type] }}</td>
              <td align="center" width="100">{{ item.amount }}</td>
              <td align="center" width="100">{{ payChannel[item.payChannel] }}</td>
              <td align="center" width="200">{{ item.createdAt.replace("T", " ") }}</td>
              <td align="center" width="200">{{ item.confirmAt ? item.confirmAt.replace("T", " ") : "--" }}</td>
              <td align="center" width="100">{{ payStatus[item.status] }}</td>
            </tr>
          </tbody>
          <tbody v-if="!commList.length">
            <tr>
              <td colspan="8" align="center">暂无数据</td>
            </tr>
          </tbody>
        </table>
      </div>
      <Page style="margin: 20px" @on-change="changeCurrent" :current="pageNum" :total="totalElements" show-total show-elevator />
    </div>
  </div>
</template>
<script>
import _data from "@/views/apv/_edata"; // 数据字典
import API from "@/api/apv";

export default {
  name: "depositList",
  data() {
    return {
      depositBalance: 0,
      pageNum: 1,
      totalElements: 0,
      commList: [],
      bailType: _data.bailType,
      payChannel: _data.payChannel,
      payStatus: _data.payStatus,
    };
  },
  mounted() {
    this.depositData();
    this.queryList();
  },
  methods: {
    jumpto(path) {
      this.$router.push({
        path,
      });
    },

    // 切换分页
    changeCurrent(curr) {
      this.pageNum = curr;
      this.getCommList();
    },

    // 查询按钮
    queryList() {
      this.commList = [];
      this.pageNum = 1;
      this.getCommList();
    },

    // 获取保证金
    depositData() {
      API.getDeposit().then((res) => {
        this.depositBalance = res.data.depositBalance;
      });
    },

    // 设备列表
    getCommList() {
      let data = {
        pageNum: this.pageNum,
        pageSize: 10,
      };
      API.depositList(data).then((res) => {
        if (res.data.totalElements) {
          this.totalElements = res.data.totalElements;
          this.commList = res.data.content;
        } else {
          this.commList = [];
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
@import "style/_cus_list.scss";

$_boder: 1px solid $border-color-1;

.list_but {
  padding: 0 20px !important;

  .deposit {
    padding: 20px;
    background-color: $background-main;
    border-radius: 3px;
    border: $_boder;

    p {
      line-height: 2;
      font-size: 16px;
      padding-bottom: 10px;
      border-bottom: $_boder;
    }

    .bail {
      border-left: $_boder;
      margin: 0 10px;
      padding-left: 10px;
      color: $color-main;
      font-size: 18px;
    }
  }
}
</style>
