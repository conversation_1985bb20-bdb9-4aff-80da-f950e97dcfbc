<template>
  <div class="orderPay">
    <Breadcrumb class="bread">
      <Breadcrumb-item to="/index/">新能源管理</Breadcrumb-item>
      <Breadcrumb-item to="/depositList">账户管理</Breadcrumb-item>
      <Breadcrumb-item>支付保证金</Breadcrumb-item>
    </Breadcrumb>
    <div class="content">
      <p class="cus_title">支付保证金</p>
      <div class="success-sub">
        <Form class="form" ref="form" :model="formObj" label-position="left" :label-width="120">
          <FormItem label="用户账号">
            {{ company }}
          </FormItem>
          <FormItem label="保证金金额">
            <RadioGroup v-model="radioCheck">
              <Radio label="300000" class="radio">
                <span>300,000.00元</span>
              </Radio>
              <Radio label="500000" class="radio">
                <span>500,000.00元</span>
              </Radio>
              <Radio label="1000000" class="radio">
                <span>1000,000.00元</span>
              </Radio>
              <br />
              <Radio label="0" class="radio" style="margin-top: 10px"> <Input v-model="formObj.amount" placeholder="其他金额" maxlength="9" style="width: 120px" /> 元 </Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="付款方式">
            <RadioGroup v-model="radioPay">
              <Radio disabled label="ONLINE" class="radio">
                <span>在线支付</span>
              </Radio>
              <Radio label="TRANSFER" class="radio">
                <span>转账汇款</span>
              </Radio>
            </RadioGroup>
          </FormItem>
          <div class="transfer" v-if="radioPay === 'TRANSFER'">
            <div class="transfer_title"><Icon type="md-alert" color="#666" style="margin-right: 5px" />请通过线下转账方式支付</div>
            <p>银行：中国建设银行股份有限公司青岛海尔路支行</p>
            <p>户名：青岛海尔光伏新能源有限公司</p>
            <p>账号：37150198551000001771</p>
            <p style="margin-top: 15px">
              <Upload class="inlinebox" :action="actionUrl" :headers="headers" :on-success="fileUploadSuccess" :format="['jpg', 'jpeg', 'png', 'pdf']" :max-size="102400" :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize">
                <Button>上传打款凭证</Button>
              </Upload>
              <a class="file_pics" v-if="formObj.transferVoucher" :href="formObj.transferVoucher" target="_blank">
                <img :src="formObj.transferVoucher" width="100px" />
              </a>
            </p>
          </div>
        </Form>
      </div>
      <div style="margin: 50px 0; text-align: center"><Button type="success" size="large" style="width: 200px" @click="debounce(pay)">确认支付</Button></div>
      <!-- <div class="tip_box">
                <p>1. 默认在线支付</p>
                <p>2. 选择在线支付，不显示下面账号信息，点击确认充值，跳转在线支付页面，支付完成后充值</p>
                <p>3. 选择转账汇款，显示下面账号信息，线下打款后上传付款凭证，点击确认充值，待海阳财务审核</p>
            </div> -->
    </div>
  </div>
</template>
<script>
import API from "@/api/osp";

export default {
  name: "bailPay",
  data() {
    return {
      zoneId: "", // 服务区域保证金传参
      company: "",
      radioCheck: "300000",
      radioPay: "TRANSFER",
      formObj: {
        amount: "",
        transferVoucher: "",
      },
      actionUrl: `${process.env.VUE_APP_BASE_API}/merchant/oss/image/upload.do`,
      headers: { Authorization: `Bearer ${JSON.parse(window.localStorage.getItem("logins")).access_token}` },
    };
  },

  created() {
    this.zoneId = this.$route.query?.id || "";
  },

  mounted() {
    this.spInfo();
  },
  methods: {
    // 文件上传
    fileUploadSuccess(response, file, fileList) {
      this.formObj.transferVoucher = response.result[0].imageUrl;
    },

    handleFormatError(file) {
      this.$Notice.warning({
        title: "上传格式不正确",
        desc: "请上传正确的格式文件",
      });
    },

    handleMaxSize(file) {
      this.$Notice.warning({
        title: "上传图片过大",
        desc: file.name + "图片不应超过 20M，请压缩后上传.",
      });
    },

    // 服务商信息
    spInfo() {
      API.getSpInfo().then((res) => {
        if (res.data.success) {
          this.company = res.data.result.name;
        } else {
          this.company = "";
          this.$Message.error(res.data.error);
        }
      });
    },

    // 转账付款
    pay() {
      let datas = {
        zoneId: this.zoneId,
        amount: this.radioCheck === "0" ? this.formObj.amount : this.radioCheck,
        payChannel: this.radioPay,
        transferVoucher: this.formObj.transferVoucher,
      };
      let amout = Number(datas.amount);
      if (amout <= 0) {
        this.$Message.error("不低于 0 元！");
      } else {
        API.addDeposit(datas).then((res) => {
          if (res.data.success) {
            this.$Message.success("提交支付成功");
            this.$router.push("/apv/depositList");
          } else {
            this.$Message.error(res.data.error);
          }
        });
      }
    },
  },
};
</script>
<style lang="scss">
@import "@/style/orderPay.scss";

$_boder: 1px solid $border-color-1;

.cus_title {
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  line-height: 2.4;
  font-size: 16px;
  font-weight: bolder;
  border-bottom: 1px solid #f5f7f9;
  &::before {
    margin-right: 8px;
    content: "";
    width: 4px;
    height: 16px;
    background: #03b8cc;
    border-radius: 3px;
  }
}

.form {
  margin-top: 20px;
  padding: 0 20px;

  .radio {
    margin-right: 20px;
  }
}

.transfer {
  padding: 20px 0;
  border-top: 1px solid #eee;
  font-size: 14px;
  line-height: 2;

  .transfer_title {
    @extend .flex-row-center;
    display: inline-flex;
    font-weight: bolder;
    margin-bottom: 10px;
    padding: 5px 0;
  }

  p {
    //padding-left: 20px;
  }
}

.file_pics {
  @extend .flex-row-center-center;
  width: 100px;
  border-radius: 8px;
  cursor: pointer;
  overflow: hidden;
}

.tip_box {
  color: #999;
  line-height: 2;
}
</style>
