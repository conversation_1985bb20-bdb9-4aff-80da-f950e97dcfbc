<template>
    <div class="main">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">首页</Breadcrumb-item>
            <Breadcrumb-item to="/apv/apvReg">光伏运维管理</Breadcrumb-item>
            <Breadcrumb-item>入驻</Breadcrumb-item>
        </Breadcrumb>
        <div class="cus_reg">
            <div class="step_div" style="width: 80%;">
                <Steps :current="stepKey">
                    <Step title="信息提报"></Step>
                    <Step title="信息审核"></Step>
                    <Step title="企业资质审核"></Step>
                    <Step title="有效服务兵"></Step>
                    <Step title="入驻声明"></Step>
                    <Step title="开始运营"></Step>
                </Steps>
            </div>
            <!-- 信息提报 -->
            <div class="cus_form" v-if="[-1, 4, 12].includes(status)">
                <p class="cus_title">营业执照信息</p>
                <!-- [2, 4, 12].includes(licenseObj.status)?true:false" -->
                <Form class="form" ref="licenseObj" :model="licenseObj" :label-width="200">
                    <FormItem label="公司名称" prop="name" :rules="{ required: true, message: ' ' }">
                        <Input :disabled="!!shuDate.name" class="item_large" type="text" maxlength="50"
                            v-model="licenseObj.name" placeholder="请填写营业执照上的公司名称" />
                    </FormItem>
                    <FormItem label="类型" prop="companyType" :rules="{ required: true, message: ' ' }">
                        <Select :disabled="!!shuDate.companyType" v-model="licenseObj.companyType"
                            style="width:120px;margin-right:5px;">
                            <Option v-for="(item, index) in companyTypeList" :value="item.value" :key="index">
                                {{ item.label
                                }}
                            </Option>
                        </Select>
                    </FormItem>
                    <FormItem label="营业执照编号" prop="serial" :rules="{ required: true, message: ' ' }">
                        <Input :disabled="!!shuDate.businessLicenceNo" class="item_large" type="text" maxlength="18"
                            v-model="licenseObj.serial" placeholder="请填写统一社会信用代码或营业执照编号" />
                    </FormItem>
                    <FormItem label="法人代表" prop="legal" :rules="{ required: true, message: ' ' }">
                        <Input :disabled="!!shuDate.legalRepresentative" class="item_small" type="text" maxlength="10"
                            v-model="licenseObj.legal" placeholder="请填写法定代表人" />
                    </FormItem>
                    <FormItem label="纳税人类型" prop="taxpayerType" :rules="{ required: true, message: ' ' }">
                        <Select :disabled="!!shuDate.taxpayerType" v-model="licenseObj.taxpayerType"
                            style="width:120px;margin-right:5px;">
                            <Option v-for="(item, index) in taxpayerTypeList" :value="item.value" :key="index">
                                {{ item.label
                                }}
                            </Option>
                        </Select>
                    </FormItem>
                    <FormItem label="公司地址" required>
                        <!-- {{ licenseObj.cityId }} -->
                        <Select :disabled="!!shuDate.provinceId" v-model="licenseObj.provinceId"
                            @on-change="typeOneChange" style="width:120px;margin-right:5px;">
                            <Option v-for="(item, index) in addressOneList" :value="item.id" :key="index">{{ item.name
                                }}
                            </Option>
                        </Select>
                        <Select :disabled="!!shuDate.cityId" v-model="licenseObj.cityId" @on-change="typeTwoChange"
                            style="width:120px;margin-right:5px;">
                            <Option v-for="(item, index) in addressTwoList" :value="item.id" :key="index">{{ item.name
                                }}
                            </Option>
                        </Select>
                        <Select :disabled="!!shuDate.regionId" v-model="licenseObj.regionId" style="width:120px;">
                            <Option v-for="(item, index) in addressThreeList" :value="item.id" :key="index">{{ item.name
                                }}
                            </Option>
                        </Select>
                    </FormItem>
                    <FormItem label="" prop="address" :rules="{ required: true, message: ' ' }">
                        <Input :disabled="!!shuDate.address" class="item_large" type="text" maxlength="50"
                            v-model="licenseObj.address" placeholder="详细地址" />
                    </FormItem>
                    <FormItem label="邮政编码" prop="zipCode" :rules="{ required: true, message: ' ' }">
                        <Input :disabled="!!shuDate.zipCode" class="item_large" type="text" maxlength="6"
                            v-model="licenseObj.zipCode" placeholder="请填写公司当地邮政编码" />
                    </FormItem>
                    <FormItem label="注册资本" prop="registeredCapital" :rules="{ required: true, message: ' ' }">
                        <Input :disabled="!!shuDate.registeredCapital" class="item_large" type="text" maxlength="6"
                            v-model="licenseObj.registeredCapital" placeholder="请填写营业执照注册资本,单位万元" />
                        万元
                    </FormItem>
                    <FormItem label="成立日期" required>
                        <DatePicker :disabled="!!shuDate.companyStartAt" :value="licenseObj.esTime" type="date"
                            placeholder="成立日期" @on-change="data => {
                                return selectDate(data, 'esTime');
                            }
                                " style="width:30%"></DatePicker>
                    </FormItem>
                    <FormItem label="营业执照有效起止日期" required>
                        <DatePicker :disabled="!!shuDate.businessLicenceStartAt" type="date"
                            :value="licenseObj.startTime" placeholder="起始日期" @on-change="data => {
                                return selectDate(data, 'startTime');
                            }" style="width:150px;margin-right: 5px;">
                        </DatePicker>
                        <DatePicker :disabled="!!shuDate.businessLicenceEndAt" type="date" :value="licenseObj.endTime"
                            style="width:150px" placeholder="截止日期" @on-change="data => {
                                return selectDate(data, 'endTime');
                            }">
                        </DatePicker>
                    </FormItem>
                    <FormItem label="营业执照" required>
                        <Upload :action="actionUrl" :headers="headers" :show-upload-list="false" :on-success="(response, file, fileList) => {
                            return fileUploadSuccess(response, file, fileList, 'licenseObj');
                        }
                            " :on-progress="progress" :format="['jpg', 'jpeg', 'png', 'gif']" :max-size="40960"
                            :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize">
                            <Button :disabled="!!shuDate.businessLicenceImg" icon="md-cloud-upload">上传扫描件
                            </Button>
                            <span class="tips" style="margin-left: 20px;">
                                <Icon type="md-information-circle" />
                                支持PNG，JPG，GIF格式，建议上传尺寸 800*600
                            </span>
                        </Upload>
                        <a v-if="licenseObj.imageUrl" :href="licenseObj.imageUrl" target="_blank">
                            {{ licenseObj.imageUrl }}
                        </a>
                    </FormItem>
                    <FormItem label="联系人" prop="consignee" :rules="{ required: true, message: ' ' }">
                        <Input :disabled="!!shuDate.consignee" class="item_large" type="text" maxlength="10"
                            v-model="licenseObj.consignee" placeholder="请填写联系人" />
                    </FormItem>
                    <FormItem label="联系人手机号" prop="consigneeMobile" :rules="{ required: true, message: ' ' }">
                        <Input :disabled="!!shuDate.consigneeMobile" class="item_large" type="text" maxlength="20"
                            v-model="licenseObj.consigneeMobile" placeholder="请填写联系人手机号" />
                    </FormItem>
                    <FormItem label="联系人邮箱" prop="email" :rules="{ required: true, message: ' ' }">
                        <Input :disabled="!!shuDate.email" class="item_large" type="text" maxlength="30"
                            v-model="licenseObj.email" placeholder="请填写联系人邮箱" />
                    </FormItem>
                </Form>
                <p class="cus_title">开户许可证</p>
                <Form class="form" ref="bankObj" :model="bankObj" :label-width="200">
                    <FormItem label="银行账户名" prop="bankAccountName" :rules="{ required: true, message: ' ' }">
                        <Input :disabled="!!shuDate.bankAccountName" class="item_large" type="text" maxlength="20"
                            v-model="bankObj.bankAccountName" placeholder="请填写银行账户名" />
                    </FormItem>
                    <FormItem label="银行账号" prop="bankAccount" :rules="{ required: true, message: ' ' }">
                        <Input :disabled="!!shuDate.bankAccount" class="item_large" type="text" maxlength="30"
                            v-model="bankObj.bankAccount" placeholder="请填写银行账号" />
                    </FormItem>
                    <FormItem label="开户银行" prop="bankName" :rules="{ required: true, message: ' ' }">
                        <Select :disabled="!!shuDate.bankName" v-model="bankObj.bankName" :label-in-value="true"
                            @on-change="bankChange" style="width:120px;margin-right:5px;">
                            <Option v-for="(item, index) in bankTypeList" :value="item.name" :key="index">{{ item.name
                                }}
                            </Option>
                        </Select>
                    </FormItem>
                    <FormItem label="开户支行" required>
                        <Select :disabled="!bankObj.bankName" v-model="bankObj.bankProvinceCode" :label-in-value="true"
                            @on-change="bankOneChange" style="width:120px;margin-right:5px;">
                            <Option v-for="(item, index) in houseBankOneList" :value="item.code" :key="index">
                                {{ item.name }}
                            </Option>
                        </Select>
                        <Select :disabled="!bankObj.bankName" v-model="bankObj.bankCityCode" :label-in-value="true"
                            @on-change="bankTwoChange" style="width:120px;margin-right:5px;">
                            <Option v-for="(item, index) in houseBankTwoList" :value="item.code" :key="index">
                                {{ item.name }}
                            </Option>
                        </Select>
                        <Select :disabled="!bankObj.bankName" v-model="bankObj.bankRegionCode" :label-in-value="true"
                            @on-change="bankThreeChange" style="width:120px;">
                            <Option v-for="(item, index) in houseBankThreeList" :value="item.code" :key="index">
                                {{ item.name }}
                            </Option>
                        </Select>
                    </FormItem>
                    <FormItem label="" required>
                        <Select :disabled="!bankObj.bankName" v-model="bankObj.bankNumber" :label-in-value="true"
                            @on-change="houseBankChange" style="width:300px;" filterable :remote-method="remoteMethod2"
                            :loading="loading2" loading-text="搜索中" clearable>
                            <Option v-for="(item, index) in houseBankList" :value="item.bankNumber" :key="index">
                                {{ item.name }}
                            </Option>
                        </Select>
                    </FormItem>
                    <FormItem label="开户银行许可证" required>
                        <Upload :action="actionUrl" :headers="headers" :show-upload-list="false" :on-success="(response, file, fileList) => {
                            return fileUploadSuccess(response, file, fileList, 'bankObj');
                        }
                            " :on-progress="progress" :max-size="40960" :format="['jpg', 'jpeg', 'png', 'gif']"
                            :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize">
                            <Button icon="md-cloud-upload" :disabled="!!bankObj.imageUrl">上传扫描件</Button>
                            <span class="tips" style="margin-left: 20px;">
                                <Icon type="md-information-circle" />
                                支持PNG，JPG，GIF格式，建议上传尺寸 800*600
                            </span>
                        </Upload>
                        <a v-if="bankObj.imageUrl" :href="bankObj.imageUrl" target="_blank">{{ bankObj.imageUrl }}</a>
                    </FormItem>
                </Form>
                <p class="cus_title">服务信息</p>
                <Form class="form" :model="licenseObj" :label-width="200" style="padding-bottom: 0;">
                    <FormItem label="所属分中心" prop="subCenterCode" :rules="{ required: true, message: ' ' }">
                        <Select v-model="licenseObj.subCenterCode" label-in-value @on-change="subCenterChange"
                            style="width:120px;margin-right:5px;">
                            <Option v-for="item in subCenterCodeList" :value="item.value" :key="item.value">
                                {{ item.label }}
                            </Option>
                        </Select>
                    </FormItem>
                    <FormItem label="资质图片">
                        <Upload :action="actionUrl" :headers="headers" :show-upload-list="false" :on-success="(response, file, fileList) => {
                            return fileUploadSuccess(response, file, fileList, 'certificationImg');
                        }
                            " :max-size="40960" :on-progress="progress" :format="['jpg', 'jpeg', 'png', 'gif']"
                            :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize">
                            <Button icon="md-cloud-upload">上传扫描件</Button>
                        </Upload>
                        <a v-if="licenseObj.certificationImg" :href="licenseObj.certificationImg" target="_blank">
                            {{ licenseObj.certificationImg }}
                        </a>
                        <p class="tips" style="text-align: left;">
                            <Icon type="md-information-circle" />
                            上传光伏资质或行业内相关企业合作经验<br>支持PNG，JPG，GIF格式，建议上传尺寸 800*600
                        </p>
                    </FormItem>
                </Form>
                <Form class="form" :label-width="200" style="padding-top: 0;">
                    <FormItem label="服务区域" required>
                        <div class="addressDiv">
                            <div v-for="(it, ind) in AreaChange.regionList.slice(0, 17)" :key="ind" class="region-item">
                                <Select v-model="it.serviceProvinceId" placement="top"
                                    @on-change="(item) => { AreaChange.typeOneChangeCommon(item, ind) }"
                                    style="width:120px;margin-right:5px;">
                                    <Option v-for="(item, index) in AreaChange.addressOneList" :value="item.id"
                                        :key="index">
                                        {{ item.name }}
                                    </Option>
                                </Select>
                                <Select v-model="it.serviceCityId" placement="top"
                                    @on-change="(item) => AreaChange.typeTwoChangeCommon(item, ind)"
                                    style="width:120px;margin-right:5px;">
                                    <Option v-for="(item, index) in it.addressTwoList" :value="item.id" :key="index">
                                        {{ item.name }}
                                    </Option>
                                </Select>
                                <Select v-model="it.serviceAreaId" placement="top" style="width:120px;">
                                    <Option v-for="(item, index) in it.addressThreeList" :value="item.id" :key="index">
                                        {{ item.name }}
                                    </Option>
                                </Select>
                                <Button class="btn" v-if="ind" :disabled="AreaChange.regionList.length <= 1"
                                    @click="AreaChange.deleteRegion(ind)">-
                                </Button>
                                <Button class="btn" v-if="ind === AreaChange.regionList.length - 1"
                                    :disabled="!it.serviceAreaId || AreaChange.regionList.length >= 17"
                                    @click="AreaChange.addRegion(ind)">+
                                </Button>
                            </div>
                        </div>

                    </FormItem>
                </Form>
                <div class="res_btn">
                    <Button type="success" size="large" style="width: 200px;" @click="debounce(register)">
                        {{ status >= 0 ?
                            "修改提交" : "申请合作" }}
                    </Button>
                </div>
            </div>
            <!-- 信息审核 -->
            <div class="msg flex-column-center" v-if="[0, 11].includes(status)">
                <div class="flex-row-center">
                    <Icon color="#19be6b" size="36" type="ios-checkmark-circle" style="margin-right: 10px" />
                    <span class="msg_title">提交成功</span>
                </div>
                <p class="msg_tip">您申请资料已收到，工作人员将在3个工作日内主动联系您，请您保持手机通畅。</p>
            </div>
            <!-- 企业资质审核 -->
            <div class="contract" v-if="stepKey === 2">
                <qualifications :stationInfo="shuDate" />
            </div>
            <!-- 有效服务兵 -->
            <div class="contract" v-if="stepKey === 3">
                <p class="cus_title">服务人员</p>
                <Tabs type="card" :animated="false" @on-click="tabChange">
                    <TabPane label="人员管理">
                        <AllPersonnel @dataReceived="handleDataReceived" />
                    </TabPane>
                    <TabPane label="上岗资格管理">
                        <Information ref="childRef" />
                    </TabPane>
                </Tabs>
            </div>
            <!-- 入驻声明 -->
            <div class="contract" v-if="stepKey === 4">
                <p class="cus_title">入驻声明</p>
                <Tabs type="card" :animated="false" @on-click="tabChange">
                    <TabPane label="电签认证">
                        <Certification ref="certification" companyType="OPERATION_PROVIDER" />
                    </TabPane>
                    <TabPane label="签订合同">
                        <div class="flex-column-center">
                            <div>
                                <iframe id="iframe" class="pdf" :src="pdfUrl" width="100%" height="600"
                                    frameborder="0"></iframe>
                            </div>
                            <Checkbox v-model="contractConfirm" style="margin-top: 30px">已查看并同意入驻声明</Checkbox>
                        </div>
                        <div class="res_btn">
                            <Button type="primary" size="large" @click="confirmContract" style="width: 200px">同意并入驻
                            </Button>
                        </div>
                    </TabPane>
                </Tabs>
            </div>
            <!-- 开始运营 -->
            <div class="msg flex-column-center" v-if="stepKey === 5">
                <div class="flex-column-center">
                    <img src="https://cdn01.rrsjk.com/images/acf6bc8c-4c53-4fcc-86b6-dceb72f6b05e.png" width="200"
                        height="200">
                    <br />
                    <span class="msg_title">开始运营</span>
                </div>
                <p class="msg_tip">您已同意入驻声明，正在运营中</p>
                <Button type="info" @click="viewSpContract">查看声明</Button>
            </div>
        </div>
    </div>
</template>
<script>
import mixins from "@/views/apv/_mixins.js";
import _data from "../_edata.js"; // 数据字典
import { getUserInfo, getRegionList } from "@/api";
import AreaChange from "@/utils/ospAreaChange.js";
import OSP, { opSignMaster, previewOpContract, registTp, contractStatus } from "@/api/osp";
import Certification from "@/components/units/certification.vue";
import AllPersonnel from "@/components/serviceStaff/allPersonnel.vue";
import Information from "@/components/serviceStaff/information.vue";
import qualifications from "@/components/serviceStaff/qualifications.vue";

export default {
    name: "ospReg",
    mixins: [mixins],   // * 单页面混入
    data() {
        return {
            AreaChange,
            // refreshInformation: false,
            stepKey: -1,
            status: null,
            businessType: null, // 1 光伏绿能 2 光伏工商业；
            contractConfirm: false,
            contractId: '',
            actionUrl: `${process.env.VUE_APP_BASE_API}/merchant/oss/image/upload.do`,
            headers: { Authorization: `Bearer ${JSON.parse(window.localStorage.getItem("logins")).access_token}` },
            // 营业执照信息
            licenseObj: {
                id: "",
                name: "",
                companyType: "",
                serial: "",
                legal: "",
                taxpayerType: "",
                provinceId: "",
                cityId: "",
                regionId: "",
                address: "",
                zipCode: "",
                registeredCapital: "",
                esTime: "",
                startTime: "",
                endTime: "",
                imageUrl: "",
                consignee: "",
                consigneeMobile: "",
                email: "",
                subCenterCode: "",    //  优化需求新增 22/03/29
                subCenterName: "",    //  优化需求新增
                certificationImg: "",  //  优化需求新增
                identityType: ""
            },
            // 银行账户
            bankObj: {
                bankAccountName: "",
                bankAccount: "",
                bankProvinceCode: "",
                bankProvinceName: "",
                bankCityCode: "",
                bankCityName: "",
                bankRegionCode: "",
                bankRegionName: "",
                bankName: "",
                bankCode: "",
                bankNo: "",
                bankNumber: "",
                houseBankCode: "",
                houseBankName: "",
                imageUrl: ""
            },
            regionList: [{
                serviceProvinceId: "",
                provinceName: "",
                serviceCityId: "",
                cityName: "",
                serviceAreaId: "",
                regionName: "",
                areaOneList: [],
                areaTwoList: [],
                areaThreeList: []
            }],
            arrdata: [],
            bankTypeList: [],
            addressOneList: [],
            addressTwoList: [],
            addressThreeList: [],
            areaOneList: [],
            areaTwoList: [],
            areaThreeList: [],
            addressList: [],
            pdfUrl: "",
            personnelList: [],
            payObj: {},
            shuDate: [],
            shuDateregionDtoList: {},
            loading2: false,
            companyTypeList: _data.companyTypeList,
            taxpayerTypeList: _data.taxpayerTypeList,
            subCenterCodeList: _data.subCenterList
        };
    },
    components: {
        Certification,
        AllPersonnel,
        Information,
        qualifications
    },
    created() {
        this.payObj = this.$route.params;
        this.getBankList();
        this.getProvinceBank();
        this.getaddressList();
    },

    mounted() {
        // document.addEventListener('visibilitychange', this.handleVisiable)
        this.handleDataReceived();
        AreaChange.getaddressList(this.id, this, false);
    },

    destroyed() {
        // document.removeEventListener('visibilitychange', this.handleVisiable)
    },

    methods: {
        remoteMethod2(query) {
            if (query !== '') {
                this.loading2 = true
                setTimeout(() => {
                    this.loading2 = false
                }, 200)
            }
        },
        handleDataReceived(dataList) {
            this.personnelList = dataList;
        },
        getDataInformation() {
            const childComponent = this.$refs.childRef;
            childComponent.getCommList();

        },
        handleVisiable(e) {
            let that = this;
            if (e.target.visibilityState === "visible") {
                that.spInfo();
                // 2s 后再查一遍
                setTimeout(() => {
                    that.spInfo();
                }, 2000);
            }
        },

        tabChange(e) {
            if (this.stepKey === 3) {
                if (e === 1) {
                    this.getDataInformation();
                }
            } else if (this.stepKey === 4) {
                if (!this.pdfUrl && e === 1) {
                    this.getSpContract();
                }
            }
        },

        subCenterChange(e) {
            this.licenseObj.subCenterName = e.label?.trim();
        },
        spInfo() {
            OSP.getFindId().then(res => {
                //获取信息
                if (res.data.result) {
                    this.cityInit(res.data.result);
                    AreaChange.cityInit(res.data.result);
                    this.shuDate = res.data.result;
                }
                if (res.data.result !== null && res.data.result.status === 6) {
                    contractStatus().then(res => {
                        if (res.data.result === null) {
                            this.status = res.data.result;
                        } else {
                            this.contractId = res.data.result.id
                            this.status = res.data.result.status;
                        }
                        // 状态,0:申请加盟，1：一审通过，2：一审驳回，3:三审通过，4：三审驳回，5：签订合同，6：运营中, 11：二审通过，12：二审驳回，
                        if (this.status === null) {
                            this.status = -1;
                            this.stepKey = 0;
                        } else if ([0, 4, 11, 12].includes(this.status)) {
                            this.stepKey = 1;
                            if ([4, 12].includes(this.status)) {
                                this.$Message.warning({
                                    content: "审核驳回，请修改" + (res.data.result.auditRemark || ""),
                                    duration: 5,
                                    closable: true
                                });
                                this.stepKey = 0;
                                this.cityInit(res.data.result);
                                AreaChange.cityInit(res.data.result);
                            }
                        } else if ([1, 2].includes(this.status)) {
                            this.stepKey = 2;
                        } else if ([7].includes(this.status)) {
                            this.stepKey = 3;
                        } else if ([3, 5].includes(this.status)) {
                            this.stepKey = 4;
                            // this.getSpContract();
                        } else if ([6].includes(this.status)) {
                            this.getContract();
                        }
                    });

                } else {
                    contractStatus().then(res => {
                        // 状态,0:申请加盟，1：一审通过，2：一审驳回，3:三审通过，4：三审驳回，5：签订合同，6：运营中, 11：二审通过，12：二审驳回，
                        if (res.data.success) {
                            if (res.data.result === null) {
                                this.status = res.data.result;
                            } else {
                                this.contractId = res.data.result.id
                                this.status = res.data.result.status;
                            }
                            // this.businessType = res.data.result.businessType
                            if (this.status === null) {
                                this.status = -1;
                                this.stepKey = 0;
                            } else if ([0, 4, 11, 12].includes(this.status)) {
                                this.stepKey = 1;
                                if ([4, 12].includes(this.status)) {
                                    this.$Message.warning({
                                        content: "审核驳回，请修改" + (res.data.result.auditRemark || ""),
                                        duration: 5,
                                        closable: true
                                    });
                                    this.stepKey = 0;
                                    this.cityInit(res.data.result);
                                    AreaChange.cityInit(res.data.result);
                                }
                            } else if ([1, 2].includes(this.status)) {
                                this.stepKey = 2;
                            } else if ([7].includes(this.status)) {
                                this.stepKey = 3;
                            } else if ([3, 5].includes(this.status)) {
                                this.stepKey = 4;
                                // this.getSpContract();
                            } else if ([6].includes(this.status)) {
                                this.getContract();
                            }
                        } else {
                            this.status = -1;
                            this.stepKey = 0;
                        }
                    });
                }
            });
        },
        getContract() {
            if (this.payObj.status === "WAIT_SIGN" && this.payObj.type === "OP_MASTER") {
                this.stepKey = 4;
            } else {
                this.stepKey = 5;
            }
        },
        isNoEmpty(e) {
            return !["", undefined, "null", "undefined", 0, null].includes(e);
        },
        handleRegionList() {
            const regionIdList = [];
            const list = [];
            for (let i = 0; i < AreaChange.regionList.slice(0, 17).length; i++) {
                const item = AreaChange.regionList[i];
                const { serviceProvinceId, serviceCityId, serviceAreaId, addressTwoList, addressThreeList } = item;
                const serviceProvince = this.isNoEmpty(serviceProvinceId) ? AreaChange.addressOneList.filter(item => item.id === serviceProvinceId)[0].name : "";
                const serviceCity = this.isNoEmpty(serviceCityId) ? addressTwoList.filter(item => item.id === serviceCityId)[0].name : "";
                const serviceArea = this.isNoEmpty(serviceAreaId) ? addressThreeList.filter(item => item.id === serviceAreaId)[0].name : "";
                if (!serviceProvince || !serviceCity || !serviceArea) {
                    this.$Message.warning("区域选择不可为空!");
                    return true;
                }
                regionIdList.push(serviceAreaId);
                if (regionIdList.length !== [...new Set(regionIdList)].length) {
                    this.$Message.warning("区域项不可重复!");
                    return true;
                }
                list.push({
                    serviceProvinceId,
                    serviceCityId,
                    serviceAreaId,
                    serviceProvince,
                    serviceCity,
                    serviceArea
                });
            }
            this.arrdata = list.slice(0, 17);
            return false;
        },
        register() {
            if (this.handleRegionList()) return;
            let { licenseObj, bankObj } = this;

            let datas = {
                id: this.contractId ? this.contractId : '',
                address: licenseObj.address,
                bankAccount: bankObj.bankAccount,
                bankAccountName: bankObj.bankAccountName,
                bankCityCode: bankObj.bankCityCode,
                bankCityName: bankObj.bankCityName,
                bankName: bankObj.bankName,
                bankNo: bankObj.bankNo,
                bankNumber: bankObj.bankNumber,
                bankPermitImg: bankObj.imageUrl,
                bankProvinceCode: bankObj.bankProvinceCode,
                bankProvinceName: bankObj.bankProvinceName,
                bankRegionCode: bankObj.bankRegionCode,
                bankRegionName: bankObj.bankRegionName,
                businessLicenceEndAt: licenseObj.endTime.substring(0, 10),
                businessLicenceImg: licenseObj.imageUrl,
                businessLicenceNo: licenseObj.serial,
                businessLicenceStartAt: licenseObj.startTime.substring(0, 10),
                cityId: licenseObj.cityId,
                cityName: this.addressList.city_list[licenseObj.cityId],
                companyStartAt: licenseObj.esTime.substring(0, 10),
                companyType: licenseObj.companyType,
                consignee: licenseObj.consignee,
                consigneeMobile: licenseObj.consigneeMobile,
                email: licenseObj.email,
                houseBankCode: bankObj.houseBankCode,
                houseBankName: bankObj.houseBankName,
                legalRepresentative: licenseObj.legal,
                name: licenseObj.name,
                provinceId: licenseObj.provinceId,
                provinceName: this.addressList.province_list[licenseObj.provinceId],
                regionId: licenseObj.regionId,
                regionName: this.addressList.county_list[licenseObj.regionId],
                registeredCapital: licenseObj.registeredCapital,
                taxpayerType: licenseObj.taxpayerType,
                zipCode: licenseObj.zipCode,
                subCenterCode: licenseObj.subCenterCode,
                subCenterName: licenseObj.subCenterName,
                certificationImg: licenseObj.certificationImg,
                regionDtoList: this.arrdata,
                identityType: "THIRD_SERVICE"
            };

            registTp(datas).then(res => {
                if (res.data.success) {
                    this.$Message.success("提交成功");
                    this.spInfo();
                } else {
                    this.$Message.error(res.data.error);
                }
            });

        },
        valueEdit(res) {
            this.updateLicenseInfo(res);
            this.updateBankInfo(res);
        },

        updateLicenseInfo(res) {
            this.licenseObj.address = res.address;
            this.licenseObj.endTime = res.businessLicenceEndAt;
            this.licenseObj.imageUrl = res.businessLicenceImg;
            this.licenseObj.serial = res.businessLicenceNo;
            this.licenseObj.startTime = res.businessLicenceStartAt;
            this.licenseObj.cityId = String(res.cityId);
            this.licenseObj.esTime = res.companyStartAt;
            this.licenseObj.companyType = String(res.companyType);
            this.licenseObj.consignee = res.consignee;
            this.licenseObj.consigneeMobile = res.consigneeMobile;
            this.licenseObj.email = res.email;
            this.licenseObj.legal = res.legalRepresentative;
            this.licenseObj.name = res.name;
            this.licenseObj.provinceId = String(res.provinceId);
            this.licenseObj.regionId = String(res.regionId);
            this.licenseObj.registeredCapital = res.registeredCapital;
            this.licenseObj.subCenterCode = res.subCenterCode;
            this.licenseObj.subCenterName = res.subCenterName;
            this.licenseObj.certificationImg = res.certificationImg;
            this.licenseObj.taxpayerType = String(res.taxpayerType);
            this.licenseObj.zipCode = res.zipCode;
        },

        updateBankInfo(res) {
            this.bankObj.bankAccount = res.bankAccount;
            this.bankObj.bankAccountName = res.bankAccountName;
            this.bankObj.bankCityCode = String(res.bankCityCode);
            this.bankObj.bankCityName = res.bankCityName;
            this.bankObj.bankName = res.bankName?.trim();
            this.bankObj.bankNo = res.bankNo;
            this.bankObj.bankNumber = res.bankNumber;
            this.bankObj.imageUrl = res.bankPermitImg;
            this.bankObj.bankProvinceCode = String(res.bankProvinceCode);
            this.bankObj.bankProvinceName = res.bankProvinceName;
            this.bankObj.bankRegionCode = String(res.bankRegionCode);
            this.bankObj.bankRegionName = res.bankRegionName;
            this.bankObj.houseBankCode = String(res.houseBankCode);
            this.bankObj.houseBankName = res.houseBankName;
        },

        async cityInit(e) {
            let that = this;
            setTimeout(() => {
                this.typeOneChange(String(e.provinceId));
                this.licenseObj.provinceId = String(e.provinceId);
                this.typeTwoChange(String(e.cityId));
                this.licenseObj.cityId = String(e.cityId);
                this.licenseObj.regionId = String(e.regionId);
            }, 100);
            this.bankChange({ label: String(e?.bankName), value: String(e?.bankName) });
            await this.bankOneChange({ value: String(e.bankProvinceCode), label: e.bankProvinceName });
            await this.bankTwoChange({ value: String(e.bankCityCode), label: e.bankCityName });
            await this.bankThreeChange({ value: String(e.bankRegionCode), label: e.bankRegionName });
            await this.houseBankChange({ value: String(e.bankNumber), label: e.houseBankName });
            this.$nextTick(() => {
                that.valueEdit(e);
            });
        },

        // 文件上传
        fileUploadSuccess(response, file, fileList, refObj) {
            let that = this;
            if (refObj !== 'certificationImg') {
                this[refObj].imageUrl = response.result[0].imageUrl;
            } else {
                this.licenseObj.certificationImg = response.result[0].imageUrl;
            }
            setTimeout(() => {
                that.$Message.destroy();
            }, 500);
        },

        progress() {
            this.$Message.loading({
                content: '上传中...',
                duration: 0
            });
        },

        handleFormatError(file) {
            this.$Notice.warning({
                title: '上传格式不正确',
                desc: '请上传正确的格式文件'
            });
        },

        handleMaxSize(file) {
            this.$Notice.warning({
                title: '上传图片过大',
                desc: file.name + '图片不应超过 20M，请压缩后上传.'
            });
        },

        // 选择日期
        selectDate(data, refObj) {
            this.licenseObj[refObj] = data;
        },

        // 获取省市区
        getaddressList() {
            getRegionList().then(async res => {
                for (let i in res.data.result.province_list) {
                    let obj = {
                        id: i,
                        name: res.data.result.province_list[i]
                    };
                    let arr = []
                    arr.push(obj);
                    this.regionList[0].areaOneList = [...new Set(arr)]
                    this.addressOneList.push(obj);
                    this.areaOneList.push(obj)
                }
                this.addressList = res.data.result;
                this.spInfo()
            });
        },

        // 省改变
        typeOneChange(item) {
            this.addressTwoList = [];
            this.licenseObj.cityId = '';
            this.licenseObj.regionId = '';
            if (!item) return;
            let str = item.substring(0, 2);
            for (let i in this.addressList.city_list) {
                if (str === i.substring(0, 2)) {
                    let obj = {
                        id: i,
                        name: this.addressList.city_list[i]
                    };
                    this.addressTwoList.push(obj);
                }
            }
        },

        // 市变化
        typeTwoChange(item) {
            this.addressThreeList = [];
            this.licenseObj.regionId = '';
            if (!item) return;
            let str = item.substring(0, 4);
            for (let i in this.addressList.county_list) {
                if (str === i.substring(0, 4)) {
                    let obj = {
                        id: i,
                        name: this.addressList.county_list[i]
                    };
                    this.addressThreeList.push(obj);
                }
            }
        },
        getSpContract() {
            previewOpContract().then(res => {
                if (res.data.success) {
                    this.pdfUrl = res.data.result
                } else {
                    this.$Message.error(res.data.error);
                }
            })
        },

        viewSpContract() {
            previewOpContract().then(res => {
                if (res.data.success) {
                    window.open(res.data.result)
                } else {
                    this.$Message.error(res.data.error);
                }
            })
        },

        confirmContract() {
            let that = this
            let { registStatus, authStatus } = this.$refs.certification.itemObj
            if (authStatus !== 'YES' || registStatus !== 'YES') {
                this.$Message.warning('请先完成电签认证');
                return
            }
            if (!this.contractConfirm) {
                this.$Message.warning('请同意后确认');
            } else {
                this.$Message.loading({
                    content: '获取服务中，请稍后...',
                    duration: 0
                });
                opSignMaster().then(res => {
                    this.$Message.destroy()
                    if (res.data.success) {
                        this.$Modal.confirm({
                            title: '提示',
                            content: '即将跳转法大大签署',
                            onOk: () => {
                                window.open(res.data.result)
                            },
                            onCancel: () => { }
                        })
                    } else {
                        this.$Message.error(res.data.error);
                    }
                }).catch(() => {
                    this.$Message.destroy()
                    this.$Message.error("获取服务失败，请重试");
                })
            }
        }
    }
};
</script>
<style scoped lang="scss">
@import '@/style/_cus_reg.scss';

.cus_form {
    .form {
        margin: 0 auto;
        padding: 20px 0;
        width: 800px;
        box-sizing: border-box;

        .item_large {
            width: 300px;
        }

        .item_small {
            width: 200px;
        }

        .tips {
            font-size: 12px;
            color: #999;
        }
    }
}

.msg {
    padding: 10% 0;
    text-align: center;

    .msg_title {
        font-size: 20px;
        font-weight: bolder;
    }

    .msg_tip {
        margin: 50px 0;
        font-size: 14px;
    }
}

.contract {
    padding: 20px 0;

    .pdf {
        max-width: 1000px;
        min-width: 800px;
        border: 1px solid #ddd;
        border-radius: $border-raius-10;
    }
}

.addressDiv {
    display: flex;
    flex-direction: column;
}

.btn {
    padding: 0;
    margin: 0;
    width: 30px;
    height: 30px;
    margin-left: 10px;
    // border-radius: 50%;
    line-height: 30px;
    text-align: center;
}

.region-item {
    display: flex;

    &:nth-child(n+2) {
        margin-top: 10px;
    }
}
</style>
