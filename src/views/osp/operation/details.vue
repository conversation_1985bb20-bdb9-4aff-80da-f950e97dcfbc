<!-- 运维工单详情 -->
<template>
  <div>
    <Button type="text" icon="md-undo" @click="jumpto('/operationList/')">运维工单</Button>
    <div style="background: #eee; padding: 20px">
      <Card :bordered="false">
        <div class="cus_list">
          <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏运维管理</Breadcrumb-item>
            <Breadcrumb-item to="/osp/operationList">运维管理</Breadcrumb-item>
            <Breadcrumb-item>运维工单详情</Breadcrumb-item>
          </Breadcrumb>
          <div class="list_son">
            <div class="step">
              <Steps class="stepdiv" :current="typeNode('orderStatusList', infoList.status)">
                <Step v-for="(item, index) in infoList.processLogs" :key="index" :content="timeChange(item.createdAt)"
                  :title="item.desc"></Step>
              </Steps>
            </div>
          </div>
          <div class="cus_reg">
            <div class="cus_form">
              <p class="cus_title">运维工单基本信息</p>
              <Form class="form" ref="infoList" :model="infoList" inline label-position="top">
                <FormItem label="运维单号">
                  <Input v-model="infoList.workOrderSn" readonly placeholder="-"
                    style="margin-right: 50px; width: 200px" />
                </FormItem>
                <FormItem label="服务商">
                  <Input v-model="infoList.opName" readonly placeholder="-" style="margin-right: 50px; width: 200px" />
                </FormItem>
                <FormItem label="服务人员">
                  <Input v-model="infoList.opStaff" readonly placeholder="-" style="margin-right: 50px; width: 200px" />
                </FormItem>
                <FormItem label="服务人员手机号码">
                  <Input v-model="infoList.opStaffMobile" readonly placeholder="-"
                    style="margin-right: 50px; width: 200px" />
                </FormItem>
                <FormItem label="工单状态">
                  <Input :value="typeVal('orderStatusList', infoList.status)" readonly placeholder="-"
                    style="margin-right: 50px; width: 200px" />
                </FormItem>
                <FormItem label="工单来源">
                  <Input :value="typeVal('sourceArr', infoList.source)" readonly placeholder="-"
                    style="margin-right: 50px; width: 200px" />
                </FormItem>
                <FormItem label="工单类型(一级)">
                  <Input readonly placeholder="-" style="margin-right: 50px; width: 200px" />
                </FormItem>
                <FormItem label="工单类型(二级)">
                  <Input readonly placeholder="-" style="margin-right: 50px; width: 200px" />
                </FormItem>
                <FormItem label="工单类型(三级)">
                  <Input readonly placeholder="-" style="margin-right: 50px; width: 200px" />
                </FormItem>
                <FormItem label="派单时间">
                  <Input :value="timeChange(infoList.arrangeUserTime)" readonly placeholder="-"
                    style="margin-right: 50px; width: 200px" />
                </FormItem>
                <FormItem label="报修单号">
                  <Input readonly placeholder="-" style="margin-right: 50px; width: 200px" />
                </FormItem>
                <FormItem label="联系人">
                  <Input readonly placeholder="-" style="margin-right: 50px; width: 200px" />
                </FormItem>
                <FormItem label="联系人手机号码">
                  <Input readonly placeholder="-" style="margin-right: 50px; width: 200px" />
                </FormItem>
                <FormItem label="当前告警次数">
                  <Input readonly v-model="count" placeholder="-" style="margin-right: 50px; width: 200px" />
                </FormItem>
                <FormItem label="超期天数">
                  <Input readonly v-model="infoList.expireDays" placeholder="-"
                    style="margin-right: 50px; width: 200px" />
                </FormItem>
                <FormItem label="逆变器SN码">
                  <Input readonly v-model="infoList.inveterSn" placeholder="-"
                    style="margin-right: 50px; width: 200px" />
                </FormItem>
              </Form>
              <p class="cus_title">电站信息</p>
              <Form class="form" ref="infoList" :model="infoList" inline label-position="top">
                <FormItem label="电站编码">
                  <Input v-model="infoList.stationCode" readonly placeholder="-"
                    style="margin-right: 50px; width: 200px" />
                </FormItem>
                <FormItem label="电站业主">
                  <Input v-model="infoList.stationName" readonly placeholder="-"
                    style="margin-right: 50px; width: 200px" />
                </FormItem>
                <FormItem label="业主联系方式">
                  <Input v-model="infoList.stationPhone" readonly placeholder="-"
                    style="margin-right: 50px; width: 200px" />
                </FormItem>
                <FormItem label="区域">
                  <Input v-model="infoList.area" readonly placeholder="-" style="margin-right: 50px; width: 200px" />
                </FormItem>
                <FormItem label="详细地址">
                  <Input v-model="infoList.stationAddress" readonly placeholder="-"
                    style="margin-right: 50px; width: 200px" />
                </FormItem>
                <FormItem label="所属分中心">
                  <Input v-model="infoList.subCenterName" readonly placeholder="-"
                    style="margin-right: 50px; width: 200px" />
                </FormItem>
              </Form>
              <Tabs value="name1">
                <TabPane label="故障信息" name="name1">
                  <table class="cus_table" border="0" cellspacing="0" cellpadding="0" width="100%">
                    <tr>
                      <th align="center">故障/告警码</th>
                      <th align="center">故障/告警等级</th>
                      <th align="center">故障/告警码描述</th>
                      <th align="center">产品类型</th>
                      <th align="center">产品品牌</th>
                      <th align="center">产品型号</th>
                    </tr>
                    <tr>
                      <td align="center" width="80">{{ infoList.faultCode || "-" }}</td>
                      <td align="center" width="120">{{ infoList.faultLevel || "-" }}</td>
                      <td align="center" width="70">{{ infoList.faultCodeDesc || "-" }}</td>
                      <td align="center" width="70">{{ infoList.productType || "-" }}</td>
                      <td align="center" width="100">{{ infoList.brandName || "-" }}</td>
                      <td align="center" width="100">{{ infoList.sku || "-" }}</td>
                    </tr>
                  </table>
                </TabPane>
                <TabPane label="回单信息" name="name2">
                  <p class="cus_title">维修记录</p>
                  <table class="cus_table" border="0" cellspacing="0" cellpadding="0" width="100%">
                    <tr>
                      <th align="center">序号</th>
                      <th align="center">操作时间</th>
                      <th align="center">操作人</th>
                      <th align="center">来源</th>
                      <th align="center">故障现象</th>
                      <th align="center">故障原因</th>
                      <th align="center">故障解决方法</th>
                      <th align="center">维修前图片</th>
                      <th align="center">维修后图片</th>
                      <!-- <th align="center">备注</th> -->
                      <th align="center">过程描述</th>
                    </tr>
                    <tr v-for="(item, index) in itemArr" :key="index">
                      <td align="center" width="80">{{ index + 1 }}</td>
                      <td align="center" width="80">{{ item.createdAt?.replace("T", " ") || "-" }}</td>
                      <td align="center" width="80">{{ item.createdBy || "-" }}</td>
                      <td align="center" width="80">{{ typeVal('recordSource', item.source) || "-" }}</td>
                      <td align="center" width="80">{{ item.falutAppearance || "-" }}</td>
                      <td align="center" width="120">{{ item.falutReason || "-" }}</td>
                      <td align="center" width="70">{{ item.resolvent || "-" }}</td>
                      <td align="center" width="70">
                        <a v-if="item.beforeUrl" :href="item.beforeUrl" target="_blank">查看</a>
                      </td>
                      <td align="center" width="100">
                        <a v-if="item.afterUrl" :href="item.afterUrl" target="_blank">查看</a>
                      </td>
                      <!-- <td align="center" width="100">{{ item.remark || "-" }}</td> -->
                      <td align="center" width="80">{{ item.processRemark || "-" }}</td>
                    </tr>
                  </table>
                  <p class="cus_title">维修前的设备信息</p>
                  <table class="cus_table" width="100%">
                    <tr>
                      <th align="center">设备编码</th>
                      <th align="center">设备名称</th>
                      <th align="center">品牌</th>
                      <th align="center">型号</th>
                      <th align="center">数量</th>
                    </tr>
                    <tr>
                      <td align="center" width="80">{{ infoList.falutAppearance || "-" }}</td>
                      <td align="center" width="120">{{ infoList.falutReason || "-" }}</td>
                      <td align="center" width="70">{{ infoList.faultCodeDesc || "-" }}</td>
                      <td align="center" width="100">{{ "-" }}</td>
                      <td align="center" width="100">{{ "-" }}</td>
                    </tr>
                  </table>
                  <p class="cus_title">维修后的设备信息</p>
                  <table class="cus_table" width="100%">
                    <tr>
                      <th align="center">设备编码</th>
                      <th align="center">设备名称</th>
                      <th align="center">品牌</th>
                      <th align="center">型号</th>
                      <th align="center">数量</th>
                    </tr>
                    <tr>
                      <td align="center" width="80">{{ infoList.falutAppearance || "-" }}</td>
                      <td align="center" width="120">{{ infoList.falutReason || "-" }}</td>
                      <td align="center" width="70">{{ infoList.faultCodeDesc || "-" }}</td>
                      <td align="center" width="100">{{ "-" }}</td>
                      <td align="center" width="100">{{ "-" }}</td>
                    </tr>
                  </table>
                </TabPane>
                <TabPane label="工单备件" name="name3">
                  <div class="commListDiv">
                    <Table size="small" border :columns="subTableColumn" :data="subTableDataSource" />
                  </div>
                  <div v-if="FJshow">
                      <p class="cus_title">附件信息</p>
                      <Form class="form_deep" :model="orderWoForm"  :label-width="115" ref="orderWoForm" inline>
                          <div class="list_but">
                              <FormItem label="损坏件" prop="damagedPart">
                                  <div class="demo-upload-list" >
                                      <img :src="orderWoForm?.damagedPart" loading="lazy" width="100%"  alt=""/>
                                      <div class="demo-upload-list-cover" @click.stop>
                                          <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleOrderWoView('1')"></Icon>
                                      </div>
                                  </div>

                              </FormItem>
                              <FormItem label="并网箱" prop="parallelMeshBox">
                                  <div class="demo-upload-list" >
                                      <img :src="orderWoForm?.parallelMeshBox" loading="lazy" width="100%"  alt=""/>
                                      <div class="demo-upload-list-cover" @click.stop>
                                          <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleOrderWoView('2')"></Icon>
                                      </div>
                                  </div>

                              </FormItem>
                              <FormItem label="并网箱铭牌" prop="Nameplate">
                                  <div class="demo-upload-list" >
                                      <img :src="orderWoForm?.Nameplate" loading="lazy" width="100%"  alt=""/>
                                      <div class="demo-upload-list-cover" @click.stop>
                                          <Icon type="ios-eye-outline" size="20" color="#fff" @click.stop="handleOrderWoView('3')"></Icon>
                                      </div>
                                  </div>

                              </FormItem>
                          </div>
                      </Form>
                  </div>
                </TabPane>
                <TabPane label="工单记录" name="name4">
                  <Timeline>
                    <TimelineItem v-for="item in infoList.logs" :key="item.id">
                      <p class="content">{{ item.desc }}</p>
                      <p class="time">{{ item.createdAt && item.createdAt.replace("T", " ") }}</p>
                    </TimelineItem>
                  </Timeline>
                </TabPane>
              </Tabs>
            </div>
          </div>
        </div>
      </Card>
    </div>
      <!--        附件放大查看  -->
      <Modal v-model="visible" width="800px" reset-drag-position draggable sticky title="预览" footer-hide class-name="vertical-center-modal">
          <img class="preview" v-if="visibleImg" width="700" :src="visibleImg" @click="toTarget(visibleImg)" title="点击查看原图" alt="">
      </Modal>
  </div>
</template>

<script>
import _data from "@/views/osp/_edata"; // 数据字典
import API from "@/api/osp";
import OSP from "@/api/osp";
// import { Desensitized } from "@/utils/Desensitized";

export default {
  data() {
    return {
      infoList: {},
      subTableDataSource: [],
      orderStatusList: _data.orderStatusList,
      sourceArr: _data.SourceArr,
      recordSource: _data.recordSource,
      count: '',
      itemArr: [],
      selectTableTotal: 0, // 添加明细列表总条数
      subTableColumn: [
        { title: '备件登记id', width: 180, key: 'id', align: 'center' },
        { title: '备件编码', width: 180, key: 'partNo', align: 'center' },
        { title: '备件描述', width: 180, key: 'remarks', align: 'center' },
        { title: '备件名称', width: 180, key: 'materialDesc', align: 'center' },
        { title: '备件唯一码', width: 180, key: 'partNo1', align: 'center' },
        { title: '检出仓库', width: 180, key: 'poolId', align: 'center' },
        { title: '出库仓库', width: 180, key: 'warehouseName', align: 'center' },
        { title: '使用状态', width: 180, key: 'useStatus', align: 'center' },
        { title: '备件价格', width: 180, key: 'noexchangeSettlePrice', align: 'center' },
        { title: '备件品牌', width: 180, key: 'materialBrand', align: 'center' },
        { title: '备件类别', width: 180, key: 'materialType', align: 'center' },
        { title: '备件规格', width: 180, key: 'materialUnit', align: 'center' },
        { title: '审批状态', width: 180, key: 'auditStatus', align: 'center' },
        { title: '审批意见', width: 180, key: 'auditResult', align: 'center' },
        { title: '备件申请时间', width: 180, key: 'createDate', align: 'center' },
        { title: '出库时间', width: 180, key: 'sendDate', align: 'center' },
        { title: '申请入库时间', width: 180, key: 'netGetDate', align: 'center' },
        { title: '服务商出库时间', width: 180, key: 'netOutDate', align: 'center' },
        { title: '登记时间', width: 180, key: 'partSendTime', align: 'center' },
        { title: '退返时间', width: 180, key: 'badReturnDate', align: 'center' },
        { title: '退返签收时间', width: 180, key: 'receiveTime', align: 'center' },
        { title: '无旧件返还描述', width: 180, key: 'oldReturnResult', align: 'center' },
        { title: '申请渠道', width: 180, key: 'applySource', align: 'center' },
        { title: '申请人', width: 180, key: 'createBy', align: 'center' }
      ],
        orderWoForm:{
            damagedPart:null,
            parallelMeshBox:null,
            Nameplate:null
        },
        FJshow:false,
        visible:false,
        visibleImg:''
    };
  },

  mounted() {
    this.getInfo();
  },

  methods: {
    jumpto(path) {
      this.$router.push({
        path,
      });
    },

    openImg(url) {
      window.open(url);
    },

    typeVal(list, value) {
      const obj = {};
      this[list].map((e) => {
        obj[e.value] = e.label || e.value;
      });
      // console.log(obj[value]);
      return obj[value];
    },

    typeNode(list, value) {
      const obj = {};
      this[list].map((e) => {
        obj[e.value] = e.node;
      });
      return obj[value];
    },

    timeChange(params) {
      // console.log(params);
      let dates = "";
      if (params) {
        dates = params.replace("T", " ");
      }
      return dates;
    },
    // 获取工单详情
    getInfo() {
      let datas = {
        workOrderSn: this.$route.query.id,
      };
      this.count = this.$route.query.count
      API.getOrderDetails(datas).then((res) => {
        let request = res.data.result;
        // console.log(request.status);
        if (res.data.success) {
          // request.opStaffMobile = Desensitized.judgeType(request.opStaffMobile)
          // request.stationPhone = Desensitized.judgeType(request.stationPhone)
          this.infoList = request;
          this.infoList.area = (request.stationProvinceName || "") + (request.stationCityName || "") + (request.stationRegionName || "");
          this.itemArr = request.workOrderProcessList;
        } else {
          this.$Message.error(res.data.error);
        }
      });
      //获取工单备件表格信息
      let params = {
        'serviceInfoId': this.$route.query.id,
      }
      let page = {
        'page': '1',
        'rows': '999'
      }
        OSP.getSpareList(page, params).then(res => {
            this.loading = false
            let request = res.data.result;
            if (res.data.success) {
                this.selectTableTotal = request.totalElements;
                this.subTableDataSource = request.content;
            } else {
                this.selectTableTotal = 0;
                this.subTableDataSource = [];
            }
        }).catch(e => {
            console.log(e);
            this.loading = false
            this.$Message.error("获取服务失败，请重试");
        })

        OSP.orderWoPartPageAndFile(page,params).then(resp=>{
            if(resp.data.success && resp.data.result.fileList.length>0) {
                let fileList = resp.data.result.fileList
                fileList.forEach(item => {
                    if (item.detailId === 'damagedPart') {
                        this.orderWoForm.damagedPart = item.fileUrl
                    } else if (item.detailId === 'parallelMeshBox') {
                        this.orderWoForm.parallelMeshBox = item.fileUrl
                    } else if (item.detailId === 'Nameplate') {
                        this.orderWoForm.Nameplate = item.fileUrl
                    }
                })
                this.FJshow = true
            }else{
                this.FJshow=false
                this.orderWoForm={
                    damagedPart:null,
                    parallelMeshBox:null,
                    Nameplate:null
                }
            }
        }).catch(e=>{
            console.log(e);
        })

    },
      toTarget(url) {
          window.open(url)
      },
      handleOrderWoView (type) {
          this.visibleName = '附件照片';
          if(type ==='1'){
              this.visibleImg = this.orderWoForm.damagedPart;
          }else if(type ==='2'){
              this.visibleImg = this.orderWoForm.parallelMeshBox;
          }else if(type ==='3'){
              this.visibleImg = this.orderWoForm.Nameplate;
          }
          this.visible = true;
      },
  },
};
</script>

<style scoped lang="scss">
@import "@/style/_cus_list.scss";
@import "@/style/_cus_reg.scss";

.step {
  position: relative;
  margin: 0 20px;
  padding: 12px 6px 6px;
  border: 1px solid #e8eaec;

  .stepdiv {
    width: 80%;
    margin: 0 auto;
    @extend pt;
  }
}

.cus_form {
  .form {
    margin: 0 auto;
    padding: 20px 0;
    width: 800px;
    box-sizing: border-box;
  }
}

.cus_tables {
  width: 100%;
  border-collapse: collapse;
}

.cus_tables th,
.cus_tables td {
  padding: 8px;
}

.cus_tables th {
  text-align: center;
}

.cus_tables td {
  text-align: center;
}

.cus_tables tr:first-child {
  background-color: #f2f2f2;
}
.list_but {
    margin-bottom: 20px;
}
.demo-upload-list {
    @extend .flex-row-center-center;
    position: relative;
    width: 100px;
    height: 100px;
    //height: 100%;
    overflow: hidden;

    &:hover .demo-upload-list-cover {
        display: flex;
    }
}
.demo-upload-list-cover {
    @extend.flex-row-center-around;
    display: none;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,.6);
    z-index: 999;
    i{
        &:hover{
            cursor: pointer;
        }
    }
}
</style>
