<!-- 电站租金查询详情 -->
<template>
    <div>
        <Button type="text" icon="md-undo" @click="jumpto('/rentQuery')">电站租金查询</Button>
        <div style="background: #eee; padding: 20px">
            <Card :bordered="false">
                <div class="cus_list">
                    <Breadcrumb class="bread">
                        <Breadcrumb-item to="/index/">光伏运维管理</Breadcrumb-item>
                        <Breadcrumb-item to="/osp/operationList">运维管理</Breadcrumb-item>
                        <Breadcrumb-item>电站租金查询详情</Breadcrumb-item>
                    </Breadcrumb>
                    <div class="list_son">
                        <div class="list_but">
                            <div class="condition">
                                <p class="condition_p">月份</p>
                                <DatePicker ref="formDateEx" type="month"
                                    @on-change="data => { return selectMonth(data, 'month'); }" placeholder="请选择月份"
                                    style="width: 160px"></DatePicker>
                            </div>
                            <div class="condition">
                                <Button @click="debounce(queryList)" type="primary">查询</Button>
                                <Button @click="debounce(emptyList)">重置</Button>
                            </div>
                        </div>
                        <div class="commListDiv">
                            <table class="cus_table" border="0" cellspacing="0" cellpadding="0" width="100%">
                                <tr>
                                    <th align="center" class="fixedLeft1">序号</th>
                                    <th align="center">电站编码</th>
                                    <th align="center">业主姓名</th>
                                    <th align="center">租金签约类型</th>
                                    <th align="center">到账银行卡</th>
                                    <th align="center">租金金额(元)</th>
                                    <th align="center">租金月份</th>
                                    <th align="center">状态</th>
                                    <th align="center">到账时间</th>
                                    <th align="center">备注</th>
                                </tr>
                                <tbody class="tag_line" v-for="(item, index) in commList" :key="index">
                                    <tr>
                                        <td align="center" class="fixedLeft1">{{ index + 1 }}</td>
                                        <td align="center">{{ item.stationCode || '-' }}</td>
                                        <td align="center">{{ item.stationName || '-' }}</td>
                                        <td align="center">{{ typeVal('signingType', item.type) || '-' }}</td>
                                        <td align="center">{{ item.bankAccount || '-' }}</td>
                                        <td align="center">{{ item.amount || '-' }}</td>
                                        <td align="center">{{ item.rentDate || '-' }}</td>
                                        <td align="center" style="min-width: 100px">
                                            {{ typeVal('signingStatus', item.status) || '-' }}
                                        </td>
                                        <td align="center" style="min-width: 200px">
                                            {{ item.payDate && item.payDate.replace('T', ' ') || '-' }}
                                        </td>
                                        <td align="center">{{ item.remark|| '-' }}</td>
                                    </tr>
                                </tbody>
                                <tr v-if="!commList.length">
                                    <td colspan="25" align="center">暂无数据</td>
                                </tr>
                            </table>
                        </div>
                        <Page style="margin:20px;" @on-change="changeCurrent" :current="searchObj.page"
                            :total="totalElements" show-total show-elevator />
                    </div>
                </div>
            </Card>
        </div>
    </div>
</template>

<script>
import _data from "@/views/osp/_edata"; // 数据字典
import { accountDetailOp } from '@/api/osp';
import { getDicts } from "@/api/public/dict";
export default {
    data() {
        return {
            infoList: {},
            searchObj: {
                source: '',
                stationCode: '',
                date: '',
                page: 1,
                size: 10,
            },
            commList: [],
            totalElements: 0,
            signingType: [],
            signingStatus: [],
        };
    },

    mounted() {
        this.searchObj.source = this.$route.query.source
        this.searchObj.stationCode = this.$route.query.stationCode
        this.getInfo()
    },
    async created() {
        this.signingType = await getDicts("osp/merchant", "signingType").then(e => e?.list)
        this.signingStatus = await getDicts("osp/merchant", "signingStatus").then(e => e?.list)
    },
    methods: {
        //跳转
        jumpto(path) {
            this.$router.push({
                path,
            });
        },
        //查询
        queryList() {
            this.commList = [];
            this.searchObj.page = 1;
            this.getInfo();
        },
        // 选择日期
        selectMonth(value) {
            if (value) {
                const date = new Date(value);
                const year = date.getFullYear();
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const formattedDate = `${year}-${month}`;
                this.searchObj.date = formattedDate
            }
        },
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.page = curr;
            this.getInfo();
        },
        getInfo() {
            let datas = this.searchObj;
            accountDetailOp(datas).then(res => {
                let request = res.data.result
                if (request.content.length > 0) {
                    this.commList = request.content;
                    this.totalElements = request.totalElements;
                } else {
                    this.totalElements = 0;
                    this.commList = []
                }
            });
        },
        //重置
        emptyList() {
            this.searchObj.date = ''
            this.$refs.formDateEx.handleClear()
        },
        typeVal(list, value) {
            const obj = {};
            this[list].map((e) => {
                obj[e.value] = e.label || e.value;
            });
            return obj[value];
        },
    },
};
</script>

<style scoped lang="scss">
@import 'style/_cus_list.scss';
</style>
