<!-- 运维工单 -->
<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏运维管理</Breadcrumb-item>
            <Breadcrumb-item to="/osp/">运维管理</Breadcrumb-item>
            <Breadcrumb-item>电站数据变更</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">电站编码：</p>
                    <Input v-model="searchObj.stationCode" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">运维工单号：</p>
                    <Input v-model="searchObj.workOrderSn" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">状态：</p>
                    <Select v-model="searchObj.status" clearable style="width: 150px">
                        <Option v-for="item in changeDataStatus" :value="item.value" :key="item.value">{{ item.label }}
                        </Option>
                    </Select>
                </div>
            </div>
            <div class="list_but">
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                    <Button @click="debounce(addData)" type="success">新增</Button>
                </div>
            </div>
            <div class="commListDiv">
                <table class="cus_table" border="0" cellspacing="0" cellpadding="0" width="100%">
                    <tr>
                        <th align="center">序号</th>
                        <th align="center">信息类别</th>
                        <th align="center">问题小类</th>
                        <th align="center">运维工单号</th>
                        <th align="center">业主姓名</th>
                        <th align="center">所属分中心</th>
                        <th align="center">电站编码</th>
                        <th align="center">原逆变器SN</th>
                        <th align="center">新逆变器SN</th>
                        <th align="center">问题描述</th>
                        <th align="center">提报资料</th>
                        <th align="center">状态</th>
                        <th align="center">提交人</th>
                        <th align="center">创建时间</th>
                        <th align="center">终止时间</th>
                        <th align="center">审核人</th>
                        <th align="center">审核备注</th>
                        <th align="center">审核时间</th>
                        <th align="center">验收人</th>
                        <th align="center">验收资料</th>
                        <th align="center">验收备注</th>
                        <th align="center">验收时间</th>
                        <th align="center"  class="fixedRight">操作</th>
                    </tr>
                    <tr v-for="(item, index) in commList" :key="index">
                        <td align="center" width="10%">{{ index + 1 }}</td>
                        <td align="center" width="120">
                            {{ typeVal("infoCategory", item.information) || "-" }}
                        </td>
                        <td align="center" width="200" style="min-width: 180px">
                            {{ typeVal("changeInverter", item.problem) || "-" }}
                        </td>
                        <td align="center" width="200">{{ item.workOrderSn || "-" }}</td>
                        <td align="center" width="120" style="min-width: 110px">{{ item.stationName || "-" }}</td>
                        <td align="center" width="120" style="min-width: 110px">{{ item.subCenterName || "-" }}</td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.stationCode || "-" }}</td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.oldInverter || "-" }}</td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.newInverter || "-" }}</td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.problemDesc || "-" }}</td>
                        <td align="center" width="160" style="min-width: 160px">
                            <a v-if="item.submitMaterials" :href="item.submitMaterials" target="_blank">查看</a>
                            <span v-else>-</span>
                        </td>
                        <td align="center" width="100" style="min-width: 100px">
                            {{ typeVal("changeDataStatus", item.status) || "-" }}
                        </td>
                        <td align="center" width="120" style="min-width: 120px">{{ item.createdBy || "-" }}</td>
                        <td align="center" width="180" style="min-width: 180px">
                            {{ item.createdAt && item.createdAt.replace("T", "") || "-" }}
                        </td>
                        <td align="center" width="180" style="min-width: 180px">
                            {{ item.stopAt && item.stopAt.replace("T", "") || "-" }}
                        </td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.auditBy || "-" }}</td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.auditRemark || "-" }}</td>
                        <td align="center" width="100" style="min-width: 180px">
                            {{ item.auditAt && item.auditAt.replace("T", "") || "-" }}
                        </td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.acceptanceBy || "-" }}</td>
                        <td align="center" width="100" style="min-width: 100px">
                            <a v-if="item.acceptanceMaterials" :href="item.acceptanceMaterials" target="_blank">查看</a>
                            <span v-else>-</span>
                        </td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.acceptanceRemark || "-" }}</td>
                        <td align="center" width="180" style="min-width: 180px">
                            {{ item.acceptanceAt && item.acceptanceAt.replace("T", "") || "-" }}
                        </td>
                        <td align="center" width="230" style="min-width: 230px"  class="fixedRight">
                            <Button type="success" ghost size="small" style="margin: 3px" @click="toEdit(item)"
                                v-if="item.status === 'AUDIT_REJECT' || item.status === 'TECHNICAL_ACCEPTANCE_REJECT'">修改</Button>
                            <Button type="error" ghost size="small" style="margin: 3px" @click="desist(item)"
                                v-if="item.status !== 'TECHNICAL_ACCEPTANCE_OK' && item.status !== 'STOP'">终止</Button>
                            <Button type="info" ghost size="small" style="margin: 3px"
                                @click="process(item)">过程信息</Button>
                        </td>
                    </tr>
                    <tr v-if="!commList.length">
                        <td colspan="25" align="center">暂无数据</td>
                    </tr>
                </table>
            </div>
            <Page style="margin: 20px" @on-change="changeCurrent" :current="searchObj.page" :total="totalElements"
                show-total show-elevator />
        </div>
        <Modal width="600" v-model="modelEdit" draggable sticky scrollable :title="title" :footer-hide="true"
            :mask-closable="false">
            <Form :model="formItem" :label-width="120" ref="formItem" inline>
                <FormItem label="错误信息类别" prop="information" :rules="{ required: true, message: ' ' }">
                    <Select v-model="formItem.information">
                        <Option v-for="item in infoCategory" :value="item.value" :key="item.value">{{ item.label }}
                        </Option>
                    </Select>
                </FormItem>
                <FormItem label="问题小类" prop="problem" :rules="{ required: true, message: ' ' }">
                    <Select v-model="formItem.problem">
                        <Option v-for="item in changeInverter" :value="item.value" :key="item.value">{{ item.label }}
                        </Option>
                    </Select>
                </FormItem>
                <FormItem label="运维工单号" prop="workOrderSn" :rules="{ required: true, message: ' ' }">
                    <Input type="text" v-model="formItem.workOrderSn" @on-blur="stationCodeInput" clearable
                        placeholder="请输入运维工单号" />
                </FormItem>
                <FormItem label="电站编码" prop="stationCode">
                    <Input disabled type="text" v-model="formItem.stationCode" clearable placeholder="请输入电站编码" />
                </FormItem>
                <FormItem label="业主姓名" prop="stationName">
                    <Input disabled type="text" v-model="formItem.stationName" clearable placeholder="请输入姓名" />
                </FormItem>
                <FormItem label="电站所属分中心" prop="subCenterName">
                    <Input disabled type="text" v-model="formItem.subCenterName" clearable placeholder="请输入分中心" />
                </FormItem>
                <FormItem label="原逆变器" prop="oldInverter" :rules="{ required: true, message: ' ' }">
                    <Select v-model="formItem.oldInverter">
                        <Option v-for="item in oldInverter" :value="item.inverterSn" :key="item.inverterSn">
                            {{ item.inverterSn }}
                        </Option>
                    </Select>
                </FormItem>
                <FormItem label="新逆变器SN" prop="newInverter" :rules="{ required: true, message: ' ' }">
                    <Input type="text" v-model="formItem.newInverter" clearable placeholder="请输入新逆变器SN" />
                </FormItem>
                <FormItem label="问题描述" prop="problemDesc" :rules="{ required: true, message: ' ' }">
                    <Input class="item_large" type="textarea" v-model="formItem.problemDesc" clearable
                        placeholder="请填写" />
                </FormItem>
                <FormItem label="提报资料" prop="submitMaterials" :rules="{ required: true, message: ' ' }">
                    <Upload class="inlinebox" accept=".pdf" :show-upload-list="false"
                        :action="UploadClass.actionUrl('pdf')" :headers="UploadClass.headers"
                        :before-upload="UploadClass.progress"
                        :on-success="(response) => { formItem.submitMaterials = response.result[0].pdfUrl; formItem.pdfName = response.result[0].pdfName; return UploadClass.upSuccess() }"
                        :format="['pdf']" :max-size="102400" :on-format-error="UploadClass.formatError"
                        :on-exceeded-size="UploadClass.maxSizeError">
                        <Button icon="md-cloud-upload">{{ formItem.submitMaterials ? '重新' : ''
                            }}上传PDF文件</Button>
                    </Upload>
                    <a v-if="formItem.submitMaterials" :href='formItem.submitMaterials' target="_blank"
                        style="white-space: nowrap;font-size: 15px;">
                        <Icon type="md-document" /> 查看文件
                    </a>
                </FormItem>
            </Form>
            <div style="text-align: center; margin: 30px 0">
                <Button type="default" size="large" style="width: 120px; margin-right: 30px"
                    @click="modelEdit = false">取消</Button>
                <Button type="primary" size="large" style="width: 120px" @click="debounce(toNewBuilt)">
                    {{ formItem.id ? '修改' : '提交' }}
                </Button>
            </div>
        </Modal>
        <Modal width="500" v-model="termination" draggable sticky scrollable title="终止" :footer-hide="true"
            :mask-closable="false">
            <Form :model="terminationText" :label-width="100" ref="terminationText" inline>
                <FormItem label="终止原因" prop="staffName">
                    <Input class="item_large" type="textarea" v-model="terminationText.remark" clearable
                        placeholder="请填写" />
                </FormItem>
            </Form>
            <div style="text-align: center; margin: 20px 0">
                <Button type="default" size="large" style="width: 120px; margin-right: 30px"
                    @click="termination = false">取消</Button>
                <Button type="primary" size="large" style="width: 120px" @click="debounce(terminationBtn)">确定</Button>
            </div>
        </Modal>
        <Modal width="800" v-model="processInfo" draggable sticky scrollable title="过程信息" :footer-hide="true"
            :mask-closable="false">
            <table class="cus_table" border="0" cellspacing="0" cellpadding="0" width="100%">
                <tr>
                    <th align="center">序号</th>
                    <th align="center">操作时间</th>
                    <th align="center">操作人</th>
                    <th align="center">内容</th>
                    <th align="center">审核建议</th>
                    <th align="center">备注</th>
                </tr>
                <tr v-for="(item, index) in terminationList" :key="index">
                    <td align="center" width="10%">{{ index + 1 }}</td>
                    <td align="center" width="160">
                        {{ item.createdAt && item.createdAt.replace("T", "") || "-" }}
                    </td>
                    <td align="center" width="120">{{ item.createdBy || "-" }}</td>
                    <td align="center" width="120">
                        {{ typeVal("infoContent", item.node) || "-" }}
                    </td>
                    <td align="center" width="120" style="min-width: 110px">
                        {{ typeVal("recommendation", item.status) || "-" }}
                    </td>
                    <td align="center" width="120" style="min-width: 110px">{{ item.auditRemark || "-" }}</td>
                </tr>
                <tr v-if="!commList.length">
                    <td colspan="25" align="center">暂无数据</td>
                </tr>
            </table>
        </Modal>
    </div>
</template>
<script>
import _data from "@/views/osp/_edata"; // 数据字典
import API from "@/api/osp";
import { UploadClass } from '@/utils/common';
export default {
    name: "pStationChanges",
    data() {
        return {
            UploadClass,
            modelEdit: false,//新增编辑
            termination: false,//终止
            processInfo: false,//过程信息
            title: "新增",
            commList: [],
            terminationList: [],
            searchObj: {
                status: "",
                workOrderSn: "",
                stationCode: "",
                pageNum: 1,
                pageSize: 10,
            },
            formItem: {
                id: "",
                information: "",
                problem: "",
                workOrderSn: "",
                stationCode: "",
                subCenterCode: "",
                stationName: "",
                subCenterName: "",
                oldInverter: "",
                newInverter: "",
                problemDesc: "",
                submitMaterials: "",
                pdfUrl: "",
                pdfName: ""
            },
            oldInverter: [],
            terminationText: {
                id: "",
                remark: "",
            },
            pdfUrl: '',
            totalElements: 0,
            changeDataStatus: _data.changeDataStatus,//状态
            infoCategory: _data.infoCategory,//错误信息类别
            changeInverter: _data.changeInverter,//更改逆变器
            infoContent: _data.infoContent,
            recommendation: _data.recommendation,//建议
        };
    },
    mounted() {
        this.getCommList();
    },
    methods: {
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.pageNum = curr;
            this.getCommList();
        },
        // 查询按钮
        queryList() {
            this.searchObj.pageNum = 1;
            this.getCommList();
        },
        // 重置按钮
        emptyList() {
            this.searchObj = {
                status: "",
                workOrderSn: "",
                stationCode: "",
                pageNum: 1,
                pageSize: 10,
            };
            this.date = [];
        },
        // 获取列表
        getCommList() {
            let datas = this.searchObj;
            API.powerPlantChangesList(datas).then((res) => {
                let request = res.data.result;
                if (res.data.success) {
                    this.totalElements = request.totalElements;
                    this.commList = request.content;
                } else {
                    this.totalElements = 0;
                    this.commList = [];
                }
            });
        },
        stationCodeInput() {
            this.getInstalledPower(this.formItem.workOrderSn)
        },
        getInstalledPower(workOrderSn) {
            if (workOrderSn) {
                const params = {
                    stationCode: workOrderSn
                }
                API.findByStationCode(params).then(res => {
                    if (res.data.success) {
                        this.formItem.stationCode = res.data.result.stationCode
                        this.formItem.stationName = res.data.result.stationName
                        this.formItem.subCenterCode = res.data.result.subCenterCode
                        this.formItem.subCenterName = res.data.result.subCenterName
                        this.oldInverter = res.data.result.inverterList
                    } else {
                        this.formItem.stationCode =""
                        this.formItem.stationName = ""
                        this.formItem.subCenterCode = ""
                        this.formItem.subCenterName = ""
                        this.oldInverter = []
                        this.$Message.error(res.data.error);
                    }
                })
            } else {
                this.$Message.error('请输入运维工单号');
            }

        },
        //新增
        addData() {
            this.title = '新增'
            this.formItem = this.$options.data.call(this).formItem;
            this.modelEdit = true
        },
        //编辑
        toEdit(item) {
            this.title = '编辑'
            const {
                id,
                information,
                problem,
                workOrderSn,
                stationCode,
                subCenterCode,
                stationName,
                subCenterName,
                oldInverter,
                newInverter,
                problemDesc,
                submitMaterials,
                pdfUrl,
                pdfName
            } = item
            this.formItem = {
                id,
                information,
                problem,
                workOrderSn,
                stationCode,
                subCenterCode,
                stationName,
                subCenterName,
                oldInverter,
                newInverter,
                problemDesc,
                submitMaterials,
                pdfUrl,
                pdfName
            };
            this.stationCodeInput()
            this.modelEdit = true
        },
        //提交/编辑
        toNewBuilt() {
            const params = this.formItem
            const errorMessages = {
                information: '错误信息类别不能为空',
                problem: '问题小类不能为空',
                workOrderSn: '运维工单号不能为空',
                oldInverter: '旧逆变器不能为空',
                newInverter: '新逆变器SN不能为空',
                problemDesc: '问题描述不能为空',
                submitMaterials: '提报资料不能为空'
            };
            let hasError = false;
            for (const key in errorMessages) {
                if (params[key] === '') {
                    this.$Message.error(errorMessages[key]);
                    hasError = true;
                    break;
                }
            }
            if (!hasError) {
                API.createData(params).then((res) => {
                    if (res.data.success) {
                        this.$Message.success("提交成功");
                        this.queryList();
                        this.modelEdit = false;
                    } else {
                        this.$Message.error(res.data.error);
                    }
                });
            }
        },
        //中止
        desist(item) {
            this.termination = true
            this.terminationText.id = item.id
        },
        terminationBtn() {
            const params = {
                id: this.terminationText.id,
                desc: this.terminationText.remark
            }
            API.abortProcess(params).then((res) => {
                if (res.data.success) {
                    this.$Message.success("中止成功");
                    this.queryList();
                    this.termination = false;
                } else {
                    this.$Message.error(res.data.error);
                }
            })
        },
        process(item) {
            this.processInfo = true
            this.terminationList = item.logList
        },
        // 导出
        exportList() {
            let datas = this.searchObj;
            let { page, size, ...datasEx } = { ...datas };
            API.getDoExport(datasEx)
                .then((res) => {
                    let binaryData = [];
                    let link = document.createElement("a");
                    binaryData.push(res.data);
                    link.style.display = "none";
                    link.href = window.URL.createObjectURL(new Blob(binaryData));
                    link.setAttribute("download", "运维工单.xlsx");
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                })
                .catch(() => {
                    this.$Message.error("导出失败");
                });
        },
        typeVal(list, value) {
            const obj = {};
            this[list].map((e) => {
                obj[e.value] = e.label || e.value;
            });
            return obj[value];
        },
    },
};
</script>
<style lang="scss" scoped>
@import "@/style/_cus_reg.scss";
@import "@/style/_cus_list.scss";
@import '@/style/_ivu-modal.scss';
.ivu-form-item {
    width: 100%;
}

.pdf {
    width: 100%;
    height: 100%;
}

// ::v-deep .ivu-form-inline {
//     width: 100%;
// }</style>
