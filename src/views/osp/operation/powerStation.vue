<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏运维管理</Breadcrumb-item>
            <Breadcrumb-item to="/apv/">运维管理</Breadcrumb-item>
            <Breadcrumb-item>电站列表</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">电站编码：</p>
                    <Input v-model="searchObj.stationCode" placeholder="请输入电站编码" clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">业主名称：</p>
                    <Input v-model="searchObj.name" placeholder="请输入业主名称" clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">分中心：</p>
                    <Select v-model="searchObj.subCenterCode" clearable style="width: 200px">
                        <Option v-for="item in subCenterList" :value="item.value" :key="item.value">{{ item.label }}
                        </Option>
                    </Select>
                </div>
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                </div>
            </div>
            <div class="commListDiv">
                <table class="cus_table" border="0" cellspacing="0" cellpadding="0" width="100%">
                    <tr>
                        <th align="center" class="fixedLeft1">序号</th>
                        <div style="width: 143px; background: #f2f2f2; display: flex; justify-content: space-around;"
                            class="fixedLeft2">
                            <th align="center">电站编码</th>
                        </div>
                        <th align="center" class="fixedLeft3">业主名称</th>
                        <th align="center">分中心</th>
                        <th align="center">发电户号</th>
                        <th align="center">经度</th>
                        <th align="center">纬度</th>
                        <th align="center">电站地址</th>
                        <th align="center">房屋类型</th>
                        <th align="center">安装方式</th>
                        <th align="center">装机块数</th>
                        <th align="center">装机功率(KW)</th>
                        <th align="center">备案方式</th>
                        <th align="center">电站三天发电时间</th>
                        <th align="center">电站完成时间</th>
                        <th align="center">逆变器、并网箱安装照片</th>
                        <th align="center">电站正面照片</th>
                        <th align="center">电站侧面照片</th>
                        <th align="center">电站并网时间</th>
                        <th align="center">是否在质保期</th>
                        <th align="center">质保期截止日期</th>
                    </tr>
                    <tbody class="tag_line" v-for="(item, index) in commList" :key="index">
                        <tr>
                            <td align="center" class="fixedLeft1">{{ index + 1 }}</td>
                            <td align="center" class="fixedLeft2">{{ item.stationCode || '-' }}</td>
                            <td align="center" class="fixedLeft3">{{ item.name || '-' }}</td>
                            <td align="center">{{ item.subCenterName || '-' }}</td>
                            <td align="left">{{ item.elecNo || '-' }}</td>
                            <td align="left">{{ item.longitude || '-' }}</td>
                            <td align="left">{{ item.latitude || '-' }}</td>
                            <td align="left" style="min-width: 160px">
                                {{ item.provinceName + ' ' + item.cityName + ' ' + item.regionName + ' ' || '-' }}
                                {{ item.streetName || ' ' }}
                                {{ item.address || ' ' }}
                            </td>
                            <td align="left">{{ pmHouseTypeList[item.houseType] || '-' }}</td>
                            <td align="left" style="min-width: 150px">{{ pmInstallList[item.install] || '-' }}</td>
                            <td align="left">{{ item.completeConfirmQuantity || '-' }}</td>
                            <td align="left">{{ item.power || '-' }}</td>
                            <td align="left">{{ filingMethod[item.fieldMethod] || '-' }}</td>
                            <td align="center" style="min-width: 200px">
                                {{ item.firstThreePowerAt && item.firstThreePowerAt.replace('T', ' ') || '-' }}
                            </td>
                            <td align="center" style="min-width: 200px">
                                {{ item.finishAt && item.finishAt.replace('T', ' ') || '-' }}
                            </td>
                            <td align="center">
                                <img v-if="item.imageUrl1" :src="pdfImgView(item.imageUrl1)" width="60" height="50"
                                    @click="openpdfImg(item.imageUrl1)">
                                <span v-else>-</span>
                            </td>
                            <td align="center">
                                <img v-if="item.imageUrl2" :src="pdfImgView(item.imageUrl2)" width="60" height="50"
                                    @click="openpdfImg(item.imageUrl2)">
                                <span v-else>-</span>
                            </td>
                            <td align="center">
                                <img v-if="item.imageUrl3" :src="pdfImgView(item.imageUrl3)" width="60" height="50"
                                    @click="openpdfImg(item.imageUrl3)">
                                <span v-else>-</span>
                            </td>
                            <td align="center" style="min-width: 200px">
                                {{ item.firstThreePowerAt && item.firstThreePowerAt.replace('T', ' ') || '-' }}
                            </td>
                            <td align="center">{{ item.isWarranty || '-' }}</td>
                            <td align="center" style="min-width: 200px">
                                {{ item.endWarrantyAt && item.endWarrantyAt.replace('T', ' ') || '-' }}
                            </td>
                        </tr>
                    </tbody>
                    <tr v-if="!commList.length">
                        <td colspan="25" align="center">暂无数据</td>
                    </tr>
                </table>
            </div>
            <Page style="margin:20px;" @on-change="changeCurrent" :current="searchObj.page" :total="totalElements"
                show-total show-elevator />
        </div>
    </div>
</template>
<script>
import _data from '@/views/osp/_edata'; // 数据字典
import { lightOpStation } from '@/api/osp';

export default {
    data() {
        return {
            modelPdf: false,
            searchObj: {
                name: '',
                stationCode: '',
                subCenterCode: '',
                pageNum: 1,
                pageSize: 10,
            },
            commList: [],
            numPages: null,
            totalElements: 0,
            subCenterList: _data.subCenterList,
            pmHouseTypeList: _data.pmHouseTypeList,
            pmInstallList: _data.pmInstallList,
            filingMethod: _data.filingMethod,
        };
    },

    mounted() {
        this.getCommList();
    },

    methods: {
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.pageNum = curr;
            this.getCommList();
        },

        // 查询按钮
        queryList() {
            this.commList = [];
            this.searchObj.pageNum = 1;
            this.getCommList();
        },

        // 重置按钮
        emptyList() {
            this.searchObj = {
                name: '',
                stationCode: '',
                subCenterCode: '',
                pageNum: 1,
                pageSize: 10,
            }
        },

        // 电站列表
        getCommList() {
            let datas = this.searchObj;
            lightOpStation(datas).then(res => {
                let request = res.data.result
                if (request.content.length > 0) {
                    this.totalElements = request.totalElements;
                    this.commList = request.content;
                    //
                } else {
                    this.totalElements = 0;
                    this.commList = []
                }
            });
        },

        pdfImgView(obj) {
            if (obj) {
                return obj
            } else {
                return 'null'
            }
        },

        openpdfImg(obj) {
            if (obj) {
                window.open(obj)
            } else {
                this.$Message.warning('暂无图片');
            }
        },
    }
};
</script>
<style lang="scss" scoped>
@import 'style/_cus_list.scss';

.fixedLeft1 {
    position: sticky;
    left: 0;
    @extend .back-filter;
}

.fixedLeft2 {
    position: sticky;
    left: 60px;
    @extend .back-filter;
}

.fixedLeft3 {
    position: sticky;
    left: 203px;
    @extend .back-filter;
}

.tag_line {
    position: relative;
}

:deep(.ivu-modal-fullscreen) {
    width: 100vw !important;
}

@media print {

    .cus_list {
        display: none
    }

    .noprint {
        display: none
    }
}
</style>
