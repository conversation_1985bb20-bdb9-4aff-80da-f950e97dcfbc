<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏运维管理</Breadcrumb-item>
            <Breadcrumb-item to="/apv/">运维管理</Breadcrumb-item>
            <Breadcrumb-item>逆变器列表</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">逆变器序列号</p>
                    <Input v-model="searchObj.inveterSn" placeholder="请输入..." clearable style="width: 150px" />
                </div>
                <div class="condition">
                    <p class="condition_p">逆变器状态</p>
                    <Select v-model="searchObj.inveterState" clearable style="width:120px;">
                        <Option v-for="(item, val) in inveterStateList" :value="val" :key="val">{{ item }}</Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">绑定状态</p>
                    <Select v-model="searchObj.bindStatus" clearable style="width:120px;">
                        <Option v-for="(item, val) in bindStatusList" :value="val" :key="val">{{ item }}</Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">电站名称</p>
                    <Input v-model="searchObj.stationName" placeholder="请输入..." clearable style="width: 150px" />
                </div>
                <div class="condition">
                    <p class="condition_p">电站编码</p>
                    <Input v-model="searchObj.stationCode" placeholder="请输入..." clearable style="width: 150px" />
                </div>
                <div class="condition">
                    <p class="condition_p">采集器状态</p>
                    <Select v-model="searchObj.collectorState" clearable style="width:120px;">
                        <Option v-for="(item, val) in collectorStateList" :value="val" :key="val">{{ item }}</Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">是否连续发电</p>
                    <Select v-model="searchObj.elecStatus" clearable style="width:120px;">
                        <Option v-for="(item, val) in elecStatusList" :value="val" :key="val">{{ item }}</Option>
                    </Select>
                </div>
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button v-if="!lightSub" @click="debounce(exportList)" type="success">导出</Button>
                </div>
            </div>
            <div class="commListDiv">
                <perfect-scrollbar :settings="{ suppressScrollY: true, useBothWheelAxes: true }">
                <table class="cus_table" border="0" cellspacing="0" cellpadding="0" width="100%">
                    <tr>
                        <th align="center">最后更新时间</th>
                        <th align="center">品牌</th>
                        <th align="center">逆变器SN</th>
                        <th align="center">所属电站</th>
                        <th align="center">所属电站编码</th>
                        <th align="center">电站状态</th>
                        <th align="center">最近三天是否连续发电</th>
                        <th align="center">工作状态</th>
                        <th align="center">采集器SN</th>
                        <th align="center">采集器状态</th>
                        <!-- <th align="center">本次上电工作时</th> -->
                        <th align="center">实时功率</th>
                        <th align="center">满发小时数</th>
                        <th align="center">状态</th>
                        <th align="center">启用时间</th>
                        <th align="center">停用时间</th>
                        <th align="center">额定功率（KW）</th>
                        <th align="center">交流频率（HZ）</th>
                        <th align="center">日发电量（kWh）</th>
                        <th align="center">月发电量（kWh）</th>
                        <th align="center">年发电量（kWh）</th>
                        <th align="center">累计发电量（kWh）</th>
                    </tr>
                    <tbody v-for="(item, index) in commList" :key="index">
                        <tr>
                            <td align="center" width="100" style="min-width: 80px;">{{ item.dataTimeAt  && item.dataTimeAt.substr(0, 10) || '-' }}</td>
                            <td align="center" width="100" style="min-width: 80px;">{{ item.brandName || '-' }}</td>
                            <td align="left" width="100" style="min-width: 180px;"> <a @click="openDetail(item.dataTimeAt.substr(0, 10), item.inveterSn)">{{ item.inveterSn || '-' }} </a></td>
                            <td align="center" width="100" style="min-width: 210px;">{{ item.stationName || '未绑定' }}</td>
                            <td align="left" width="80" style="min-width: 100px;">{{ item.stationCode || '-' }}</td>
                            <td align="center" width="100" style="min-width: 120px;">{{ status[item.stationStatus] || '-' }}</td>
                            <td align="center" width="100" style="min-width: 60px;">{{ elecStatusList[item.elecStatus] || '-' }}</td>
                            <td align="center" width="100" style="min-width: 60px;">{{ inveterStateList[item.inveterState] || '-' }}</td>
                            <td align="left" width="100" style="min-width: 100px;">{{ item.collectorSn || '-' }}</td>
                            <td align="center" width="100" style="min-width: 60px;"> {{ collectorStateList[item.collectorState] || '未激活' }} </td>
                            <!-- <td align="center" width="100" style="min-width: 60px;">{{ item.currentWorkingTime || '0' }}</td> -->
                            <td align="center" width="100" style="min-width: 60px;">{{ item.pac || '0' }}</td>
                            <td align="center" width="100" style="min-width: 60px;">{{ item.fullHour || '0' }}</td>
                            <td align="center" width="100" style="min-width: 80px;">{{ item.nowStatus || '-' }}</td>
                            <td align="center" style="min-width: 160px">
                                {{ item.startAt && item.startAt.replace('T', ' ') || '-' }}
                            </td>
                            <td align="center" style="min-width: 160px">
                                {{ item.stopAt && item.stopAt.replace('T', ' ') || '-' }}
                            </td>
                            <td align="center" width="100" style="min-width: 60px;">{{ item.power || '0' }}</td>
                            <td align="center" width="100" style="min-width: 60px;">{{ item.fac || '0' }}</td>
                            <td align="center" width="100" style="min-width: 60px;">{{ item.elecDay || '0' }}</td>
                            <td align="center" width="100" style="min-width: 60px;">{{ item.elecMonth || '0' }}</td>
                            <td align="center" width="100" style="min-width: 60px;">{{ item.elecYear || '0' }}</td>
                            <td align="center" width="100" style="min-width: 60px;">{{ item.elecTotal || '0' }}</td>
                        </tr>
                    </tbody>
                    <tbody v-if="!commList.length">
                        <tr><td colspan="19" align="center">暂无数据</td></tr>
                    </tbody>
                </table>
                </perfect-scrollbar>
            </div>
            <Page style="margin:20px;" @on-change="changeCurrent" :page-size="searchObj.pageSize" :current="searchObj.pageNum" :total="totalElements" show-total show-elevator />
        </div>
        <Modal v-model="modelDetail" footer-hide width="1100" draggable sticky title="逆变器详情">
            <OspInveter v-if="modelDetail" :commObj="commObj" ></OspInveter>
        </Modal>
    </div>
</template>
<script>
import OspInveter from '@/components/unitsApv/ospInverter.vue'
import _data from '@/views/apv/_edata.js'; // 数据字典
import API from '@/api/osp';

export default {
    name: 'operationInverter',
    data() {
        return {
            modelDetail: false,
            searchObj: {
                bindStatus: '',   // 绑定状态（YES:已绑定，NO:未绑定）
                collectorState: '',  // 采集器状态(1：在线 2：离线 3：报警)
                elecStatus: '',  // 是否连续发电（YES:是，NO:否）
                inveterSn: '',
                inveterState: '',  // 逆变器状态(1：在线 2：离线 3：报警)
                stationCode: '',
                stationName: '',
                page: 1,
                size: 10
            },
            totalElements: 0,
            commList: [],
            status: _data.status,
            bindStatusList: _data.bindStatusList,
            collectorStateList: _data.collectorStateList,
            elecStatusList: _data.elecStatusList,
            inveterStateList: _data.inveterStateList,
            commObj: {}
        };
    },

    components: {
        "OspInveter": OspInveter
    },

    computed: {
        lightSub() {
            return localStorage.getItem('userArr').includes('light_sp_sub')
        }
    },

    mounted() {
        this.queryList()
    },

    methods: {
        jumpto(path) {
            this.$router.push({
                path
            });
        },

        // 切换分页
        changeCurrent(curr) {
            this.searchObj.page = curr;
            this.getCommList();
        },

        // 查询按钮
        queryList() {
            this.commList = [];
            this.searchObj.page = 1;
            this.getCommList();
        },

        // 导出
        exportList() {
            let datas = this.searchObj;
            let { pageNum, pageSize, ...datasEx } = { ...datas }
            API.exportInveter(datasEx).then(res => {
                let binaryData = [];
                let link = document.createElement('a');
                binaryData.push(res.data);
                link.style.display = 'none';
                link.href = window.URL.createObjectURL(new Blob(binaryData));
                link.setAttribute('download', '逆变器列表.xlsx');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            })
            .catch(() => {
                this.$Message.error('导出失败');
            });
        },

        // 设备列表
        getCommList() {
            let datas = this.searchObj;
            API.lightInveterList(datas).then(res => {
                let request = res.data.result;
                if (request.content.length > 0) {
                    this.totalElements = request.totalElements;
                    this.commList = request.content;
                } else {
                    this.totalElements = 0;
                    this.commList = [];
                }
            });
        },

        openDetail(dataTime, inveterSn) {
            let datas = {
                dataTime, inveterSn
            };
            API.inveterDetail(datas).then(res => {
                if (res.data.success) {
                    this.commObj = res.data.result
                    this.modelDetail = true
                } else {
                    this.$Message.error(res.error);
                }
            })
        }
    }
};
</script>
<style lang="scss" scoped>
@import 'style/_cus_list.scss';

.model_view {
    max-height: calc(100vh - 280px);
    overflow: auto;
}
</style>
