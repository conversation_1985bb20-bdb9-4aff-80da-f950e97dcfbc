<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏运维管理</Breadcrumb-item>
            <Breadcrumb-item to="/apv/">运维管理</Breadcrumb-item>
            <Breadcrumb-item>运维知识库</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">文档标题：</p>
                    <Input v-model="searchObj.title" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">类型：</p>
                    <Select v-model="searchObj.type" clearable style="width:120px;">
                        <Option v-for="(item, val) in dataTypeList" :value="val" :key="val">{{ item }}</Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">上传时间：</p>
                    <DatePicker ref="formDateEx" @on-change="data => { return selectDate(data, 'createdAt'); }"
                        format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择"
                        style="width: 220px" />
                </div>
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                </div>
            </div>
            <div class="commListDiv">
                <table class="cus_table" border="0" cellspacing="0" cellpadding="0" width="100%">
                    <tr>
                        <th align="center">序号</th>
                        <th align="left">文档标题</th>
                        <th align="left">文档图像</th>
                        <th align="center">文档类型</th>
                        <th align="center">业务类别</th>
                        <th align="left">文档</th>
                        <th align="center">首次上传时间</th>
                    </tr>
                    <tbody class="tag_line" v-for="(item, index) in commList" :key="index">
                        <tr>
                            <td align="center" width="10%">{{ index + 1 }}</td>
                            <td align="left" width="25%">{{ item.title || '-' }}</td>
                            <td align="center" width="10%">
                                <img :src="pdfImgView(item)" width="60" @click="openpdfImg(item)">
                            </td>
                            <td align="center" width="10%">{{ dataTypeList[item.type] || '-' }}</td>
                            <td align="center" width="10%">{{ businessType[item.businessType] || '-' }}</td>
                            <td align="left" width="25%">
                                <a @click="previewPdfFun(item)">{{ item.title + '.' + item.suffix || '-' }}
                                </a>
                            </td>
                            <td align="center" width="20%" style="min-width: 120px;">{{ item.createAt &&
                        item.createAt.replace('T', ' ') || '-' }}</td>
                        </tr>
                    </tbody>
                    <tbody v-if="!commList.length">
                        <tr>
                            <td colspan="8" align="center">暂无数据</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <Page style="margin:20px;" @on-change="changeCurrent" :current="searchObj.page" :total="totalElements"
                show-total show-elevator />
        </div>
        <Modal class="noprint" v-model="modelPdf" id="modal" sticky scrollable :title="title" :footer-hide="true"
            fullscreen :mask-closable="false" @on-visible-change="pdfVisible">
            <div class="pdf" v-if="pdfUrl">
                <iframe id="iframe" width="100%" height="100%" :src="`${pdfUrl}?page=hsn#toolbar=0`"
                    frameborder="0"></iframe>
            </div>
        </Modal>
    </div>
</template>
<script>
import _data from '@/views/osp/_edata'; // 数据字典
import { knowledgeList } from '@/api/osp';
import { Base64 } from 'js-base64';

export default {
    data() {
        return {
            modelPdf: false,
            searchObj: {
                title: '',
                type: '',
                pageNum: 1,
                pageSize: 10,
                createdAtStart: '',
                createdAtEnd: ''
            },
            numPages: null,
            totalElements: 0,
            dataTypeList: _data.knowledgeType,
            businessType: _data.businessType,
            commList: [],
            pdfUrl: '',
            pdfImgUrl: '',
            title: ''
        };
    },

    mounted() {
        this.getCommList();
        this.disDown();
    },

    updated() {
        this.pdfVisible();
    },

    destroyed() {
        this.stopDisDown();
    },

    methods: {
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.pageNum = curr;
            this.getCommList();
        },

        // 选择日期
        selectDate(date, refObj) {
            this.searchObj[refObj + 'Start'] = date[0];
            this.searchObj[refObj + 'End'] = date[1];
        },

        // 查询按钮
        queryList() {
            this.commList = [];
            this.searchObj.pageNum = 1;
            this.getCommList();
        },

        // 重置按钮
        emptyList() {
            this.searchObj = {
                title: '',
                type: '',
                pageNum: 1,
                pageSize: 10,
                createdAtStart: '',
                createdAtEnd: ''
            }
            this.$refs.formDateEx.handleClear()
        },

        // 文档列表
        getCommList() {
            let datas = this.searchObj;
            knowledgeList(datas).then(res => {
                // console.log(res)
                let request = res.data.result
                if (request.content.length > 0) {
                    this.totalElements = request.totalElements;
                    this.commList = request.content;
                    function getFileExtension(url) {
                        const decodedUrl = Base64.decode(url);
                        const fileName = decodedUrl.substring(decodedUrl.lastIndexOf('/') + 1);
                        return fileName.substring(fileName.lastIndexOf('.') + 1);
                    }
                    this.commList.forEach((res) => {
                        res.suffix = getFileExtension(res.url);
                    });
                } else {
                    this.totalElements = 0;
                    this.commList = []
                }
            });
        },

        disDown() {
            document.oncontextmenu = function () {
                return false;
            }
            document.onkeydown = function (e) {
                if (e.ctrlKey && (e.code === 'KeyA' || e.code === 'KeyC' || e.code === 'KeyD' || e.code === 'KeyF' || e.code === 'KeyI' || e.code === 'KeyJ' || e.code === 'KeyP' || e.code === 'KeyS' || e.code === 'KeyU' || e.code === 'KeyV' || e.code === 'F5')) {
                    return false;
                }
                if (e.key == 'Alt' || e.code == 'F12') {
                    return false
                }
            };
        },

        stopDisDown() {
            document.oncontextmenu = function () {
                return true;
            }
            document.onkeydown = function (e) {
                if (e.ctrlKey && (e.code === 'KeyA' || e.code === 'KeyC' || e.code === 'KeyD' || e.code === 'KeyF' || e.code === 'KeyI' || e.code === 'KeyJ' || e.code === 'KeyP' || e.code === 'KeyS' || e.code === 'KeyU' || e.code === 'KeyV' || e.code === 'F5')) {
                    return true;
                }
                if (e.key == 'Alt' || e.code == 'F12') {
                    return true
                }
            };
        },

        // 预览pdf
        previewPdfFun(sels) {
            this.$Message.loading({
                content: '加载中...',
                duration: 0
            });
            if (sels.url) {
                const currentUrl = Base64.decode(sels.url)
                const fileName = currentUrl.substring(currentUrl.lastIndexOf('/') + 1);
                const fileExtension = fileName.substring(fileName.lastIndexOf('.') + 1);
                if (fileExtension === 'pdf') {
                    this.modelPdf = true
                    this.title = sels.title
                    this.pdfUrl = Base64.decode(sels.url)
                    this.$Message.destroy()
                } else if (fileExtension === 'zip' || fileExtension === 'dwg') {
                    this.pdfUrl = Base64.decode(sels.url)
                    this.$Message.destroy()
                } else {
                    window.open('https://cbs.rrsjk.com/preview/onlinePreview?url=' + encodeURIComponent(sels.url));
                    this.$Message.destroy()
                }

            } else {
                this.$Message.destroy()
                this.$Message.warning('文档打开失败');
            }
        },

        pdfVisible(e) {
            let htmlObj = document.getElementsByTagName("html")
            if (e === false) {
                this.title = ''
                this.pdfUrl = ''
                htmlObj[0].style.overflowY = ""
            } else if (e === true) {
                htmlObj[0].style.overflowY = "hidden"
            }
        },

        pdfImgView(obj) {
            if (obj.picUrl) {
                return Base64.decode(obj.picUrl)
            } else {
                return 'null'
            }
        },

        openpdfImg(obj) {
            if (obj.picUrl) {
                window.open(Base64.decode(obj.picUrl))
            } else {
                this.$Message.warning('暂无图片');
            }
        },
    }
};
</script>
<style lang="scss" scoped>
@import 'style/_cus_list.scss';


.tag_line {
    position: relative;
}

.tag {
    position: absolute;
    top: 50%;
    left: 10px;
    transform: translateY(-50%);
    opacity: 0.3;
}

.tips {
    padding: 20px 20px 0;
    font-size: 12px;
    color: #999;
}

:deep(.ivu-modal-fullscreen) {
    width: 100vw !important;
}

.pdf {
    width: 100%;
    height: 100%;
}

@media print {

    .cus_list {
        display: none
    }

    .noprint {
        display: none
    }
}
</style>
