<!-- 运维工单 -->
<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏运维管理</Breadcrumb-item>
            <Breadcrumb-item to="/osp/">运维管理</Breadcrumb-item>
            <Breadcrumb-item>运维工单</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">运维单号：</p>
                    <Input v-model="searchObj.workOrderSn" placeholder="请输入..." clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">电站编码：</p>
                    <Input v-model="searchObj.stationCode" placeholder="请输入..." clearable style="width: 150px" />
                </div>
                <div class="condition">
                    <p class="condition_p">电站名字：</p>
                    <Input v-model="searchObj.stationName" placeholder="请输入..." clearable style="width: 150px" />
                </div>
                <div class="condition">
                    <p class="condition_p">逆变器SN码：</p>
                    <Input v-model="searchObj.inveterSn" placeholder="请输入..." clearable style="width: 150px" />
                </div>
                <!-- <div class="condition">
          <p class="condition_p">工单类型：</p>
          <Select v-model="searchObj.install" clearable style="width: 120px">
            <Option v-for="item in installList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </div> -->
                <div class="condition">
                    <p class="condition_p">分中心：</p>
                    <Select v-model="searchObj.subCenterCode" clearable style="width: 120px">
                        <Option v-for="item in subCenterList" :value="item.value" :key="item.value">{{ item.label }}
                        </Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">工单状态：</p>
                    <Select v-model="searchObj.status" clearable style="width: 120px">
                        <Option v-for="item in orderStatusList" :value="item.value" :key="item.value">{{ item.label }}
                        </Option>
                    </Select>
                </div>
                <div class="condition">
                    <p class="condition_p">超期申诉审核状态：</p>
                    <Select v-model="searchObj.appealStatus" clearable style="width: 120px">
                        <Option v-for="item in qualsStatus" :value="item.value" :key="item.value">{{ item.label }}
                        </Option>
                    </Select>
                </div>
            </div>
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">故障/告警等级：</p>
                    <Input v-model="searchObj.faultLevel" placeholder="请输入..." clearable style="width: 150px" />
                </div>
                <div class="condition">
                    <p class="condition_p">派单时间：</p>
                    <DatePicker ref="formDateSh" :value="date" @on-change="(data) => {
                        return selectDate(data, 'create');
                    }
                        " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择"
                        style="width: 240px">
                    </DatePicker>
                </div>
                <div class="condition">
                    <p class="condition_p">工单关闭时间：</p>
                    <DatePicker ref="formDateSh" :value="date" @on-change="(data) => {
                        return selectDate(data, 'stop');
                    }
                        " format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="请选择"
                        style="width: 240px">
                    </DatePicker>
                </div>
            </div>
            <div class="list_but">
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                    <Button @click="debounce(exportList)" type="success">导出</Button>
                </div>
            </div>
            <div class="commListDiv">
                <table class="cus_table" border="0" cellspacing="0" cellpadding="0" width="100%">
                    <tr>
                        <th align="center">运维单号</th>
                        <th align="center">电站编码</th>
                        <th align="center">逆变器SN码</th>
                        <!-- <th align="center">工单类型(一级)</th>
            <th align="center">工单类型(二级)</th>
            <th align="center">工单类型(三级)</th> -->
                        <th align="center">工单状态</th>
                        <th align="center">服务人员</th>
                        <th align="center">服务人员手机号码</th>
                        <th align="center">联系人</th>
                        <th align="center">联系人手机号码</th>
                        <th align="center">电站业主</th>
                        <th align="center">业主联系方式</th>
                        <th align="center">区域</th>
                        <th align="center">详细地址</th>
                        <th align="center">所属分中心</th>
                        <th align="center">故障类型</th>
                        <th align="center">代码</th>
                        <th align="center">等级</th>
                        <th align="center">描述</th>
                        <th align="center">当前告警次数</th>
                        <th align="center">产品类型</th>
                        <th align="center">产品品牌</th>
                        <th align="center">产品型号</th>
                        <th align="center">预约上门时间</th>
                        <th align="center">派工时间</th>
                        <th align="center">完工时间</th>
                        <th align="center">超期天数</th>
                        <th align="center">工单来源</th>

                        <th align="center">是否在保质期</th>
                        <th align="center">运维业务类型</th>
                        <th align="center">超期申诉描述</th>
                        <th align="center">超期申诉见证资料</th>
                        <th align="center">超期申诉提交时间</th>
                        <th align="center">超期申诉审核状态</th>
                        <th align="center">审核人</th>
                        <th align="center">审核时间</th>
                        <th align="center">审核驳回原因</th>
                        <th class="fixedRight" align="center">操作</th>
                    </tr>
                    <tr v-for="(item, index) in commList" :key="index">
                        <td align="center" width="80">{{ item.workOrderSn || "-" }}</td>
                        <td align="center" width="120">{{ item.stationCode || "-" }}</td>
                        <td align="center" width="120">{{ item.inveterSn || "-" }}</td>
                        <!-- <td align="center" width="70">{{ item.name || "-" }}</td>
            <td align="center" width="70">{{ item.phone || "-" }}</td>
            <td align="center" width="100">{{ item.idCardNumber || "-" }}</td> -->
                        <td align="center" width="200" style="min-width: 120px">
                            {{ typeVal("orderStatusList", item.status) || "-" }}
                        </td>
                        <td align="center" width="120" style="min-width: 110px">{{ item.opStaff || "-" }}</td>
                        <td align="center" width="120" style="min-width: 110px">
                            {{ item.opStaffMobile ? getEncryptedPhone(item.opStaffMobile, 'phone') || '-' : '-' }}
                        </td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.idCardNumber || "-" }}</td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.idCardNumber || "-" }}</td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.stationName || "-" }}</td>
                        <td align="center" width="100" style="min-width: 100px">
                            {{ item.opStaffMobile ? getEncryptedPhone(item.stationPhone, 'phone') || '-' : '-' }}
                        </td>
                        <td align="center" width="160" style="min-width: 160px">{{ item.area || "-" }}</td>
                        <td align="center" width="200" style="min-width: 150px">{{ item.stationAddress || "-" }}</td>
                        <td align="center" width="200" style="min-width: 150px">{{ item.subCenterName || "-" }}</td>
                        <td align="center" width="100" style="min-width: 100px">
                            {{ typeVal("faultCodeTypeList", item.faultCodeType) || "-" }}
                        </td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.faultCode || "-" }}</td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.faultLevel || "-" }}</td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.faultCodeDesc || "-" }}</td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.count || "-" }}</td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.productType || "-" }}</td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.brandName || "-" }}</td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.sku || "-" }}</td>
                        <td align="center" width="160" style="min-width: 160px">
                            {{ item.doorTime && item.doorTime.replace("T", "") || "-" }}</td>
                        <td align="center" width="160" style="min-width: 160px">
                            {{ item.arrangeUserTime && item.arrangeUserTime.replace("T", " ") || "-" }}
                        </td>
                        <td align="center" width="160" style="min-width: 160px">
                            {{ item.completeTime && item.completeTime.replace("T", " ") || "-" }}
                        </td>
                        <td align="center" width="110" style="min-width: 110px">{{ item.expireDays || "-" }}</td>
                        <td align="center" width="100" style="min-width: 120px">{{ typeVal("sourceArr", item.source) ||
                            "-" }}</td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.isWarranty || "-" }}</td>
                        <td align="center" width="100" style="min-width: 100px">
                            {{ typeVal('businessType', item.businessType) || '-' }}
                        </td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.appealDescription || "-" }}</td>
                        <td align="center" width="100" style="min-width: 100px">
                            <a v-if="item.appealUrl" @click="debounce(downFile(item.appealUrl))" target="_blank">查看</a>
                            <span v-else>-</span>
                        </td>
                        <td align="center" width="100" style="min-width: 200px">{{ item.appealAt || "-" }}</td>
                        <td align="center" width="100" style="min-width: 100px">
                            {{ typeVal('qualsStatus', item.appealStatus) || "-" }}
                        </td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.audiBy || "-" }}</td>
                        <td align="center" width="100" style="min-width: 200px">{{ item.audiAt || "-" }}</td>
                        <td align="center" width="100" style="min-width: 100px">{{ item.audiRemark || "-" }}</td>
                        <td class="fixedRight" align="center" width="230" style="min-width: 230px">
                            <Button type="info" ghost size="small"
                                v-if="item.status == 'WAIT_DISPATCH' || item.status == 'DISPATCHED'"
                                @click="getView(item)" style="margin: 3px">{{ item.status == 'WAIT_DISPATCH' ? "一键派工" :
                                    "转派" }}</Button>
                            <Button type="info" ghost size="small"
                                @click="jumpto('/operationList/details/', item.workOrderSn, item.count)"
                                style="margin: 3px">详情</Button>
                            <Button type="info" ghost size="small" @click="spareApple(item)"
                                style="margin: 3px">工单备件</Button>
                            <Button type="info" ghost size="small" v-if="item.status !== 'CLOSED'"
                                @click="submitReason(item)" style="margin: 3px">过程记录</Button>
                            <Button type="info" ghost size="small"
                                v-if="isWithinDateRange && (item.source === 'STATION_ALARM' && item.businessType === 1 && item.status === 'CLOSED' && item.appealStatus !== 'WAIT_AUDI' && item.appealStatus !== 'AUDI_OK')"
                                @click="appeal(item)" style="margin: 3px">
                                {{ item.appealStatus === 'AUDI_REJECT' ? "修改申诉" : "发起申诉" }}
                            </Button>
                            <!-- <Button type="info" ghost size="small" @click="getComplete(item.workOrderSn)" style="margin: 3px">确认完工</Button> -->
                        </td>
                    </tr>
                    <tr v-if="!commList.length">
                        <td colspan="25" align="center">暂无数据</td>
                    </tr>
                </table>
            </div>
            <Page style="margin: 20px" @on-change="changeCurrent" :current="searchObj.page" :total="totalElements"
                show-total show-elevator />
        </div>
        <Modal width="600" v-model="modelEdit" draggable sticky scrollable :title="title" :footer-hide="true"
            :mask-closable="false">
            <table class="cus_table view_table" border="0" cellspacing="0" cellpadding="0" :model="formObj">
                <tr>
                    <td>*请选择运维工单{{ formObj.workOrderSn }}的服务人员</td>
                </tr>
                <tr>
                    <td>
                        <Select v-model="formObj.staffNoNew" clearable style="width: 120px">
                            <Option v-for="item in staffList" :value="item.staffNo" :key="item.id">{{ item.staffName }}
                            </Option>
                        </Select>
                    </td>
                </tr>
                <tr>
                    <td>
                        <p v-if="formObj.opStaff">原服务人员：{{ formObj.opStaff }} {{ formObj.opStaffMobile }}</p>
                    </td>
                </tr>
                <tr>
                    <td align="center">
                        <Button size="small" @click="cancel()" style="margin: 3px">取消</Button>
                        <Button type="info" ghost size="small" @click="debounce(changeStaff)"
                            style="margin: 3px">确认</Button>
                    </td>
                </tr>
            </table>
        </Modal>
        <Modal width="600" v-model="submitInfo" draggable sticky scrollable :title="title" :footer-hide="true"
            :mask-closable="false">
            <div style="width: 100%;">
                <h3 style="padding-left: 5px;">基础信息</h3>
                <Form :label-width="120" inline>
                    <FormItem label="电站编码：" prop="information" class="formStyle">{{ submitInfoObj.stationCode }}
                    </FormItem>
                    <FormItem label="电站业主名称：" prop="problem" class="formStyle">{{ submitInfoObj.stationName }}
                    </FormItem>
                </Form>
            </div>
            <div style="width: 100%; margin-top: 10px;">
                <Tabs value="name1">
                    <TabPane label="描述信息" name="name1">
                        <Form :model="formItem" :label-width="120" style="width: 100%;" ref="formItem" inline>
                            <FormItem label="故障：" prop="falutAppearanceId" style="width: 100%;"
                                :rules="{ required: true, message: ' ' }">
                                <div class="formSel">
                                    <Select v-model="formItem.falutAppearanceId" style="width: 30%" placeholder="选择故障现象"
                                        @on-change="handleSelectionChange">
                                        <Option v-for="item in phenomenonList" :value="item.id" :key="item.id">
                                            {{ item.falutAppearance }}
                                        </Option>
                                    </Select>
                                    <Select v-model="formItem.falutReasonId" style="width: 30%" placeholder="选择故障原因"
                                        @on-change="findSecondChange">
                                        <Option v-for="item in reasonList" :value="item.id" :key="item.id">{{
                                            item.falutReason }}
                                        </Option>
                                    </Select>
                                    <Select v-model="formItem.resolventId" style="width: 30%" placeholder="选择解决方法">
                                        <Option v-for="item in thirdList" :value="item.id" :key="item.id">{{
                                            item.resolvent }}
                                        </Option>
                                    </Select>
                                </div>
                            </FormItem>
                            <FormItem label="过程描述：" prop="remark" style="width: 100%"
                                :rules="{ required: true, message: ' ' }">
                                <Input class="item_large" type="textarea" style="width: 100%" v-model="formItem.remark"
                                    clearable placeholder="请填写" />
                            </FormItem>
                        </Form>
                        <div style="text-align: center; margin: 30px 0">
                            <Button type="default" size="large" style="width: 120px; margin-right: 30px"
                                @click="submitInfo = false">取消</Button>
                            <Button type="primary" size="large" style="width: 120px" @click="submit">
                                提交
                            </Button>
                        </div>
                    </TabPane>
                    <TabPane label="服务过程记录" name="name2">
                        <div class="tabDiv">
                            <table class="cus_table" border="0" cellspacing="0" cellpadding="0" width="100%">
                                <tr>
                                    <th class="fixedLeft" align="center">序号</th>
                                    <th align="center">操作时间</th>
                                    <th align="center">操作人</th>
                                    <th align="center">来源</th>
                                    <th align="center">故障现象</th>
                                    <th align="center">故障原因</th>
                                    <th align="center">故障解决方法</th>
                                    <th align="center">维修前图片</th>
                                    <th align="center">维修后图片</th>
                                    <!-- <th align="center">备注</th> -->
                                    <th align="center">过程描述</th>
                                </tr>
                                <tr v-for="(item, index) in itemArr" :key="index">
                                    <td align="center" class="fixedLeft" width="80">{{ index + 1 }}</td>
                                    <td align="center" style="min-width: 160px">{{ item.createdAt?.replace("T", " ") ||
                                        "-" }}</td>
                                    <td align="center" style="min-width: 160px" width="80">{{ item.createdBy || "-" }}
                                    </td>
                                    <td align="center" style="min-width: 80px" width="80">{{ typeVal('recordSource',
                                        item.source) || "-"
                                        }}</td>
                                    <td align="center" style="min-width: 100px" width="80">{{ item.falutAppearance ||
                                        "-" }}</td>
                                    <td align="center" width="120">{{ item.falutReason || "-" }}</td>
                                    <td align="center" style="min-width: 120px" width="70">{{ item.resolvent || "-" }}
                                    </td>
                                    <td align="center" width="70">
                                        <a v-if="item.beforeUrl" :href="item.beforeUrl" target="_blank">查看</a>
                                    </td>
                                    <td align="center" width="100">
                                        <a v-if="item.afterUrl" :href="item.afterUrl" target="_blank">查看</a>
                                    </td>
                                    <!-- <td align="center" width="100">{{ item.remark || "-" }}</td> -->
                                    <td align="center" width="80">{{ item.processRemark || "-" }}</td>
                                </tr>
                                <tr v-if="!itemArr.length">
                                    <td colspan="25" align="center">暂无数据</td>
                                </tr>
                            </table>
                        </div>

                    </TabPane>
                </Tabs>
            </div>

        </Modal>
        <Modal width="700" v-model="appealModal" draggable sticky scrollable title="超期申诉" :footer-hide="true"
            :mask-closable="false">
            <div>
                <p class="cus_title" style="margin-bottom: 10px;">基础信息</p>
                <Form :label-width="150">
                    <FormItem label="运维单号：" prop="workOrderSn" style="margin-bottom: 0;">
                        {{ appealObj.workOrderSn || '-' }}
                    </FormItem>
                    <FormItem label="电站编码：" prop="stationCode" style="margin-bottom: 0;">
                        {{ appealObj.stationCode || '-' }}
                    </FormItem>
                    <FormItem label="电站业主名称：" prop="stationName" style="margin-bottom: 0;">
                        {{ appealObj.stationName || '-' }}
                    </FormItem>
                    <FormItem label="地址：" prop="stationAddress" style="margin-bottom: 0;">
                        {{ appealObj.stationAddress || '-' }}
                    </FormItem>
                </Form>
            </div>
            <p class="cus_title">申诉信息</p>
            <Form :model="appealItem" :label-width="150" ref="appealItem" inline>
                <FormItem label="过程描述：" prop="appealDescription" style="width: 100%"
                    :rules="{ required: true, message: ' ' }">
                    <Input class="item_large" type="textarea" style="width: 100%" v-model="appealItem.appealDescription"
                        clearable placeholder="请填写" />
                </FormItem>
                <FormItem label="上传见证性资料：" prop="url" style="width: 100%" :rules="{ required: true, message: ' ' }">
                    <Upload class="inlinebox" accept=".zip" :show-upload-list="false"
                        :action="UploadClass.actionUrl('file')" :headers="UploadClass.headers"
                        :before-upload="UploadClass.progress"
                        :on-success="(response) => { appealItem.url = response.result[0].fileUrl; appealItem.importFileName = response.result[0].fileName; return UploadClass.upSuccess() }"
                        :format="['zip']" :max-size="102400" :on-format-error="UploadClass.formatError"
                        :on-exceeded-size="UploadClass.maxSizeError">
                        <Button type="primary" icon="ios-folder">{{ appealItem.url ? '重新' : ''
                            }}上传（.zip）</Button>
                    </Upload>
                    <!-- <a v-if="appealItem.url" :href='appealItem.url' target="_blank"
                        style="white-space: nowrap;font-size: 15px;">
                        <Icon type="md-document" />
                        {{ appealItem.importFileName || "见证性资料压缩包.zip" }}
                    </a> -->
                    <span v-if="appealItem.url" style="margin-top: 10px;">
                        <Icon type="md-document" /> {{ appealItem.importFileName || "见证性资料压缩包.zip" }}
                    </span>
                    <Icon v-if="appealItem.url" type="ios-close-circle-outline" color="#dd3c3c"
                        @click="appealItem.url = '', appealItem.importFileName = ''"
                        style="margin-left: 10px;cursor: pointer;" />
                    <p class="tips" style="margin-top: 20px;text-align: left;line-height: 1.5;">
                        <Icon type="md-information-circle" />
                        请上传见证性资料压缩包
                    </p>
                </FormItem>

            </Form>
            <div style="text-align: center; margin: 30px 0">
                <Button type="default" size="large" style="width: 120px; margin-right: 30px"
                    @click="appealModal = false">取消</Button>
                <Button type="primary" size="large" style="width: 120px" @click="debounce(appealSubmit)">
                    确认
                </Button>
            </div>
        </Modal>
        <spareApply ref="spareApplyRef" @getList="getCommList" :row-data="rowData" />
    </div>
</template>
<script>
import spareApply from "./spareApply";
import _data from "@/views/osp/_edata"; // 数据字典
import API from "@/api/osp";
import dataEncryption from '@/utils/encryption'
import { UploadClass } from '@/utils/common';
import { getDicts } from "@/api/public/dict";
export default {
    name: "operationList",
    components: {
        spareApply
    },
    data() {
        return {
            modelEdit: false,
            appealModal: false,
            searchObj: {
                status: "",
                workOrderSn: "",
                stationCode: "",
                faultLevel: "",
                pageNum: 1,
                pageSize: 10,
                createStart: "",
                createEnd: "",
                subCenterCode: "",
                stationName: "",
                stopStart: "",
                stopEnd: "",
                inveterSn: "",
                appealStatus: "",
            },
            appealObj: {},
            appealItem: {
                appealDescription: "",
                url: "",
            },
            formObj: {},
            itemArr: [],
            staffList: [],
            totalElements: 0,
            date: [],
            submitInfo: false,
            formItem: {
                falutAppearanceId: '',
                falutReasonId: '',
                resolventId: '',
                remark: '',
                source: '',
                workOrderSn: '',
            },
            rowData: {},
            loading: false,
            submitInfoObj: {},
            phenomenonList: [],
            reasonList: [],
            thirdList: [],
            commList: [],
            title: "一键派工",
            status: _data.status,
            orderStatusList: _data.orderStatusList,
            subCenterList: _data.subCenterList,
            sourceArr: _data.SourceArr,
            faultCodeTypeList: _data.faultCodeTypeList,
            recordSource: _data.recordSource,
            businessType: [],
            merchantType: [],
            qualsStatus: [],
            UploadClass,
            actionUrl: `${process.env.VUE_APP_BASE_API}/merchant/oss/image/upload.do`,
            headers: { Authorization: `Bearer ${JSON.parse(window.localStorage.getItem('logins')).access_token}` },
        };
    },
    computed: {
        isWithinDateRange() {
            const currentDate = new Date();
            const day = currentDate.getDate();
            return day >= 20 && day <= 25;
        },
    },
    mounted() {
        this.getFindFirst()
        this.getCommList();
        this.getStaff();
        this.qualsAddWarrantyRole()
    },
    async created() {
        this.merchantType = await getDicts("osp/merchant", "merchantType").then(e => e?.list)
        this.qualsStatus = await getDicts("osp/merchant", "qualsStatus").then(e => e?.list)
        this.businessType = await getDicts("osp/merchant", "businessType").then(e => e?.list)
        this.queryList();
    },
    methods: {
        //增加质保期角色
        qualsAddWarrantyRole() {
            API.qualsAddNewWarrantyRole().then((res) => {
                if (!res.data.success) {
                    this.$Message.error(res.data.error);
                }
            });
        },
        //数据加密
        getEncryptedPhone(data, type) {
            switch (type) {
                case 'phone':
                    return dataEncryption.encryptData(data, 'phone');
                default:
                    return '-';
            }
        },
        // 工单备件申请
        spareApple(row) {
            this.rowData = row
            this.$refs.spareApplyRef.modelEdit = true
            this.$nextTick(() => {
                this.$refs.spareApplyRef.getList()
            })
        },
        jumpto(path, id, count) {
            this.$router.push({
                path,
                query: { id, count },
            });
        },

        // 选择日期
        selectDate(date, refObj) {
            // this.date = date;
            this.searchObj[refObj + "Start"] = date[0];
            this.searchObj[refObj + "End"] = date[1];
        },
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.pageNum = curr;
            this.getCommList();
        },

        // 查询按钮
        queryList() {
            this.searchObj.pageNum = 1;
            this.getCommList();
        },

        // 重置按钮
        emptyList() {
            this.searchObj = {
                status: "",
                workOrderSn: "",
                stationCode: "",
                faultLevel: "",
                pageNum: 1,
                pageSize: 10,
                createStart: "",
                createEnd: "",
                stopStart: "",
                stopEnd: "",
            };
            this.date = [];
        },
        // 获取服务人员
        getStaff() {
            API.getStaffList().then((res) => {
                let request = res.data.result;
                if (res.data.success) {
                    this.staffList = request;
                }
            });
        },

        // 获取工单列表
        getCommList() {
            this.$Message.loading({
                content: "查询中...",
                duration: 0,
            });
            let datas = this.searchObj;
            API.getOprationList(datas).then((res) => {
                this.$Message.destroy();
                let request = res.data.result;
                if (res.data.success & (request.content.length > 0)) {
                    this.totalElements = request.totalElements;
                    this.commList = request.content;
                    request.content.map((e, inx) => {
                        e.area = (e.stationProvinceName || "") + (e.stationCityName || "") + (e.stationRegionName || "");
                    });
                } else {
                    this.totalElements = 0;
                    this.commList = [];
                }
            });
        },

        // 派工/改派弹窗
        getView(e) {
            this.modelEdit = true;
            this.formObj = e;

            if (this.formObj.opStaff) {
                this.title = "改派";
                // this.formObj.staffNo = this.staffList.find((item) => item.staffName === e.opStaff).staffNo;

            } else {
                this.title = "一键派工";
            }
        },

        // 取消
        cancel() {
            this.modelEdit = false;
            this.formObj = {};
        },

        // 派工/改派
        changeStaff() {
            if (this.formObj.opStaffNo === this.formObj.staffNoNew) {
                this.$Message.error("请选择不同的服务人员");
            } else {
                let datas = {
                    workOrderSn: this.formObj.workOrderSn,
                    staffNo: this.formObj.staffNoNew,
                };
                API.changeDispatcher(datas).then((res) => {
                    if (res.data.success) {
                        this.$Message.success("派工成功");
                        this.cancel();
                        this.$Message.destroy();
                        this.getCommList();
                        this.formObj = {};
                    } else {
                        this.$Message.warning(res.data.error);
                    }
                });
            }
        },
        //获取故障现象
        getFindFirst() {
            API.findFirst().then(res => {
                if (res.data.success) {
                    this.phenomenonList = res.data.result
                } else {
                    this.$Message.error(res.data.error);
                }
            });
        },
        //获取故障现象id
        handleSelectionChange(value) {
            const selectedId = value;
            this.getFindSecond(selectedId)
        },
        //获取故障原因
        getFindSecond(id) {
            const params = {
                parentId: id
            }
            API.findSecond(params).then(res => {
                if (res.data.success) {
                    this.reasonList = res.data.result
                } else {
                    this.$Message.error(res.data.error);
                }
            });
        },
        //获取故障原因id
        findSecondChange(value) {
            const thirdId = value;
            this.getfindThird(thirdId)
        },
        //三级故障字典
        getfindThird(id) {
            const params = {
                parentId: id
            }
            API.findThird(params).then(res => {
                if (res.data.success) {
                    this.thirdList = res.data.result
                } else {
                    this.$Message.error(res.data.error);
                }
            });

        },
        //提交故障原因
        submitReason(item) {
            this.getInfo(item.workOrderSn)
            this.submitInfoObj = item
            this.formItem.source = item.source
            this.formItem.workOrderSn = item.workOrderSn
            this.submitInfo = true
        },
        submit() {
            const params = this.formItem;
            if (!params.falutAppearanceId) {
                this.$Message.error('故障现象不能为空');
            } else if (!params.falutReasonId) {
                this.$Message.error('故障原因不能为空');
            } else if (!params.resolventId) {
                this.$Message.error('解决方法不能为空');
            } else if (!params.remark) {
                this.$Message.error('过程描述不能为空');
            } else {
                API.addProcess(params).then(res => {
                    if (res.data.success) {
                        this.$Message.success('提交成功');
                        this.queryList();
                        this.submitInfo = false;
                    } else {
                        this.$Message.error(res.data.error);
                    }
                });
            }
        },
        getInfo(workOrderSn) {
            let datas = {
                workOrderSn: workOrderSn,
            };
            API.getOrderDetails(datas).then((res) => {
                let request = res.data.result;
                if (res.data.success) {
                    this.itemArr = request.workOrderProcessList;
                } else {
                    this.$Message.error(res.data.error);
                }
            });
        },
        //申述
        appeal(item) {
            this.appealObj = item
            const {
                appealDescription,
                appealUrl,
                workOrderSn,
            } = item
            this.appealItem = {
                appealDescription,
                url: appealUrl,
                workOrderSn,
            };
            this.appealModal = true
        },
        appealSubmit() {
            let data = {
                appealDescription: this.appealItem.appealDescription,
                url: this.appealItem.url,
                workOrderSn: this.appealObj.workOrderSn
            }
            if (!data.appealDescription) {
                this.$Message.error('描述不能为空');
            } else if (!data.url) {
                this.$Message.error('请上传见证性资料');
            } else {
                API.appealCreateAppeal(data).then(res => {
                    if (res.data.success) {
                        this.$Message.success('提交成功');
                        this.queryList();
                        this.appealModal = false
                    } else {
                        this.$Message.error(res.data.error);
                    }
                })
            }
        },
        downFile(url) {
            const fileName = '超期申诉见证资料';
            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络响应不正常');
                    }
                    return response.blob();
                })
                .then(blob => {
                    const link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = fileName;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                })
                .catch(error => {
                    console.error('下载文件时出错:', error);
                });
        },
        // 确认完工
        // getComplete(params) {
        //   let datas = {
        //     workOrderSn: params,
        //   };
        //   API.completeOrder(datas).then((res) => {
        //     console.log(res);
        //     if (res.data.success) {
        //       this.$Message.success("已确认完工");
        //     } else {
        //       this.$Message.warning(res.data.error);
        //     }
        //   });
        // },
        // 导出
        exportList() {
            let datas = this.searchObj;
            let { page, size, ...datasEx } = { ...datas };
            API.getDoExport(datasEx)
                .then((res) => {
                    let binaryData = [];
                    let link = document.createElement("a");
                    binaryData.push(res.data);
                    link.style.display = "none";
                    link.href = window.URL.createObjectURL(new Blob(binaryData));
                    link.setAttribute("download", "运维工单.xlsx");
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                })
                .catch(() => {
                    this.$Message.error("导出失败");
                });
        },
        typeVal(list, value) {
            const obj = {};
            this[list].map((e) => {
                obj[e.value] = e.label || e.value;
            });
            return obj[value];
        },
    },
};
</script>
<style lang="scss" scoped>
@import "@/style/_cus_reg.scss";
@import "@/style/_cus_list.scss";
@import '@/style/_ivu-modal.scss';

.formStyle {
    width: 100%;
    margin-bottom: 0;
}

.formSel {
    width: 100%;
    display: flex;
    justify-content: space-between;
}

::v-deep .ivu-tabs-nav {
    font-size: 18px;
}

.tabDiv {
    width: 100%;
    height: 150px;
    overflow-y: scroll;
}
</style>
