<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏运维管理</Breadcrumb-item>
            <Breadcrumb-item to="/apv/">运维管理</Breadcrumb-item>
            <Breadcrumb-item>电站租金查询</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">电站编码：</p>
                    <Input v-model="searchObj.stationCode" placeholder="请输入电站编码" clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">业主姓名：</p>
                    <Input v-model="searchObj.stationName" placeholder="请输入业主姓名" clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                </div>
            </div>
            <div class="commListDiv">
                <table class="cus_table" border="0" cellspacing="0" cellpadding="0" width="100%">
                    <tr>
                        <th align="center" class="fixedLeft1">序号</th>
                        <th align="center">电站编码</th>
                        <th align="center">业主姓名</th>
                        <th align="center">安装地址</th>
                        <th align="center">发电户号</th>
                        <th align="center">装机功率(KW)</th>
                        <th align="center">并网时间</th>
                        <th align="center">操作</th>
                    </tr>
                    <tbody class="tag_line" v-for="(item, index) in commList" :key="index">
                        <tr>
                            <td align="center" class="fixedLeft1">{{ index + 1 }}</td>
                            <td align="center">{{ item.stationCode || '-' }}</td>
                            <td align="center">{{ item.stationName || '-' }}</td>
                            <td align="center">{{ item.installAddress || '-' }}</td>
                            <td align="center">{{ item.elecNo || '-' }}</td>
                            <td align="center">{{ item.completeConfirmPower || '-' }}</td>
                            <td align="center" style="min-width: 200px">
                                {{ item.gridApplyAt && item.gridApplyAt.replace('T', ' ') || '-' }}
                            </td>
                            <td align="center">
                                <Button type="info" ghost size="small"
                                    @click="jumpto('/rentQuery/rentQueryDetail/', item.source, item.stationCode)"
                                    style="margin: 3px">付款详情</Button>
                            </td>
                        </tr>
                    </tbody>
                    <tr v-if="!commList.length">
                        <td colspan="25" align="center">暂无数据</td>
                    </tr>
                </table>
            </div>
            <Page style="margin:20px;" @on-change="changeCurrent" :current="searchObj.page" :total="totalElements"
                show-total show-elevator />
        </div>
    </div>
</template>
<script>
import _data from '@/views/osp/_edata'; // 数据字典
import { accountRecordByOp } from '@/api/osp';
export default {
    name: "rentQuery",
    data() {
        return {
            searchObj: {
                stationCode: '',
                stationName: '',
                page: 1,
                size: 10,
            },
            commList: [],
            numPages: null,
            totalElements: 0,
        };
    },

    mounted() {
        this.getCommList();
    },

    methods: {
        jumpto(path, source, stationCode) {
            this.$router.push({
                path,
                query: { source, stationCode },
            });
        },
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.page = curr;
            this.getCommList();
        },

        // 查询按钮
        queryList() {
            this.commList = [];
            this.searchObj.page = 1;
            this.getCommList();
        },

        // 电站列表
        getCommList() {
            let datas = this.searchObj;
            accountRecordByOp(datas).then(res => {
                let request = res.data.result
                if (request.content.length > 0) {
                    this.commList = request.content;
                    this.totalElements = request.totalElements;
                } else {
                    this.totalElements = 0;
                    this.commList = []
                }
            });
        },
        // 重置按钮
        emptyList() {
            this.searchObj = {
                stationCode: '',
                stationName: '',
                page: 1,
                size: 10,
            }
        },

    }
};
</script>
<style lang="scss" scoped>
@import 'style/_cus_list.scss';

.fixedLeft1 {
    position: sticky;
    left: 0;
    @extend .back-filter;
}

.tag_line {
    position: relative;
}

:deep(.ivu-modal-fullscreen) {
    width: 100vw !important;
}

@media print {

    .cus_list {
        display: none
    }

    .noprint {
        display: none
    }
}
</style>
