<template>
    <Modal width="800" v-model="modelEdit" draggable sticky scrollable :title="modelTitle" :footer-hide="true" :mask-closable="false">
        <div style="position: relative; width: 100%; height: 60vh;">
            <div style="padding-bottom: 40px; width: 100%; height: 60vh; overflow-y: auto">
                <div class="list_but" style="margin-bottom: 10px">
                    <div class="condition">
                        <Button @click="handleAdd" size="small" type="primary"> <Icon type="md-add"></Icon>新增地址</Button>
                        <span style="margin-left: 10px">您已的创建{{addressTotal}}个地址，最多创建25个。</span>
                    </div>
                </div>
                <Card class="addressCard" v-for="(item,index) in userAddressList" :key="index" >
                    <p class="cancel">
                        <span @click="handleDel(item)" size="small" type="text" style="float: right;font-size: 15px;color:#999;cursor: pointer;"><Icon type="md-close"></Icon></span>
                    </p>
                    <div @click.stop="choseAddress(item)">
                        <ul>
                            <li>
                                <p class="text">收货人:{{item.linkMan}}<Button  v-if="item.ifDefault=='1'" type="warning" ghost size="small" style="margin-left: 10px;"> 默认</Button></p>
                                <p class="text">所在地区:{{item.province}}{{item.city}}{{item.region}}</p>
                                <p class="text">手机:{{item.phone}}</p>
                                <p class="text">地址:{{item.receiveAddress}}</p>
                                <p class="btn-group">
                                    <Button v-if="item.ifDefault=='0'" @click.stop="handleDefault(item.id)" size="small" type="text" style="color: #2d8cf0;margin-right: 10px;">设为默认地址</Button>
                                    <Button @click.stop="handleEdit(item.id)" size="small" type="text" style="color: #2d8cf0;">编辑</Button>
                                </p>
                            </li>
                        </ul>
                    </div>
                </Card>
                <Card class="addressCard" style="display: none">

                </Card>
                <!-- 添加 -->
                <Modal width="900" v-model="modelEditAdd" draggable sticky scrollable :title="childTitle" :footer-hide="true" :styles="{ top: 'calc(100px + 4vh)' }" :mask-closable="false" @on-cancel="handleCancel">
                    <div style="position: relative; width: 100%; height: 50vh;">
                        <div style="padding-bottom: 40px; width: 100%; height: 50vh; overflow-y: auto">
                            <Form :model="addDetailQuery" :rules="ruleValidate" :label-width="100" ref="formItem" inline>
                                <div>
                                    <FormItem label="收货人" prop="linkMan">
                                        <Input type="text" maxlength="15" v-model="addDetailQuery.linkMan" clearable placeholder="收货人" style="width:150px;margin-right:5px;" />
                                    </FormItem>
                                    <FormItem label="手机号码" prop="phone">
                                        <Input type="text" maxlength="15" v-model="addDetailQuery.phone" clearable placeholder="手机号码"
                                               style="width:150px;margin-right:5px;" />
                                    </FormItem>
                                </div>
                                <div>
                                    <FormItem label="所在省" prop="provinceCode">
                                        <Select v-model="addDetailQuery.provinceCode" @on-change="typeOneChange" filterable style="width:150px;margin-right:5px;">
                                            <Option v-for="(item, index) in addressOneList" :value="item.id" :key="index">{{ item.name }}
                                            </Option>
                                        </Select>
                                    </FormItem>
                                    <FormItem label="所在市" prop="cityCode">
                                        <Select v-model="addDetailQuery.cityCode" @on-change="typeTwoChange" filterable style="width:150px;margin-right:5px;">
                                            <Option v-for="(item, index) in addressTwoList" :value="item.id" :key="index">{{ item.name }}
                                            </Option>
                                        </Select>
                                    </FormItem>
                                    <FormItem label="所在区" prop="regionCode">
                                        <Select v-model="addDetailQuery.regionCode" style="width:150px;" filterable @on-change="typeThreeChange" >
                                            <Option v-for="(item, index) in addressThreeList" :value="item.id" :key="index">{{ item.name }}
                                            </Option>
                                        </Select>
                                    </FormItem>
                                </div>
                                <FormItem label="详细地址" prop="receiveAddress" >
                                    <Input type="textarea" maxlength="100" v-model="addDetailQuery.receiveAddress" clearable placeholder="详细地址" style="width: 750px" />
                                </FormItem>
                            </Form>
                        </div>
                        <div style="position: absolute; z-index: 100; right: 20px; bottom: -30px; text-align: right; margin: 30px 0">
                            <Button type="primary"
                                    @click="debounce(submitSelect)">保存</Button>
                            <Button type="default" style="margin-left: 10px"
                                    @click="handleCancel">取消</Button>
                        </div>
                    </div>
                </Modal>
            </div>
        </div>
    </Modal>
</template>

<script>
import Common from "@/api/ospSpare/common";
import _data from "@/views/ospSpare/_edata"; // 数据字典
import { getRegionList } from "@/api";
import API from '@/api/ospSpare/orderManage/address'
export default {
    name: "addEdit",
    data() {
        return {
            modelEdit: false,
            modelTitle: '收件地址',
            childTitle: '新增地址',
            orderId: '',
            data: {},
            queryPage: { // 查询条件 No页数 Size每页大小
                page: 1,
                rows: 10
            },
            modelEditAdd: false,
            addDetailQuery: {
                linkMan: '',
                phone: '',
                provinceCode: '',
                province: '',
                cityCode: '',
                city: '',
                regionCode: '',
                region: '',
                receiveAddress: ''
            },
            selectTableData: [], // 添加明细列表
            selectTableTotal: 0, // 添加明细列表总条数
            productMap: {},
            materialBrandOptions:[],
            materialBrandMap:{},
            materialTypeOptions:[],
            materialTypeMap:{},
            addressList:{},
            addressOneList: [],
            addressTwoList: [],
            addressThreeList: [],
            userAddressList: [],
            addressTotal:'',
            ruleValidate: {
                linkMan: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                phone: [
                    { required: true, message: '请输入', trigger: 'blur' },
                ],
                provinceCode: [
                    { required: true, message: '请选择', trigger: 'change' }
                ],
                cityCode: [
                    { required: true, message: '请选择', trigger: 'change' }
                ],
                regionCode: [
                    { required: true, message: '请选择', trigger: 'change' }
                ],
                receiveAddress: [
                    { required: true, message: '请输入', trigger: 'change' }
                ]
            },
        }
    },
    watch: {
        // 打开新增弹窗
        modelEdit(val) {
            if (val) {
                this.getList()
            }
        },
        // 打开添加明细弹窗
        modelEditAdd(val) {
            if (val) {

            }
        }
    },
    mounted() {
        this.getaddressList();
    },
    methods: {
        // 选择地址
        choseAddress(item){
            this.$emit('choseAddress',item)
            this.modelEdit=false
        },
        // 设置为默认
        handleDefault(id){
            API.updateDefault({ 'id':id }).then(res=>{
                if(res.data.success){
                    this.$Message.destroy();
                    this.$Message.success(res.data.result);
                    this.getList()
                }else {
                    this.$Message.destroy();
                    this.$Message.error(res.data.error);
                }
            })
        },
        // 编辑地址
        handleEdit(id){
            API.editLoading(id).then(res=>{
                if(res.data.success){
                    //根据省加载市下拉数据
                    let str = res.data.result.provinceCode.substring(0, 2);
                    for (let i in this.addressList.city_list) {
                        if (str === i.substring(0, 2)) {
                            let obj = {
                                id: i,
                                name: this.addressList.city_list[i]
                            };
                            this.addressTwoList.push(obj);
                        }
                    }
                    //根据 市 加载 区 下拉数据
                    let str2 = res.data.result.cityCode.substring(0, 4);
                    for (let i in this.addressList.county_list) {
                        if (str2 === i.substring(0, 4)) {
                            let obj = {
                                id: i,
                                name: this.addressList.county_list[i]
                            };
                            this.addressThreeList.push(obj);
                        }
                    }
                    this.addDetailQuery=res.data.result
                    this.modelEditAdd = true
                    this.childTitle="编辑地址"
                }else {
                    this.$Message.destroy();
                    this.$Message.error(res.data.error);
                }
            })
        },
        // 删除地址
        handleDel(item){
            this.$message.confirm('此操作将永久删除该文件, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$Message.loading({
                    content: "删除中...",
                    duration: 0,
                });
                let data={
                    id: item.id,
                    warehouseId: item.warehouseId,
                    ifDefault: item.ifDefault,
                }
                API.delete(data).then(res => {
                    if(res.data.success) {
                        this.$Message.destroy();
                        this.$Message.success(res.data.result);
                        this.getList()
                    } else {
                        this.$Message.destroy();
                        this.$Message.error(res.data.error);
                    }
                })
            })
        },
        // 新增地址
        handleAdd(){
            this.modelEditAdd = true
            this.childTitle="新增地址"
        },
        // 获取省市区
        getaddressList() {
            getRegionList().then(res => {
                for (let i in res.data.result.province_list) {
                    let obj = {
                        id: i,
                        name: res.data.result.province_list[i]
                    };
                    this.addressOneList.push(obj);
                }
                this.addressList = res.data.result;
            });
        },
        // 省改变
        typeOneChange(item) {
            this.addressTwoList = [];
            this.addDetailQuery.city = '';
            this.addDetailQuery.cityCode = '';
            this.addDetailQuery.region = '';
            this.addDetailQuery.regionCode = '';
            if (!item) return;
            this.addressOneList.forEach(d=>{
                if(item ==d.id){
                    this.addDetailQuery.province=d.name
                }
            })
            let str = item.substring(0, 2);
            for (let i in this.addressList.city_list) {
                if (str === i.substring(0, 2)) {
                    let obj = {
                        id: i,
                        name: this.addressList.city_list[i]
                    };
                    this.addressTwoList.push(obj);
                }
            }
        },
        // 市变化
        typeTwoChange(item) {
            this.addressThreeList = [];
            this.addDetailQuery.region = '';
            this.addDetailQuery.regionCode = '';
            if (!item) return;
            this.addressTwoList.forEach(d=>{
                if(item ==d.id){
                    this.addDetailQuery.city=d.name
                }
            })
            let str = item.substring(0, 4);
            for (let i in this.addressList.county_list) {
                if (str === i.substring(0, 4)) {
                    let obj = {
                        id: i,
                        name: this.addressList.county_list[i]
                    };
                    this.addressThreeList.push(obj);
                }
            }
        },
        // 区变化
        typeThreeChange(item) {
            if (!item) return;
            this.addressThreeList.forEach(d=>{
                if(item ==d.id){
                    this.addDetailQuery.region=d.name
                }
            })
        },
        handleReset(){
            this.addDetailQuery={
                linkMan: '',
                    phone: '',
                    provinceCode: '',
                    province: '',
                    cityCode: '',
                    city: '',
                    regionCode: '',
                    region: '',
                    receiveAddress: ''
            }
        },
        handleCancel(){
            this.modelEditAdd=false
            this.handleReset()
        },
        // 新增订单初始化
        add_init() {

        },
        // 编辑订单初始化
        edit_init() {

        },
        submitSelect() {
            this.$refs.formItem.validate((valid) => {
                if (valid) {
                    let params=JSON.parse(JSON.stringify(this.addDetailQuery))
                    API.addList(params).then(res=>{
                        if(res.data.success) {
                            this.$Message.destroy();
                            this.$Message.success(res.data.result);
                            this.modelEditAdd=false
                            this.getList()
                            this.handleReset()
                        } else {
                            this.$Message.destroy();
                            this.$Message.error(res.data.error);
                        }
                    })
                }
            })
        },
        getList(){
            API.getList({}).then(res=>{
                if(res.data.success){
                    this.userAddressList=res.data.result.list
                    this.addressTotal=res.data.result.total
                }else {
                    this.$Message.destroy();
                    this.$Message.error(res.data.error);
                }
            })
        },
        submitPop() {
        }
    }
}
</script>

<style lang="scss" scoped>
@import '@/style/_cus_list.scss';

.res_btn {
    padding: 20px 0;
    text-align: center;
}
.AddrssButtom{
    position: absolute;
    right: 112px;
    bottom: 40px;
    color: #2d8cf0;
}
.text{
    margin-bottom: 15px;
}
.btn-group{
    display: flex;
    justify-content: flex-end;
}
.addressCard{
    width: 750px;
    margin-bottom: 15px;
}
.cancel{
    position: absolute;
    right: 10px;
}
</style>
