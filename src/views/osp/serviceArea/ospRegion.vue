<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏运维管理 </Breadcrumb-item>
            <Breadcrumb-item to="/osp/">基础信息</Breadcrumb-item>
            <Breadcrumb-item>服务区域</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <!--  v-if="spService" -->
                <div class="condition">
                    <ButtonGroup>
                        <Button @click="addRegionsList" type="primary" icon="md-add">新增区域申请</Button>
                    </ButtonGroup>
                </div>
            </div>
            <div class="commListDiv">
                <table class="cus_table" border="0" cellspacing="0" cellpadding="0" width="100%">
                    <tr>
                        <th align="center">序号</th>
                        <th align="center">区域名称</th>
                        <th align="center">申请时间</th>
                        <th align="center">状态</th>
                        <th align="center">驳回原因</th>
                        <th align="center">授权时间</th>
                        <!-- v-if="spService" -->
                        <th align="center">操作</th>
                    </tr>
                    <tr v-for="(item, index) in commList" :key="index">
                        <td align="center" width="50">{{ index + 1 }}</td>
                        <td align="center" width="150">{{ item.provinceName }} {{ item.cityName }} {{ item.regionName }}
                        </td>
                        <td align="center" width="120">{{ item.createdAt && item.createdAt.replace('T', ' ') }}</td>
                        <td align="center" width="100">{{ areaStatus[item.status] }}</td>
                        <td align="center" width="120">
                            <p class="remark">{{ item.audiRemark || '-' }}</p>
                        </td>
                        <td align="center" width="120">
                            {{ item.updatedAt && item.updatedAt.replace('T', ' ') || '-' }}</td>
                            <!--  v-if="spService" -->
                        <td align="center" width="100">
                            <Button v-if="item.status === 'REJECT'" @click="editRegionsList(item)" type="warning"
                                size="small" ghost style="margin: 3px">去修改</Button>
                            <Button v-if="item.status === 'REJECT'" type="error" size="small" ghost style="margin: 3px"
                                @click="delRegionsList(item)">删除</Button>
                            <Button v-else-if="item.status === 'WAIT_SIGN'" @click="ospContract" type="primary" ghost
                                size="small" style="margin: 3px">去签署</Button>
                            <Button v-else-if="item.status === 'WAIT_PAY'" @click="toOspMargin" type="success" ghost
                                size="small" style="margin: 3px">去支付</Button>
                            <span v-else>-</span>
                        </td>
                    </tr>
                    <tr v-if="!commList?.length">
                        <td colspan="5" align="center">暂无数据</td>
                    </tr>
                </table>
            </div>
            <Page style="margin:20px;" @on-change="changeCurrent" :page-size="searchObj.pageSize"
                :current="searchObj.pageNum" :total="totalElements" show-total show-elevator />
        </div>
        <Modal width="600" v-model="serviceArea" draggable sticky scrollable :title="editId ? '编辑服务区域' : '新增服务区域'"
            :footer-hide="true" :mask-closable="false">
            <Form :label-width="90">
                <p class="formTitle">丨基本信息</p>
                <FormItem label="服务区域" required v-if="!editId">
                    <div class="addressDiv">
                        <div v-for="(it, ind) in AreaChange.regionList" :key="ind" class="region-item">
                            <Select v-model="it.serviceProvinceId" placement="top"
                                @on-change="(item) => { AreaChange.typeOneChangeCommon(item, ind) }"
                                style="width:120px;margin-right:5px;">
                                <Option v-for="(item, index) in AreaChange.addressOneList" :value="item.id" :key="index">
                                    {{ item.name }}</Option>
                            </Select>
                            <Select v-model="it.serviceCityId" placement="top"
                                @on-change="(item) => AreaChange.typeTwoChangeCommon(item, ind)"
                                style="width:120px;margin-right:5px;">
                                <Option v-for="(item, index) in it.addressTwoList" :value="item.id" :key="index">{{
                                    item.name }}</Option>
                            </Select>
                            <Select v-model="it.serviceAreaId" placement="top" style="width:120px;">
                                <Option v-for="(item, index) in it.addressThreeList" :value="item.id" :key="index">{{
                                    item.name }}</Option>
                            </Select>
                            <Button class="btn" v-if="ind" :disabled="AreaChange.regionList.length <= 1"
                                @click="AreaChange.deleteRegion(ind)">-</Button>
                            <Button class="btn" v-if="ind === AreaChange.regionList.length - 1"
                                :disabled="!it.serviceAreaId || AreaChange.regionList.length >= 5"
                                @click="AreaChange.addRegion(ind)">+</Button>
                        </div>
                    </div>
                </FormItem>
                <FormItem label="服务区域" required v-else>
                    <div>
                        <Select v-model="regionObj.provinceId" @on-change="typeOneChange"
                            style="width:150px;margin-right:5px;">
                            <Option v-for="(item, index) in addressOneLists" :value="item.id" :key="index">{{ item.name }}
                            </Option>
                        </Select>
                        <Select v-model="regionObj.cityId" @on-change="typeTwoChange" style="width:150px;margin-right:5px;">
                            <Option v-for="(item, index) in addressTwoLists" :value="item.id" :key="index">{{ item.name }}
                            </Option>
                        </Select>
                        <Select v-model="regionObj.regionId" style="width:150px;">
                            <Option v-for="(item, index) in addressThreeLists" :value="item.id" :key="index">{{ item.name }}
                            </Option>
                        </Select>
                    </div>
                </FormItem>
            </Form>
            <div style="text-align: center; margin: 30px 0">
                <Button type="default" size="large" style="width: 120px; margin-right: 30px"
                    @click="serviceArea = false">取消</Button>
                <Button type="success" v-if="!regionObj.edit" size="large" style="width: 120px"
                    @click="debounce(saveUsers)">确认</Button>
                <Button type="success" v-else size="large" style="width: 120px" @click="debounce(saveEditUser)">修改</Button>
            </div>
        </Modal>
        <Modal width="350" v-model="delModal" draggable sticky scrollable title="删除服务区域" :footer-hide="true"
            :mask-closable="false">
            <p class="delTitle">是否删除该服务区域</p>
            <div style="text-align: center; margin: 30px 0 10px 0">
                <Button type="default" size="large" style="width: 100px; margin-right: 30px"
                    @click="delModal = false">取消</Button>
                <Button type="success" size="large" style="width: 100px" @click="debounce(delRegions)">确认</Button>
            </div>
        </Modal>
        <Modal width="350" v-model="arginModal" draggable sticky scrollable title="支付保证金" :footer-hide="true"
            :mask-closable="false">
            <p class="delTitle">是否跳转运维保证金支付页面</p>
            <div style="text-align: center; margin: 30px 0 10px 0">
                <Button type="default" size="large" style="width: 100px; margin-right: 30px"
                    @click="arginModal = false">取消</Button>
                <Button type="success" size="large" style="width: 100px" @click="debounce(marginRegions)">确认</Button>
            </div>
        </Modal>
        <Modal width="350" v-model="contractModal" draggable sticky scrollable title="签署合同" :footer-hide="true"
            :mask-closable="false">
            <p class="delTitle">是否跳转公司合同管理页面</p>
            <div style="text-align: center; margin: 30px 0 10px 0">
                <Button type="default" size="large" style="width: 100px; margin-right: 30px"
                    @click="contractModal = false">取消</Button>
                <Button type="success" size="large" style="width: 100px" @click="debounce(marginContract)">确认</Button>
            </div>
        </Modal>
    </div>
</template>

<script>
import API from '@/api/osp';
import _data from '@/views/apv/_edata'; // 数据字典
import { getRegionList } from '@/api/index';
import AreaChange from '@/utils/ospAreaChange.js';
export default {
    data() {
        return {
            AreaChange,
            contractModal: false,
            arginModal: false,
            delModal: false,
            delId: '',
            // stepKey: -1,
            serviceArea: false,
            id: '',
            editId: '',
            addressLists: [],
            addressOneLists: [],
            addressTwoLists: [],
            addressThreeLists: [],
            addressOneList: [],
            addressTwoList: [],
            addressThreeList: [],
            commList: [],
            searchObj: {
                pageNum: 1,
                pageSize: 10
            },
            totalElements: 0,
            regionList: [{
                serviceProvinceId: '',
                provinceName: '',
                serviceCityId: '',
                cityName: '',
                serviceAreaId: '',
                regionName: '',
                areaOneList: [],
                areaTwoList: [],
                areaThreeList: [],
            }],
            arrdata: [],
            regionObj: {
                provinceId: '',
                provinceName: '',
                cityId: '',
                cityName: '',
                regionId: '',
                regionName: '',
            },
            areaStatus: _data.areaStatus,
            spService: false,
        };
    },

    mounted() {
        // this.stepKey = 0
        this.getList()
        // this.jianzhan()
        AreaChange.getaddressList(this.id, this, false);
    },
    created() {
        this.getaddressList();
    },
    methods: {

        // 切换分页
        changeCurrent(curr) {
            this.searchObj.pageNum = curr;
            this.getList();
        },

        // 查询按钮
        queryList() {
            this.searchObj.pageNum = 1;
            this.getList();
        },
        statusRes(e) {
            switch (e) {
                case 'DISABLE':
                    return '已禁用'
                    break;
                case 'ENABLE':
                    return '已启用'
                    break;
                default:
                    return '未启用'
            }
        },

        getList() {
            let params = this.searchObj
            API.regionsList(params).then(res => {
                if (res.status === 200) {
                    this.totalElements = res.data.totalElements;
                    this.commList = res.data.content;
                } else {
                    this.totalElements = 0;
                    this.commList = [];
                    this.$Message.error(res.data.error);
                }
            });

        },
        // jianzhan() {
        //     API.getFindId().then(res => {
        //         if (res.data.result !== null) {
        //             this.spService = false
        //         } else {
        //             this.spService = true
        //         }
        //     })
        // },
        isNoEmpty(e) {
            return !['', undefined, 'null', 'undefined', 0, null].includes(e)
        },
        handleRegionList() {
            const regionIdList = []
            const list = []
            for (let i = 0; i < AreaChange.regionList.length; i++) {
                const item = AreaChange.regionList[i]
                const { serviceProvinceId, serviceCityId, serviceAreaId, addressTwoList, addressThreeList } = item
                const serviceProvince = this.isNoEmpty(serviceProvinceId) ? AreaChange.addressOneList.filter(item => item.id === serviceProvinceId)[0].name : ''
                const serviceCity = this.isNoEmpty(serviceCityId) ? addressTwoList.filter(item => item.id === serviceCityId)[0].name : ''
                const serviceArea = this.isNoEmpty(serviceAreaId) ? addressThreeList.filter(item => item.id === serviceAreaId)[0].name : ''
                if (!serviceProvince || !serviceCity || !serviceArea) {
                    this.$Message.warning('区域选择不可为空!');
                    return true
                }
                regionIdList.push(serviceAreaId)
                if (regionIdList.length !== [...new Set(regionIdList)].length) {
                    this.$Message.warning('区域项不可重复!');
                    return true
                }
                list.push({ serviceProvinceId, serviceCityId, serviceAreaId, serviceProvince, serviceCity, serviceArea })
            }
            this.arrdata = list
            return false
        },
        //添加
        addRegionsList() {
            this.editId = '';
            AreaChange.regionList = [
                {
                    serviceProvinceId: "", //服务省地址
                    serviceProvince: "", //服务省地址名称
                    serviceCityId: "", //服务市地址ID
                    serviceCity: "", //服务市地址名称
                    serviceAreaId: "", //服务区域ID
                    serviceArea: "", //服务区域
                    addressTwoList: [],
                    addressThreeList: [],
                },
            ];
            AreaChange.serviceProvinceId = "";
            AreaChange.serviceCityId = "";
            AreaChange.serviceAreaId = "";
            this.serviceArea = true
        },
        //确认
        saveUsers() {
            if (this.handleRegionList()) return
            let datas = {
                regionDtoList: this.arrdata
            }
            API.addNewArea(datas).then(res => {
                if (res.data.success) {
                    this.$Message.success('添加成功');
                    AreaChange.regionList = [{
                        serviceProvinceId: "", //服务省地址
                        serviceProvince: "", //服务省地址名称
                        serviceCityId: "", //服务市地址ID
                        serviceCity: "", //服务市地址名称
                        serviceAreaId: "", //服务区域ID
                        serviceArea: "", //服务区域
                        addressOneList: [],
                        addressTwoList: [],
                        addressThreeList: [],
                    }];
                    this.getList();
                    this.serviceArea = false
                } else {
                    this.$Message.error(res.data.error)
                }
            }).catch(res => {
                this.$Message.error(res.data.message);
            })
        },
        cityInit(e) {
            e.provinceId = String(e.provinceId);
            e.cityId = String(e.cityId);
            e.regionId = String(e.regionId);
            this.typeOneChange(e.provinceId);
            this.typeTwoChange(e.cityId);
            this.regionObj = e
        },
        //编辑
        editRegionsList(item) {
            this.cityInit(item)
            this.regionObj.edit = true
            this.editId = item.id
            this.serviceArea = true

        },
        saveEditUser() {
            let datas = this.regionObj
            let area = {
                id: this.editId,
                provinceName: this.addressLists.province_list[this.regionObj.provinceId],
                cityName: this.addressLists.city_list[this.regionObj.cityId],
                regionName: this.addressLists.county_list[this.regionObj.regionId],
            }
            Object.assign(datas, area)
            API.editUpdate(datas).then(res => {
                if (res.data.success) {
                    this.$Message.success('修改成功');
                    this.getList();
                    this.serviceArea = false
                } else {
                    this.$Message.error(res.data.error)
                }
            }).catch(res => {
                this.$Message.error(res.data.message);
            })
        },
        // 删除
        delRegionsList(item) {
            this.delModal = true
            this.delId = item.id
        },
        delRegions() {
            let params = {
                id: this.delId
            }
            API.regionDelete(params).then(res => {
                if (res.data.success) {
                    this.getList()
                    this.delModal = false
                } else {
                    this.$Message.error(res.data.error);
                }
            });
        },
        //跳转支付页面
        toOspMargin() {
            this.arginModal = true
        },
        marginRegions() {
            this.$router.push({
                name: "ospMargin",
            });
        },
        //跳转合同管理页面
        ospContract() {
            this.contractModal = true
        },
        marginContract() {
            this.$router.push({
                name: "ospContract",
            });
        },
        // 获取省市区
        getaddressList() {
            getRegionList().then(res => {
                for (let i in res.data.result.province_list) {
                    let obj = {
                        id: i,
                        name: res.data.result.province_list[i]
                    };
                    this.addressOneLists.push(obj);
                }
                this.addressLists = res.data.result;
            });
        },

        // 省改变
        typeOneChange(item) {
            this.addressTwoLists = [];
            // this.regionObj.cityId = '';
            // this.regionObj.regionId = '';
            if (!item) return;
            let str = item.substring(0, 2);
            for (let i in this.addressLists.city_list) {
                if (str === i.substring(0, 2)) {
                    let obj = {
                        id: i,
                        name: this.addressLists.city_list[i]
                    };
                    this.addressTwoLists.push(obj);
                }
            }
        },

        // 市变化
        typeTwoChange(item) {
            this.addressThreeLists = [];
            // this.regionObj.regionId = '';
            if (!item) return;
            let str = item.substring(0, 4);
            for (let i in this.addressLists.county_list) {
                if (str === i.substring(0, 4)) {
                    let obj = {
                        id: i,
                        name: this.addressLists.county_list[i]
                    };
                    this.addressThreeLists.push(obj);
                }
            }
        },
    }
};
</script>

<style scoped lang="scss">
@import '@/style/_cus_list.scss';

.formTitle {
    font-weight: bolder;
    margin-bottom: 20px;
}

.delTitle {
    font-size: 16px;
    font-weight: bolder;
    width: 100%;
    text-align: center;
}

.step {
    position: relative;
    margin: 0 20px;
    padding: 12px 6px 6px;
    border: 1px solid #e8eaec;

    .step_title {
        position: absolute;
        top: 6px;
        left: 6px;
        display: inline-block;
        padding: 10px 16px;
        font-size: 14px;
        background: #f5f7f9;
        color: #333;
    }

    .remark {
        width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
    }
}

.addressDiv {
    display: flex;
    flex-direction: column;
}

.btn {
    padding: 0;
    margin: 0;
    width: 30px;
    height: 30px;
    margin-left: 10px;
    // border-radius: 50%;
    line-height: 30px;
    text-align: center;
}

.region-item {
    display: flex;

    &:nth-child(n+2) {
        margin-top: 10px;
    }
}
</style>
