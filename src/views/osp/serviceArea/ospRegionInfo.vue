<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏运维管理 </Breadcrumb-item>
            <Breadcrumb-item to="/osp/">基础信息</Breadcrumb-item>
            <Breadcrumb-item>服务区域</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
        </div>
        <div class="cus_reg">
            <div class="contract" >
                <p class="cus_title">合同文本</p>
                <div class="flex-column-center">
                    <iframe id="iframe" class="pdf" :src="pdfUrl" width="100%" height="500" frameborder="0"></iframe>
                    <Checkbox v-model="contractConfirm" style="margin-top: 30px;">已查看并同意合作协议</Checkbox>
                </div>
                <div class="res_btn"><Button type="success" size="large" @click="confirmContract" style="width: 200px;">确认</Button></div>
            </div>
        </div>
    </div>
</template>

<script>
import API from '@/api/osp';

export default {
    data() {
        return {
            // stepKey: 1,
            id: '',
            pdfUrl: '',
            contractConfirm: false,
            areaObj: {
                address: '',
                consignee: '',
                mobile: '',
                provinceId: 0, // 用户地址
                cityId: 0,
                regionId: 0,
                spId: 0,
                zoneId: ''
            }
        };
    },

    mounted() {
        let sn = (this.stationId = this.$route.query?.sn)
		this.id = sn
        document.addEventListener('visibilitychange', this.handleVisiable)
    },

    destroyed() {
        document.removeEventListener('visibilitychange', this.handleVisiable)
    },

    methods: {
        handleVisiable(e) {
            let that = this
            if (e.target.visibilityState === 'visible') {
                that.getaddressList();
                // 2s 后再查一遍
                setTimeout(() => { that.getaddressList() }, 2000)
            }
        },

        stopFun() {
            return false
        },

        // 合同预览
        getRegionContract() {
            let datas = {
                zoneId: this.id
            };
            API.previewRegionContract(datas).then(res => {
                if (res.data.success) {
                    this.pdfUrl = res.data.result
                } else {
                    this.$Message.error(res.data.error);
                }
            });
        },

        // 签署合同
        confirmContract() {
            let that = this
            if (!this.contractConfirm) {
                this.$Message.warning('请先确认补充协议');
            } else {
                this.$Message.loading({
                    content: '获取服务中，请稍后...',
                    duration: 0
                });
                let datas = {
                    id: this.id
                }
                API.opAuthority(datas).then(res => {
                    this.modalView = false
                    this.$Message.destroy()
                    if (res.data.success) {
                        this.$Modal.confirm({
                            title: '提示',
                            content: '即将跳转法大大签署',
                            onOk: () => {
                                window.open(res.data.result)
                            },
                            onCancel: () => { }
                        })
                    } else {
                        this.$Message.error(res.data.error);
                    }
                }).catch(() => {
                    this.$Message.destroy()
                    this.$Message.error("获取服务失败，请重试");
                })
            }
        },

        // 支付步骤
        toshowPay() {
            return false
        },
    }
};
</script>

<style scoped lang="scss">
@import '@/style/_cus_list.scss';
@import '@/style/_cus_reg.scss';

.step {
    position: relative;
    margin: 0 20px;
    padding: 12px 6px 6px;
    border: 1px solid #e8eaec;

    .step_title {
        position: absolute;
        top: 6px;
        left: 6px;
        display: inline-block;
        padding: 10px 16px;
        font-size: 14px;
        background: #f5f7f9;
        color: #333;
    }
}

.cus_form {
    .form {
        margin: 0 auto;
        padding: 20px 0;
        width: 800px;
        box-sizing: border-box;

        .item_large {
            width: 300px;
        }

        .item_small {
            width: 200px;
        }

        .tips {
            display: inline-block;
            width: 100%;
            text-align: center;
            font-size: 12px;
            color: #999;
        }

        .file_pics {
            @extend .flex-row-center-center;
            width: 80px;
            height: 80px;
            border-radius: 8px;
            background: #f8f8f8;
            cursor: pointer;
            overflow: hidden;
        }

        .inlinebox {
            @extend .flex-column-center-center;
            margin-right: 15px;
            display: inline-flex;
        }
    }
}

.contract {
    padding: 20px 0;

    .pdf {
        max-width: 1000px;
        min-width: 800px;
        border: 1px solid #ddd;
    }
}
</style>
