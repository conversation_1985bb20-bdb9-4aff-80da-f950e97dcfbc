<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏运维管理 </Breadcrumb-item>
            <Breadcrumb-item to="/ospMargin">账户管理</Breadcrumb-item>
            <Breadcrumb-item>押金管理</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="deposit">
                    <div class="depositBalance">
                        <p>
                            <Icon type="ios-information-circle-outline" size="20" color="#c7003a" /> 运维保证金总金额
                            <em class="bail">{{ devOpsMargin }}</em>元
                        </p>
                        <p v-if="magento">
                            <Icon type="ios-information-circle-outline" size="20" color="#c7003a" /> 建站保证金总金额
                            <em class="bail">{{ constructionDeposit }}</em>元
                        </p>
                    </div>
                    <Button type="success" @click="jumpto()" style="margin-top: 10px;">支付保证金</Button>
                    <Button type="default" icon="ios-refresh" @click="debounce(queryList)"
                        style="float: right;margin-top: 10px"></Button>
                </div>
            </div>
            <div class="commListDiv">
                <table class="cus_table" cellspacing="0" cellpadding="0" width="100%">
                    <tr>
                        <th align="center">编号</th>
                        <th align="center">交易概述</th>
                        <th align="center">类型</th>
                        <th align="center">金额</th>
                        <th align="center">支付方式</th>
                        <th align="center">交易时间</th>
                        <th align="center">入账时间</th>
                        <th align="center">状态</th>
                        <th align="center">操作</th>
                    </tr>
                    <tbody v-for="(item, index) in commList" :key="index">
                        <tr>
                            <td align="left" width="120">{{ item.depositNo }}</td>
                            <td align="center" width="120">{{ item.tradeDesc }}</td>
                            <td align="center" width="120">{{ typeVal("ospMarginType", item.type) || '-' }}</td>
                            <td align="center" width="120">{{ item.amount }}</td>
                            <td align="center" width="120">{{ typeVal("payChannel", item.payChannel) || '-' }}</td>
                            <td align="center" width="200" style="min-width: 120px;">{{ item.createdAt.replace('T', ' ') }}
                            </td>
                            <td align="center" width="200" style="min-width: 120px;">
                                {{ item.confirmAt ? item.confirmAt.replace('T', ' ') : '--' }}
                            </td>
                            <td align="center" width="120" style="min-width: 120px;">
                                <Tooltip v-if="item.status === 'REJECT'" placement="left"
                                    :content="'原因：' + item.rejectReason" max-width="200">
                                    <a>{{ typeVal("payStatus", item.status) || '-' }}</a>
                                </Tooltip>
                                <span v-else>{{ typeVal("payStatus", item.status) || '-' }}</span>
                            </td>
                            <td align="center" width="120">
                                <Button v-if="item.status === 'REJECT' && item.payChannel === 'TRANSFER'" type="warning"
                                    size="small" ghost @click="editTo(item)">修改</Button>
                                <Button v-if="item.status === 'TO_PAY'" type="info" size="small" ghost
                                    @click="payTo(item)">去支付</Button>
                                <span v-if="item.status === 'CONFIRMED'">-</span>
                            </td>
                        </tr>
                    </tbody>
                    <tbody v-if="!commList.length">
                        <tr>
                            <td colspan="99" align="center">暂无数据</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <Page style="margin:20px;" @on-change="changeCurrent" :current="pageNum" :total="totalElements" show-total
                show-elevator />
        </div>
    </div>
</template>
<script>
import _data from '@/views/osp/_edata';   // 数据字典
import API from "@/api/osp";
import _API from "@/api/apv";
import store from "@/store/modules/index";

export default {
    name: 'ospMargin',
    data() {
        return {
            magento: false,
            constructionDeposit: 0,
            devOpsMargin: 0,
            pageNum: 1,
            totalElements: 0,
            commList: [],
            ospMarginType: _data.ospMarginType,
            payChannel: _data.payChannel,
            payStatus: _data.payStatus,
            limitData: {},
            spService: false,
        };
    },
    computed: {
        spId() {
            return store.state?.spInfo?.id
        }
    },
    mounted() {
        this.queryList();
    },
    methods: {
        // jianzhan() {
        //     API.getFindId().then(res => {
        //         if (res.data.result !== null) {
        //             this.spService = false
        //         } else {
        //             this.spService = true
        //         }
        //     })
        // },
        editTo(item) {
            this.$router.push({
                name: "ospPayment",
                params: item
            });
        },
        payTo(item) {
            let params = {
                id: item.id
            }
            API.marginCheckToPay(params).then(res => {
                if (res.data.success) {
                    this.$router.push({
                        name: "ospPayment",
                        params: { item: item }
                    });
                } else {
                    this.$Message.error(res.data.error + '请先签署合同');
                }
            })
        },

        // 切换分页
        changeCurrent(curr) {
            this.pageNum = curr;
            this.getCommList();
        },

        // 查询按钮
        queryList() {
            this.commList = [];
            this.pageNum = 1;
            this.getCommList();
            this.depositData()
        },

        // 获取保证金
        depositData() {
            //运维保证金
            API.marginSumPay().then(res => {
                if (res.data.success) {
                    if (res.data.result) {
                        this.devOpsMargin = res.data.result
                    } else {
                        this.devOpsMargin = 0
                    }
                }
            })
            //建站保证金
            API.getFindId().then(res => {
                if (res.data.result !== null) {
                    _API.getDeposit().then(res => {
                        if (res.data.depositBalance) {
                            this.constructionDeposit = res.data.depositBalance
                            this.magento = true
                        } else {
                            this.constructionDeposit = 0
                            this.magento = true
                        }
                    })
                }else{
                    this.magento = false
                }
            })
        },
        // 设备列表
        getCommList() {
            let data = {
                pageNum: this.pageNum,
                pageSize: 10
            };
            API.marginList(data).then(res => {
                if (res.data.totalElements) {
                    this.totalElements = res.data.totalElements;
                    this.commList = res.data.content;
                } else {
                    this.commList = [];
                }
            });
        },
        jumpto() {
            this.$router.push('ospMargin/ospPayment');
        },
        typeVal(list, value) {
            const obj = {};
            this[list].map((e) => {
                obj[e.value] = e.label || e.value;
            });
            return obj[value];
        },
    }
};
</script>
<style lang="scss" scoped>
@import 'style/_cus_list.scss';
$_boder: 1px solid $border-color-1;

.list_but {
    padding: 0 20px !important;

    .deposit {
        padding: 20px;
        background-color: $background-main;
        border-radius: 3px;
        border: $_boder;

        .depositBalance {
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: $_boder;

            p {
                line-height: 2;
                font-size: 16px;
                padding-bottom: 10px;

            }
        }

        .bail {
            border-left: $_boder;
            margin: 0 10px;
            padding-left: 10px;
            color: $color-main;
            font-size: 18px;
        }
    }
}

.model_view {
    li {
        padding: 10px
    }
}
</style>
