<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏运维管理</Breadcrumb-item>
            <Breadcrumb-item to="/apv/">保险管理</Breadcrumb-item>
            <Breadcrumb-item>保单号查询</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">电站编码：</p>
                    <Input v-model="searchObj.stationCode" placeholder="请输入电站编码" clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">电站名称：</p>
                    <Input v-model="searchObj.stationName" placeholder="请输入电站名称" clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                </div>
            </div>
            <div class="commListDiv">
                <table class="cus_table" border="0" cellspacing="0" cellpadding="0" width="100%">
                    <tr>
                        <th align="center" class="fixedLeft1">序号</th>
                        <th align="center">电站编码</th>
                        <th align="center">电站名称</th>
                        <th align="center">项目公司(投保人)</th>
                        <th align="center">资产所属</th>
                        <th align="center">财产一切险</th>
                        <th align="center">公众责任险</th>
                        <th align="center">起止时间</th>
                    </tr>
                    <tbody class="tag_line" v-for="(item, index) in commList" :key="index">
                        <tr>
                            <td align="center" class="fixedLeft1">{{ index + 1 }}</td>
                            <td align="center">{{ item.stationCode || '-' }}</td>
                            <td align="center">{{ item.stationName || '-' }}</td>
                            <td align="center">{{ item.projectCompanyName || '-' }}</td>
                            <td align="center">{{ typeVal('specialFlag', item.specialFlag) || '-' }}</td>
                            <td align="center">{{ item.propertyAllRisk || '-' }}</td>
                            <td align="center">{{ item.publicLiabilityInsurance || '-' }}</td>
                            <td align="center" style="min-width: 200px">
                                {{ item.timePeriod|| '-' }}
                            </td>
                        </tr>
                    </tbody>
                    <tr v-if="!commList.length">
                        <td colspan="25" align="center">暂无数据</td>
                    </tr>
                </table>
            </div>
            <Page style="margin:20px;" @on-change="changeCurrent" :current="searchObj.page" :total="totalElements"
                show-total show-elevator />
        </div>
    </div>
</template>
<script>
import _data from '@/views/osp/_edata'; // 数据字典
import { policyNumberList } from '@/api/osp';
import { getDicts } from "@/api/public/dict";
export default {
    name: "policyNumber",
    data() {
        return {
            searchObj: {
                stationCode: '',
                stationName: '',
                page: 1,
                size: 10,
            },
            commList: [],
            numPages: null,
            totalElements: 0,
            specialFlag: [],
        };
    },
    async created() {
        this.specialFlag = await getDicts("osp/merchant", "specialFlag").then(e => e?.list)
    },
    mounted() {
        this.getCommList();
    },

    methods: {
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.page = curr;
            this.getCommList();
        },

        // 查询按钮
        queryList() {
            this.commList = [];
            this.searchObj.page = 1;
            this.getCommList();
        },

        // 电站列表
        getCommList() {
            let datas = this.searchObj;
            policyNumberList(datas).then(res => {
                let request = res.data.result
                if (request.content.length > 0) {
                    this.commList = request.content;
                    this.totalElements = request.totalElements;
                } else {
                    this.totalElements = 0;
                    this.commList = []
                }
            });
        },
        // 重置按钮
        emptyList() {
            this.searchObj = {
                stationCode: '',
                stationName: '',
                page: 1,
                size: 10,
            }
        },
        typeVal(list, value) {
            const obj = {};
            this[list].map((e) => {
                obj[e.value] = e.label || e.value;
            });
            return obj[value];
        },
    }
};
</script>
<style lang="scss" scoped>
@import 'style/_cus_list.scss';

.fixedLeft1 {
    position: sticky;
    left: 0;
    @extend .back-filter;
}

.tag_line {
    position: relative;
}

:deep(.ivu-modal-fullscreen) {
    width: 100vw !important;
}

@media print {

    .cus_list {
        display: none
    }

    .noprint {
        display: none
    }
}
</style>
