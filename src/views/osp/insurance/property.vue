<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏运维管理</Breadcrumb-item>
            <Breadcrumb-item to="/apv/">保险管理</Breadcrumb-item>
            <Breadcrumb-item>保险报案(财产一切险)</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">电站编码：</p>
                    <Input v-model="searchObj.stationCode" placeholder="请输入电站编码" clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">电站名称：</p>
                    <Input v-model="searchObj.stationName" placeholder="请输入电站名称" clearable style="width: 200px" />
                </div>
                <div class="condition">
                    <p class="condition_p">审核状态：</p>
                    <Select v-model="searchObj.auditStatus" clearable style="width: 200px">
                        <Option v-for="item in insuranceStatus" :value="item.value" :key="item.value">{{ item.label }}
                        </Option>
                    </Select>
                </div>
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                </div>
            </div>
            <div class="list_but">
                <div class="condition">
                    <Button @click="debounce(addInfo)" type="primary">新增</Button>
                    <Button @click="debounce(exportList)" type="success">导出</Button>
                </div>
            </div>
            <div class="commListDiv">
                <table class="cus_table" border="0" cellspacing="0" cellpadding="0" width="100%">
                    <tr>
                        <th align="center" class="fixedLeft1">序号</th>
                        <th align="center">事故发生日期</th>
                        <th align="center">报案日期</th>
                        <th align="center">保单号</th>
                        <th align="center">险种</th>
                        <th align="center">所属保险公司</th>
                        <th align="center">资产所属</th>
                        <th align="center">所属项目公司</th>
                        <th align="center">电站所属中心</th>
                        <th align="center">电站编码</th>
                        <th align="center">电站名称</th>
                        <th align="center">安装地址</th>
                        <th align="center">运维商名称</th>
                        <th align="center">出险情况</th>
                        <th align="center">组件受损数量</th>
                        <th align="center">组件品牌规格</th>
                        <th align="center">组件规格</th>
                        <th align="center">逆变器受损数量</th>
                        <th align="center">逆变器品牌</th>
                        <th align="center">逆变器规格</th>
                        <th align="center">其他受损情况</th>
                        <th align="center">天气证明</th>
                        <th align="center">盗窃公安机关证明</th>
                        <th align="center">火灾事故认证书</th>
                        <th align="center">其他</th>
                        <th align="center">组件受损现场照片</th>
                        <th align="center">组件修复后照片</th>
                        <th align="center">其他</th>
                        <th align="center">创建人</th>
                        <th align="center">创建时间</th>
                        <th align="center">修改时间</th>
                        <th align="center">审核状态</th>
                        <th align="center">驳回原因</th>
                        <th align="center">审核时间</th>
                        <th class="fixedRight" align="center">操作</th>
                    </tr>
                    <tbody class="tag_line" v-for="(item, index) in commList" :key="index">
                        <tr>
                            <!-- {{ item.createdAt?.replace("T", " ") || "-" }} -->
                            <td align="center" class="fixedLeft1">{{ index + 1 }}</td>
                            <td align="center" style="min-width: 110px">{{ item.incidentDate || "-" }}</td>
                            <td align="center" style="min-width: 110px">{{ item.reportDate || "-" }}</td>
                            <td align="center" style="min-width: 120px">{{ item.policyNo || "-" }}</td>
                            <td align="center">{{ item.riskType || "-" }}</td>
                            <td align="center">{{ item.insurerCompany || "-" }}</td>
                            <td align="center">{{ typeVal('specialFlag', item.specialFlag) || "-" }}</td>
                            <td align="center" style="min-width: 110px">{{ item.projectCompanyName || "-" }}</td>
                            <td align="center">{{ item.subCenterName || "-" }}</td>
                            <td align="center">{{ item.stationCode || "-" }}</td>
                            <td align="center">{{ item.stationName || "-" }}</td>
                            <td align="center" style="min-width: 120px">{{ item.address || "-" }}</td>
                            <td align="center" style="min-width: 110px">{{ item.opName || "-" }}</td>
                            <td align="center">{{ item.incidentSituation || "-" }}</td>
                            <td align="center">{{ item.totalLossAmount || "-" }}</td>
                            <td align="center">{{ item.componentQuality || "-" }}</td>
                            <td align="center">{{ item.componentSpecification || "-" }}</td>
                            <td align="center">{{ item.rejectionLossAmount || "-" }}</td>
                            <td align="center">{{ item.inverterBrand || "-" }}</td>
                            <td align="center">{{ item.inverterModel || "-" }}</td>
                            <td align="center">{{ item.otherIncidentSituation || "-" }}</td>
                            <td align="center">
                                <a v-if="item.weatherCertificate" :href="item.weatherCertificate" target="_blank">查看</a>
                            </td>
                            <td align="center">
                                <a v-if="item.policeCertificate" :href="item.policeCertificate" target="_blank">查看</a>
                            </td>
                            <td align="center">
                                <a v-if="item.fireCertificate" :href="item.fireCertificate" target="_blank">查看</a>
                            </td>
                            <td align="center">
                                <a v-if="item.otherCertificate" :href="item.otherCertificate" target="_blank">查看</a>
                            </td>
                            <td align="center">
                                <a v-if="item.componentDamageScenePhotos" :href="item.componentDamageScenePhotos"
                                    target="_blank">查看</a>
                            </td>
                            <td align="center">
                                <a v-if="item.componentRepairPhotos" :href="item.componentRepairPhotos"
                                    target="_blank">查看</a>
                            </td>
                            <td align="center">
                                <a v-if="item.otherPhotos" :href="item.otherPhotos" target="_blank">查看</a>
                            </td>
                            <td align="center">{{ item.createdBy || "-" }}</td>
                            <td align="center" style="min-width: 200px">{{ item.createdAt || "-" }}</td>
                            <td align="center" style="min-width: 200px">{{ item.updateAt || "-" }}</td>
                            <td align="center">{{ typeVal('insuranceStatus', item.auditStatus) || "-" }}</td>
                            <td align="center">{{ item.rejectRemark || "-" }}</td>
                            <td align="center" style="min-width: 200px">{{ item.auditTime || "-" }}</td>
                            <td class="fixedRight" style="min-width: 150px" align="center">
                                <Button v-if="item.auditStatus !== 'AUDIT_PASS'" type="info" ghost size="small"
                                    style="margin: 3px" @click="toEdit(item)">修改</Button>
                                <Button v-if="item.auditStatus !== 'AUDIT_PASS'" type="error" ghost size="small"
                                    style="margin: 3px" @click="delInfo(item.id)">删除</Button>
                            </td>
                        </tr>
                    </tbody>
                    <tr v-if="!commList.length">
                        <td colspan="25" align="center">暂无数据</td>
                    </tr>
                </table>
            </div>
            <Page style="margin: 20px" @on-change="changeCurrent" :current="searchObj.page" :total="totalElements"
                show-total show-elevator />
        </div>
        <Drawer width="40%" v-model="modelAdd" draggable reset-drag-position :title="formItem.id ? '修改' : '新增'">
            <div class="cus_form">
                <p class="cus_title">电站信息</p>
                <Form v-if="modelAdd" :model="formItem" :label-width="180">
                    <FormItem label="事故发生日期" prop="incidentDate" :rules="{ required: true, message: '请选择事故发生日期' }">
                        <DatePicker type="date" :value="formItem.incidentDate" placeholder="请选择" @on-change="(data) => {
                            return selectDate(data, 'incidentDate');
                        }" format="yyyy-MM-dd" style=" width: 350px" />
                    </FormItem>
                    <FormItem label="报案日期" prop="reportDate" :rules="{ required: true, message: '请选择报案日期' }">
                        <DatePicker type="date" :value="formItem.reportDate" placeholder="请选择" @on-change="(data) => {
                            return selectDate(data, 'reportDate');
                        }" format="yyyy-MM-dd" style=" width: 350px" />
                    </FormItem>
                    <FormItem label="保单号" prop="policyNo">
                        <Input type="text" maxlength="15" v-model="formItem.policyNo" clearable placeholder="请输入保单号"
                            style="width: 350px" />
                    </FormItem>
                    <FormItem label="险种" prop="riskType">
                        <Input type="text" maxlength="15" v-model="formItem.riskType" clearable placeholder="请输入险种"
                            style="width: 350px" />
                    </FormItem>
                    <FormItem label="所属保险公司" prop="insurerCompany">
                        <Input type="text" maxlength="15" v-model="formItem.insurerCompany" clearable
                            placeholder="请输入所属保险公司" style="width: 350px" />
                    </FormItem>
                    <FormItem label="电站编码" prop="stationCode" :rules="{ required: true, message: '请输入电站编码' }">
                        <Input type="text" maxlength="15" v-model="formItem.stationCode" @on-blur="stationCodeInput"
                            clearable placeholder="请输入电站编码" style="width: 350px" />
                    </FormItem>
                    <FormItem label="电站业主名称" prop="stationName">
                        <Input type="text" maxlength="15" v-model="formItem.stationName" clearable
                            placeholder="请输入电站业主名称" style="width: 350px" disabled />
                    </FormItem>
                </Form>
                <p class="cus_title">受损情况</p>
                <Form v-if="modelAdd" :model="formItem" :label-width="180">
                    <FormItem label="出险情况" prop="incidentSituation">
                        <Input type="text" maxlength="15" v-model="formItem.incidentSituation" clearable
                            placeholder="请输入出险情况" style="width: 350px" />
                        <Alert show-icon type="warning" style="font-size: 12px; width: 350px; margin-top: 5px;">
                            <Icon type="ios-alert" slot="icon"></Icon>
                            出险情况：盗窃、抢劫、雷电、暴雨、洪水、暴风、龙卷风、冰雹、台风、飓风、暴雪、冰凌、沙尘暴等
                        </Alert>
                    </FormItem>
                    <FormItem label="电站所属组件品牌规格" prop="componentQuality">
                        <Select v-model="formItem.componentQuality" style="width: 350px" placeholder="请选择">
                            <Option v-for="item in componentBrandList" :value="item.label" :key="item.value">
                                {{ item.label }}
                            </Option>
                        </Select>
                    </FormItem>
                    <FormItem label="电站所属组件规格" prop="componentSpecification">
                        <Select v-model="formItem.componentSpecification" style="width: 350px" placeholder="请选择">
                            <Option v-for="item in componentSkudList" :value="item.label" :key="item.value">
                                {{ item.label }}
                            </Option>
                        </Select>
                    </FormItem>
                    <FormItem label="电站所属组件受损数量" prop="totalLossAmount">
                        <InputNumber class="item_small" :precision="0" :max="99" :min="1"
                            v-model="formItem.totalLossAmount" placeholder="请填写整数" style="width: 350px" />
                    </FormItem>
                    <FormItem label="电站所属逆变器品牌" prop="inverterBrand">
                        <Select v-model="formItem.inverterBrand" style="width: 350px" placeholder="请选择">
                            <Option v-for="item in inverterBrandList" :value="item.label" :key="item.value">
                                {{ item.label }}
                            </Option>
                        </Select>
                    </FormItem>
                    <FormItem label="电站所属逆变器规格" prop="inverterModel">
                        <Select v-model="formItem.inverterModel" style="width: 350px" placeholder="请选择">
                            <Option v-for="item in inverterSkudList" :value="item.label" :key="item.value">
                                {{ item.label }}
                            </Option>
                        </Select>
                    </FormItem>
                    <FormItem label="逆变器受损数量" prop="rejectionLossAmount">
                        <InputNumber class="item_small" :precision="0" :max="99" :min="1"
                            v-model="formItem.rejectionLossAmount" placeholder="请填写整数" style="width: 350px" />
                    </FormItem>
                    <FormItem label="其他受损情况（辅材等）" prop="otherIncidentSituation">
                        <Input type="text" maxlength="15" v-model="formItem.otherIncidentSituation" clearable
                            placeholder="请输入其他受损情况" style="width: 350px" />
                    </FormItem>
                </Form>
                <p class="cus_title">出险资料</p>
                <Form v-if="modelAdd" :model="formItem" :label-width="180">
                    <FormItem label="出险情况" prop="weatherCertificate">
                        <div style="display: flex; align-items: center;">
                            <Upload class="inlinebox" accept=".pdf" :show-upload-list="false"
                                :action="UploadClass.actionUrl('file')" :headers="UploadClass.headers"
                                :before-upload="UploadClass.progress"
                                :on-success="(response) => { formItem.weatherCertificate = response.result[0].fileUrl; return UploadClass.upSuccess() }"
                                :format="['pdf']" :max-size="102400" :on-format-error="UploadClass.formatError"
                                :on-exceeded-size="UploadClass.maxSizeError">
                                <div style="display: flex;">
                                    <Button icon="md-cloud-upload">
                                        {{ formItem.weatherCertificate ? '重新' : '' }}上传文件
                                    </Button>
                                </div>
                            </Upload>
                            <p class="tips" style="margin:0 0 0 20px;"> 支持扩展名：.pdf</p>
                        </div>
                        <a v-if="formItem.weatherCertificate" :href='formItem.weatherCertificate' target="_blank"
                            style="white-space: nowrap;font-size: 15px;">
                            <Icon type="md-document" /> 查看文件
                        </a>
                        <Alert show-icon type="warning" style="font-size: 12px; width: 350px; margin-top: 5px;">
                            <Icon type="ios-alert" slot="icon"></Icon>
                            天气证明：大风八级以上17.2米/秒以上
                        </Alert>
                    </FormItem>
                    <FormItem label="盗窃公安机关证明" prop="policeCertificate">
                        <div style="display: flex; align-items: center;">
                            <Upload class="inlinebox" accept=".pdf" :show-upload-list="false"
                                :action="UploadClass.actionUrl('file')" :headers="UploadClass.headers"
                                :before-upload="UploadClass.progress"
                                :on-success="(response) => { formItem.policeCertificate = response.result[0].fileUrl; return UploadClass.upSuccess() }"
                                :format="['pdf']" :max-size="102400" :on-format-error="UploadClass.formatError"
                                :on-exceeded-size="UploadClass.maxSizeError">
                                <div style="display: flex;">
                                    <Button icon="md-cloud-upload">
                                        {{ formItem.policeCertificate ? '重新' : '' }}上传文件
                                    </Button>
                                </div>
                            </Upload>
                            <p class="tips" style="margin:0 0 0 20px;"> 支持扩展名：.pdf</p>
                        </div>
                        <a v-if="formItem.policeCertificate" :href='formItem.policeCertificate' target="_blank"
                            style="white-space: nowrap;font-size: 15px;">
                            <Icon type="md-document" /> 查看文件
                        </a>
                        <Alert show-icon type="warning" style="font-size: 12px; width: 350px; margin-top: 5px;">
                            <Icon type="ios-alert" slot="icon"></Icon>
                            盗窃公安机关证明：报案回执、未破案证明
                        </Alert>
                    </FormItem>
                    <FormItem label="火灾事故认证书" prop="fireCertificate">
                        <div style="display: flex; align-items: center;">
                            <Upload class="inlinebox" accept=".pdf" :show-upload-list="false"
                                :action="UploadClass.actionUrl('file')" :headers="UploadClass.headers"
                                :before-upload="UploadClass.progress"
                                :on-success="(response) => { formItem.fireCertificate = response.result[0].fileUrl; return UploadClass.upSuccess() }"
                                :format="['pdf']" :max-size="102400" :on-format-error="UploadClass.formatError"
                                :on-exceeded-size="UploadClass.maxSizeError">
                                <div style="display: flex;">
                                    <Button icon="md-cloud-upload">
                                        {{ formItem.fireCertificate ? '重新' : '' }}上传文件
                                    </Button>
                                </div>
                            </Upload>
                            <p class="tips" style="margin:0 0 0 20px;"> 支持扩展名：.pdf</p>
                        </div>
                        <a v-if="formItem.fireCertificate" :href='formItem.fireCertificate' target="_blank"
                            style="white-space: nowrap;font-size: 15px;">
                            <Icon type="md-document" /> 查看文件
                        </a>
                        <Alert show-icon type="warning" style="font-size: 12px; width: 350px; margin-top: 5px;">
                            <Icon type="ios-alert" slot="icon"></Icon>
                            火灾事故认证书：消防大队出具，明确起火原因是由哪个设备造成的
                        </Alert>
                    </FormItem>
                    <FormItem label="其他" prop="otherCertificate">
                        <div style="display: flex; align-items: center;">
                            <Upload class="inlinebox" accept=".pdf" :show-upload-list="false"
                                :action="UploadClass.actionUrl('file')" :headers="UploadClass.headers"
                                :before-upload="UploadClass.progress"
                                :on-success="(response) => { formItem.otherCertificate = response.result[0].fileUrl; return UploadClass.upSuccess() }"
                                :format="['pdf']" :max-size="102400" :on-format-error="UploadClass.formatError"
                                :on-exceeded-size="UploadClass.maxSizeError">
                                <div style="display: flex;">
                                    <Button icon="md-cloud-upload">
                                        {{ formItem.otherCertificate ? '重新' : '' }}上传文件
                                    </Button>
                                </div>
                            </Upload>
                            <p class="tips" style="margin:0 0 0 20px;"> 支持扩展名：.pdf</p>
                        </div>
                        <a v-if="formItem.otherCertificate" :href='formItem.otherCertificate' target="_blank"
                            style="white-space: nowrap;font-size: 15px;">
                            <Icon type="md-document" /> 查看文件
                        </a>
                    </FormItem>
                </Form>
                <p class="cus_title">见证性资料</p>
                <Form v-if="modelAdd" :model="formItem" :label-width="180">
                    <FormItem label="组件受损现场照片" prop="componentDamageScenePhotos">
                        <div style="display: flex; align-items: center;">
                            <Upload class="inlinebox" accept=".pdf" :show-upload-list="false"
                                :action="UploadClass.actionUrl('file')" :headers="UploadClass.headers"
                                :before-upload="UploadClass.progress"
                                :on-success="(response) => { formItem.componentDamageScenePhotos = response.result[0].fileUrl; return UploadClass.upSuccess() }"
                                :format="['pdf']" :max-size="102400" :on-format-error="UploadClass.formatError"
                                :on-exceeded-size="UploadClass.maxSizeError">
                                <div style="display: flex;">
                                    <Button icon="md-cloud-upload">
                                        {{ formItem.componentDamageScenePhotos ? '重新' : '' }}上传文件
                                    </Button>
                                </div>
                            </Upload>
                            <p class="tips" style="margin:0 0 0 20px;"> 支持扩展名：.pdf</p>
                        </div>
                        <a v-if="formItem.componentDamageScenePhotos" :href='formItem.componentDamageScenePhotos'
                            target="_blank" style="white-space: nowrap;font-size: 15px;">
                            <Icon type="md-document" /> 查看文件
                        </a>
                        <Alert show-icon type="warning" style="font-size: 12px; width: 350px; margin-top: 5px;">
                            <Icon type="ios-alert" slot="icon"></Icon>
                            组件受损现场照片：①电站全景照片（能看出损失数量）、②所有受损组件现场情况照片、③每个损坏组件SN码+铭牌拍照
                        </Alert>
                    </FormItem>
                    <FormItem label="组件修复后照片" prop="componentRepairPhotos">
                        <div style="display: flex; align-items: center;">
                            <Upload class="inlinebox" accept=".pdf" :show-upload-list="false"
                                :action="UploadClass.actionUrl('file')" :headers="UploadClass.headers"
                                :before-upload="UploadClass.progress"
                                :on-success="(response) => { formItem.componentRepairPhotos = response.result[0].fileUrl; return UploadClass.upSuccess() }"
                                :format="['pdf']" :max-size="102400" :on-format-error="UploadClass.formatError"
                                :on-exceeded-size="UploadClass.maxSizeError">
                                <div style="display: flex;">
                                    <Button icon="md-cloud-upload">
                                        {{ formItem.componentRepairPhotos ? '重新' : '' }}上传文件
                                    </Button>
                                </div>
                            </Upload>
                            <p class="tips" style="margin:0 0 0 20px;"> 支持扩展名：.pdf</p>
                        </div>
                        <a v-if="formItem.componentRepairPhotos" :href='formItem.componentRepairPhotos' target="_blank"
                            style="white-space: nowrap;font-size: 15px;">
                            <Icon type="md-document" /> 查看文件
                        </a>
                        <Alert show-icon type="warning" style="font-size: 12px; width: 350px; margin-top: 5px;">
                            <Icon type="ios-alert" slot="icon"></Icon>
                            组件修复后照片：①电站全景照片（能看出修复数量）、②各个新组件分开拍照（拍SN码+组件铭牌）
                        </Alert>
                    </FormItem>
                    <FormItem label="其他" prop="otherPhotos">
                        <div style="display: flex; align-items: center;">
                            <Upload class="inlinebox" accept=".pdf" :show-upload-list="false"
                                :action="UploadClass.actionUrl('file')" :headers="UploadClass.headers"
                                :before-upload="UploadClass.progress"
                                :on-success="(response) => { formItem.otherPhotos = response.result[0].fileUrl; return UploadClass.upSuccess() }"
                                :format="['pdf']" :max-size="102400" :on-format-error="UploadClass.formatError"
                                :on-exceeded-size="UploadClass.maxSizeError">
                                <div style="display: flex;">
                                    <Button icon="md-cloud-upload">
                                        {{ formItem.otherPhotos ? '重新' : '' }}上传文件
                                    </Button>
                                </div>
                            </Upload>
                            <p class="tips" style="margin:0 0 0 20px;"> 支持扩展名：.pdf</p>
                        </div>
                        <a v-if="formItem.otherPhotos" :href='formItem.otherPhotos' target="_blank"
                            style="white-space: nowrap;font-size: 15px;">
                            <Icon type="md-document" /> 查看文件
                        </a>
                    </FormItem>
                </Form>
            </div>
            <div style="text-align: center; margin: 30px 0">
                <Button type="default" size="large" style="width: 120px; margin-right: 30px"
                    @click="modelAdd = false">取消 </Button>
                <Button type="success" :disabled="formItem.age >= 70" size="large" style="width: 120px"
                    @click="debounce(submit)">
                    {{ formItem.id ? "修改" : "提交" }}
                </Button>
            </div>
        </Drawer>
    </div>
</template>
<script>
import _data from "@/views/osp/_edata"; // 数据字典
import { propertyList, propertyListExport, propertyFindByStationCode, propertyFindFindSkuOrSn, propertyListUpdate, propertyListAdd, propertyListDelete } from "@/api/osp";
import { UploadClass } from '@/utils/common';
import { getDicts } from "@/api/public/dict";
export default {
    name: "policyNumber",
    data() {
        return {
            UploadClass,
            searchObj: {
                stationCode: "",
                stationName: "",
                auditStatus: "",
                page: 1,
                size: 10,
            },
            modelAdd: false,
            formItem: {
                id: "",
                incidentDate: "",
                reportDate: "",
                policyNo: "",
                riskType: "",
                insurerCompany: "",
                stationCode: "",
                stationName: "",
                incidentSituation: "",
                componentQuality: "",
                componentSpecification: "",
                totalLossAmount: null,
                inverterBrand: "",
                inverterModel: "",
                rejectionLossAmount: null,
                otherIncidentSituation: "",
                weatherCertificate: "",
                policeCertificate: "",
                fireCertificate: "",
                otherPhotos: "",
                componentDamageScenePhotos: "",
                componentRepairPhotos: "",
                otherCertificate: "",
            },
            commList: [],
            componentBrandList: [],//组件品牌规格
            componentSkudList: [],//组件规格
            inverterBrandList: [],//逆变器品牌规格
            inverterSkudList: [],//逆变器规格
            numPages: null,
            totalElements: 0,
            insuranceStatus: [],
            specialFlag: [],
        };
    },
    async created() {
        this.insuranceStatus = await getDicts("osp/merchant", "insuranceStatus").then(e => e?.list)
        this.specialFlag = await getDicts("osp/merchant", "specialFlag").then(e => e?.list)
    },
    mounted() {
        this.getCommList();
    },

    methods: {
        // 切换分页
        changeCurrent(curr) {
            this.searchObj.page = curr;
            this.getCommList();
        },

        // 查询按钮
        queryList() {
            this.commList = [];
            this.searchObj.page = 1;
            this.getCommList();
        },
        // 电站列表
        getCommList() {
            let datas = this.searchObj;
            propertyList(datas).then((res) => {
                let request = res.data.result;
                if (request.content.length > 0) {
                    this.commList = request.content;
                    this.totalElements = request.totalElements;
                } else {
                    this.totalElements = 0;
                    this.commList = [];
                }
            });
        },
        // 选择日期
        selectDate(data, refObj) {
            let date = data.replace('T', ' ');
            this.formItem[refObj] = date;
        },
        stationCodeInput() {
            this.getInstalledPower(this.formItem.stationCode)
            this.getFindFindSkuOrSn(this.formItem.stationCode)
        },
        //业主名称
        getInstalledPower(stationCode) {
            if (stationCode) {
                const params = {
                    stationCode: stationCode
                }
                propertyFindByStationCode(params).then(res => {
                    if (res.data.success) {
                        this.formItem.stationName = res.data.result.name
                        this.formItem.specialFlag = res.data.result.specialFlag
                        this.formItem.projectCompanyName = res.data.result.projectCompanyName
                        this.formItem.projectCompanyCode = res.data.result.projectCompanyCode
                        this.formItem.subCenterName = res.data.result.subCenterName
                        this.formItem.address = (res.data.result.provinceName || '-') +
                            (res.data.result.cityName || '-') +
                            (res.data.result.regionName || '-') +
                            (res.data.result.streetName || '-');
                    } else {
                        this.formItem.stationName = ""
                        this.$Message.error(res.data.error);
                    }
                })
            } else {
                this.$Message.error('请输入电站编码');
            }
        },
        //组件
        getFindFindSkuOrSn(stationCode) {
            if (stationCode) {
                const params = {
                    stationCode: stationCode
                }
                propertyFindFindSkuOrSn(params).then(res => {
                    if (res.data.success) {
                        this.componentBrandList = res.data.result.MODULE.map(item => ({
                            value: item.brandId,
                            label: item.brandName
                        }));
                        this.componentSkudList = res.data.result.MODULE.map(item => ({
                            value: item.sku,
                            label: item.skuName
                        }));
                        this.inverterBrandList = res.data.result.INVERTER.map(item => ({
                            value: item.brandId,
                            label: item.brandName
                        }));
                        this.inverterSkudList = res.data.result.INVERTER.map(item => ({
                            value: item.sku,
                            label: item.skuName
                        }));
                    } else {
                        this.componentBrandList = [],//组件品牌规格
                            this.componentSkudList = [],//组件规格
                            this.inverterBrandList = [],//逆变器品牌规格
                            this.inverterSkudList = [],//逆变器规格
                            this.$Message.error(res.data.error);
                    }
                })
            } else {
                this.$Message.error('请输入电站编码');
            }
        },
        //新增
        addInfo() {
            this.resetFormItem()
            this.modelAdd = true
        },
        toEdit(item) {
            this.formItem = { ...item };
            this.stationCodeInput()
            this.modelAdd = true
        },
        //新增/编辑
        submit() {
            const params = this.formItem
            if (!params.incidentDate) {
                this.$Message.error("请选择事故发生日期");
                return false
            }
            if (!params.reportDate) {
                this.$Message.error("请选择报案日期");
                return false
            }
            if (!params.stationCode) {
                this.$Message.error("请输入电站编码");
                return false
            }
            const postName = params.id ? propertyListUpdate(params) : propertyListAdd(params);
            postName.then(res => {
                if (res.data.success) {
                    this.$Message.success("提交成功");
                    this.queryList();
                    this.modelAdd = false;
                } else {
                    this.$Message.error(res.data.error);
                }

            })

        },
        //删除
        delInfo(id) {
            let datas = {
                id: id
            }
            let that = this
            this.$Modal.confirm({
                title: '删除',
                content: '<p>请确认是否删除</p>',
                onOk: () => {
                    propertyListDelete(datas).then(res => {
                        if (res.data.success) {
                            this.$Message.success('删除成功');
                            that.queryList();
                        } else {
                            that.$Message.error(res.data.error);
                        }
                    });
                },
            })
        },
        // 导出
        exportList() {
            let datas = this.searchObj;
            let { page, size, ...datasEx } = { ...datas };
            propertyListExport(datasEx)
                .then((res) => {
                    let binaryData = [];
                    let link = document.createElement("a");
                    binaryData.push(res.data);
                    link.style.display = "none";
                    link.href = window.URL.createObjectURL(new Blob(binaryData));
                    link.setAttribute("download", "保险报案(财产一切险).xlsx");
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                })
                .catch(() => {
                    this.$Message.error("导出失败");
                });
        },
        resetFormItem() {
            this.formItem = {
                id: "",
                incidentDate: "",
                reportDate: "",
                policyNo: "",
                riskType: "",
                insurerCompany: "",
                stationCode: "",
                stationName: "",
                incidentSituation: "",
                componentQuality: "",
                componentSpecification: "",
                totalLossAmount: null,
                inverterBrand: "",
                inverterModel: "",
                rejectionLossAmount: null,
                otherIncidentSituation: "",
                weatherCertificate: "",
                policeCertificate: "",
                fireCertificate: "",
                otherPhotos: "",
                componentDamageScenePhotos: "",
                componentRepairPhotos: "",
                otherCertificate: "",
            };
        },
        // 重置按钮
        emptyList() {
            this.searchObj = {
                stationCode: "",
                stationName: "",
                page: 1,
                size: 10,
            };
        },
        typeVal(list, value) {
            const obj = {};
            this[list].map((e) => {
                obj[e.value] = e.label || e.value;
            });
            return obj[value];
        },
    },
};
</script>
<style lang="scss" scoped>
@import "style/_cus_list.scss";

.fixedLeft1 {
    position: sticky;
    left: 0;
    @extend .back-filter;
}

.tag_line {
    position: relative;
}

:deep(.ivu-modal-fullscreen) {
    width: 100vw !important;
}

@media print {
    .cus_list {
        display: none;
    }

    .noprint {
        display: none;
    }
}

.cus_title {
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    line-height: 2.4;
    font-size: 16px;
    font-weight: bolder;
    border-bottom: 1px solid #f5f7f9;

    @extend.sub-dot-custom;
}
</style>
