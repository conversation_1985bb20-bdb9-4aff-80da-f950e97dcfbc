<!-- 上岗资格管理 -->
<template>
    <div class="cus_list">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏运维管理</Breadcrumb-item>
            <Breadcrumb-item to="/">服务人员</Breadcrumb-item>
            <Breadcrumb-item>上岗资格管理</Breadcrumb-item>
        </Breadcrumb>
        <div class="list_son">
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">员工编号：</p>
                    <Input v-model="staffObj.staffNo" placeholder="请输入..." clearable style="width: 150px" />
                </div>
                <div class="condition">
                    <p class="condition_p">员工姓名：</p>
                    <Input v-model="staffObj.staffName" placeholder="请输入..." clearable style="width: 150px" />
                </div>
                <div class="condition">
                    <p class="condition_p">手机号码：</p>
                    <Input v-model="staffObj.mobile" placeholder="请输入..." clearable style="width: 150px" />
                </div>
                <!-- <div class="condition">
          <p class="condition_p">上岗考试：</p>
          <Select v-model="staffObj.status" clearable style="width: 120px">
            <Option v-for="item in installList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </div> -->
            </div>
            <div class="list_but">
                <div class="condition">
                    <p class="condition_p">电工证号：</p>
                    <Input v-model="staffObj.electricNo" placeholder="请输入..." clearable style="width: 150px" />
                    <!-- <Select v-model="staffObj.faultLevel" clearable style="width: 120px">
            <Option v-for="item in installList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select> -->
                </div>
                <div class="condition">
                    <p class="condition_p">审核状态：</p>
                    <Select v-model="staffObj.auditStatus" clearable style="width: 120px">
                        <Option v-for="item in auditStatusLists" :value="item.value" :key="item.value">{{ item.label }}
                        </Option>
                    </Select>
                </div>
                <div class="condition">
                    <Button @click="debounce(queryList)" type="primary">查询</Button>
                    <Button @click="debounce(emptyList)">重置</Button>
                </div>
            </div>
            <div class="commListDiv">
                <table class="cus_table" border="0" cellspacing="0" cellpadding="0" width="100%">
                    <tr>
                        <th align="center">员工编号</th>
                        <th align="center">员工姓名</th>
                        <th align="center">性别</th>
                        <th align="center">手机号码</th>
                        <th align="center">岗位</th>
                        <!-- <th align="center">上岗考试</th> -->
                        <!-- <th align="center">电工证</th> -->
                        <th align="center">审核状态</th>
                        <th align="center">驳回原因</th>
                        <th align="center">有效日期</th>
                        <th align="center">电工证号</th>
                        <th align="center">电工证照片</th>
                        <th align="center">身份证号</th>
                        <th align="center">安全承诺书</th>
                        <th align="center">高处作业证有效日期</th>
                        <th align="center">高处作业证</th>
                        <th align="center" class="fixedRight">操作</th>
                    </tr>
                    <tr v-for="(item, index) in commList" :key="index">
                        <td align="center" width="80">{{ item.staffNo }}</td>
                        <td align="center" width="120">{{ item.staffName }}</td>
                        <td align="center" width="70">{{ typeVal('genderList', item.sex) }}</td>
                        <td align="center" width="70">
                            {{ item.mobile ? getEncryptedPhone(item.mobile, 'phone') || '-' : '-' }}
                        </td>
                        <td align="center" width="100" style="min-width: 120px">{{ typeVal('dutyList', item.duty) }}
                        </td>
                        <td align="center" width="100">{{ typeVal('auditStatusList', item.auditStatus) }}</td>
                        <td align="center" width="100">{{ item.rejectReason }}</td>
                        <td align="center" width="120" style="min-width: 110px">{{ item.electricEndDate &&
                            item.electricEndDate.substr(0, 10) }}</td>
                        <td align="center" width="120" style="min-width: 110px">{{ item.electricNo }}</td>
                        <td align="center" width="80" style="min-width: 100px">
                            <a v-if="item.electricMainUrl" :href="item.electricMainUrl" target="_blank">正面</a>
                            <a v-if="item.electricBakUrl" :href="item.electricBakUrl" target="_blank"
                                style="margin-left: 5px">反面</a>
                        </td>
                        <td align="center" width="80" style="min-width: 100px">
                            {{ item.idCardNo ? getEncryptedPhone(item.idCardNo, 'idCard') || '-' : '-' }}
                        </td>

                        <td align="center" width="100" style="min-width: 100px">
                            <a v-if="item.safetyUrl" @click="debounce(showFile(item.safetyUrl))" target="_blank">查看</a>
                            <span v-else>-</span>
                        </td>
                        <td align="center" width="100" style="min-width: 100px">
                            {{ item.highEndDate && item.highEndDate.substr(0, 10) || '-' }}
                        </td>
                        <td align="center" width="80" style="min-width: 100px">
                            <a v-if="item.highFrontUrl" :href="item.highFrontUrl" target="_blank">正面</a>
                            <a v-if="item.highBackUrl" :href="item.highBackUrl" target="_blank"
                                style="margin-left: 5px">反面</a>
                        </td>
                        <td align="center" width="80" style="min-width: 100px" class="fixedRight">
                            <Button type="info" v-if="!item.auditStatus" ghost size="small" @click="getView(item.id)"
                                style="margin: 3px; color: #2d8cf0">
                                上传资料
                            </Button>
                            <Button type="info" v-else-if="item.auditStatus === 'TWOREJECTED' || !item.safetyUrl"
                                ghost size="small" @click="getView(item.id)" style="margin: 3px; color: #2d8cf0">
                                修改资料
                            </Button>
                            <span v-else>-</span>
                        </td>
                    </tr>
                    <tr v-if="!commList.length">
                        <td colspan="11" align="center">暂无数据</td>
                    </tr>
                </table>
            </div>
            <Page style="margin: 20px" @on-change="changeCurrent" :current="staffObj.pageNum" :total="totalElements"
                show-total show-elevator />
        </div>
        <Modal width="800" v-model="modelEdit" draggable sticky scrollable title="上传资料" :footer-hide="true"
            :mask-closable="false">
            <Form :model="formItem" :label-width="130" inline>
                <FormItem label="员工姓名" required>
                    <Input class="item_large" type="text" maxlength="15" v-model="formItem.staffName" clearable
                        placeholder="请输入" style="width: 200px" disabled />
                </FormItem>
                <FormItem label="性别" required>
                    <Select v-model="formItem.sex" style="width: 120px; margin-right: 5px" disabled>
                        <Option v-for="item in genderList" :value="item.value" :key="item.value">{{ item.label }}
                        </Option>
                    </Select>
                </FormItem>
                <FormItem label="手机号码" required>
                    <Input class="item_large" type="text" maxlength="11" v-model="formItem.mobile" clearable
                        placeholder="请输入" style="width: 150px" disabled />
                </FormItem>
                <FormItem label="身份证号" required>
                    <Input class="item_large" type="text" v-model="formItem.idCardNo" clearable placeholder="请输入"
                        style="width: 200px" disabled />
                </FormItem>
                <FormItem label="电工证号" required>
                    <Input class="item_large" type="text" v-model="formItem.electricNo" clearable placeholder="请输入"
                        style="width: 200px" />
                </FormItem>
                <FormItem label="电工证有效日期" required>
                    <DatePicker type="date" :value="formItem.electricStartDate" placeholder="起始日期" @on-change="data => {
                        return selectDate(data, 'electricStartDate');
                    }
                        " format="yyyy-MM-dd HH:mm:ss" style="width: 180px; margin-right: 5px"></DatePicker>
                    <DatePicker type="date" :value="formItem.electricEndDate" placeholder="截止日期" @on-change="data => {
                        return selectDate(data, 'electricEndDate');
                    }
                        " format="yyyy-MM-dd HH:mm:ss" style="width: 180px"></DatePicker>
                </FormItem>
                <FormItem label="电工证照片" required>
                    <div style="display: flex">
                        <Upload class="inlinebox" :show-upload-list="false" :action="actionUrl" :headers="headers"
                            :on-progress="progress" :on-success="(response, file, fileList) => {
                                return fileUploadSuccess(response, file, fileList, 'electricMainUrl');
                            }
                                " :format="['jpg', 'jpeg', 'png']" :max-size="40960"
                            :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize">
                            <div class="file_pics">
                                <img v-if="formItem.electricMainUrl" :src="formItem.electricMainUrl" width="100%"
                                    title="右键新标签页查看" />
                                <Icon v-else type="md-add-circle" size="16" color="#666" />
                            </div>
                            <span class="tips">上传正面</span>
                        </Upload>
                        <Upload class="inlinebox" :show-upload-list="false" :action="actionUrl" :headers="headers"
                            :on-progress="progress" :on-success="(response, file, fileList) => {
                                return fileUploadSuccess(response, file, fileList, 'electricBakUrl');
                            }
                                " :format="['jpg', 'jpeg', 'png']" :max-size="40960"
                            :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize">
                            <div class="file_pics">
                                <img v-if="formItem.electricBakUrl" :src="formItem.electricBakUrl" title="右键新标签页查看" />
                                <Icon v-else type="md-add-circle" size="16" color="#666" />
                            </div>
                            <span class="tips">上传反面</span>
                        </Upload>
                        <p class="tips" style="margin-left: 20px">支持扩展名：.jpg .jpeg .png...</p>
                    </div>
                </FormItem>
                <FormItem label="安全承诺书下载签字" required>
                    <div style="display: flex; align-items: center;">
                        <Button type="primary" ghost size="small" style="height: 30px; margin-right: 20px;"
                            @click="debounce(secureDown)">
                            安全承诺书下载
                        </Button>
                        <Upload class="inlinebox" accept=".docx" :show-upload-list="false"
                            :action="UploadClass.actionUrl('file')" :headers="UploadClass.headers"
                            :before-upload="UploadClass.progress"
                            :on-success="(response) => { formItem.safetyUrl = response.result[0].fileUrl; return UploadClass.upSuccess() }"
                            :format="['docx']" :max-size="102400" :on-format-error="UploadClass.formatError"
                            :on-exceeded-size="UploadClass.maxSizeError">
                            <div style="display: flex;">
                                <Button icon="md-cloud-upload">
                                    {{ formItem.safetyUrl ? '重新' : '' }}上传文件
                                </Button>
                            </div>
                        </Upload>
                        <p class="tips" style="margin:18px 0 0 20px;"> 支持扩展名：.docx </p>
                    </div>
                    <a v-if="formItem.safetyUrl" @click="debounce(showFile(formItem.safetyUrl))" target="_blank"
                        style="white-space: nowrap;font-size: 15px;">
                        <Icon type="md-document" /> 查看文件
                    </a>
                </FormItem>
                <FormItem label="高处作业证有效日期" required>
                    <DatePicker type="date" :value="formItem.highStartDate" placeholder="起始日期" @on-change="data => {
                        return selectDate(data, 'highStartDate');
                    }
                        " format="yyyy-MM-dd HH:mm:ss" style="width: 180px; margin: 10px 5px 0 0;"></DatePicker>
                    <DatePicker type="date" :value="formItem.highEndDate" placeholder="截止日期" @on-change="data => {
                        return selectDate(data, 'highEndDate');
                    }
                        " format="yyyy-MM-dd HH:mm:ss" style="width: 180px; margin: 10px 5px 0 0;"></DatePicker>
                </FormItem>
                <FormItem label="高处作业证" required>
                    <div style="display: flex">
                        <Upload class="inlinebox" :show-upload-list="false" :action="actionUrl" :headers="headers"
                            :on-progress="progress" :on-success="(response, file, fileList) => {
                                return fileUploadSuccess(response, file, fileList, 'highFrontUrl');
                            }
                                " :format="['jpg', 'jpeg', 'png']" :max-size="40960"
                            :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize">
                            <div class="file_pics">
                                <img v-if="formItem.highFrontUrl" :src="formItem.highFrontUrl" width="100%"
                                    title="右键新标签页查看" />
                                <Icon v-else type="md-add-circle" size="16" color="#666" />
                            </div>
                            <span class="tips">上传正面</span>
                        </Upload>
                        <Upload class="inlinebox" :show-upload-list="false" :action="actionUrl" :headers="headers"
                            :on-progress="progress" :on-success="(response, file, fileList) => {
                                return fileUploadSuccess(response, file, fileList, 'highBackUrl');
                            }
                                " :format="['jpg', 'jpeg', 'png']" :max-size="40960"
                            :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize">
                            <div class="file_pics">
                                <img v-if="formItem.highBackUrl" :src="formItem.highBackUrl" title="右键新标签页查看" />
                                <Icon v-else type="md-add-circle" size="16" color="#666" />
                            </div>
                            <span class="tips">上传反面</span>
                        </Upload>
                        <p class="tips" style="margin-left: 20px">支持扩展名：.jpg .jpeg .png...</p>
                    </div>
                </FormItem>
            </Form>
            <div style="text-align: center; margin: 30px 0">
                <Button type="default" size="large" style="width: 120px; margin-right: 30px"
                    @click="modelEdit = false">取消</Button>
                <Button type="primary" size="large" style="width: 120px" @click="debounce(saveStaff)">
                    {{ ['CHECKPENDING', 'REJECTED', 'TWOREJECTED'].includes(formItem.auditStatus) ? '修改' : '提交' }}
                </Button>
            </div>
        </Modal>
    </div>
</template>
<script>
import _data from '@/views/osp/_edata'; // 数据字典
import API from '@/api/osp';
import dataEncryption from '@/utils/encryption'
import { UploadClass } from '@/utils/common';
export default {
    name: 'operationList',
    data() {
        return {
            UploadClass,
            modelEdit: false,
            indeterminate: false,
            checkAll: false,
            preview: false,
            staffObj: {
                staffStatus: '', // 员工状态
                staffNo: '', // 员工编号
                staffName: '', // 员工姓名
                sex: '', // 性别
                realNameAuth: '', // 实名认证
                pageNum: 1,
                pageSize: 10,
                mobile: '', // 手机号码
                idCardNo: '', // 身份证号
                electricNo: '', // 电工证
                duty: '', // 岗位
                auditStatus: '' // 审核状态
            },
            formItem: {},
            totalElements: 0,
            genderList: _data.genderList,
            dutyList: _data.dutyList,
            staffStatusList: _data.staffStatusList,
            auditStatusList: _data.auditStatusList,
            auditStatusLists: _data.auditStatusLists,
            commList: [],
            tagIndex: 0,
            actionUrl: `${process.env.VUE_APP_BASE_API}/merchant/oss/image/upload.do`,
            headers: { Authorization: `Bearer ${JSON.parse(window.localStorage.getItem('logins')).access_token}` }
        };
    },
    mounted() {
        this.getCommList();
    },
    methods: {
        //数据加密
        getEncryptedPhone(data, type) {
            switch (type) {
                case 'phone':
                    return dataEncryption.encryptData(data, 'phone');
                case 'idCard':
                    return dataEncryption.encryptData(data, 'idCard');
                default:
                    return '-';
            }
        },
        jumpto(path) {
            this.$router.push({
                path
            });
        },

        // 文件上传
        fileUploadSuccess(response, file, fileList, refObj) {
            let that = this;
            this.formItem[refObj] = response.result[0].imageUrl;
            setTimeout(() => {
                that.$Message.destroy();
            }, 500);
        },

        progress() {
            this.$Message.loading({
                content: '上传中...',
                duration: 0
            });
        },

        handleFormatError(file) {
            this.$Notice.warning({
                title: '上传格式不正确',
                desc: '请上传正确的格式文件'
            });
        },

        handleMaxSize(file) {
            this.$Notice.warning({
                title: '上传图片过大',
                desc: file.name + '图片不应超过 20M，请压缩后上传.'
            });
        },

        typeVal(list, value) {
            const obj = {};
            this[list].map(e => {
                obj[e.value] = e.label || e.value;
            });
            return obj[value];
        },

        // 选择日期
        selectDate(data, refObj) {
            let date = data.replace('T', ' ');
            this.formItem[refObj] = date;
        },
        secureDown() {
            const url = 'https://cdn01.rrsjk.com/images/e24e9f7c-ca91-4d67-be69-1460c70e5234.docx';
            this.downFile(url)
        },
        showFile(url) {
            this.downFile(url)
        },
        downFile(url) {
            const fileName = '安全承诺书.docx';
            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络响应不正常');
                    }
                    return response.blob();
                })
                .then(blob => {
                    const link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = fileName;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                })
                .catch(error => {
                    console.error('下载文件时出错:', error);
                });
        },
        // 上传资料
        saveStaff() {
            const that = this
            let datas = this.formItem;
            console.log(datas, 'datas');

            API.uploadData(datas).then(res => {
                if (res.data.success) {
                    this.$Message.success('保存成功');
                    this.modelEdit = false;
                    setTimeout(() => {
                        that.getCommList();
                    }, 1500)
                } else {
                    this.$Message.error(res.data.error);
                }
            });
        },

        // 打开弹窗
        getView(id) {
            this.modelType = true;
            let datas = {
                id: id
            };
            API.getPersonDetails(datas).then(res => {
                if (res.data) {
                    this.formItem = res.data.result;
                    this.modelEdit = true;
                } else {
                    this.$Message.error(res.data.error);
                }
            });
        },

        // 切换分页
        changeCurrent(curr) {
            this.staffObj.pageNum = curr;
            this.getCommList();
        },

        // 查询按钮
        queryList() {
            this.staffObj.pageNum = 1;
            this.getCommList();
        },

        // 重置按钮
        emptyList() {
            this.staffObj = {
                staffStatus: '', // 员工状态
                staffNo: '', // 员工编号
                staffName: '', // 员工姓名
                sex: '', // 性别
                realNameAuth: '', // 实名认证
                pageNum: 1,
                pageSize: 10,
                mobile: '', // 手机号码
                idCardNo: '', // 身份证号
                electricNo: '', // 电工证
                duty: '', // 岗位
                auditStatus: '' // 审核状态
            };
        },

        // 员工列表
        getCommList() {
            this.$Message.loading({
                content: '查询中...',
                duration: 0
            });
            let datas = this.staffObj;
            API.getPersonnelList(datas).then(res => {
                this.$Message.destroy();
                let request = res.data.result;
                if (request.content.length > 0) {
                    this.totalElements = request.totalElements;
                    this.commList = request.content;
                } else {
                    this.totalElements = 0;
                    this.commList = [];
                }
            });
        }
    }
};
</script>
<style lang="scss" scoped>
@import '@/style/_cus_reg.scss';
@import '@/style/_cus_list.scss';
@import '@/style/_ivu-modal.scss';


.tips {
    display: inline-block;
    width: 100%;
    text-align: center;
    font-size: 12px;
    color: #999;
}

.file_pics {
    @extend .flex-row-center-center;
    width: 80px;
    height: 80px;
    border-radius: 8px;
    background: #f8f8f8;
    cursor: pointer;
    overflow: hidden;

    img {
        height: 100%;
        width: 100%;
    }
}

.inlinebox {
    @extend .flex-column-center-center;
    margin-right: 15px;
    display: inline-flex;
}
</style>
