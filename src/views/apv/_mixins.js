/*
* @Author: lufy
* @Date:  2022-03-28 15:05:03
* @FilePath: /src/views/apv/_mixins.js
* @Description: apv 系列混入通用方法：获取银行支行
*/

import API from '@/api/apv';
export default {
    data() {
        return {
            bankTypeList: [],
            houseBankOneList: [],
            houseBankTwoList: [],
            houseBankThreeList: [],
            houseBankList: [],
        }
    },
    methods: {
        // 重置支行
        clearHouseBank() {
            this.houseBankList = [];
            this.bankObj.houseBankCode = '';
            this.bankObj.houseBankName = '';
            this.bankObj.bankNo = '';
            this.bankObj.bankNumber = '';
        },

        // 主银行
        async getBankList() {
            let res = await API.findMainBankInfo();
            if (res.data.success) {
                res.data.result?.map(e => {
                    let obj = {
                        id: e.bankCode,
                        name: e.bankName
                    };
                    this.bankTypeList.push(obj);
                });
            }
        },

        // 获取银行省市区
        async getProvinceBank() {
            const res = await API.findProvinceBank()
            res.data.result?.map(e => {
                let obj = {
                    id: e.id,
                    code: e.areaCode,
                    name: e.areaName
                };
                this.houseBankOneList.push(obj);
            });
        },

        // 获取支行
        async getHouseBank(data) {
            const res = await API.findHouseBank(data)
            res.data.result?.map((e, inx) => {
                let obj = {
                    id: inx,
                    bankCode: e.bankCode,
                    name: e.bankName,
                    bankNo: e.bankNo,
                    bankNumber: e.bankNumber
                };
                this.houseBankList.push(obj);
            });
        },
        // -------------------------------------------------------------------------------------------------- //

        // 主银行改变
        bankChange(item) {
            const bankNameTrim = item?.value?.trim()
            const findObj = this.bankTypeList.find(e => e.name.includes(bankNameTrim))
            this.bankObj.bankName = bankNameTrim || '';
            this.bankObj.bankCode = findObj?.id || '';
            this.clearHouseBank()
            if (!this.bankObj.bankRegionCode) return;
            let data = {
                bankCode: findObj?.id,
                regionCode: this.bankObj.bankRegionCode
            };
            this.getHouseBank(data);
        },

        // 省改变
        bankOneChange(item) {
            return new Promise((resolve) => {
                this.bankObj.bankProvinceName = item ? item?.label : '';
                this.houseBankTwoList = [];
                this.bankObj.bankCityCode = '';
                this.bankObj.bankRegionCode = '';
                this.clearHouseBank()
                if (!item) return;
                let parent = this.houseBankOneList.find(e => e.id === item.value) || this.houseBankOneList.find(e => e.code === item.value)
                this.bankObj.bankProvinceCode = parent?.code || ''
                this.bankObj.bankProvinceId = parent?.id || ''
                let data = {
                    parentCode: parent?.code || ''
                };
                API.findCityBank(data).then(res => {
                    res.data.result?.map(e => {
                        let obj = {
                            id: e.id,
                            code: e.areaCode,
                            name: e.areaName
                        };
                        this.houseBankTwoList.push(obj);
                    });
                    resolve(true)
                })
            })
        },

        // 市变化
        bankTwoChange(item) {
            return new Promise((resolve) => {
                this.bankObj.bankCityName = item ? item?.label : '';
                this.houseBankThreeList = [];
                this.bankObj.bankRegionCode = '';
                this.clearHouseBank()
                if (!item) return;
                let parent = this.houseBankTwoList.find(e => e.id === item.value) || this.houseBankTwoList.find(e => e.code == item.value)
                this.bankObj.bankCityCode = parent?.code || ''
                this.bankObj.bankCityId = parent?.id || ''
                let data = {
                    parentCode: parent?.code || ''
                };
                API.findRegionBank(data).then(res => {
                    res.data.result.map(e => {
                        let obj = {
                            id: e.id,
                            code: e.areaCode,
                            name: e.areaName
                        };
                        this.houseBankThreeList.push(obj);
                    });
                    resolve(true)
                });
            })
        },

        // 区改变
        bankThreeChange(item) {
            return new Promise(async (resolve) => {
                this.bankObj.bankRegionName = item ? item?.label : '';
                this.clearHouseBank()
                if (!this.bankObj.bankCode) return;
                if (!item) return;
                let parent = this.houseBankThreeList.find(e => e.id === item.value) || this.houseBankThreeList.find(e => e.code === item.value)
                this.bankObj.bankRegionCode = parent?.code || ''
                this.bankObj.bankRegionId = parent?.id || ''
                let data = {
                    bankCode: this.bankObj.bankCode,
                    regionCode: parent?.code || ''
                };
                // 等待支行数据加载完毕
                await this.getHouseBank(data);
                resolve(true)
            })
        },

        // 支行改变
        houseBankChange(item) {
            return new Promise((resolve) => {
                if (!item) return;
                let obj = this.houseBankList?.find(e => e.bankNumber === item.value)
                if (!!obj) {
                    this.bankObj.houseBankName = obj.name
                    this.bankObj.houseBankCode = obj.bankCode;
                    this.bankObj.bankNo = obj.bankNo;
                    this.bankObj.bankNumber = obj.bankNumber;
                }
                resolve(true)
            })
        },
    }
}
