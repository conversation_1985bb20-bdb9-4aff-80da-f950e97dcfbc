/*
 * @Author: lufy
 * @FilePath: /src/views/apv/_edata.js
 * @Description: apv 数据字典
 * @remark: 历史优化原因，新增字典全部按对象 Object 类型处理。
 */
/**
 * @description: 现场验收审核状态
 */
export const offLineAuditStatus = {
    "AUDIT_OK": "审核通过",
    "AUDIT_REJECT": "审核驳回",
    "WAIT_AUDIT": "待审核"
};

export default {
    /**
     * @description: 公司类型
     */
    companyTypeList: [{
            value: "1",
            label: "上市公司"
        },
        {
            value: "2",
            label: "股份有限公司"
        },
        {
            value: "3",
            label: "有限责任公司"
        },
        {
            value: "4",
            label: "合伙经营"
        },
        {
            value: "5",
            label: "个人独资"
        },
        {
            value: "6",
            label: "个体户"
        },
        {
            value: "7",
            label: "国有企业"
        },
        {
            value: "8",
            label: "集体企业"
        },
        {
            value: "9",
            label: "有限公司"
        }
    ],

    /**
     * @description: 纳税人类型
     */
    taxpayerTypeList: [{
            value: "1",
            label: "一般纳税人"
        },
        {
            value: "2",
            label: "小规模纳税人"
        },
        {
            value: "3",
            label: "个体户"
        },
        {
            value: "4",
            label: "个人"
        }
    ],

    /**
     * @description: 【房屋类型】
     */
    houseTypeList: [{
            value: "flat",
            label: "平屋顶",
            show: true
        },
        {
            value: "slope",
            label: "斜屋顶",
            show: true
        },
        {
            value: "double_slope",
            label: "双面坡",
            show: true
        },
        {
            value: "ground",
            label: "院内地面",
            show: true
        },
        // 业务隐藏 At 2023.05.29
        // {
        //   value: "hwdm",
        //   label: "户外地面",
        // },
        {
            value: "yghb",
            label: "渔光互补",
            show: false // * At 2023-10-19 取消
        },
        {
            value: "nghb",
            label: "农光互补",
            show: false // * At 2023-10-19 取消
        },
        {
            value: "ygf",
            label: "阳光房",
            show: true
        }
    ],

    /**
     * @description: 【安装方式】
     */
    installList: [{
            value: "flatToBolt",
            label: "平屋顶-平改坡（膨胀螺栓固定）",
            show: false // * At 2023-10-19 取消
        },
        {
            value: "flatToBase",
            label: "平屋顶-平改坡（底梁固定）",
            show: false // * At 2023-10-19 取消
        },
        {
            value: "flatToWeight",
            label: "平屋顶-平改坡（配重块）",
            show: false // * At 2023-10-19 取消
        },
        {
            value: "groundPileFix",
            label: "院内地面-柱桩固定",
            show: true
        },
        {
            value: "flatBolt",
            label: "平屋顶-膨胀螺栓固定",
            show: true
        },
        {
            value: "slopePull",
            label: "斜屋顶-前拉后拽",
            show: true
        },
        {
            value: "slopePullShort",
            label: "斜屋顶-前拉后拽+短柱",
            show: true
        },
        {
            value: "flatWeight",
            label: "平屋顶-配重块",
            show: true
        },
        {
            value: "waterFloating",
            label: "水面漂浮",
            show: false // * At 2023-10-19 取消
        },
        {
            value: "pileFilx",
            label: "柱桩固定",
            show: false // * At 2023-10-19 取消
        },
        {
            value: "sunlightRoomFix",
            label: "阳光房-化学锚栓固定/膨胀螺栓", // * At 2023-10-19 修改
            show: true
        },
        {
            value: "slopeHook",
            label: "斜屋顶-挂钩固定安装",
            show: true
        },
        // {
        //     value: 'flatSun',
        //     label: '平顶屋-阳光屋顶'
        // }
        // * 新增试点
        // * 试点放开 At 2023-10-09
        {
            value: "slope_one",
            label: "双面坡-南北坡-前拉后拽",
            show: true
        },
        {
            value: "slope_two",
            label: "双面坡-南北坡-前拉后拽+短柱",
            show: true
        },
        {
            value: "slope_three",
            label: "双面坡-南北坡-双坡挂钩",
            show: true
        },
        {
            value: "slope_four",
            label: "双面坡-南北坡-双坡挂钩+水槽",
            show: true
        },
        {
            value: "slope_five",
            label: "双面坡-东西坡-前拉后拽",
            show: true
        },
        {
            value: "slope_six",
            label: "双面坡-东西坡-前拉后拽+短柱",
            show: true
        },
        {
            value: "slope_seven",
            label: "双面坡-东西坡-双坡挂钩",
            show: true
        },
        {
            value: "slope_eight",
            label: "双面坡-东西坡-双坡挂钩+水槽",
            show: true
        },
        {
            value: "U_SHAPE_SINK",
            label: "阳光房-U型水槽-三级导水",
            show: true
        },
        {
            value: "FOURTH_LEVEL_WATER_DIVERSION",
            label: "阳光房-U型水槽-四级导水",
            show: true
        },
        {
            value: "YGF_U_THREE_E",
            label: "庭院阳光房-U型水槽-三级导水",
            show: true
        },
        {
            value: "YGF_U_FOUR_E",
            label: "庭院阳光房-U型水槽-四级导水",
            show: true
        },
        {
            "value": "slope_nine",
            "label": "双面坡-南北坡-前拉后拽-N",
            "show": true,
            "disable": false
        },
        {
            "value": "slope_ten",
            "label": "双面坡-南北坡-双坡挂钩-N",
            "show": true,
            "disable": false
        }
        // {
        //     value: "U_SHAPE_SINK_GABLE_SLOPE",
        //     label: "U型水槽-人字坡",
        //     show: true
        // },
        // {
        //     value: "U_SHAPE_SINK_SINGLE_SIDED_SLOPE",
        //     label: "U型水槽-单面坡",
        //     show: true
        // },
        // {
        //     value: "FOURTH_LEVEL_WATER_DIVERSION_SIDED_SLOPE",
        //     label: "四级导水-单面坡",
        //     show: true
        // },
        // {
        //     value: "FOURTH_LEVEL_WATER_DIVERSION_GABLE_SLOPE",
        //     label: "四级导水-人字坡",
        //     show: true
        // },
    ],

    /**
     * @description: 【屋顶平面图形】
     */
    shapList: [],

    /**
     * @description: 【组件尺寸】
     */
    componentSizeList: [],

    /**
     * @description: 电站审核/下单状态
     */
    status: {
        INIT: "待提交审核",
        WAIT_AUDIT: "待审核",
        AUDIT_REJECT: "驳回",
        WAIT_CONFIG: "审核完成待配方案",
        WAIT_ORDER: "待下单",
        ROADWORKING: "施工中",
        ROADWORK_CONFIRM: "施工信息待确认", // 待服务商确认  2022.10 施工队业务新增
        ROADWORK_REJECT: "施工信息驳回", // 2022.10 施工队业务新增
        COMPLETE_WAIT_AUDIT: "待审核", // 完工确认审核
        COMPLETE_AUDIT_REJECT: "被驳回", // 完工确认审核驳回
        // * At 2023-10-27 技术审核、商务审核 调整
        // APPLY_CHECK: "施工完成待申请并网验收",
        // WAIT_CHECK: "待验收",
        // WAIT_FIRST_AUDIT: "待终审一验",
        // FIRST_AUDIT_REJECT: "终审一验驳回",
        // WAIT_FINAL_AUDIT: "待终审二验",
        // FINAL_AUDIT_REJECT: "终审二验驳回",
        WAIT_TECH_CHECK: "待技术审核",
        TECH_CHECK_REJECT: "技术审核驳回",
        WAIT_UPLOAD_GRID_INFO: "提交并网验收资料",
        WAIT_FIRST_AUDIT: "待商务审核",
        FIRST_AUDIT_REJECT: "商务审核驳回",
        OFF_LINE_INIT: "待提交整改信息",
        WAIT_OFF_LINE_CHECK: "待现场验收", // * At 2024-04-23 新增流程
        OFF_LINE_CHECK_REJECT: "现场验收驳回", // * At 2024-04-23 新增流程
        ENABLE: "已完成",
        DISABLE: "删除",
        STOP: "已终止",
        REPURCHASE: "已回购"
    },

    /**
     * @description: 备案类型
     */
    beiAnType: {
        COMPANY: "公司备案",
        HOUSEHOLD: "户用备案"
    },

    /**
     * @description: 政策列表
     */
    policyType: {
        KUAI: "块",
        W: "瓦"
    },

    /**
     * @description: 保证金
     */
    bailType: {
        ADD: "交保证金",
        SUBTRACT: "扣保证金"
    },
    payChannel: {
        TRANSFER: "转账汇款",
        ALIPAY: "支付宝",
        WECHAT: "微信"
    },
    payStatus: {
        WAIT: "待确认",
        REJECT: "审核驳回",
        CONFIRMED: "已入账"
    },

    /**
     * @description: 组件类型
     */
    moduleList: [{
            value: "MODULE",
            label: "组件"
        },
        {
            value: "INVERTER",
            label: "逆变器"
        },
        {
            value: "DISTRIBUTION_BOX",
            label: "配电箱"
        }
    ],

    installPlanList: {
        "GABLE_SLOPE": "人字坡",
        "SINGLE_SIDED_SLOPE": "单面坡"
    },

    /**
     * @description: 辅料类型
     */
    moduleListInfo: {
        "MODULE": "组件",
        "INVERTER": "逆变器",
        "DISTRIBUTION_BOX": "配电箱",
        "U_SHAPED_BOTTOM_SUPPORT": "U型底托",
        "U80_BLOCK_COMBINATION": "U80压块组合",
        "U_SHAPED_SINK": "U型水槽",
        "U_SHAPED_CLAMP": "U楔型夹",
        "U_SHAPED_SINK_BRACKET": "U型水槽托架",
        "M8_20_BOLT_NUT": "M8*20螺栓螺母",
        "U_SHAPED_SMALL_SINK": "U小水槽",
        "U_SHAPED_NORTH_SOUTH_MAIN_SINK": "U南北主水槽",
        "U_SHAPED_EDGE_SINK": "U包边水槽",
        "EPDM_RUBBER": "EPDM橡胶",
        "INTEGRATED_RIDGE_CONNECTORS": "屋脊链接一体化",
        "HORIZONTAL_UPPER_PRESSURE_BLOCK": "横排上压块",
        "HORIZONTAL_DOWNWARD_PRESSURE_BLOCK": "U横排下压块",
        "M8_30_BOLT_NUT": "M8*30螺栓螺母",
        "VERTICAL_CONDUCTIVE_SHEET": "竖排导电片",
        "HORIZONTAL_CONDUCTIVE_SHEET": "横排导电片",
        "STUD": "立柱",
        "BEAM": "支撑梁",
        "MAIN_BEAM": "主梁",
        "ANGLE_IRON": "角钢",
        "STUD_FEET_FIXED_BOLT": "柱脚固定螺栓",
        "STUD_BOTTOM_IRON": "柱底钢板",
        "STUD_BOTTOM_STIFFENING_PLATE": "柱底加劲板",
        "TRIANGLE_CONNECTOR": "三角链接件",
        "M10_80_BOLT_NUT": "M10*80螺栓螺母",
        "RED_LINE": "PV1-F/H1Z2Z2-K4平方红线",
        "BLACK_LINE": "PV1-F/H1Z2Z2-K4平方黑线",
        "U70_BLOCK_COMBINATION": "70压块组合",
        "U110_BLOCK_COMBINATION": "110压块组合",
        "VERTICAL_SMALL_WATER_TANK": "纵向小水槽",
        "HORIZONTAL_SINK": "横向水槽",
        "VERTICAL_MIDDLE_WATER_TANK": "纵向中水槽",
        "MAIN_WATER_TANK": "主水槽",
        "EDGING_SINK": "包边水槽",
        "RIDGE_SINK": "屋脊水槽",
        "M_SHAPED_SINK": "M型水槽",
        "WEDGE_CLAMP": "楔型夹",
        "M_SHAPED_SINK_BRACKET": "M型水槽托架",
        "CARRIER_CONDUCTIVE_SHEET": "托架导电片",
        "M_80_BLOCK_COMBINATION": "M80压块组合",
        "M_SHAPED_SMALL_SINK": "M小水槽",
        "PURLIN": "檩条",
        "BEAM_BRACING": "梁斜撑",
        "STUD_BOTTOM_IRON_2": "柱底钢板2",
        "PRE_EMBEDDED_U_SHAPED_BOLT": "预埋U型螺栓",
    },

    /**
     * @description: 仓库状态
     */
    storeStatus: [{
            value: "ALL",
            label: "所有"
        },
        {
            value: "WAIT_SEND_GOODS",
            label: "待发货"
        },
        {
            value: "WAIT_IN",
            label: "待签收"
        },
        {
            value: "FINISHED",
            label: "已签收"
        }
    ],
    signStoreStatus: {
        WAIT_AUDIT: "待审核",
        AUDIT_OK: "已审核",
        AUDIT_REJECT: "已驳回",
        CONTRACT_SIGNED: "已签",
        CONTRACT_NOT_SIGNED: "未签",
    },

    /**
     * @description: 出库状态
     */
    storeOutStatus: [{
            value: "ALL",
            label: "所有"
        },
        {
            value: "FINISHED",
            label: "已出库"
        },
        {
            value: "WAIT",
            label: "待出库"
        }
    ],

    /**
     * @description: 合同状态
     */
    contractStatusConfirm: [{
            value: "INIT",
            label: "初始"
        },
        {
            value: "WAIT_CONFIRM",
            label: "待确认"
        },
        {
            value: "WAIT_SIGN,WAIT_USER_SIGN",
            label: "待签合同"
        },
        {
            value: "WAIT_PARTNER_SIGN",
            label: "待合伙人签合同"
        },
        {
            value: "FINISHED",
            label: "已签署"
        },
        {
            value: "DISABLE",
            label: "已作废"
        },
        {
            value: "WAIIT_SERVICE_SING",
            label: "待我司盖章"
        },
        {
            value: "WAIT_COMPANY_SIGN",
            label: "待项目公司签合同"
        }
    ],
    contractStatus: [{
            value: "INIT",
            label: "初始"
        },
        {
            value: "WAIT_CONFIRM",
            label: "待确认"
        },
        {
            value: "WAIT_SIGN,WAIT_USER_SIGN",
            label: "待签合同"
        },
        {
            value: "WAIT_PARTNER_SIGN",
            label: "待合伙人签合同"
        },
        {
            value: "SIGNED",
            label: "已签署"
        },
        {
            value: "DISABLE",
            label: "已作废"
        },
        {
            value: "WAIIT_SERVICE_SING",
            label: "待我司盖章"
        },
        {
            value: "WAIT_COMPANY_SIGN",
            label: "待项目公司签合同"
        }
    ],
    contractStatusValue: {
        INIT: "初始",
        WAIT_CONFIRM: "待确认",
        WAIT_SIGN: "待签合同",
        WAIT_USER_SIGN: "待签合同",
        WAIT_PARTNER_SIGN: "待合伙人签合同",
        SIGNED: "已签署",
        FINISHED: "已签署",
        DISABLE: "已作废",
        WAIIT_SERVICE_SING: "待我司盖章",
        WAIT_COMPANY_SIGN: "待项目公司签合同"
    },
    // 合伙人
    contractStatusPartner: [{
            value: "INIT",
            label: "初始"
        },
        {
            value: "WAIT_CONFIRM",
            label: "待确认"
        },
        {
            value: "WAIT_SIGN,WAIT_USER_SIGN",
            label: "待业主签合同"
        },
        {
            value: "WAIT_PARTNER_SIGN",
            label: "待合伙人签合同"
        },
        {
            value: "SIGNED",
            label: "已签署"
        },
        {
            value: "DISABLE",
            label: "已作废"
        }
    ],
    // 合伙人
    contractStatusValuePartner: {
        INIT: "初始",
        WAIT_CONFIRM: "待确认",
        "WAIT_SIGN,": "待业主签合同",
        WAIT_USER_SIGN: "待业主签合同",
        WAIT_PARTNER_SIGN: "待合伙人签合同",
        SIGNED: "已签署",
        FINISHED: "已签署",
        DISABLE: "已作废"
    },

    /**
     * @description: 合同类型
     */
    contractType: {
        STOP: "终止协议",
        MASTER: "主合同",
        ZH_MASTER: "主合同",
        CHD_MASTER: "主合同",
        INSTALL: "装机补充协议",
        FINANCE: "结算补充协议",
        CREDIT_AUTH: "信息授权协议",
        OWNERSHIP_PROVE: "权属证明"
    },

    /**
     * @description: 供应商采购状态
     */
    orderStatus: [{
            value: "WAIT_DELIVERED",
            label: "待发货"
        },
        {
            value: "PARD_DELIVERED",
            label: "部分发货 "
        },
        {
            value: "DELIVERED",
            label: "已发货"
        }
    ],

    /**
     * @description: 光E宝签约状态
     */
    eSignStatus: {
        NO_SIGN: "未签约",
        SIGNED: "已签约 ",
        ALLOW_SIGN: "可签约",
        NO_NEED_SIGN: "无需签约"
    },
    eSignStatusList: {
        0: "未签约",
        1: "已签约 ",
        "-1": "可签约"
    },

    /**
     * @description: 光E宝划转状态
     */
    effectStatusList: {
        "01": "未生效",
        "02": "已生效"
    },

    /**
     * @description: 光E宝合同类型
     */
    eContractTypeList: {
        "01": "个人光伏贷",
        "02": "开发商租赁个人屋顶",
        "03": "个人融资租"
    },

    /**
     * @description: 光E宝用户状态
     */
    authorisationStatusList: {
        "00": "未授权",
        "01": "预授权 ",
        "02": "已授权 ",
        "03": "授权结束 ",
        "04": "失败 ",
        "05": "授权暂停 ",
        "06": "授权中止 "
    },

    /**
     * @description: 方案变更状态
     */
    editStatus: {
        WAIT_AUDIT: "待审核",
        AUDIT_REJECT: "审核驳回",
        ENABLE: "审核通过"
    },

    /**
     * @description: 对账单状态
     */
    sapStatusList: {
        1: "可核销",
        2: "请款申请中",
        // '3': '请款审核驳回',
        // '4': '请款审核通过',
        5: "核销发票中",
        6: "发票已核销",
        7: "已确认",
        8: "出款中",
        9: "已出款",
        // '-2': '已作废',
        '-3': "已冻结",
    },

    /**
     * @description: 结算单状态
     */
    applyStatusList: {
        // '1': '待请款',
        2: "请款申请中",
        // '3': '请款审核驳回',
        // '4': '请款审核通过',
        5: "核销发票中",
        6: "发票已核销",
        7: "已确认",
        8: "出款中",
        9: "已结算"
    },

    /**
     * @description: 区域申请状态
     */
    areaStatus: {
        WAIT_AUDIT: "待审核",
        REJECT: "已拒绝",
        WAIT_SIGN: "待签协议",
        WAIT_PAY: "待支付保证金",
        ENABLE: "已授权",
        DISABLE: "禁用"
    },
    /**
     * @description: 终止状态
     */
    stopStatus: {
        INIT: "无",
        WAIT_AUDIT: "待审核",
        WAIT_SIGN: "待签署终止协议",
        STOP: "已终止",
        AUDIT_REJECT: "已驳回",
        REVOCATION: "已撤回"
    },
    stopReason: ["当地变压器超容", "相关部门审批不通过", "政策原因停止安装", "用户不想装了", "邻里纠纷", "遮挡物无法拆除"],

    /**
     * @description: 公司编码
     */
    companyCode: {
        "1PG0": "青岛海尔光伏新能源有限公司",
        4110: "乐农物联网有限公司"
    },

    /**
     * @description: 逆变器相关
     */
    bindStatusList: {
        YES: "已绑定",
        NO: "未绑定"
    },
    collectorStateList: {
        1: "在线",
        2: "离线",
        3: "报警"
    },
    elecStatusList: {
        YES: "是",
        NO: "否"
    },
    inveterStateList: {
        1: "在线",
        2: "离线",
        3: "报警"
    },

    /**
     * @description: 项目类型
     */
    proTypeList: {
        "COMMON": "普通户用",
        "HOUSEHOLD": "户用项目租赁模式",
        "PUB_BUILD": "公共建筑租赁模式",
        "WHOLE_VILLAGE": "整村模式"
    },

    /**
     * @description: 施工队
     */
    // 派工状态
    dispatchStatus: {
        WAIT_CONFIRM: "派工待确认",
        ACCEPT: "已承接",
        CANCEL: "已取消"
    },
    // 施工状态
    roadWorkStatus: {
        INIT: "初始",
        BASIC_BUILD: "基础搭建",
        RACK_MOUNTING: "支架安装",
        MODULE_INSTALL: "组件安装",
        INVETER_INSTALL: "逆变器及配电箱安装",
        SYSTEM_GROUND: "系统接地",
        ROADWORK_WAIT_SUBMIT: "施工信息待提交",
        ROADWORK_WAIT_AUDIT: "施工信息待审核",
        ROADWORK_REJECT: "施工信息驳回",
        FINISHED: "施工完成"
    },
    // 开工状态
    startWorkStatus: {
        NO_START: "未开工",
        STARTED: "已开工"
    },

    /**
     * @description: 分中心
     */
    subCenterList: [{
            value: "GFBJ",
            label: "北京"
        },
        {
            value: "GFCC",
            label: "长春"
        },
        {
            value: "GFCD",
            label: "成都"
        },
        {
            value: "GFCQ",
            label: "重庆"
        },
        {
            value: "GFCS",
            label: "长沙"
        },
        {
            value: "GFDL",
            label: "大连"
        },
        {
            value: "GFFZ",
            label: "福州"
        },
        {
            value: "GFGY",
            label: "贵阳"
        },
        {
            value: "GFGZ",
            label: "广州"
        },
        {
            value: "GFHEB",
            label: "哈尔滨"
        },
        {
            value: "GFHF",
            label: "合肥"
        },
        {
            value: "GFHHHT",
            label: "内蒙"
        },
        {
            value: "GFHK",
            label: "海口"
        },
        {
            value: "GFHZ",
            label: "杭州"
        },
        {
            value: "GFJIN",
            label: "济宁"
        },
        {
            value: "GFJN",
            label: "济南"
        },
        {
            value: "GFJZ",
            label: "锦州"
        },
        {
            value: "GFKM",
            label: "昆明"
        },
        {
            value: "GFLZ",
            label: "兰州"
        },
        {
            value: "GFNB",
            label: "宁波"
        },
        {
            value: "GFNC",
            label: "南昌"
        },
        {
            value: "GFNJ",
            label: "南京"
        },
        {
            value: "GFNN",
            label: "南宁"
        },
        {
            value: "GFQD",
            label: "青岛"
        },
        {
            value: "GFSH",
            label: "上海"
        },
        {
            value: "GFSJZ",
            label: "石家庄"
        },
        {
            value: "GFSY",
            label: "沈阳"
        },
        {
            value: "GFSZ",
            label: "深圳"
        },
        {
            value: "GFTJ",
            label: "天津"
        },
        {
            value: "GFTS",
            label: "唐山"
        },
        {
            value: "GFTY",
            label: "太原"
        },
        {
            value: "GFWH",
            label: "武汉"
        },
        {
            value: "GFWX",
            label: "无锡"
        },
        {
            value: "GFXA",
            label: "西安"
        },
        {
            value: "GFXF",
            label: "襄樊"
        },
        {
            value: "GFXJ",
            label: "新疆"
        },
        {
            value: "GFXM",
            label: "厦门"
        },
        {
            value: "GFXN",
            label: "西宁"
        },
        {
            value: "GFXZ",
            label: "徐州"
        },
        {
            value: "GFYC",
            label: "银川"
        },
        {
            value: "GFYT",
            label: "烟台"
        },
        {
            value: "GFZZ",
            label: "郑州"
        }
    ],

    /**
     * @description: 获单人员状态
     */
    orderUserStatusList: [{
            value: "ENABLE",
            label: "正常"
        },
        {
            value: "FREEZE",
            label: "冻结"
        },
        {
            value: "DISABLE",
            label: "已删除"
        }
    ],

    /**
     * @description: 越秀模式
     */
    // 工行二类户开具状态
    ownerCardStatus: {
        WAIT_HANDLE: "未办理",
        HANDLED: "已办理"
    },
    // 授权协议
    ownerSignStatus: {
        WAIT_SIGN: "未签署",
        SIGNED: "已签署"
    },
    // 状态
    ownerStatus: {
        WAIT_SIGN: "待签署",
        WAIT_CHECK: "待审核",
        APPROVED: "预审通过",
        REJECT: "预审拒绝"
    },
    //能源币类型
    recordTypeList: [{
            value: "LIGHT_FINISHED",
            label: "到账"
        },
        {
            value: "RETURNED",
            label: "退回"
        },
        {
            value: "SHOP",
            label: "消耗"
        }
    ],
    /**
     * @description: 分阶段完工
     */
    //支架完工图片类型
    holderImageList: {
        BACK: "背拉照片",
        FIXED: "框架固定照片",
        COMPLETE_MAIN: "支架安装完整体正面照片",
        COMPLETE_SIDE: "支架安装完整体侧（背）面照片"
    },
    //电站类型
    stationTypeList: {
        WHOLE_VILLAGE: "整村推进",
        HOUSEHOLD: "户用租赁",
        PUB_BUILD: "公共租赁",
        COMMON: "普通户用"
    },
    //完工办理类型
    typeList: {
        SUPPORT_COMPLETE: "支架完工",
        MODULE_PART_COMPLETE: "组件部分完工"
    },
    //状态
    holderStatus: {
        WAIT_AUDIT: "待审核",
        AUDIT_OK: "审核通过",
        AUDIT_REJECT: "审核驳回"
    },
    //支架完工图片类型
    moudleImageList: {
        BACK: "侧（背）面照片",
        MAIN: "正面照片"
    },
    /**
     * @description: 光伏调拨单
     */
    // 调拨单状态
    transferStatusList: {
        WAIT_TRANSFER: "待调出",
        TRANSFERED: "已调出",
        COMPLETED: "调拨完成",
        CANCELED: "已取消"
    },
    /**
     * @description: 光伏调拨单类型
     */
    // 调拨单类型
    transferTypeList: {
        ORDINARY_TRANSFER: "普通调拨",
        FROM_TRANSIT: "中转仓发货",
    },
    /**
     * @description: 海外订单管理
     */
    //// 预测订单
    // CREATED("CREATED", "待审核"),
    // SCBREJECT("SCBREJECT", "审核驳回"),
    // SCBPASS("SCBPASS", "待评审"),
    // GYLREJECT("GYLREJECT", "评审驳回"),
    // GYLPASS("GYLPASS", "评审完成");
    forecastOrdersStatus: [
        // { value: "CREATED", label: "待审核" },
        // { value: "SCBREJECT", label: "审核驳回" },
        // { value: "SCBPASS", label: "待评审" },
        // { value: "GYLREJECT", label: "评审驳回" },
        {
            value: "GYLPASS",
            label: "评审完成"
        }
    ],
    //// 备货订单
    // 待确认 CREATED
    // 待排产 PRODUCTION
    // 待发货 SHIPPED
    // 待收货 RECEIVED
    // 已签收 SIGNED
    stockOrderStatus: [
        // { value: "CREATED", label: "待确认" },
        {
            value: "PRODUCTION",
            label: "待排产"
        },
        {
            value: "SHIPPED",
            label: "待发货"
        },
        {
            value: "RECEIVED",
            label: "待收货"
        },
        {
            value: "SIGNED",
            label: "已签收"
        }
    ],
    /**
     * @description: 资料库
     */
    // 资料库文档类型
    dataTypeList: {
        TRAINING: "培训资料",
        EXPERTISE: "专业知识",
        TECHNICAL_STANDARD: "技术标准",
        SCHEME_PARAMETERS: "方案参数",
        SCHEME_ATLAS: "方案图集"
    },
    //资料库业务类别
    businessType: {
        0: "建站",
        1: "运维"
    },

    /**
     * @description: 划转用户
     */
    // 划转用户列表状态
    transferList: {
        WAIT_IMPORT_USER: "待导入用户信息",
        WAIT_IMPORT_REPAY_PLAN: "待导入用户还款计划",
        IMPORT_SUCCESS: "导入成功",
        SIGN: "签约成功"
    },
    // 配电箱品牌
    distributionList: {
        "1": "迈威漫",
        "2": "顶创HV",
        "3": "昌松电气",
        "4": "科帝臣",
        "5": "东盈电力",
        "6": "大志美德",
        "7": "加能",
        "8": "天正"
    },
    /**
     * @description: 银联签约列表
     */
    // 状态
    bankStatusList: {
        WAIT_IMPORT: "待导入银联",
        IMPORT_SUCCESS: "导入成功",
        IMPORT_FAIL: "导入失败",
        SIGN_SUCCESS: "签约成功"
    },
    // 认证状态
    authStatusList: {
        0: "未认证",
        1: "已认证"
    },
    // 签约状态
    signStatusList: {
        0: "未签约",
        1: "已签约"
    },
    // 签约状态
    queryOverFlag: {
        0: "否",
        1: "是",
    },
    selectCharacter: [{
            value: "SURVEYOR",
            label: "勘测员"
        },
        {
            value: "ACQUIRER",
            label: "获单员"
        },
        {
            value: "POWER_STATION_INFO",
            label: "信息员"
        },
        {
            value: "STORE_MANAGER",
            label: "仓库管理员"
        },
        {
            value: "FINANCE",
            label: "财务"
        },
        {
            value: "REGIONAL_MANAGER",
            label: "区域经理"
        }
        // {
        //     value: "CONSTRUCTOR",
        //     label: "施工员",
        // }
    ],
    emStatus: [{
            value: "ENABLE",
            label: "有效"
        },
        {
            value: "DISABLE",
            label: "失效"
        }
    ],
    serverAll: [{
            value: "YES",
            label: "是"
        },
        {
            value: "NO",
            label: "否"
        }
    ],
    quotaStatus: {
        ENABLE: "已启用",
        DISABLE: "已失效"
    },
    quotaType: {
        COMMON: "普通档",
        APPEND: "追加档"
    },
    /**
     * @description: 并网奖励状态
     */
    gridRewardStatus: [{
            value: "NOT_PAY",
            label: "未出款"
        },
        {
            value: "PAY_SUCCESS",
            label: "已出款"
        }
    ],
    /**
     * @description: 并网奖励类型
     */
    gridRewardType: [{
            value: "1",
            label: "并网时效奖励"
        },
        {
            value: "2",
            label: "月度规模奖励"
        },
        {
            value: "3",
            label: "并网时效奖励(项目)"
        },
        {
            value: "4",
            label: "并网时效奖励(负向)"
        },
        {
            value: "5",
            label: "安装量对赌奖励"
        }
    ],
    /**
     * @description: 并网类型
     */
    gridType: [{
            value: "1",
            label: "并网时效奖励(户用)"
        },
        {
            value: "2",
            label: "月度规模奖励"
        },
        {
            value: "3",
            label: "并网时效奖励(项目)"
        },
        {
            value: "4",
            label: "并网时效奖励(负向)"
        },
        {
            value: "5",
            label: "安装量对赌奖励"
        }
    ],
    /**
     * @description: 即时性激励状态
     */
    incentivesStatus: [{
            value: "1",
            label: "未出款"
        },
        {
            value: "2",
            label: "已出款"
        }
    ],
    basicInfoStatus: [{
            value: "0, 2",
            label: "待审核"
        },
        {
            value: "1,3",
            label: "审核驳回"
        },
        {
            value: "4",
            label: "审核通过"
        }
    ],
    // * 结算汇总状态
    settleStatus: {
        "WAIT_SETTLE": "待结算",
        "INVOICE_VERIFIED": "发票已核销",
        "SETTLED": "已结算"
    },
    policyStandardObj: {
        STATION_ORDER: "电站下单时间",
        MODULE_SEND: "组件发货时间" // installDays
    },
    policyTypeObj: {
        IMMEDIATE_INCENTIVE: "即时性激励"
    },
    // 用户登记状态
    zhOwnerStatus: {
        SUBMITTED: "已提交业主信息"
    },
    // 待勘测状态
    surveyStatus: {
        "0": "服务中"
    },
    //电站开发预警报表
    earlyWarningStatus: {
        INIT: "待提交审核",
        WAIT_AUDIT: "待审核",
        AUDIT_REJECT: "驳回",
        WAIT_CONFIG: "审核完成待配方案",
        WAIT_ORDER: "待下单",
        ROADWORKING: "施工中",
        ROADWORK_CONFIRM: "施工信息待确认",
        ROADWORK_REJECT: "施工信息驳回",
        COMPLETE_WAIT_AUDIT: "待审核",
        COMPLETE_AUDIT_REJECT: "被驳回",
        APPLY_CHECK: "施工完成待申请并网验收",
        WAIT_CHECK: "待验收",
        // WAIT_FIRST_AUDIT: "待终审一验",
        // FIRST_AUDIT_REJECT: "终审一验驳回",
        WAIT_FINAL_AUDIT: "待终审二验",
        FINAL_AUDIT_REJECT: "终审二验驳回",
        WAIT_TECH_CHECK: "待技术审核",
        TECH_CHECK_REJECT: "技术审核驳回",
        WAIT_UPLOAD_GRID_INFO: "提交并网验收资料",
        WAIT_FIRST_AUDIT: "待商务审核",
        FIRST_AUDIT_REJECT: "商务审核驳回",
        ENABLE: "已完成",
        DISABLE: "删除",
        STOP: "已终止"
    },
    // 组件库龄
    ageGtConditionList: {
        30: "大于30天",
        45: "大于45天",
        60: "大于60天"
    },
    // 发票状态
    invoiceStatusList: {
        1: '未使用',
        2: '核销中',
        3: '已核销',
    },
    // 发票占用状态
    occupyStatusList: {
        0: '未占用',
        1: '已占用',
    },
    // 发票类型
    invoiceTypeList: {
        '01': '纸质专票',
        '04': '纸质普票',
        '10': '电子普票',
        '08': '电子专票',
        '08xdp': '全电专',
        '10xdp': '全电普',
    },
    // 检验结果
    checkStatus: {
        0: "检查通过(正常)",
        1: "检查不通过(失控)",
        2: "检查不通过(作废)",
        3: "检查不通过(红冲)",
        4: "检查不通过(异常)",
    },
    // 请款申请审核状态
    purchaseAuditStatusList: {
        0: "待总部审核",
        1: "审核通过",
        2: "审核驳回",
    },
    // 请款申请对账单状态
    confirmStatusList: {
        0: "待确认",
        1: "已确认",
    },
    // 请款申请发票关联状态
    invoiceRelevanceList: {
        0: "待关联",
        1: "已关联",
        2: "已驳回",
        3: "已核销",
    },
    // 请款申请类型
    purchaseTypeList: {
        12: "原材料总账",
        13: "工程安装",
    },
    // 请款申请节点
    operateTypeList: {
        1: '提交',
        2: '修改',
        3: '总部经办审核通过',
        4: '总部经办审核驳回',
        5: '确认对账单',
        6: '关联发票',
        7: '发票核销',
        8: '出款',
    },
    // 额度政策类型
    categoryType: {
        FOUR_ITEM: '整套',
        TWO_ITEM: '两件套'
    },
    // 基础政策-屋顶类型
    policyHouseTypeList: [{
            value: "普通屋顶",
            label: "普通屋顶",
        },
        {
            value: "双面坡",
            label: "双面坡",
        },
        {
            value: "院内地面",
            label: "院内地面",
        },
        {
            value: "阳光房",
            label: "阳光房",
        },
    ],
    // 基础政策-模式
    policyModeList: [{
            label: "BT",
            value: "HR",
        },
        {
            label: "EPC(YX)",
            value: "YX",
        },
        {
            label: "EPC(ZH)",
            value: "ZH",
        },
        {
            label: "EPC(HD)",
            value: "CHD_EPC",
        },
    ],
    // 借件申请状态
    borrowItemsStatusList: {
        WAIT_AUDIT: "待审核",
        AUDIT_PASS: "已审核",
        AUDIT_REJECT: "已驳回",
    },
    // 借件出库状态
    borrowItemsList: {
        WAIT_DELIVERY: "待发货",
        WAIT_SIGNED: "待签收",
        SIGNED: "已签收",
    },
    // 负激励申诉查询状态
    awardAppealAuditStatusList: {
        0: "待审核",
        1: "审核通过",
        2: "审核驳回",
    },
    // 负激励申诉状态
    awardAppealStatusList: {
        WAIT_SUB_CENTER_AUDIT: "待分中心审核",
        SUB_CENTER_AUDIT_REJECT: "分中心审核驳回",
        WAIT_MARKET_LEADER_AUDIT: "待市场部主管审核",
        MARKET_LEADER_AUDIT_REJECT: "市场部主管审核驳回",
        WAIT_XIAOWEI_AUDIT: "待小微主审核",
        XIAOWEI_AUDIT_REJECT: "小微主驳回",
        WAIT_ENABLE: "待启用",
        CANCEL: "取消",
        ENABLE: "启用",
        DISABLE: "禁用",
    },
    // 负激励类型
    awardTypeList: [{
        label: '并网时效奖励(负向)',
        value: '4'
    }, ],
    // 负激励申诉原因
    awardReasonList: [{
            label: '政府叫停',
            value: '1'
        },
        {
            label: '区域限容',
            value: '2'
        },
        {
            label: '自然灾害',
            value: '3'
        },
        {
            label: '其他原因',
            value: '99'
        },
    ],
    // 共享额度状态
    shareQuotaStatus: [{
            label: "待分中心审核",
            value: "WAIT_CENTER_AUDIT",
        },
        {
            label: "待总部大区审核",
            value: "WAIT_HEAD_REGION_AUDIT",
        },
        {

            label: "待业务经理审核",
            value: "WAIT_BUSINESS_MANAGER_AUDIT",
        },
        {
            label: "待供应链审核",
            value: "WAIT_SUPPLY_CHAIN_AUDIT",
        },
        {
            label: "待链群主审核",
            value: "WAIT_CHAIN_OWNER_AUDIT",
        },
        {
            label: "待签署",
            value: "WAIT_SIGN",
        },
        {
            label: "已完成",
            value: "ENABLE",
        },
        {
            label: "已驳回",
            value: "REJECT",
        },
        {
            label: "已终止",
            value: "STOP",
        },
    ],
    // 结算汇总-质保金状态
    depositStatusList: {
        CREATED: "已生成",
        EFFECTIVE: "已生效",
        SETTING: "结算中",
        SETTLED: "已结算",
    },
    // 质保金-业务类别
    businessTypeList: {
        DETETION: "扣留保证金",
    },
    // 质保金-类型
    retentionMoneyType: {
        SERVICE_DEPOSIT: "服务保证金",
    },
    // 质保金-状态
    retentionMoneyStatus: [{
            label: "已生成",
            value: "CREATED",
        },
        {
            label: "已生效",
            value: "FFECTIVE",
        },
        {
            label: "结算中",
            value: "SETTING",
        },
        {
            label: "已结算",
            value: "SETTLED",
        },
    ],
    // 新商政策-政策类型
    newMerchantPolicyType: {
        NEW_MERCHANT: "新商政策",
    },
    // 新商政策-兑现标准
    newMerchantPolicyRewardStandardList: {
        INSTALL_STANDARD_30: "30天内成功安装电站",
        ONGRID_STANDARD_60: "60天内成功并网电站",
    },
    // 新商政策-兑现单位
    newMerchantPolicyRewardUnitList: {
        W: "瓦",
    },
    // 新商政策-兑现节点
    newMerchantPolicyRewardNodeList: {
        ACTIVE_UNIFY_60: "活跃期60天后统一核算",
    },
    // 新商政策
    newMerchantPolicyStandardType: [{
        label: "新商政策",
        value: "NEW_MERCHANT",
    }, ],
    // 新商政策结算-付款状态
    newMerchantPolicyStandardStatus: [{
            value: "NOT_PAY",
            label: "未出款"
        },
        {
            value: "PAY_SUCCESS",
            label: "已出款"
        }
    ],
    // 激励政策-模式
    policyMode: {
        'BT': 'BT',
        'YX': 'YX',
        'YX_EPC': 'EPC(YX)',
        'ZH_EPC': 'EPC(ZH)',
        'CHD_EPC': 'EPC(HD)',
        'EPC': 'EPC',
    },
    // 借件出库-订单类型
    orderSourceList: {
        '0': "普通订单",
        '1': "特殊订单",
    },
    // 电站回购-状态
    enrolmentSupplyStatus: [{
            label: "待审核",
            value: "WAIT_AUDIT",
        },
        {
            label: "已驳回",
            value: "AUDIT_REJECT",
        },
        {
            label: "待签署协议",
            value: "WAIT_SIGNED",
        },
        {
            label: "待支付",
            value: "WAIT_PAY",
        },
        {
            label: "待验收",
            value: "WAIT_RECEIPT",
        },
        {
            label: "交易完成",
            value: "FINISH",
        },
        {
            label: "已取消",
            value: "CANCEL",
        },
    ],
    // 电站回购-支付方式
    enrolmentSupplyPayType: [{
            label: "转账汇款",
            value: "TRANSFER",
        },
        {
            label: "扣减保证金",
            value: "DEPOSIT",
        },
    ],
    // 电站回购-付款状态
    enrolmentSupplyPayStatus: [{
            label: "待付款",
            value: "NOT_PAY",
        },
        {
            label: "待财务确认",
            value: "WAIT_FINANCE_CONFIRM",
        },
        {
            label: "已付款",
            value: "PAY_SUCCESS",
        },
    ],
    // 电站回购-发票类型
    enrolmentSupplyInvoiceType: [{
            label: "普通发票",
            value: "PP",
        },
        {
            label: "增值税专用发票",
            value: "ZP",
        },
    ],
    // 电站回购-购买原因
    enrolmentSupplyReasonType: [{
            label: "服务商原因",
            value: "SP",
        },
        {
            label: "其他原因",
            value: "OTHER",
        },
    ],
    // 配货管理-状态
    distManageStatusList: [{
            label: "待提交",
            value: "TO_BE_SUBMITTED",
        },
        {
            label: "下单待审核",
            value: "WAIT_CONFIRM",
        },
        {
            label: "待配货",
            value: "WAIT",
        },
        {
            label: "已配货",
            value: "FINISHED",
        },
        {
            label: "已配货",
            value: "FINISHED",
        },
        {
            label: "已退回",
            value: "RETURNED",
        },
        {
            label: "已终止",
            value: "STOP",
        }
    ],
    // 辅料配货单-状态
    orderManageStatusList: [{
            label: "待审核",
            value: "WAIT_AUDIT",
        },
        {
            label: "待发货",
            value: "WAIT_SEND",
        },
        {
            label: "待签收",
            value: "WAIT_SIGN",
        },
        {
            label: "已签收",
            value: "ALREADY_SIGN",
        }
    ],
    // 主料配货单-状态
    distriStatusList: [{
            label: "待提交",
            value: "TO_BE_SUBMITTED",
        },
        {
            label: "待奥客隆审核",
            value: "WAIT_FOR_AUCLON_TO_CONFIRM",
        },
        {
            label: "待分中心审核",
            value: "WAIT_FOR_SUB_CENTER_TO_AUDIT",
        },
        {
            label: "待供应链配货",
            value: "WAIT_FOR_SUPPLY_CHAIN_TO_MAKE_STOCK",
        },
        {
            label: "已配货",
            value: "FINISHED",
        },
        {
            label: "已退回",
            value: "RETURNED",
        },
        {
            label: "已终止",
            value: "STOP",
        }
    ],
    // 配货方式
    distriAffirmStatusList: [{
            label: "直营配货",
            value: "DIRECT_SALES",
        },
        {
            label: "调拨配货",
            value: "ALLOCATION",
        }
    ],
    gridStatusList: [{
            value: 'ENABLE',
            label: '已启用',
        },
        {
            value: 'DISABLE',
            label: '已失效',
        },
    ],
    gridUnitList: [{
            value: 'KUAI',
            label: '块',
        },
        {
            value: 'W',
            label: '瓦',
        },
    ],
    gridAwardTypeList: [{
            value: '0',
            label: '组件发货时间',
        },
        {
            value: '1',
            label: '其他',
        },
    ],
    applyForList: [{
            value: 'INIT',
            label: '待分中心审核',
        },
        {
            value: 'SUBCENTER',
            label: '待分中心审核',
        },
        {
            value: 'WAIT_PARALLEL_AUDIT',
            label: '待总部审核',
        },
        {
            value: 'FINANCE',
            label: '待财务审核',
        },
        {
            value: 'CHAIN',
            label: '链群主审核',
        },
        {
            value: 'CONFIRM_WAIT',
            label: '总部确认',
        },
        {
            value: 'CONFIRM_SP',
            label: '待服务商确认',
        },
        {
            value: 'CONFIRM',
            label: '待财务确认',
        },
        {
            value: 'SETTLEMENT',
            label: '待退款/待汇款',
        },
        {
            value: 'REJECT_SUB',
            label: '分中心驳回',
        },
        {
            value: 'REJECT',
            label: '总部已驳回',
        },
        {
            value: 'TERMINATE',
            label: '已终止',
        },
        {
            value: 'ENABLE',
            label: '已退出',
        }
    ]
};
