<template>
    <div class="layout">
        <template v-if="microEnv">
            <keep-alive>
                <router-view v-if="$route.meta.keepAlive" class="views"/>
            </keep-alive>
            <router-view class="views" v-if="!$route.meta.keepAlive"/>
        </template>
        <template v-else>
            <Header ref="PageHeader"/>
            <div class="content">
                <div class="content_left" ref="Sider" :style="{ height: autoHeight }">
                    <slider-menus ref="sliderMenus"/>
                </div>
                <Layout class="main_view" :style="{ paddingLeft: '15px' }">
                    <template>
                        <keep-alive>
                            <router-view v-if="$route.meta.keepAlive" class="views"/>
                        </keep-alive>
                        <router-view class="views" v-if="!$route.meta.keepAlive"/>
                    </template>
                </Layout>
            </div>
        </template>
    </div>
</template>
<script>
import {mapGetters, mapState} from "vuex";
import SliderMenus from "@/components/layout/sliderMenus.vue";
import Header from "../components/layout/header.vue";


export default {
    name: "microApp",
    data() {
        return {
            userArr: [],
            autoHeight: null
        };
    },

    components: {
        Header,
        SliderMenus
    },

    computed: {
        microEnv() {
            return window.__POWERED_BY_QIANKUN__
        }
    },

    mounted() {
        !this.microEnv && this.listeners()
    },

    beforeDestroy() {
        !this.microEnv && this.removeScrollListener()
    },

    methods: {
        // * 监听 header和scroll 菜单自适应
        listeners() {
            // * scroll
            window.addEventListener('scroll', this.handleResize)
            // * 监听header视图
            const dom = this.$refs.PageHeader.$el;
            this.observer = new ResizeObserver(this.handleResize);
            this.observer.observe(dom, {box: "content-box"});
        },

        removeScrollListener() {
            window.removeEventListener('scroll', this.handleResize);
            this.observer.disconnect();
        },

        handleResize() {
            let sc = window.innerHeight - this.$refs.Sider?.getBoundingClientRect()?.top - 15
            this.autoHeight = sc + 'px'
        }
    }
};
</script>
<style scoped lang="scss">
.layout {
    min-width: 1143px;
    background: #f5f7f9;
    position: relative;
    border-radius: 4px;
}

.content {
    position: relative;
    width: 100%;
    max-width: 1920px;
    height: auto;
    margin: 0 auto;
    padding: 0 15px;
}

.content_left {
    position: sticky;
    top: 15px;
    width: 18%;
    max-width: 240px;
    height: $min-height;
    float: left;
    background: #fff;
    transition: width 0.5s, height 0.5s;
    overflow-x: hidden;
    overflow-y: auto;
    border-radius: $border-raius-10;
}

.main_view {
    background: transparent;
    overflow-x: hidden;

    .views {
        border-radius: $border-raius-10;
    }
}

.skeleton {
    position: relative;
    min-height: calc(100vh - 270px);
    background: #fff;
}
</style>
