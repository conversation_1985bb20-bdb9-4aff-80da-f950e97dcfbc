<template>
    <div class="login">
        <div class="head">
            <img class="logo_img" src="../assets/logo1.png" alt />
        </div>
        <div class="content">
            <div class="content_son">
                <div class="content_son_left"><img src="../assets/img/login11.png" alt /></div>
                <div class="content_son_right">
                    <div class="login_div">
                        <p class="login_p">
                            商户中心
                        </p>
                        <div class="login_div_two">
                            <span><img src="../assets/img/bj27.png" alt /></span>
                            <input id="userName" class="inputs" v-model="userName" placeholder="请输入账号" type="text" />
                        </div>
                        <div class="login_div_two">
                            <span><img src="../assets/img/bj28.png" alt /></span>
                            <input id="passWord" class="inputs" v-model="passWord" placeholder="请输入密码" type="password" />
                        </div>
                        <div class="login_div_four">
                            <div :class="{ color_two: isActive }" @click="isShow" class="checkboxTwo"><div></div></div>
                            <p class="login_div_four_p">自动登录</p>
                        </div>
                        <i-button type="error" class="colorSign" @click="loginBtn" long>登录</i-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import {getUserInfo, login} from '@/api';
export default {
    name: 'login',
    data() {
        return {
            userName: '',
            passWord: null,
            isActive: false
        };
    },
    created() {},
    methods: {
        loginBtn() {
            if (!this.userName || !this.passWord) {
                this.$Message.error('填写内容不能为空');
                return;
            }
            let data = {
                username: this.userName,
                password: this.passWord,
                grant_type: 'password',
                scope: 'pc'
            };
            login(data)
                .then(res => {
                    localStorage.clear();
                    res.data.startTime = new Date().getTime();
                    localStorage.setItem('logins', JSON.stringify(res.data));
                })
                .then(() => {
                    let data = {
                        access_token: JSON.parse(localStorage.getItem('logins')).access_token
                    };
                    getUserInfo(data).then(res => {
                        let authorities = res.data.authorities
                        const userArr = authorities?.map(e => e.authority)
                        localStorage.setItem('userArr', userArr);
                    }).then(() => {
                        this.$router.push({ path: '/' });
                    })
                })
                .catch(err => {
                    if(err.error === 'invalid_token') {
                        localStorage.clear();
                        router.push("/login");
                    } else {
                        this.$Message.error(err.data?.error_description || err?.error_description || '登录失败，请确认账户和密码');
                    }
                });
        },

        isShow() {
            this.isActive = !this.isActive;
        }
    }
};
</script>
<style scoped lang="scss">
@import '@/style/login.scss';
</style>
