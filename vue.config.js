const { name } = require("./package");
const CompressionWebpackPlugin = require("compression-webpack-plugin");
const TerserPlugin = require("terser-webpack-plugin");
const isProduction = process.env.NODE_ENV === "production";
const isDev = process.env.NODE_ENV === "development";

module.exports = {
   publicPath: `/sub/${process.env.VUE_APP_SUBNAME}`,
  filenameHashing: true,
  configureWebpack: (config) => {
    // * 文件压缩
    const CompressionConfig = new CompressionWebpackPlugin({
      filename: "[path].gz[query]",
      algorithm: "gzip",
      test: /\.(js|css|html|json|xml|svg|txt|md|ico)(\?.*)?$/i,
      threshold: 10240,
      minRatio: 0.8,
      deleteOriginalAssets: false, // 保留原始文件
    });

    const TerserPluginConfig = new TerserPlugin({
      parallel: true,
      terserOptions: {
        compress: {
          drop_console: true, // 移除 console
          drop_debugger: true, // 移除 debugger
          pure_funcs: ["console.log"], // 移除 console.log
        },
        mangle: {
          toplevel: true, // 顶层变量重命名
        },
        output: {
          comments: false, // 删除所有注释
        },
      },
    });

    // 定义通用的配置更改，包括输出文件名
    const commonConfig = {
      output: {
        // 使用时间戳作为文件名的一部分
        filename: `js/[name].[hash].bundle.js`,
        chunkFilename: `js/[name].[chunkhash].chunk.js`,
        library: `${name}-[name]`,
        libraryTarget: "umd", // 把微应用打包成 umd 库格式
        jsonpFunction: `webpackJsonp_${name}`, // webpack 5 需要把 jsonpFunction 替换成 chunkLoadingGlobal
      },
      optimization: {
        minimizer: [],
      },
      plugins: [CompressionConfig],
    };

    // 如果是生产环境，添加额外的配置
    if (isProduction || isDev) {
      // * 构建文件大小的性能提示
      commonConfig.performance = {
        hints: false,
      };
      commonConfig.optimization.minimizer.push(TerserPluginConfig);
    } else {
      // 本地开发环境需要的插件
      // * commonConfig.plugins.push(new BundleAnalyzerPlugin());
    }

    return commonConfig;
  },
  chainWebpack: (config) => {
    // 设置目录别名alias
    config.resolve.alias
      .set("assets", "@/assets")
      .set("components", "@/components")
      .set("views", "@/views")
      .set("style", "@/style")
      .set("api", "@/api")
      .set("store", "@/store")
      .set("filters", "@/filters")
      .set("utils", "@/utils")
      .set("router", "@/router");
  },
  css: {
    // 是否使用css分离插件 ExtractTextPlugin
    extract: isProduction,
    // 开启 CSS source maps?
    sourceMap: !isProduction,
    // css预设器配置项
    // 启用 CSS modules for all css / pre-processor files.
    requireModuleExtension: true,
    loaderOptions: {
      sass: {
        data: '@import "@/style/_mixin.scss";@import "@/style/_variables.scss";@import "@/style/_flex.scss";', // 全局引入
      },
    },
  },
  lintOnSave: !isProduction, // default false
  // 打包时不生成.map文件
  productionSourceMap: false,
  devServer: {
    headers: {
      "Access-Control-Allow-Origin": "*",
    },
    open: true, // 启动服务后是否打开浏览器
    host: "127.0.0.1",
    port: 8880, // 服务端口
    https: false,
    // 设置代理，用来解决本地开发跨域问题，如果设置了代理，那你本地开发环境的axios的baseUrl要写为 '' ，即空字符串
    // proxy: 'http://console.rrsjk.com' // 设置代理
    proxy: {
      "/down": {
        target: "https://cdn01.rrsjk.com",
        changeOrigin: true,
        pathRewrite: { "^/down": "" },
      },
      "/api/merchant": {
            // target: "http://***********:8087/",
            target: "http://operation.xiaoxianglink.com/",
            changeOrigin: true,
            pathRewrite: { "^/api": "" }
        },
        '/api': {
            target: "http://console.rrsjk.com",
            changeOrigin: true,
            pathRewrite: { "^/api": "" }
        }
    },
  },
};
