{"name": "nahui-pv.merchant-micro.zch", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve --mode local", "preview": "vue-cli-service serve --mode production", "build": "vue-cli-service build --mode production", "dev-build": "vue-cli-service build --mode development", "lint": "vue-cli-service lint", "analyzer": "vue-cli-service build --mode production --report"}, "dependencies": {"axios": "1.6.0", "lodash": "^4.17.21", "perfect-scrollbar": "^1.5.5", "qiankun": "^2.10.16", "view-design": "4.7.0", "vue": "2.7.14", "vue-router": "3.6.5", "vue2-perfect-scrollbar": "^1.5.56", "vuex": "^3.0.1", "xlsx": "^0.16.1", "echarts": "^5.3.3"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.0.0", "@vue/cli-plugin-eslint": "^4.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-service": "^4.0.0", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "^10.0.1", "babel-plugin-component": "^1.1.1", "compression-webpack-plugin": "^2.0.0", "core-js": "^3.22.5", "eslint": "^5.8.0", "eslint-plugin-vue": "^5.0.0", "postcss-px2rem": "^0.3.0", "qs": "^6.7.0", "sass": "1.36.0", "sass-loader": "7.3.1", "script-loader": "^0.7.2", "uglifyjs-webpack-plugin": "^2.1.2", "vue-template-compiler": "^2.5.21"}}