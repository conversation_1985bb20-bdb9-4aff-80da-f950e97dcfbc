[利用Jenkins + nginx 实现前端项目自动构建与持续集成](https://segmentfault.com/a/1190000019212628)

## Frame About
- 脚手架工具：vue-cli4（内置 webpack4）
- 前端框架：vue2
- 路由管理：vue-router
- 接口服务：axios
- 状态管理器：Vuex3(左侧菜单使用)
- 开发语言：JavaScript(Es6++)
- UI组件：iView2
- css样式：sass(scss)
- 代码规范：Eslint、Prettier等
- 其它插件见 'package.json'。比较老的代码和调用不解释，请自行理解，谨慎处理。

## Mixins
- 自封装 vue 防抖组件。（熟练掌握通用防抖/截流可使用 Lodash 开源代码库）

## Project setup 
初始化/安装依赖
```shell
npm install 
or
yarn
```
### Compiles and hot-reloads for development(local) 
本地运行
```shell
npm run serve
or
yarn serve
```
### Compiles and hot-reloads for production(local) 
本地运行生产环境（无热更新，加载时间长）
```shell
npm run preview
or
yarn preview
```
### Compiles and minifies for development  
打包测试环境
```shell
npm run dev-build
or
yarn dev-build
```
### Compiles and minifies for production  
打包生产环境
```shell
npm run build
or
yarn build
```
### Lints and fixes files  
检测语法错误  
历史问题较多，可忽略
2022.08 更新：可在自己项目开启 eslint（已修复影响运行的历史问题），自定义配置检查。建议配合自己工具的语法插件 eslint 一起使用。
```
npm run lint
or
yarn lint
```